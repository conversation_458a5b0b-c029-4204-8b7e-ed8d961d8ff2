/**
 * 优化菜单样式脚本
 * 移除图标相关CSS，优化字体和行间距
 */

const fs = require('fs');
const path = require('path');

// 优化后的菜单样式
const optimizedMenuStyles = `        .nav-item {
            display: block;
            padding: 10px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 14px;
            line-height: 1.4;
            font-weight: 400;
        }

        .nav-item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
            font-weight: 500;
        }

        .nav-section-title {
            font-size: 11px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 8px 20px 4px;
            margin-top: 16px;
            line-height: 1.4;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 8px;
        }`;

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 优化单个文件的菜单样式
function optimizeMenuStyles(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找并替换 .nav-item 样式块
        const navItemPattern = /\.nav-item\s*\{[^}]*\}(?:\s*\.nav-item:hover\s*\{[^}]*\})?(?:\s*\.nav-item\.active\s*\{[^}]*\})?(?:\s*\.nav-item\s+i\s*\{[^}]*\})?/g;
        
        if (navItemPattern.test(content)) {
            content = content.replace(navItemPattern, optimizedMenuStyles);
            
            // 移除单独的图标样式规则
            content = content.replace(/\.nav-item\s+i\s*\{[^}]*\}/g, '');
            
            // 优化 nav-section-title 样式
            const sectionTitlePattern = /\.nav-section-title\s*\{[^}]*\}/g;
            if (!sectionTitlePattern.test(content)) {
                // 如果没有找到现有样式，添加新样式
                const styleEndPattern = /<\/style>/;
                content = content.replace(styleEndPattern, `
        .nav-section-title {
            font-size: 11px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 8px 20px 4px;
            margin-top: 16px;
            line-height: 1.4;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 8px;
        }
    </style>`);
            }
            
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已优化: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (未找到菜单样式)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🎨 开始优化菜单样式...\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    for (const file of htmlFiles) {
        if (optimizeMenuStyles(file)) {
            successCount++;
        } else {
            failCount++;
        }
    }
    
    console.log('\n📊 优化统计:');
    console.log(`✅ 成功优化: ${successCount} 个文件`);
    console.log(`⏭️  跳过文件: ${failCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 菜单样式优化完成！');
        console.log('💡 已移除图标相关CSS规则');
        console.log('🎯 已优化字体大小和行间距');
        console.log('✨ 菜单现在更加简洁美观');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    optimizeMenuStyles,
    main
};

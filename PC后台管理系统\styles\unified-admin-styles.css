/*
智能家居管理系统 - 统一后台管理样式库
版本: v3.0 - 智能家居项目黑白灰统一设计规范
基于: 智能家居项目 H5 页面设计规范
更新时间: 2025-01-16
*/

/* ==================== CSS 变量定义 ==================== */
:root {
  /* 主色调 - 智能家居项目标准 */
  --primary-black: #1a1a1a;
  --primary-white: #ffffff;
  --primary-gray: #374151;

  /* 功能性色彩 - 保持专业感的同时增加视觉层次 */
  --accent-blue: #3b82f6;        /* 蓝色强调色 - 用于链接和重要操作 */
  --accent-blue-light: #dbeafe;  /* 浅蓝色背景 */
  --accent-blue-dark: #1d4ed8;   /* 深蓝色悬停 */

  --success-green: #10b981;      /* 成功状态 */
  --success-light: #d1fae5;      /* 成功背景 */

  --warning-orange: #f59e0b;     /* 警告状态 */
  --warning-light: #fef3c7;      /* 警告背景 */

  --error-red: #ef4444;          /* 错误状态 */
  --error-light: #fee2e2;        /* 错误背景 */

  /* 中性色系 - 完整灰度阶梯 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 文字颜色系统 */
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-light: #d1d5db;
  --text-inverse: #ffffff;
  --text-link: #3b82f6;          /* 链接颜色 */

  /* 背景颜色系统 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-muted: #f9fafb;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 边框颜色 */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 间距系统 - 更紧凑 */
  --spacing-xs: 3px;
  --spacing-sm: 6px;
  --spacing-md: 12px;
  --spacing-lg: 18px;
  --spacing-xl: 24px;
  --spacing-2xl: 36px;

  /* 圆角系统 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* 毛玻璃效果系统 - 黑白灰设计规范 */
  --glass-blur-light: blur(8px);
  --glass-blur-medium: blur(12px);
  --glass-blur-heavy: blur(16px);

  /* 黑白灰毛玻璃背景 */
  --glass-bg-white: rgba(255, 255, 255, 0.85);
  --glass-bg-light-gray: rgba(249, 250, 251, 0.8);
  --glass-bg-medium-gray: rgba(243, 244, 246, 0.85);
  --glass-bg-dark-gray: rgba(107, 114, 128, 0.8);
  --glass-bg-black: rgba(26, 26, 26, 0.85);

  /* 毛玻璃边框和阴影 */
  --glass-border-light: rgba(229, 231, 235, 0.3);
  --glass-border-dark: rgba(107, 114, 128, 0.3);
  --glass-shadow-light: 0 8px 32px rgba(0, 0, 0, 0.08);
  --glass-shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* ==================== 毛玻璃效果系统 ==================== */
/* 基础毛玻璃类 */
.glass-light {
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-medium {
  backdrop-filter: var(--glass-blur-medium);
  -webkit-backdrop-filter: var(--glass-blur-medium);
  background: var(--glass-bg-medium);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-heavy {
  backdrop-filter: var(--glass-blur-heavy);
  -webkit-backdrop-filter: var(--glass-blur-heavy);
  background: var(--glass-bg-medium);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-dark {
  backdrop-filter: var(--glass-blur-medium);
  -webkit-backdrop-filter: var(--glass-blur-medium);
  background: var(--glass-bg-dark);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--glass-shadow);
  color: var(--text-inverse);
}

.glass-muted {
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  background: var(--glass-bg-muted);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* 降级方案 - 不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(1px)) {
  .glass-light,
  .glass-medium,
  .glass-heavy {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
  }

  .glass-dark {
    background: var(--primary-black);
    border: 1px solid var(--border-medium);
  }

  .glass-muted {
    background: var(--bg-muted);
    border: 1px solid var(--border-light);
  }
}

/* ==================== 基础样式重置 ==================== */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  color: var(--text-primary);
  background: var(--bg-secondary);
  line-height: 1.3;
  font-size: 13px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==================== 图标背景统一 ==================== */
.stat-icon,
.card-icon,
.icon-primary {
    background: var(--primary-black) !important;
    color: var(--text-inverse) !important;
}

/* 功能性图标 - 使用适度的色彩区分 */
.icon-users,
.icon-user-management {
    background: var(--accent-blue) !important;
    color: var(--text-inverse) !important;
}

.icon-products,
.icon-inventory {
    background: var(--success-green) !important;
    color: var(--text-inverse) !important;
}

.icon-orders,
.icon-sales {
    background: var(--warning-orange) !important;
    color: var(--text-inverse) !important;
}

.icon-projects,
.icon-construction {
    background: var(--gray-700) !important;
    color: var(--text-inverse) !important;
}

.icon-design,
.icon-creative {
    background: var(--primary-black) !important;
    color: var(--text-inverse) !important;
}

.icon-requirements,
.icon-tasks {
    background: var(--gray-600) !important;
    color: var(--text-inverse) !important;
}

/* 次要图标层次 */
.icon-secondary {
    background: var(--gray-600) !important;
    color: var(--text-inverse) !important;
}

.icon-tertiary {
    background: var(--gray-500) !important;
    color: var(--text-inverse) !important;
}

.icon-quaternary {
    background: var(--gray-400) !important;
    color: var(--text-inverse) !important;
}

/* ==================== 按钮系统 ==================== */
.btn,
.button,
.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    border: 1px solid transparent;
    min-height: 36px;
}

/* 主要按钮 - 使用蓝色强调 */
.btn-primary,
.action-btn-primary {
    background: var(--accent-blue) !important;
    color: var(--text-inverse) !important;
    border-color: var(--accent-blue) !important;
}

.btn-primary:hover,
.action-btn-primary:hover {
    background: var(--accent-blue-dark) !important;
    border-color: var(--accent-blue-dark) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 查看按钮 - 保持黑色 */
.btn-view {
    background: var(--primary-black) !important;
    color: var(--text-inverse) !important;
    border-color: var(--primary-black) !important;
}

.btn-view:hover {
    background: var(--gray-700) !important;
    border-color: var(--gray-700) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 次要按钮 */
.btn-secondary,
.btn-manage,
.action-btn-secondary {
    background: var(--bg-primary) !important;
    color: var(--text-secondary) !important;
    border-color: var(--border-light) !important;
}

.btn-secondary:hover,
.btn-manage:hover,
.action-btn-secondary:hover {
    background: var(--bg-muted) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-medium) !important;
    transform: translateY(-1px);
}

/* 按钮尺寸 */
.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    min-height: 28px;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 16px;
    min-height: 44px;
}

/* 块级按钮 */
.btn-block {
    width: 100%;
    display: flex;
}

/* 按钮禁用状态 */
.btn:disabled,
.btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* ==================== 卡片系统 ==================== */
.card,
.overview-card,
.stat-card,
.metric-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.card:hover,
.overview-card:hover,
.stat-card:hover,
.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-medium);
}

/* 指标卡片布局 - 图标和数据同行显示 */
.metric-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.metric-data {
    flex: 1;
}

.metric-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.2;
}

.metric-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 0;
}

.metric-change {
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.metric-change.positive {
    color: var(--success-green);
}

.metric-change.negative {
    color: var(--error-red);
}

/* ==================== 表单系统 ==================== */
.form-input,
.input,
.search-input,
.filter-input,
.filter-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    transition: all var(--transition-base);
    min-height: 36px;
}

.form-input:focus,
.input:focus,
.search-input:focus,
.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-input::placeholder,
.input::placeholder,
.search-input::placeholder {
    color: var(--text-muted);
}

/* 表单标签 */
.form-label,
.label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

/* ==================== 状态标签系统 ==================== */
.status-badge,
.badge,
.tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    border: 1px solid var(--border-light);
    background: var(--bg-muted);
    color: var(--text-secondary);
}

/* 功能性状态颜色 */
.status-active,
.status-completed,
.badge-success {
    background: var(--success-green) !important;
    color: var(--text-inverse) !important;
    border-color: var(--success-green) !important;
}

.status-pending,
.status-processing,
.badge-warning {
    background: var(--warning-orange) !important;
    color: var(--text-inverse) !important;
    border-color: var(--warning-orange) !important;
}

.status-inactive,
.status-cancelled,
.status-error,
.badge-error {
    background: var(--error-red) !important;
    color: var(--text-inverse) !important;
    border-color: var(--error-red) !important;
}

/* 主要和次要标签 */
.badge-primary {
    background: var(--accent-blue) !important;
    color: var(--text-inverse) !important;
    border-color: var(--accent-blue) !important;
}

.badge-secondary {
    background: var(--gray-600) !important;
    color: var(--text-inverse) !important;
    border-color: var(--gray-600) !important;
}

.badge-muted {
    background: var(--gray-400) !important;
    color: var(--text-inverse) !important;
    border-color: var(--gray-400) !important;
}

/* ==================== 导航系统 ==================== */
.sidebar {
    background: var(--bg-primary);
    border-right: 2px solid var(--border-medium);
    width: 200px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar-header {
    padding: 20px 16px;
    border-bottom: 1px solid var(--border-light);
}

.logo-icon {
    background: var(--primary-black) !important;
    color: var(--text-inverse) !important;
    width: 28px;
    height: 28px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.nav-menu {
    padding: var(--spacing-md) 0;
}

.nav-section {
    margin-bottom: 12px;
}

.nav-section-title {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 6px 16px;
    margin-bottom: var(--spacing-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 6px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: all var(--transition-base);
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background: var(--bg-muted);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--bg-muted) !important;
    color: var(--primary-black) !important;
    border-right-color: var(--gray-500) !important;
}

.nav-item-icon {
    width: 16px;
    text-align: center;
    color: inherit;
}

/* 顶部导航 */
.top-nav {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-md) var(--spacing-xl);
    margin-left: var(--spacing-md);
    margin-right: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb-item {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.user-avatar {
    width: 28px;
    height: 28px;
    background: var(--gray-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-weight: 600;
    cursor: pointer;
}

/* ==================== 表格系统 ==================== */
.table,
.users-table,
.requirements-table {
    width: 100%;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th,
.users-table th,
.requirements-table th {
    background: var(--bg-muted);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 14px;
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.table td,
.users-table td,
.requirements-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
    font-size: 14px;
}

.table tr:hover,
.users-table tbody tr:hover,
.requirements-table tbody tr:hover {
    background: var(--bg-muted) !important;
}

.table tr:last-child td,
.users-table tbody tr:last-child td,
.requirements-table tbody tr:last-child td {
    border-bottom: none;
}

/* ==================== 分页系统 ==================== */
.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-lg);
}

.page-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-light);
    background: var(--bg-primary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all var(--transition-base);
    min-width: 36px;
    text-align: center;
}

.page-btn:hover:not(.active) {
    background: var(--bg-muted);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.page-btn.active {
    background: var(--primary-black) !important;
    color: var(--text-inverse) !important;
    border-color: var(--primary-black) !important;
}

/* ==================== 工具类样式 ==================== */
/* 文字颜色 */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--text-light) !important; }
.text-inverse { color: var(--text-inverse) !important; }

/* 背景颜色 */
.bg-primary { background: var(--bg-primary) !important; }
.bg-secondary { background: var(--bg-secondary) !important; }
.bg-muted { background: var(--bg-muted) !important; }

/* 边框 */
.border { border: 1px solid var(--border-light) !important; }
.border-light { border-color: var(--border-light) !important; }
.border-medium { border-color: var(--border-medium) !important; }
.border-dark { border-color: var(--border-dark) !important; }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* 圆角 */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

/* 间距 */
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

/* 字体大小 */
.text-xs { font-size: 12px !important; }
.text-sm { font-size: 14px !important; }
.text-base { font-size: 16px !important; }
.text-lg { font-size: 18px !important; }
.text-xl { font-size: 20px !important; }
.text-2xl { font-size: 24px !important; }

/* 字体粗细 */
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

/* ==================== 滚动条样式 ==================== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-sm);
    transition: background var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ==================== 响应式设计 ==================== */
.main-content {
    margin-left: 220px;
    flex: 1;
    background: var(--bg-secondary);
    min-height: 100vh;
    transition: margin-left var(--transition-slow);
    border-left: 1px solid var(--border-light);
}

.page-content {
    padding: var(--spacing-xl);
    margin-left: var(--spacing-md);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-slow);
        box-shadow: none;
    }

    .sidebar.open {
        transform: translateX(0);
        box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
    }

    .main-content {
        margin-left: 0;
        border-left: none;
    }

    .page-content {
        padding: var(--spacing-md);
        margin-left: 0;
    }

    .top-nav {
        margin-left: var(--spacing-sm);
        margin-right: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

    .system-overview {
        grid-template-columns: 1fr !important;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .table,
    .users-table,
    .requirements-table {
        font-size: 12px;
    }

    .table th,
    .table td,
    .users-table th,
    .users-table td {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .page-content {
        padding: var(--spacing-sm);
    }

    .card,
    .overview-card {
        padding: var(--spacing-md);
    }

    .btn {
        font-size: 12px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .quick-actions-grid {
        grid-template-columns: 1fr !important;
    }
}

/* ==================== 特殊组件样式 ==================== */
/* 进度条 */
.progress-bar {
    background: var(--bg-muted);
    border-radius: var(--radius-sm);
    overflow: hidden;
    height: 8px;
}

.progress-fill {
    background: var(--primary-black);
    height: 100%;
    transition: width var(--transition-slow);
}

/* 通知徽章 */
.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    font-weight: 600;
}

/* 加载状态 */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-placeholder {
    background: var(--bg-muted);
    border-radius: var(--radius-sm);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ==================== 强制覆盖内联样式 ==================== */
/* 移除所有彩色渐变 */
*[style*="linear-gradient"] {
    background: var(--primary-black) !important;
}

*[style*="background: linear-gradient"] {
    background: var(--primary-black) !important;
}

*[style*="background-color: #"] {
    background-color: var(--bg-secondary) !important;
}

/* 确保所有链接颜色统一 */
a {
    color: var(--text-link) !important;
    text-decoration: none !important;
    transition: color var(--transition-base);
}

a:hover {
    color: var(--accent-blue-dark) !important;
}

/* 导航链接保持原色 */
.nav-item {
    color: var(--text-secondary) !important;
}

.nav-item:hover {
    color: var(--text-primary) !important;
}

.nav-item.active {
    color: var(--primary-black) !important;
}

/* 统一输入框占位符 */
::placeholder {
    color: var(--text-muted) !important;
}

/* ==================== 打印样式 ==================== */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .sidebar {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .btn,
    .action-btn {
        border: 1px solid black !important;
        background: white !important;
        color: black !important;
    }
}

/* ==================== 兼容性和回退 ==================== */
/* 确保在不支持 CSS 变量的浏览器中有基本样式 */
@supports not (color: var(--primary-black)) {
    .btn-primary { background: #1a1a1a; color: white; }
    .btn-secondary { background: white; color: #6b7280; border: 1px solid #e5e7eb; }
    .card { background: white; border: 1px solid #e5e7eb; }
    .sidebar { background: white; border-right: 1px solid #e5e7eb; }
}

/* ==================== 毛玻璃效果系统 ==================== */
/* 基础毛玻璃类 - 黑白灰设计规范 */
.glass-white {
  backdrop-filter: var(--glass-blur-medium);
  -webkit-backdrop-filter: var(--glass-blur-medium);
  background: var(--glass-bg-white);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.glass-light {
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  background: var(--glass-bg-light-gray);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.glass-medium {
  backdrop-filter: var(--glass-blur-medium);
  -webkit-backdrop-filter: var(--glass-blur-medium);
  background: var(--glass-bg-medium-gray);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-medium);
}

.glass-dark {
  backdrop-filter: var(--glass-blur-medium);
  -webkit-backdrop-filter: var(--glass-blur-medium);
  background: var(--glass-bg-dark-gray);
  border: 1px solid var(--glass-border-dark);
  box-shadow: var(--glass-shadow-medium);
  color: var(--text-inverse);
}

.glass-black {
  backdrop-filter: var(--glass-blur-heavy);
  -webkit-backdrop-filter: var(--glass-blur-heavy);
  background: var(--glass-bg-black);
  border: 1px solid var(--glass-border-dark);
  box-shadow: var(--glass-shadow-medium);
  color: var(--text-inverse);
}

/* 组件特定毛玻璃效果 */
.sidebar.glass-effect {
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  background: var(--glass-bg-white);
  border-right: 1px solid var(--glass-border-light);
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.08);
}

.card.glass-effect {
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  background: var(--glass-bg-white);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.modal.glass-effect {
  backdrop-filter: var(--glass-blur-heavy);
  -webkit-backdrop-filter: var(--glass-blur-heavy);
  background: var(--glass-bg-white);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-medium);
}

/* 降级方案 - 不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(1px)) {
  .glass-white,
  .glass-light,
  .glass-medium {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
  }

  .glass-dark {
    background: var(--gray-600);
    border: 1px solid var(--border-medium);
  }

  .glass-black {
    background: var(--primary-black);
    border: 1px solid var(--border-dark);
  }

  .sidebar.glass-effect {
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
  }

  .card.glass-effect {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
  }

  .modal.glass-effect {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
  }
}

/* ==================== 结束标记 ==================== */
/*
智能家居管理系统 - 统一后台管理样式库 v3.1
完成时间: 2025-01-16
符合规范: 智能家居项目黑白灰设计规范 + 毛玻璃效果
技术标准: CSS 变量 + 响应式设计 + 现代化 B 端 UI + 毛玻璃视觉效果
*/

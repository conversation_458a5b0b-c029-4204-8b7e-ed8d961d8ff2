<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目中心 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #1f2937;
            --secondary-color: #3b82f6;
            --light-bg: #f8fafc;
            --border-color: #e5e7eb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --radius-md: 8px;
            --radius-sm: 4px;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: var(--light-bg);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .nav-item.active {
            background: var(--light-bg);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--light-bg);
        }

        /* 第一层：面包屑导航 */
        .breadcrumb {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        /* 第二层：主菜单栏 */
        .main-tabs {
            background: white;
            padding: 16px 24px;
            display: flex;
            gap: 8px;
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
        }

        .main-tab {
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            background: var(--light-bg);
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .main-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .main-tab:hover:not(.active) {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        /* 项目生命周期 */
        .lifecycle-container {
            background: white;
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .lifecycle-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .lifecycle-stages {
            display: flex;
            justify-content: space-between;
            gap: 16px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .stage-card {
            min-width: 180px;
            text-align: center;
            padding: 20px;
            border-radius: var(--radius-md);
            background: #f9fafb;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
        }

        .stage-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-sm);
        }

        .stage-icon {
            font-size: 28px;
            margin-bottom: 12px;
        }

        .stage-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .stage-description {
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* 第三层：子菜单栏 */
        .sub-tabs {
            background: white;
            padding: 12px 24px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
            border-bottom: 1px solid var(--border-color);
        }

        .sub-tab {
            padding: 8px 16px;
            border-radius: var(--radius-sm);
            background: white;
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .sub-tab.active {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .sub-tab:hover:not(.active) {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        /* 第四层：内容区域 */
        .content-container {
            flex: 1;
            padding: 24px;
            background: var(--light-bg);
        }

        .content-card {
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .content-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
        }

        .content-body {
            padding: 24px;
        }

        /* 项目图纸样式 */
        .drawings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }

        .drawing-card {
            border-radius: var(--radius-sm);
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
        }

        .drawing-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-sm);
        }

        .drawing-preview {
            height: 160px;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 48px;
        }

        .drawing-info {
            padding: 16px;
        }

        .drawing-name {
            font-weight: 600;
            margin-bottom: 6px;
        }

        .drawing-meta {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
        }

        /* 项目人员样式 */
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }

        .team-member {
            border-radius: var(--radius-sm);
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
        }

        .team-member:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-sm);
        }

        .member-header {
            padding: 16px;
            background: #f9fafb;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .member-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .member-role {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .member-body {
            padding: 16px;
        }

        .member-contact {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 项目进度样式 */
        .progress-container {
            margin-top: 16px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .progress-title {
            font-weight: 600;
        }

        .progress-percent {
            font-weight: 600;
            color: var(--secondary-color);
        }

        .progress-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--secondary-color);
            border-radius: 4px;
        }

        .progress-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .stat-card {
            background: #f9fafb;
            border-radius: var(--radius-sm);
            padding: 16px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: var(--radius-sm);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #374151;
        }

        .btn-outline {
            background: white;
            color: var(--primary-color);
            border-color: var(--border-color);
        }

        .btn-outline:hover {
            background: #f1f5f9;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .main-tabs, .sub-tabs {
                flex-wrap: wrap;
            }
            
            .lifecycle-stages {
                flex-wrap: wrap;
            }
            
            .stage-card {
                min-width: 140px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="logo-text">智能家居</div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="#" class="nav-item">我的待办</a>
                    <a href="#" class="nav-item">我的订单</a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="#" class="nav-item">设计商品</a>
                    <a href="#" class="nav-item">需求管理</a>
                    <a href="#" class="nav-item">设计中心</a>
                    <a href="#" class="nav-item">设计案例</a>
                    <a href="#" class="nav-item active">项目中心</a>
                    <a href="#" class="nav-item">施工管理</a>
                    <a href="#" class="nav-item">施工指导</a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">系统设置</div>
                    <a href="#" class="nav-item">系统设置</a>
                    <a href="#" class="nav-item">知识库</a>
                    <a href="#" class="nav-item">帮助中心</a>
                </div>
            </nav>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 第一层：面包屑导航 -->
            <div class="breadcrumb">
                <h1 class="breadcrumb-title">项目中心</h1>
                <p class="breadcrumb-description">管理智能家居施工项目进度和质量</p>
            </div>
            
            <!-- 第二层：主菜单栏 -->
            <div class="main-tabs">
                <button class="main-tab active">
                    <i class="fas fa-list"></i> 项目列表
                </button>
                <button class="main-tab">
                    <i class="fas fa-project-diagram"></i> 项目空间
                </button>
                <button class="main-tab">
                    <i class="fas fa-file-contract"></i> 合同管理
                </button>
                <button class="main-tab">
                    <i class="fas fa-chart-line"></i> 项目分析
                </button>
                <button class="main-tab">
                    <i class="fas fa-cog"></i> 项目设置
                </button>
            </div>
            
            <!-- 项目生命周期 -->
            <div class="lifecycle-container">
                <h2 class="lifecycle-title">项目生命周期</h2>
                <div class="lifecycle-stages">
                    <div class="stage-card">
                        <div class="stage-icon">📋</div>
                        <h3 class="stage-name">需求分析</h3>
                        <p class="stage-description">客户需求收集与分析</p>
                    </div>
                    
                    <div class="stage-card">
                        <div class="stage-icon">🎨</div>
                        <h3 class="stage-name">设计阶段</h3>
                        <p class="stage-description">方案设计与效果图</p>
                    </div>
                    
                    <div class="stage-card">
                        <div class="stage-icon">🛒</div>
                        <h3 class="stage-name">采购阶段</h3>
                        <p class="stage-description">设备采购与物流</p>
                    </div>
                    
                    <div class="stage-card">
                        <div class="stage-icon">🔧</div>
                        <h3 class="stage-name">施工阶段</h3>
                        <p class="stage-description">现场施工与安装</p>
                    </div>
                    
                    <div class="stage-card">
                        <div class="stage-icon">⚡</div>
                        <h3 class="stage-name">调试阶段</h3>
                        <p class="stage-description">系统调试与测试</p>
                    </div>
                    
                    <div class="stage-card">
                        <div class="stage-icon">✅</div>
                        <h3 class="stage-name">验收交付</h3>
                        <p class="stage-description">项目验收与交付</p>
                    </div>
                </div>
            </div>
            
            <!-- 第三层：子菜单栏 -->
            <div class="sub-tabs">
                <button class="sub-tab" data-content="drawings">
                    <i class="fas fa-drafting-compass"></i> 项目图纸
                </button>
                <button class="sub-tab active" data-content="team">
                    <i class="fas fa-users"></i> 项目人员
                </button>
                <button class="sub-tab" data-content="progress">
                    <i class="fas fa-tasks"></i> 项目进度
                </button>
                <button class="sub-tab" data-content="finance">
                    <i class="fas fa-coins"></i> 项目财务
                </button>
                <button class="sub-tab" data-content="quality">
                    <i class="fas fa-clipboard-check"></i> 质量检查
                </button>
            </div>
            
            <!-- 第四层：内容区域 -->
            <div class="content-container">
                <!-- 项目图纸内容 -->
                <div class="content-card" id="drawings-content" style="display: none;">
                    <div class="content-header">
                        <h2 class="content-title">项目图纸管理</h2>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 上传图纸
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="drawings-grid">
                            <div class="drawing-card">
                                <div class="drawing-preview">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="drawing-info">
                                    <div class="drawing-name">客厅平面布置图</div>
                                    <div class="drawing-meta">
                                        <span>2023-06-10</span>
                                        <span>2.4MB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="drawing-card">
                                <div class="drawing-preview">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="drawing-info">
                                    <div class="drawing-name">照明系统设计图</div>
                                    <div class="drawing-meta">
                                        <span>2023-06-12</span>
                                        <span>1.8MB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="drawing-card">
                                <div class="drawing-preview">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="drawing-info">
                                    <div class="drawing-name">弱电系统施工图</div>
                                    <div class="drawing-meta">
                                        <span>2023-06-15</span>
                                        <span>3.2MB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="drawing-card">
                                <div class="drawing-preview">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="drawing-info">
                                    <div class="drawing-name">安防系统布置图</div>
                                    <div class="drawing-meta">
                                        <span>2023-06-18</span>
                                        <span>2.1MB</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 项目人员内容 -->
                <div class="content-card" id="team-content">
                    <div class="content-header">
                        <h2 class="content-title">项目团队管理</h2>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加成员
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="team-grid">
                            <div class="team-member">
                                <div class="member-header">
                                    <div class="member-avatar">张</div>
                                    <div class="member-info">
                                        <div class="member-name">张工</div>
                                        <div class="member-role">项目经理</div>
                                    </div>
                                </div>
                                <div class="member-body">
                                    <div class="member-contact">
                                        <i class="fas fa-phone"></i> 138****8888
                                    </div>
                                    <div class="member-contact">
                                        <i class="fas fa-envelope"></i> <EMAIL>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="team-member">
                                <div class="member-header">
                                    <div class="member-avatar">李</div>
                                    <div class="member-info">
                                        <div class="member-name">李工</div>
                                        <div class="member-role">技术负责人</div>
                                    </div>
                                </div>
                                <div class="member-body">
                                    <div class="member-contact">
                                        <i class="fas fa-phone"></i> 139****9999
                                    </div>
                                    <div class="member-contact">
                                        <i class="fas fa-envelope"></i> <EMAIL>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="team-member">
                                <div class="member-header">
                                    <div class="member-avatar">王</div>
                                    <div class="member-info">
                                        <div class="member-name">王工</div>
                                        <div class="member-role">电工</div>
                                    </div>
                                </div>
                                <div class="member-body">
                                    <div class="member-contact">
                                        <i class="fas fa-phone"></i> 137****7777
                                    </div>
                                    <div class="member-contact">
                                        <i class="fas fa-envelope"></i> <EMAIL>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="team-member">
                                <div class="member-header">
                                    <div class="member-avatar">赵</div>
                                    <div class="member-info">
                                        <div class="member-name">赵工</div>
                                        <div class="member-role">安装工程师</div>
                                    </div>
                                </div>
                                <div class="member-body">
                                    <div class="member-contact">
                                        <i class="fas fa-phone"></i> 136****6666
                                    </div>
                                    <div class="member-contact">
                                        <i class="fas fa-envelope"></i> <EMAIL>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 项目进度内容 -->
                <div class="content-card" id="progress-content" style="display: none;">
                    <div class="content-header">
                        <h2 class="content-title">项目进度跟踪</h2>
                        <button class="btn btn-outline">
                            <i class="fas fa-chart-line"></i> 查看统计
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="progress-container">
                            <div class="progress-header">
                                <div class="progress-title">整体项目进度</div>
                                <div class="progress-percent">65%</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%;"></div>
                            </div>
                        </div>
                        
                        <div class="progress-stats">
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">已完成任务</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">7</div>
                                <div class="stat-label">进行中任务</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">待处理任务</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">2</div>
                                <div class="stat-label">延期任务</div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 32px;">
                            <h3 style="margin-bottom: 16px; font-weight: 600;">阶段完成情况</h3>
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">需求分析</div>
                                    <div class="progress-percent">100%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%; background: var(--success-color);"></div>
                                </div>
                            </div>
                            
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">设计阶段</div>
                                    <div class="progress-percent">90%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 90%;"></div>
                                </div>
                            </div>
                            
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">采购阶段</div>
                                    <div class="progress-percent">75%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%;"></div>
                                </div>
                            </div>
                            
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">施工阶段</div>
                                    <div class="progress-percent">40%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 40%;"></div>
                                </div>
                            </div>
                            
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">调试阶段</div>
                                    <div class="progress-percent">0%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                            
                            <div class="progress-container">
                                <div class="progress-header">
                                    <div class="progress-title">验收交付</div>
                                    <div class="progress-percent">0%</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 子菜单切换功能
            const subTabs = document.querySelectorAll('.sub-tab');
            const contentCards = {
                'drawings': document.getElementById('drawings-content'),
                'team': document.getElementById('team-content'),
                'progress': document.getElementById('progress-content')
            };
            
            subTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有激活状态
                    subTabs.forEach(t => t.classList.remove('active'));
                    
                    // 设置当前激活状态
                    this.classList.add('active');
                    
                    // 获取当前内容区域
                    const contentKey = this.getAttribute('data-content');
                    
                    // 隐藏所有内容区域
                    Object.values(contentCards).forEach(card => {
                        card.style.display = 'none';
                    });
                    
                    // 显示当前内容区域
                    if (contentCards[contentKey]) {
                        contentCards[contentKey].style.display = 'block';
                    }
                });
            });
            
            // 主菜单切换功能
            const mainTabs = document.querySelectorAll('.main-tab');
            mainTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有激活状态
                    mainTabs.forEach(t => t.classList.remove('active'));
                    
                    // 设置当前激活状态
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
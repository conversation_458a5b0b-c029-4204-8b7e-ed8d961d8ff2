<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - 智能家居管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.5;
            font-weight: bold;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.5;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 页面标题 */
        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 24px 0;
            line-height: 1.5;
        }

        /* 统计卡片 */
        .knowledge-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 20px;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 2px;
        }

        /* 知识库布局 */
        .knowledge-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 24px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .knowledge-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e5e7eb;
            padding: 20px;
        }

        .knowledge-sidebar .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 0;
            border: none;
        }

        .knowledge-sidebar h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .knowledge-main {
            padding: 20px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: 1px solid transparent;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #111827;
            border-color: #111827;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 搜索和筛选 */
        .search-filters {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
        }

        .filter-select {
            background: #ffffff;
        }

        /* 知识列表 */
        .knowledge-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }

        .knowledge-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s;
        }

        .knowledge-item:hover {
            border-color: #1f2937;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .knowledge-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .knowledge-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 16px;
            flex-shrink: 0;
        }

        .knowledge-info {
            flex: 1;
        }

        .knowledge-title {
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .knowledge-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #6b7280;
        }

        .knowledge-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .pagination button {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: #ffffff;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .pagination button:hover:not(:disabled) {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .knowledge-layout {
                grid-template-columns: 1fr;
            }

            .knowledge-sidebar {
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .search-filters {
                flex-direction: column;
            }

            .knowledge-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item active">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">知识库管理</h1>
                            <p class="breadcrumb-description">管理产品文档、教程视频和技术支持资料</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <div class="knowledge-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalArticles">0</span>
                            <span class="stat-label">知识文档</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalVideos">0</span>
                            <span class="stat-label">教程视频</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalImages">0</span>
                            <span class="stat-label">参考图片</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalCategories">0</span>
                            <span class="stat-label">知识分类</span>
                        </div>
                    </div>
                </div>

                <div class="knowledge-layout">
                    <!-- 左侧分类树 -->
                    <div class="knowledge-sidebar">
                        <div class="sidebar-header">
                            <h3>知识分类</h3>
                            <button class="btn btn-sm btn-primary" onclick="showAddCategoryModal()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="category-tree" id="categoryTree">
                            <!-- 分类树将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 右侧内容区域 -->
                    <div class="knowledge-content">
                        <div class="knowledge-toolbar">
                            <div class="toolbar-left">
                                <button class="btn btn-primary" onclick="showAddKnowledgeModal()">
                                    <i class="fas fa-plus"></i> 新增知识
                                </button>
                                <button class="btn btn-secondary" onclick="uploadFiles()">
                                    <i class="fas fa-upload"></i> 上传文件
                                </button>
                                <button class="btn btn-secondary" onclick="exportKnowledge()">
                                    <i class="fas fa-download"></i> 导出知识库
                                </button>
                            </div>
                            <div class="toolbar-right">
                                <div class="search-box">
                                    <input type="text" id="searchInput" placeholder="搜索知识..." onkeyup="searchKnowledge()">
                                    <i class="fas fa-search"></i>
                                </div>
                                <select id="typeFilter" onchange="filterKnowledge()">
                                    <option value="">所有类型</option>
                                    <option value="document">文档</option>
                                    <option value="video">视频</option>
                                    <option value="image">图片</option>
                                    <option value="case">案例</option>
                                </select>
                            </div>
                        </div>

                        <div class="knowledge-grid" id="knowledgeGrid">
                            <!-- 知识内容将通过JavaScript动态生成 -->
                        </div>

                        <div class="pagination-container">
                            <div class="pagination-info">
                                显示第 <span id="pageStart">1</span> - <span id="pageEnd">12</span> 条，共 <span id="totalCount">0</span> 条记录
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn" disabled>
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </button>
                                <div class="page-numbers" id="pageNumbers"></div>
                                <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增知识模态框 -->
    <div id="knowledgeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="knowledgeModalTitle">新增知识</h3>
                <span class="modal-close" onclick="closeKnowledgeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="knowledgeForm" onsubmit="submitKnowledgeForm(event)">
                    <div class="form-group">
                        <label for="knowledgeTitle">标题 <span class="required">*</span></label>
                        <input type="text" id="knowledgeTitle" name="title" required>
                        <div class="error-message" id="titleError"></div>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeType">类型 <span class="required">*</span></label>
                        <select id="knowledgeType" name="type" required>
                            <option value="">请选择类型</option>
                            <option value="document">文档</option>
                            <option value="video">视频</option>
                            <option value="image">图片</option>
                            <option value="case">案例</option>
                        </select>
                        <div class="error-message" id="typeError"></div>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeCategory">分类 <span class="required">*</span></label>
                        <select id="knowledgeCategory" name="category" required>
                            <option value="">请选择分类</option>
                        </select>
                        <div class="error-message" id="categoryError"></div>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeDescription">描述 <span class="required">*</span></label>
                        <textarea id="knowledgeDescription" name="description" rows="3" placeholder="请输入知识描述..." required></textarea>
                        <div class="error-message" id="descriptionError"></div>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeContent">内容</label>
                        <textarea id="knowledgeContent" name="content" rows="6" placeholder="请输入详细内容..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeAuthor">作者</label>
                        <input type="text" id="knowledgeAuthor" name="author" placeholder="请输入作者姓名">
                    </div>

                    <div class="form-group">
                        <label for="knowledgeTags">标签</label>
                        <input type="text" id="knowledgeTags" name="tags" placeholder="请输入标签，用逗号分隔">
                        <div class="form-hint">例如：安装,教程,智能家居</div>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeFile">文件上传</label>
                        <div class="file-upload-area" onclick="document.getElementById('knowledgeFileInput').click()">
                            <input type="file" id="knowledgeFileInput" accept="*/*" style="display: none;" onchange="handleFileUpload(event)">
                            <div class="upload-placeholder" id="fileUploadPlaceholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传文件或拖拽文件到此处</p>
                                <p class="upload-hint">支持各种格式文件，大小不超过 100MB</p>
                            </div>
                            <div class="upload-preview" id="fileUploadPreview" style="display: none;">
                                <div class="file-info" id="fileInfo"></div>
                                <button type="button" class="remove-file" onclick="removeFile()">&times;</button>
                            </div>
                        </div>
                        <div class="error-message" id="fileError"></div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeKnowledgeModal()">取消</button>
                        <button type="submit" class="btn btn-primary" id="knowledgeSubmitBtn">
                            <span class="btn-text">保存</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> 保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新增分类模态框 -->
    <div id="categoryModal" class="modal" style="display: none;">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>新增分类</h3>
                <span class="modal-close" onclick="closeCategoryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="categoryForm" onsubmit="submitCategoryForm(event)">
                    <div class="form-group">
                        <label for="categoryName">分类名称 <span class="required">*</span></label>
                        <input type="text" id="categoryName" name="name" required placeholder="请输入分类名称">
                        <div class="error-message" id="categoryNameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="parentCategory">父级分类</label>
                        <select id="parentCategory" name="parent">
                            <option value="">无（顶级分类）</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()">取消</button>
                        <button type="submit" class="btn btn-primary" id="categorySubmitBtn">
                            <span class="btn-text">保存</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> 保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 知识查看模态框 -->
    <div id="viewModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="viewModalTitle">知识详情</h3>
                <span class="modal-close" onclick="closeViewModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="viewModalContent">
                    <!-- 内容将动态填充 -->
                </div>
            </div>
        </div>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-error {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        }

        .form-hint {
            color: #6b7280;
            font-size: 12px;
            margin-top: 4px;
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }

        .required {
            color: #ef4444;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 知识库专用样式 */
        .knowledge-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .toolbar-left {
            display: flex;
            gap: 12px;
        }

        .toolbar-right {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 8px 12px 8px 36px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            width: 250px;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .knowledge-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s;
        }

        .knowledge-card:hover {
            border-color: #1f2937;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .knowledge-preview {
            text-align: center;
            margin-bottom: 16px;
            color: #6b7280;
        }

        .knowledge-info {
            flex: 1;
        }

        .knowledge-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .knowledge-type {
            display: inline-block;
            padding: 2px 8px;
            background: #dbeafe;
            color: #1e40af;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .knowledge-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .knowledge-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 12px;
        }

        .knowledge-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .category-tree {
            margin-top: 16px;
        }

        .category-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            color: #374151;
            margin-bottom: 4px;
        }

        .category-item:hover {
            background: #e5e7eb;
        }

        .category-item.active {
            background: #1f2937;
            color: #ffffff;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding: 16px 0;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: #ffffff;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .page-number:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .page-number.active {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-small .modal-content {
            max-width: 400px;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 24px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            line-height: 1;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 0 24px 24px;
        }

        /* 文件上传样式 */
        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.2s ease, background-color 0.2s ease;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .upload-placeholder i {
            font-size: 32px;
            color: #9ca3af;
            margin-bottom: 12px;
        }

        .upload-placeholder p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }

        .upload-hint {
            font-size: 12px !important;
            color: #9ca3af !important;
            margin-top: 4px !important;
        }

        .upload-preview {
            position: relative;
        }

        .file-info {
            padding: 12px;
            background: #f3f4f6;
            border-radius: 6px;
            text-align: left;
        }

        .remove-file {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #ef4444;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .remove-file:hover {
            background: #dc2626;
        }

        .required {
            color: #ef4444;
        }

        .error-message {
            display: none;
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .btn-loading {
            display: none;
            align-items: center;
            gap: 6px;
        }

        .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .knowledge-layout {
                grid-template-columns: 1fr;
            }

            .knowledge-sidebar {
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .toolbar-left, .toolbar-right {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box input {
                width: 100%;
            }

            .knowledge-grid {
                grid-template-columns: 1fr;
            }

            .pagination-container {
                flex-direction: column;
                gap: 16px;
            }
        }
    </style>
    
    <script>
        // 设计系统模拟 (简化版)
        const ds = {
            showLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0.5';
                    element.style.pointerEvents = 'none';
                }
            },
            hideLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '1';
                    element.style.pointerEvents = 'auto';
                }
            },
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            formatDate: (dateString, format = 'YYYY-MM-DD') => {
                if (!dateString) return '';
                const date = new Date(dateString);
                if (format === 'YYYY-MM-DD') {
                    return date.toLocaleDateString('zh-CN');
                }
                return date.toLocaleString('zh-CN');
            }
        };

        class KnowledgeManager {
            constructor() {
                this.knowledge = [];
                this.filteredKnowledge = [];
                this.categories = [];
                this.currentPage = 1;
                this.pageSize = 12;
                this.totalCount = 0;
                this.currentCategory = null;
                this.nextId = 1;

                this.loadData();
                this.init();
            }

            init() {
                this.loadKnowledge();
                this.loadCategories();
                this.populateCategorySelect();
                this.updateStats();
                this.renderCategories();
                this.renderKnowledge();
                this.renderPagination();
                this.bindEvents();
            }

            populateCategorySelect() {
                const categorySelect = document.getElementById('knowledgeCategory');
                if (categorySelect) {
                    categorySelect.innerHTML = '<option value="">请选择分类</option>' +
                        this.categories.map(category =>
                            `<option value="${category.id}">${category.name}</option>`
                        ).join('');
                }
            }

            // 数据管理
            loadData() {
                // 加载知识库数据
                this.knowledge = JSON.parse(localStorage.getItem('knowledge') || '[]');
                this.categories = JSON.parse(localStorage.getItem('knowledge_categories') || '[]');

                // 初始化默认数据
                if (this.knowledge.length === 0) {
                    this.knowledge = [
                        {
                            id: 1,
                            title: 'Aqara智能开关安装指南',
                            type: 'document',
                            category: 'installation',
                            description: '详细的智能开关安装步骤和注意事项，包含工具准备、接线方法、调试步骤等完整流程',
                            content: '# Aqara智能开关安装指南\n\n## 工具准备\n- 螺丝刀\n- 电笔\n- 绝缘胶带\n\n## 安装步骤\n1. 关闭电源\n2. 拆除原开关\n3. 连接智能开关\n4. 固定开关\n5. 测试功能',
                            author: '技术部',
                            tags: ['安装', '智能开关', 'Aqara'],
                            createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
                            views: 156,
                            downloads: 23,
                            fileSize: '2.5MB',
                            fileName: 'aqara_switch_guide.pdf'
                        },
                        {
                            id: 2,
                            title: '智能家居系统调试教程',
                            type: 'video',
                            category: 'tutorial',
                            description: '完整的智能家居系统调试流程视频教程，从设备连接到场景配置的全过程演示',
                            content: '本视频教程详细演示了智能家居系统的调试过程，包括设备配对、网络配置、场景设置等关键步骤。',
                            author: '技术部',
                            tags: ['调试', '教程', '视频'],
                            createdAt: new Date(Date.now() - 86400000 * 4).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
                            views: 89,
                            downloads: 12,
                            fileSize: '125MB',
                            fileName: 'smart_home_debug.mp4'
                        },
                        {
                            id: 3,
                            title: '客厅智能化改造案例',
                            type: 'case',
                            category: 'cases',
                            description: '120平米客厅智能化改造的完整案例分享，包含设计方案、设备选型、施工过程等',
                            content: '# 客厅智能化改造案例\n\n## 项目概况\n- 面积：120平米\n- 预算：5万元\n- 工期：7天\n\n## 改造内容\n- 智能照明系统\n- 智能窗帘\n- 智能音响\n- 智能空调控制',
                            author: '设计部',
                            tags: ['案例', '客厅', '改造'],
                            createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),
                            views: 234,
                            downloads: 45,
                            fileSize: '8.2MB',
                            fileName: 'living_room_case.pdf'
                        },
                        {
                            id: 4,
                            title: '智能传感器布置图',
                            type: 'image',
                            category: 'reference',
                            description: '各类智能传感器的最佳布置位置参考图，包含温湿度、人体感应、门窗传感器等',
                            content: '智能传感器布置参考图集，展示了不同类型传感器在各个房间的最佳安装位置和注意事项。',
                            author: '设计部',
                            tags: ['传感器', '布置图', '参考'],
                            createdAt: new Date(Date.now() - 86400000 * 2).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
                            views: 67,
                            downloads: 18,
                            fileSize: '3.1MB',
                            fileName: 'sensor_layout.jpg'
                        },
                        {
                            id: 5,
                            title: '常见故障排除手册',
                            type: 'document',
                            category: 'troubleshooting',
                            description: '智能家居设备常见故障的排除方法，包含网络连接、设备离线、功能异常等问题解决方案',
                            content: '# 常见故障排除手册\n\n## 网络连接问题\n- 检查WiFi密码\n- 重启路由器\n- 检查信号强度\n\n## 设备离线问题\n- 检查电源\n- 重新配对\n- 检查网络',
                            author: '客服部',
                            tags: ['故障', '排除', '手册'],
                            createdAt: new Date(Date.now() - 86400000 * 1).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
                            views: 345,
                            downloads: 67,
                            fileSize: '1.8MB',
                            fileName: 'troubleshooting_guide.pdf'
                        }
                    ];
                    this.saveKnowledge();
                }

                if (this.categories.length === 0) {
                    this.categories = [
                        { id: 'installation', name: '安装指南', parent: null, description: '设备安装相关文档' },
                        { id: 'tutorial', name: '教程视频', parent: null, description: '操作教程和培训视频' },
                        { id: 'cases', name: '应用案例', parent: null, description: '实际项目案例分享' },
                        { id: 'reference', name: '参考资料', parent: null, description: '技术参考和规范文档' },
                        { id: 'troubleshooting', name: '故障排除', parent: null, description: '故障诊断和解决方案' },
                        { id: 'maintenance', name: '维护保养', parent: null, description: '设备维护和保养指南' }
                    ];
                    this.saveCategories();
                }

                // 设置下一个ID
                this.nextId = this.knowledge.length > 0 ? Math.max(...this.knowledge.map(k => k.id)) + 1 : 1;
            }

            saveKnowledge() {
                localStorage.setItem('knowledge', JSON.stringify(this.knowledge));
            }

            saveCategories() {
                localStorage.setItem('knowledge_categories', JSON.stringify(this.categories));
            }

            getNextKnowledgeId() {
                return this.nextId++;
            }

            loadKnowledge() {
                // 应用筛选条件
                this.filteredKnowledge = [...this.knowledge];

                // 分类筛选
                if (this.currentCategory) {
                    this.filteredKnowledge = this.filteredKnowledge.filter(item =>
                        item.category === this.currentCategory
                    );
                }

                // 搜索筛选
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    const keyword = searchInput.value.trim().toLowerCase();
                    this.filteredKnowledge = this.filteredKnowledge.filter(item =>
                        item.title.toLowerCase().includes(keyword) ||
                        item.description.toLowerCase().includes(keyword) ||
                        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(keyword))) ||
                        item.author.toLowerCase().includes(keyword)
                    );
                }

                // 类型筛选
                const typeFilter = document.getElementById('typeFilter');
                if (typeFilter && typeFilter.value) {
                    this.filteredKnowledge = this.filteredKnowledge.filter(item =>
                        item.type === typeFilter.value
                    );
                }

                // 按创建时间倒序排列
                this.filteredKnowledge.sort((a, b) => {
                    const aTime = new Date(a.createdAt || 0);
                    const bTime = new Date(b.createdAt || 0);
                    return bTime - aTime;
                });

                this.totalCount = this.filteredKnowledge.length;
            }

            loadCategories() {
                // 分类数据已在loadData中加载
            }

            updateStats() {
                const totalKnowledge = this.knowledge.length;
                const totalArticles = this.knowledge.filter(k => k.type === 'document').length;
                const totalVideos = this.knowledge.filter(k => k.type === 'video').length;
                const totalImages = this.knowledge.filter(k => k.type === 'image').length;
                const totalCases = this.knowledge.filter(k => k.type === 'case').length;
                const totalCategories = this.categories.length;

                // 计算本月新增
                const thisMonth = new Date();
                thisMonth.setDate(1);
                thisMonth.setHours(0, 0, 0, 0);
                const newThisMonth = this.knowledge.filter(k =>
                    new Date(k.createdAt) >= thisMonth
                ).length;

                // 计算总浏览量
                const totalViews = this.knowledge.reduce((sum, k) => sum + (k.views || 0), 0);

                // 更新统计显示
                const totalKnowledgeEl = document.getElementById('totalKnowledge');
                const totalArticlesEl = document.getElementById('totalArticles');
                const totalVideosEl = document.getElementById('totalVideos');
                const totalImagesEl = document.getElementById('totalImages');
                const totalCategoriesEl = document.getElementById('totalCategories');
                const newThisMonthEl = document.getElementById('newThisMonth');
                const totalViewsEl = document.getElementById('totalViews');

                if (totalKnowledgeEl) totalKnowledgeEl.textContent = totalKnowledge;
                if (totalArticlesEl) totalArticlesEl.textContent = totalArticles;
                if (totalVideosEl) totalVideosEl.textContent = totalVideos;
                if (totalImagesEl) totalImagesEl.textContent = totalImages;
                if (totalCategoriesEl) totalCategoriesEl.textContent = totalCategories;
                if (newThisMonthEl) newThisMonthEl.textContent = newThisMonth;
                if (totalViewsEl) totalViewsEl.textContent = totalViews;

                // 如果没有找到统计元素，创建简单的统计显示
                if (!totalKnowledgeEl) {
                    console.log('知识库统计:', {
                        总知识条目: totalKnowledge,
                        文档: totalArticles,
                        视频: totalVideos,
                        图片: totalImages,
                        案例: totalCases,
                        分类: totalCategories,
                        本月新增: newThisMonth,
                        总浏览量: totalViews
                    });
                }
            }

            renderCategories() {
                const container = document.getElementById('categoryTree');
                container.innerHTML = `
                    <div class="category-item ${!this.currentCategory ? 'active' : ''}" onclick="knowledgeManager.selectCategory(null)">
                        <i class="fas fa-list"></i> 全部知识
                    </div>
                ` + this.categories.map(category => `
                    <div class="category-item ${this.currentCategory === category.id ? 'active' : ''}" onclick="knowledgeManager.selectCategory('${category.id}')">
                        <i class="fas fa-folder"></i> ${category.name}
                    </div>
                `).join('');
            }

            renderKnowledge() {
                const grid = document.getElementById('knowledgeGrid');
                grid.innerHTML = '';

                if (this.filteredKnowledge.length === 0) {
                    grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: var(--text-secondary);">暂无知识内容</div>';
                    return;
                }

                this.filteredKnowledge.forEach(item => {
                    const card = document.createElement('div');
                    card.className = 'knowledge-card';
                    card.innerHTML = `
                        <div class="knowledge-preview">
                            ${this.getTypeIcon(item.type)}
                        </div>
                        <div class="knowledge-info">
                            <div class="knowledge-title">${item.title}</div>
                            <div class="knowledge-type">${this.getTypeText(item.type)}</div>
                            <div class="knowledge-description">${item.description}</div>
                            <div class="knowledge-meta">
                                <span>作者: ${item.author}</span>
                                <span>浏览: ${item.views}</span>
                            </div>
                            <div class="knowledge-actions">
                                <button class="btn btn-sm btn-secondary" onclick="knowledgeManager.editKnowledge(${item.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="knowledgeManager.viewKnowledge(${item.id})" title="查看">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="knowledgeManager.downloadKnowledge(${item.id})" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="knowledgeManager.deleteKnowledge(${item.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    grid.appendChild(card);
                });
            }

            renderPagination() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageStart').textContent = start;
                document.getElementById('pageEnd').textContent = end;
                document.getElementById('totalCount').textContent = this.totalCount;

                document.getElementById('prevBtn').disabled = this.currentPage <= 1;
                document.getElementById('nextBtn').disabled = this.currentPage >= totalPages;

                const pageNumbers = document.getElementById('pageNumbers');
                pageNumbers.innerHTML = '';

                for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                    const pageBtn = document.createElement('div');
                    pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    pageNumbers.appendChild(pageBtn);
                }
            }

            bindEvents() {
                document.getElementById('searchInput').addEventListener('input', () => this.searchKnowledge());
                document.getElementById('typeFilter').addEventListener('change', () => this.filterKnowledge());
            }

            selectCategory(categoryId) {
                this.currentCategory = categoryId;
                this.renderCategories();
                this.loadKnowledge();
                this.renderKnowledge();
                this.renderPagination();
            }

            searchKnowledge() {
                this.loadKnowledge();
                this.renderKnowledge();
                this.renderPagination();
            }

            filterKnowledge() {
                this.loadKnowledge();
                this.renderKnowledge();
                this.renderPagination();
            }
                    return typeMatch && categoryMatch;
                });
                this.renderKnowledge();
            }

            getTypeIcon(type) {
                const iconMap = {
                    'document': '<i class="fas fa-file-alt" style="font-size: 48px;"></i>',
                    'video': '<i class="fas fa-video" style="font-size: 48px;"></i>',
                    'image': '<i class="fas fa-image" style="font-size: 48px;"></i>',
                    'case': '<i class="fas fa-lightbulb" style="font-size: 48px;"></i>'
                };
                return iconMap[type] || '<i class="fas fa-file" style="font-size: 48px;"></i>';
            }

            getTypeText(type) {
                const typeMap = { 'document': '文档', 'video': '视频', 'image': '图片', 'case': '案例' };
                return typeMap[type] || type;
            }

            editKnowledge(id) {
                const item = this.knowledge.find(k => k.id === parseInt(id));
                if (!item) {
                    ds.showToast('知识条目不存在', 'error');
                    return;
                }

                const form = document.getElementById('knowledgeForm');
                const modal = document.getElementById('knowledgeModal');

                // 设置编辑模式
                form.dataset.editId = id;

                // 填充表单
                document.getElementById('knowledgeModalTitle').textContent = '编辑知识';
                document.getElementById('knowledgeTitle').value = item.title;
                document.getElementById('knowledgeType').value = item.type;
                document.getElementById('knowledgeCategory').value = item.category;
                document.getElementById('knowledgeDescription').value = item.description || '';
                document.getElementById('knowledgeContent').value = item.content || '';
                document.getElementById('knowledgeAuthor').value = item.author || '';
                document.getElementById('knowledgeTags').value = item.tags ? item.tags.join(', ') : '';

                // 显示模态框
                modal.style.display = 'flex';
            }

            viewKnowledge(id) {
                const item = this.knowledge.find(k => k.id === parseInt(id));
                if (!item) {
                    ds.showToast('知识条目不存在', 'error');
                    return;
                }

                // 增加浏览量
                item.views = (item.views || 0) + 1;
                this.saveKnowledge();

                // 创建详情模态框
                const modal = document.createElement('div');
                modal.className = 'knowledge-detail-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="modal-content" onclick="event.stopPropagation()" style="background: white; border-radius: 12px; width: 90%; max-width: 800px; max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                            <div class="modal-header" style="padding: 24px 24px 0 24px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">${item.title}</h3>
                                <button onclick="this.closest('.knowledge-detail-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 4px;">&times;</button>
                            </div>

                            <div class="modal-body" style="padding: 0 24px 24px 24px;">
                                <div style="display: grid; gap: 20px;">
                                    <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                        <span class="type-badge ${item.type}" style="padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500; background: #f3f4f6; color: #374151;">${this.getTypeText(item.type)}</span>
                                        <span class="category-badge" style="padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500; background: #dbeafe; color: #1e40af;">${this.getCategoryName(item.category)}</span>
                                    </div>

                                    <div>
                                        <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">描述</h5>
                                        <p style="margin: 0; color: #6b7280; line-height: 1.6;">${item.description}</p>
                                    </div>

                                    ${item.content ? `
                                    <div>
                                        <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">内容</h5>
                                        <div style="background: #f9fafb; padding: 16px; border-radius: 8px; border: 1px solid #e5e7eb;">
                                            <pre style="margin: 0; white-space: pre-wrap; font-family: inherit; color: #6b7280; line-height: 1.6;">${item.content}</pre>
                                        </div>
                                    </div>
                                    ` : ''}

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">作者</h5>
                                            <p style="margin: 0; color: #6b7280;">${item.author}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">浏览量</h5>
                                            <p style="margin: 0; color: #6b7280;">${item.views || 0} 次</p>
                                        </div>
                                    </div>

                                    ${item.tags && item.tags.length > 0 ? `
                                    <div>
                                        <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">标签</h5>
                                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                            ${item.tags.map(tag => `<span style="padding: 2px 8px; background: #f3f4f6; color: #6b7280; border-radius: 12px; font-size: 11px;">${tag}</span>`).join('')}
                                        </div>
                                    </div>
                                    ` : ''}

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">创建时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${ds.formatDate(item.createdAt)}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">更新时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${ds.formatDate(item.updatedAt)}</p>
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; display: flex; gap: 8px; justify-content: flex-end;">
                                    <button onclick="this.closest('.knowledge-detail-modal').remove()" style="padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">关闭</button>
                                    <button onclick="knowledgeManager.editKnowledge(${item.id}); this.closest('.knowledge-detail-modal').remove();" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">编辑</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                            <span>${item.createdAt}</span>
                        </div>
                        <div class="detail-row">
                            <strong>浏览次数：</strong>
                            <span>${item.views}</span>
                        </div>
                        <div class="detail-row">
                            <strong>描述：</strong>
                            <div style="margin-top: 8px; line-height: 1.6;">${item.description || '暂无描述'}</div>
                        </div>
                    </div>
                `;
                document.getElementById('viewModal').style.display = 'flex';
            }

            downloadKnowledge(id) {
                const item = this.knowledge.find(k => k.id === id);
                if (!item) return;
                
                showToast(`开始下载: ${item.title}`, 'info');
                // 模拟文件下载
                setTimeout(() => {
                    showToast('下载完成！', 'success');
                }, 1500);
            }

            deleteKnowledge(id) {
                const item = this.knowledge.find(k => k.id === parseInt(id));
                if (!item) {
                    ds.showToast('知识条目不存在', 'error');
                    return;
                }

                if (confirm(`确定要删除知识条目"${item.title}"吗？此操作不可撤销！`)) {
                    this.knowledge = this.knowledge.filter(k => k.id !== parseInt(id));

                    // 保存到本地存储
                    this.saveKnowledge();

                    // 重新加载和渲染
                    this.loadKnowledge();
                    this.updateStats();
                    this.renderKnowledge();
                    this.renderPagination();

                    ds.showToast('知识条目删除成功', 'success');
                }
            }

            getCategoryName(categoryId) {
                const category = this.categories.find(c => c.id === categoryId);
                return category ? category.name : categoryId;
            }

            goToPage(page) {
                this.currentPage = page;
                this.loadKnowledge();
            }

            previousPage() {
                if (this.currentPage > 1) this.goToPage(this.currentPage - 1);
            }

            nextPage() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                if (this.currentPage < totalPages) this.goToPage(this.currentPage + 1);
            }

            showAddCategoryModal() { 
                document.getElementById('categoryModal').style.display = 'block';
                document.getElementById('categoryForm').reset();
                clearFormErrors();
            }
            showAddKnowledgeModal() { 
                document.getElementById('knowledgeModal').style.display = 'block';
                document.getElementById('knowledgeModalTitle').textContent = '新增知识';
                document.getElementById('knowledgeForm').reset();
                clearFormErrors();
            }

            editKnowledge(id) {
                // 模拟获取知识详情
                const knowledge = this.knowledgeData.find(k => k.id === id);
                if (knowledge) {
                    document.getElementById('knowledgeModal').style.display = 'block';
                    document.getElementById('knowledgeModalTitle').textContent = '编辑知识';
                    document.getElementById('knowledgeTitle').value = knowledge.title;
                    document.getElementById('knowledgeCategory').value = knowledge.category;
                    document.getElementById('knowledgeDescription').value = knowledge.description;
                    document.getElementById('knowledgeContent').value = knowledge.content || '';
                    document.getElementById('knowledgeForm').dataset.editId = id;
                    clearFormErrors();
                }
            }

            viewKnowledge(id) {
                const knowledge = this.knowledgeData.find(k => k.id === id);
                if (knowledge) {
                    document.getElementById('viewModal').style.display = 'block';
                    document.getElementById('viewTitle').textContent = knowledge.title;
                    document.getElementById('viewCategory').textContent = knowledge.category;
                    document.getElementById('viewDescription').textContent = knowledge.description;
                    document.getElementById('viewDate').textContent = knowledge.date;
                    document.getElementById('viewAuthor').textContent = knowledge.author;
                    document.getElementById('viewContent').innerHTML = knowledge.content || '<p>暂无详细内容</p>';
                }
            }

            downloadKnowledge(id) {
                const knowledge = this.knowledgeData.find(k => k.id === id);
                if (knowledge) {
                    // 创建下载内容
                    const content = `标题: ${knowledge.title}\n分类: ${knowledge.category}\n描述: ${knowledge.description}\n作者: ${knowledge.author}\n日期: ${knowledge.date}\n\n内容:\n${knowledge.content || '暂无详细内容'}`;
                    
                    // 创建blob和下载链接
                    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${knowledge.title}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showToast('下载成功', 'success');
                }
            }

            deleteKnowledge(id) {
                if (confirm('确定要删除这条知识记录吗？此操作不可恢复。')) {
                    this.knowledgeData = this.knowledgeData.filter(k => k.id !== id);
                    this.updateStats();
                    this.renderKnowledgeList();
                    showToast('删除成功', 'success');
                }
            }

            uploadFiles() {
                ds.showToast('文件上传功能开发中...', 'info');
            }

            exportKnowledge() {
                try {
                    ds.showToast('正在导出知识库数据...', 'info');

                    // 获取当前筛选的知识条目
                    const exportData = this.filteredKnowledge;

                    // 生成CSV内容
                    const headers = ['ID', '标题', '类型', '分类', '描述', '作者', '标签', '浏览量', '下载量', '创建时间', '更新时间'];
                    const csvContent = [
                        headers.join(','),
                        ...exportData.map(item => [
                            item.id,
                            `"${item.title.replace(/"/g, '""')}"`,
                            this.getTypeText(item.type),
                            this.getCategoryName(item.category),
                            `"${item.description.replace(/"/g, '""')}"`,
                            `"${item.author.replace(/"/g, '""')}"`,
                            `"${(item.tags || []).join('; ')}"`,
                            item.views || 0,
                            item.downloads || 0,
                            ds.formatDate(item.createdAt),
                            ds.formatDate(item.updatedAt)
                        ].join(','))
                    ].join('\n');

                    // 创建下载链接
                    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `knowledge_export_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    ds.showToast(`知识库导出成功 (${exportData.length}条记录)`, 'success');
                } catch (error) {
                    ds.showToast('导出失败', 'error');
                    console.error('导出失败:', error);
                }
            }
        }

        let knowledgeManager;
        function showAddCategoryModal() { knowledgeManager.showAddCategoryModal(); }
        function showAddKnowledgeModal() { knowledgeManager.showAddKnowledgeModal(); }
        function searchKnowledge() { knowledgeManager.searchKnowledge(); }
        function filterKnowledge() { knowledgeManager.filterKnowledge(); }
        function previousPage() { knowledgeManager.previousPage(); }
        function nextPage() { knowledgeManager.nextPage(); }
        function uploadFiles() { knowledgeManager.uploadFiles(); }
        function exportKnowledge() { knowledgeManager.exportKnowledge(); }
        function editKnowledge(id) { knowledgeManager.editKnowledge(id); }
        function viewKnowledge(id) { knowledgeManager.viewKnowledge(id); }
        function downloadKnowledge(id) { knowledgeManager.downloadKnowledge(id); }
        function deleteKnowledge(id) { knowledgeManager.deleteKnowledge(id); }

        // 模态框控制函数
        function closeKnowledgeModal() {
            document.getElementById('knowledgeModal').style.display = 'none';
            document.getElementById('knowledgeForm').reset();
            delete document.getElementById('knowledgeForm').dataset.editId;
            clearFormErrors();
        }

        function submitKnowledgeForm() {
            const form = document.getElementById('knowledgeForm');

            // 表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!validateKnowledgeForm()) {
                return;
            }

            const formData = new FormData(form);

            const knowledgeData = {
                title: formData.get('title').trim(),
                type: formData.get('type') || 'document',
                category: formData.get('category'),
                description: formData.get('description').trim(),
                content: formData.get('content').trim(),
                author: formData.get('author') || '当前用户',
                tags: formData.get('tags') ? formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag) : []
            };

            // 基本验证
            if (!knowledgeData.title || !knowledgeData.description) {
                ds.showToast('标题和描述为必填项', 'error');
                return;
            }

            const editId = form.dataset.editId;
            if (editId) {
                // 编辑模式
                const index = knowledgeManager.knowledge.findIndex(k => k.id == parseInt(editId));
                if (index !== -1) {
                    knowledgeManager.knowledge[index] = {
                        ...knowledgeManager.knowledge[index],
                        ...knowledgeData,
                        updatedAt: new Date().toISOString()
                    };
                    ds.showToast('知识条目更新成功', 'success');
                } else {
                    ds.showToast('知识条目不存在', 'error');
                    return;
                }
            } else {
                // 新增模式
                const newKnowledge = {
                    id: knowledgeManager.getNextKnowledgeId(),
                    ...knowledgeData,
                    views: 0,
                    downloads: 0,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    fileSize: '0KB',
                    fileName: ''
                };
                knowledgeManager.knowledge.unshift(newKnowledge);
                ds.showToast('知识条目创建成功', 'success');
            }

            // 保存到本地存储
            knowledgeManager.saveKnowledge();

            // 重新加载和渲染
            knowledgeManager.loadKnowledge();
            knowledgeManager.updateStats();
            knowledgeManager.renderKnowledge();
            knowledgeManager.renderPagination();

            closeKnowledgeModal();
        }

        function closeCategoryModal() {
            document.getElementById('categoryModal').style.display = 'none';
            document.getElementById('categoryForm').reset();
            clearFormErrors();
        }

        function submitCategoryForm() {
            const form = document.getElementById('categoryForm');
            const formData = new FormData(form);
            
            const categoryName = formData.get('categoryName');
            if (!categoryName.trim()) {
                showFormError('categoryName', '分类名称不能为空');
                return;
            }

            // 这里可以添加分类到下拉选项中
            const categorySelect = document.getElementById('knowledgeCategory');
            const option = document.createElement('option');
            option.value = categoryName;
            option.textContent = categoryName;
            categorySelect.appendChild(option);

            showToast('分类添加成功', 'success');
            closeCategoryModal();
        }

        function closeViewModal() {
            document.getElementById('viewModal').style.display = 'none';
        }

        function handleFileUpload(event) {
            const files = event.target.files;
            const fileList = document.getElementById('fileList');
            
            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                    <button type="button" onclick="removeFile(this)" class="btn-remove">×</button>
                `;
                fileList.appendChild(fileItem);
            }
        }

        function removeFile(button) {
            button.parentElement.remove();
        }

        function clearFormErrors() {
            const errorElements = document.querySelectorAll('.form-error');
            errorElements.forEach(el => el.remove());
            
            const inputElements = document.querySelectorAll('.form-input.error');
            inputElements.forEach(el => el.classList.remove('error'));
        }

        function showFormError(fieldName, message) {
            const field = document.getElementById(fieldName) || document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('error');
                
                // 移除已存在的错误信息
                const existingError = field.parentNode.querySelector('.form-error');
                if (existingError) {
                    existingError.remove();
                }
                
                // 添加新的错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'form-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
        }

        function validateKnowledgeForm() {
            clearFormErrors();
            let isValid = true;

            const title = document.getElementById('knowledgeTitle').value.trim();
            if (!title) {
                showFormError('knowledgeTitle', '标题不能为空');
                isValid = false;
            }

            const type = document.getElementById('knowledgeType').value;
            if (!type) {
                showFormError('knowledgeType', '请选择类型');
                isValid = false;
            }

            const category = document.getElementById('knowledgeCategory').value;
            if (!category) {
                showFormError('knowledgeCategory', '请选择分类');
                isValid = false;
            }

            const description = document.getElementById('knowledgeDescription').value.trim();
            if (!description) {
                showFormError('knowledgeDescription', '描述不能为空');
                isValid = false;
            }

            const description = document.getElementById('knowledgeDescription').value.trim();
            if (!description) {
                showFormError('knowledgeDescription', '描述不能为空');
                isValid = false;
            }

            return isValid;
        }

        function showToast(message, type = 'info') {
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                `;
                document.body.appendChild(toastContainer);
            }

            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                margin-bottom: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            toast.textContent = message;

            toastContainer.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 添加文件输入处理
        document.addEventListener('DOMContentLoaded', function() {
            knowledgeManager = new KnowledgeManager();
            
            // 为文件输入添加事件监听器
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileUpload);
            }
            
            // 为模态框点击外部关闭添加事件监听器
            window.addEventListener('click', function(event) {
                const knowledgeModal = document.getElementById('knowledgeModal');
                const categoryModal = document.getElementById('categoryModal');
                const viewModal = document.getElementById('viewModal');
                
                if (event.target === knowledgeModal) {
                    closeKnowledgeModal();
                }
                if (event.target === categoryModal) {
                    closeCategoryModal();
                }
                if (event.target === viewModal) {
                    closeViewModal();
                }
            });
        });
    </script>
</body>
</html>

/**
 * 数据库模型定义
 * 使用Sequelize ORM进行数据库操作
 */

const { Sequelize, DataTypes, Op } = require('sequelize');
const bcrypt = require('bcrypt');

// 数据库连接配置
const sequelize = new Sequelize(
    process.env.DB_NAME || 'smart_home_products',
    process.env.DB_USER || 'root',
    process.env.DB_PASSWORD || '',
    {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        dialect: 'mysql',
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        timezone: '+08:00',
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
        pool: {
            max: 10,
            min: 0,
            acquire: 30000,
            idle: 10000
        },
        define: {
            timestamps: true,
            underscored: false,
            createdAt: 'created_at',
            updatedAt: 'updated_at'
        }
    }
);

// 用户模型
const User = sequelize.define('User', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    username: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
            len: [3, 100],
            isAlphanumeric: true
        }
    },
    email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
        validate: {
            isEmail: true
        }
    },
    password_hash: {
        type: DataTypes.STRING(255),
        allowNull: false
    },
    role: {
        type: DataTypes.ENUM('admin', 'manager', 'user'),
        defaultValue: 'user'
    },
    status: {
        type: DataTypes.ENUM('active', 'inactive', 'suspended'),
        defaultValue: 'active'
    },
    profile: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    last_login_at: {
        type: DataTypes.DATE,
        allowNull: true
    }
}, {
    tableName: 'users',
    indexes: [
        { fields: ['username'] },
        { fields: ['email'] },
        { fields: ['role'] },
        { fields: ['status'] }
    ],
    hooks: {
        beforeCreate: async (user) => {
            if (user.password_hash) {
                user.password_hash = await bcrypt.hash(user.password_hash, 12);
            }
        },
        beforeUpdate: async (user) => {
            if (user.changed('password_hash')) {
                user.password_hash = await bcrypt.hash(user.password_hash, 12);
            }
        }
    }
});

// 用户实例方法
User.prototype.validatePassword = async function(password) {
    return bcrypt.compare(password, this.password_hash);
};

User.prototype.toSafeJSON = function() {
    const values = Object.assign({}, this.get());
    delete values.password_hash;
    return values;
};

// 商品分类模型
const ProductCategory = sequelize.define('ProductCategory', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    parent_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
        references: {
            model: 'product_categories',
            key: 'id'
        }
    },
    sort_order: {
        type: DataTypes.INTEGER,
        defaultValue: 0
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    metadata: {
        type: DataTypes.JSON,
        defaultValue: {}
    }
}, {
    tableName: 'product_categories',
    indexes: [
        { fields: ['code'] },
        { fields: ['parent_id'] },
        { fields: ['sort_order'] }
    ]
});

// 品牌模型
const Brand = sequelize.define('Brand', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true
    },
    code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    logo_url: {
        type: DataTypes.STRING(500),
        allowNull: true
    },
    website: {
        type: DataTypes.STRING(500),
        allowNull: true
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    metadata: {
        type: DataTypes.JSON,
        defaultValue: {}
    }
}, {
    tableName: 'brands',
    indexes: [
        { fields: ['name'] },
        { fields: ['code'] }
    ]
});

// 商品模型
const Product = sequelize.define('Product', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    user_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
            len: [2, 255]
        }
    },
    sku: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
            is: /^[A-Z]{2}\d{3,}$/
        }
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    short_description: {
        type: DataTypes.STRING(500),
        allowNull: true
    },
    category_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
        references: {
            model: 'product_categories',
            key: 'id'
        }
    },
    brand_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
        references: {
            model: 'brands',
            key: 'id'
        }
    },
    price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0
        }
    },
    cost_price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        validate: {
            min: 0
        }
    },
    market_price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        validate: {
            min: 0
        }
    },
    stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0,
        validate: {
            min: 0
        }
    },
    min_stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0
    },
    max_stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0
    },
    sales: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0
    },
    views: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0
    },
    weight: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
        validate: {
            min: 0
        }
    },
    dimensions: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    status: {
        type: DataTypes.ENUM('active', 'inactive', 'draft', 'out_of_stock'),
        defaultValue: 'draft'
    },
    is_featured: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    },
    is_digital: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    },
    medusa_synced: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    },
    medusa_id: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    images: {
        type: DataTypes.JSON,
        defaultValue: []
    },
    attributes: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    metadata: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    seo_title: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    seo_description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    seo_keywords: {
        type: DataTypes.STRING(500),
        allowNull: true
    }
}, {
    tableName: 'products',
    indexes: [
        { fields: ['user_id'] },
        { fields: ['sku'] },
        { fields: ['category_id'] },
        { fields: ['brand_id'] },
        { fields: ['status'] },
        { fields: ['price'] },
        { fields: ['stock'] },
        { fields: ['sales'] },
        { fields: ['created_at'] },
        { fields: ['name'] }
    ],
    hooks: {
        afterUpdate: async (product, options) => {
            // 自动更新库存状态
            if (product.changed('stock')) {
                if (product.stock <= 0) {
                    product.status = 'out_of_stock';
                } else if (product.status === 'out_of_stock' && product.stock > 0) {
                    product.status = 'active';
                }
            }
        }
    }
});

// 商品变体模型
const ProductVariant = sequelize.define('ProductVariant', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    product_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
            model: 'products',
            key: 'id'
        }
    },
    sku: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true
    },
    name: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0
        }
    },
    cost_price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true
    },
    stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        defaultValue: 0
    },
    weight: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true
    },
    dimensions: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    attributes: {
        type: DataTypes.JSON,
        defaultValue: {}
    },
    images: {
        type: DataTypes.JSON,
        defaultValue: []
    },
    is_default: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    sort_order: {
        type: DataTypes.INTEGER,
        defaultValue: 0
    }
}, {
    tableName: 'product_variants',
    indexes: [
        { fields: ['product_id'] },
        { fields: ['sku'] },
        { fields: ['sort_order'] }
    ]
});

// 库存记录模型
const InventoryLog = sequelize.define('InventoryLog', {
    id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
    },
    product_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
            model: 'products',
            key: 'id'
        }
    },
    variant_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
        references: {
            model: 'product_variants',
            key: 'id'
        }
    },
    type: {
        type: DataTypes.ENUM('in', 'out', 'adjust', 'reserve', 'release'),
        allowNull: false
    },
    quantity: {
        type: DataTypes.INTEGER,
        allowNull: false
    },
    before_stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false
    },
    after_stock: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false
    },
    reason: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    reference_type: {
        type: DataTypes.STRING(50),
        allowNull: true
    },
    reference_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true
    },
    operator_id: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
        references: {
            model: 'users',
            key: 'id'
        }
    }
}, {
    tableName: 'inventory_logs',
    updatedAt: false,
    indexes: [
        { fields: ['product_id'] },
        { fields: ['variant_id'] },
        { fields: ['type'] },
        { fields: ['created_at'] }
    ]
});

// 定义模型关联关系
User.hasMany(Product, { foreignKey: 'user_id', as: 'products' });
Product.belongsTo(User, { foreignKey: 'user_id', as: 'owner' });

ProductCategory.hasMany(Product, { foreignKey: 'category_id', as: 'products' });
Product.belongsTo(ProductCategory, { foreignKey: 'category_id', as: 'category' });

Brand.hasMany(Product, { foreignKey: 'brand_id', as: 'products' });
Product.belongsTo(Brand, { foreignKey: 'brand_id', as: 'brand' });

Product.hasMany(ProductVariant, { foreignKey: 'product_id', as: 'variants' });
ProductVariant.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

Product.hasMany(InventoryLog, { foreignKey: 'product_id', as: 'inventoryLogs' });
InventoryLog.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

ProductVariant.hasMany(InventoryLog, { foreignKey: 'variant_id', as: 'inventoryLogs' });
InventoryLog.belongsTo(ProductVariant, { foreignKey: 'variant_id', as: 'variant' });

User.hasMany(InventoryLog, { foreignKey: 'operator_id', as: 'operatedLogs' });
InventoryLog.belongsTo(User, { foreignKey: 'operator_id', as: 'operator' });

// 自关联：分类的父子关系
ProductCategory.hasMany(ProductCategory, { foreignKey: 'parent_id', as: 'children' });
ProductCategory.belongsTo(ProductCategory, { foreignKey: 'parent_id', as: 'parent' });

// 数据库连接和同步
async function initDatabase() {
    try {
        // 测试连接
        await sequelize.authenticate();
        console.log('数据库连接成功');

        // 同步模型（开发环境）
        if (process.env.NODE_ENV === 'development') {
            await sequelize.sync({ alter: true });
            console.log('数据库模型同步完成');
        }

        return true;
    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    }
}

// 关闭数据库连接
async function closeDatabase() {
    try {
        await sequelize.close();
        console.log('数据库连接已关闭');
    } catch (error) {
        console.error('关闭数据库连接失败:', error);
    }
}

// 导出模型和工具函数
module.exports = {
    sequelize,
    Op,
    User,
    ProductCategory,
    Brand,
    Product,
    ProductVariant,
    InventoryLog,
    initDatabase,
    closeDatabase
};

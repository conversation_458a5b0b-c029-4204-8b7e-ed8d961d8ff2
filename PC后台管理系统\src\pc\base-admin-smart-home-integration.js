/**
 * Base-Admin与智能家居权限系统融合模块
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

class BaseAdminSmartHomeIntegration {
    constructor(config = {}) {
        this.baseAdminApi = config.baseAdminApi || '/api/admin'
        this.smartHomeApi = config.smartHomeApi || '/api/smart-home'
        this.authToken = null
        this.currentUser = null
        
        // 用户角色映射 (Base-Admin → 智能家居)
        this.roleMapping = {
            // Base-Admin系统角色
            'super_admin': {
                smartHomeRoles: ['SYSTEM_ADMIN'],
                systemPermissions: ['*'],
                projectPermissions: ['*']
            },
            'admin': {
                smartHomeRoles: ['SMART_HOME_DESIGNER'],
                systemPermissions: ['user:manage', 'project:manage'],
                projectPermissions: ['design:*', 'construction:view']
            },
            'user': {
                smartHomeRoles: ['OWNER'],
                systemPermissions: ['profile:edit'],
                projectPermissions: ['project:*']
            },
            
            // 智能家居专有角色
            'smart_home_designer': {
                baseAdminRole: 'admin',
                systemPermissions: ['design:manage', 'project:view'],
                projectPermissions: ['design:*', 'construction:view', 'files:*']
            },
            'constructor': {
                baseAdminRole: 'user',
                systemPermissions: ['profile:edit'],
                projectPermissions: ['construction:*', 'files:upload']
            }
        }
        
        // 权限模块映射
        this.moduleMapping = {
            // Base-Admin模块 → 智能家居模块
            'user_management': 'system',
            'role_management': 'system',
            'system_settings': 'system',
            
            // 智能家居专有模块
            'project_spaces': 'project',
            'design_management': 'design',
            'construction_management': 'construction',
            'file_management': 'files',
            'comment_management': 'comments',
            'marketing_sharing': 'marketing'
        }
    }

    /**
     * 初始化融合系统
     */
    async initialize() {
        try {
            console.log('🔧 初始化Base-Admin与智能家居融合系统...')
            
            // 1. 验证Base-Admin连接
            await this.validateBaseAdminConnection()
            
            // 2. 扩展用户模型
            await this.extendUserModel()
            
            // 3. 初始化权限映射
            await this.initializePermissionMapping()
            
            // 4. 设置事件监听
            this.setupEventListeners()
            
            console.log('✅ 融合系统初始化完成')
            return true
        } catch (error) {
            console.error('❌ 融合系统初始化失败:', error)
            return false
        }
    }

    /**
     * 验证Base-Admin连接
     */
    async validateBaseAdminConnection() {
        try {
            const response = await fetch(`${this.baseAdminApi}/health`)
            if (!response.ok) {
                throw new Error(`Base-Admin连接失败: ${response.status}`)
            }
            console.log('✅ Base-Admin连接验证成功')
        } catch (error) {
            console.error('❌ Base-Admin连接验证失败:', error)
            throw error
        }
    }

    /**
     * 扩展用户模型
     */
    async extendUserModel() {
        // 扩展Base-Admin的用户模型以支持智能家居角色
        const userExtensions = {
            smart_home_role: null,
            project_permissions: {},
            invitation_status: 'none',
            project_memberships: [],
            last_project_access: null
        }
        
        console.log('📝 用户模型扩展完成')
        return userExtensions
    }

    /**
     * 初始化权限映射
     */
    async initializePermissionMapping() {
        try {
            console.log('📋 开始初始化权限映射...')

            // 1. 同步角色映射到Base-Admin
            await this.syncRoleMappingToBaseAdmin()

            // 2. 创建智能家居专有权限资源
            await this.createSmartHomePermissionResources()

            // 3. 设置权限继承规则
            await this.setupPermissionInheritanceRules()

            // 4. 验证权限映射完整性
            await this.validatePermissionMapping()

            console.log('✅ 权限映射初始化完成')
        } catch (error) {
            console.error('❌ 权限映射初始化失败:', error)
            throw error
        }
    }

    /**
     * 同步角色映射到Base-Admin
     */
    async syncRoleMappingToBaseAdmin() {
        try {
            const roleMappingData = {
                mappings: this.roleMapping,
                timestamp: new Date().toISOString(),
                version: '1.0'
            }

            const response = await fetch(`${this.baseAdminApi}/permissions/role-mappings`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roleMappingData)
            })

            if (!response.ok) {
                throw new Error(`角色映射同步失败: ${response.status}`)
            }

            console.log('✅ 角色映射已同步到Base-Admin')
        } catch (error) {
            console.error('❌ 角色映射同步失败:', error)
            // 非致命错误，继续执行
        }
    }

    /**
     * 创建智能家居专有权限资源
     */
    async createSmartHomePermissionResources() {
        const smartHomeResources = [
            {
                name: 'project_spaces',
                displayName: '项目空间管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'INVITE', 'MANAGE']
            },
            {
                name: 'design_management',
                displayName: '设计管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT']
            },
            {
                name: 'construction_management',
                displayName: '施工管理',
                operations: ['VIEW', 'UPDATE', 'COMMENT', 'INSPECT', 'APPROVE']
            },
            {
                name: 'file_management',
                displayName: '文件管理',
                operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE', 'MANAGE']
            },
            {
                name: 'comment_management',
                displayName: '评论管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            {
                name: 'marketing_sharing',
                displayName: '营销分享',
                operations: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS']
            }
        ]

        for (const resource of smartHomeResources) {
            try {
                await fetch(`${this.baseAdminApi}/permissions/resources`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(resource)
                })
            } catch (error) {
                console.warn(`⚠️ 创建权限资源失败: ${resource.name}`, error)
            }
        }

        console.log('✅ 智能家居权限资源创建完成')
    }

    /**
     * 设置权限继承规则
     */
    async setupPermissionInheritanceRules() {
        const inheritanceRules = {
            // 项目级权限继承规则
            project_inheritance: {
                'OWNER': {
                    inherits_from: 'admin',
                    additional_permissions: ['project:*', 'invite:*']
                },
                'SMART_HOME_DESIGNER': {
                    inherits_from: 'designer',
                    additional_permissions: ['design:*', 'files:*']
                },
                'CONSTRUCTOR': {
                    inherits_from: 'user',
                    additional_permissions: ['construction:*', 'files:upload']
                }
            },
            // 模块级权限继承规则
            module_inheritance: {
                'design_management': ['project_spaces'],
                'construction_management': ['project_spaces', 'file_management'],
                'marketing_sharing': ['project_spaces']
            }
        }

        try {
            await fetch(`${this.baseAdminApi}/permissions/inheritance-rules`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(inheritanceRules)
            })

            console.log('✅ 权限继承规则设置完成')
        } catch (error) {
            console.warn('⚠️ 权限继承规则设置失败:', error)
        }
    }

    /**
     * 验证权限映射完整性
     */
    async validatePermissionMapping() {
        const validationResults = {
            roleMappings: true,
            moduleMapping: true,
            permissionResources: true,
            inheritanceRules: true
        }

        try {
            // 验证角色映射
            for (const [role, mapping] of Object.entries(this.roleMapping)) {
                if (!mapping.systemPermissions || !Array.isArray(mapping.systemPermissions)) {
                    validationResults.roleMappings = false
                    console.error(`❌ 角色映射验证失败: ${role}`)
                }
            }

            // 验证模块映射
            for (const [module, baseModule] of Object.entries(this.moduleMapping)) {
                if (!baseModule || typeof baseModule !== 'string') {
                    validationResults.moduleMapping = false
                    console.error(`❌ 模块映射验证失败: ${module}`)
                }
            }

            const allValid = Object.values(validationResults).every(result => result === true)

            if (allValid) {
                console.log('✅ 权限映射验证通过')
            } else {
                console.warn('⚠️ 权限映射验证发现问题:', validationResults)
            }

            return validationResults
        } catch (error) {
            console.error('❌ 权限映射验证失败:', error)
            return validationResults
        }
    }

    /**
     * 用户登录（融合版本）
     */
    async login(credentials) {
        try {
            // 1. 使用Base-Admin认证
            const baseAuthResponse = await fetch(`${this.baseAdminApi}/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(credentials)
            })
            
            if (!baseAuthResponse.ok) {
                throw new Error('Base-Admin认证失败')
            }
            
            const baseAuthData = await baseAuthResponse.json()
            this.authToken = baseAuthData.token
            
            // 2. 获取智能家居扩展信息
            const smartHomeProfile = await this.getSmartHomeProfile(baseAuthData.user.id)
            
            // 3. 合并用户信息
            this.currentUser = {
                ...baseAuthData.user,
                smartHomeRole: smartHomeProfile.role,
                projectPermissions: smartHomeProfile.permissions,
                projectMemberships: smartHomeProfile.memberships
            }
            
            console.log('✅ 融合登录成功:', this.currentUser.username)
            return {
                success: true,
                user: this.currentUser,
                token: this.authToken
            }
        } catch (error) {
            console.error('❌ 融合登录失败:', error)
            throw error
        }
    }

    /**
     * 获取智能家居用户档案
     */
    async getSmartHomeProfile(userId) {
        try {
            const response = await fetch(`${this.smartHomeApi}/users/${userId}/profile`, {
                headers: { 'Authorization': `Bearer ${this.authToken}` }
            })
            
            if (response.ok) {
                return await response.json()
            } else {
                // 如果没有智能家居档案，创建默认档案
                return await this.createDefaultSmartHomeProfile(userId)
            }
        } catch (error) {
            console.error('❌ 获取智能家居档案失败:', error)
            return this.createDefaultSmartHomeProfile(userId)
        }
    }

    /**
     * 创建默认智能家居档案
     */
    async createDefaultSmartHomeProfile(userId) {
        const defaultProfile = {
            role: 'OWNER', // 默认为业主角色
            permissions: {
                project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE'],
                design: ['VIEW', 'COMMENT', 'APPROVE'],
                construction: ['VIEW', 'COMMENT', 'APPROVE'],
                files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                comments: ['VIEW', 'CREATE', 'EDIT'],
                marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
            },
            memberships: []
        }
        
        console.log(`📝 为用户 ${userId} 创建默认智能家居档案`)
        return defaultProfile
    }

    /**
     * 权限检查（融合版本）
     */
    async hasPermission(userId, module, operation, projectId = null) {
        try {
            // 1. 检查Base-Admin系统权限
            const hasSystemPermission = await this.checkBaseAdminPermission(userId, module, operation)
            
            // 2. 如果涉及项目，检查项目权限
            if (projectId) {
                const hasProjectPermission = await this.checkProjectPermission(userId, projectId, module, operation)
                return hasSystemPermission && hasProjectPermission
            }
            
            return hasSystemPermission
        } catch (error) {
            console.error('❌ 权限检查失败:', error)
            return false
        }
    }

    /**
     * 检查Base-Admin系统权限
     */
    async checkBaseAdminPermission(userId, module, operation) {
        try {
            const response = await fetch(`${this.baseAdminApi}/permissions/check`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: userId,
                    resource: this.moduleMapping[module] || module,
                    action: operation
                })
            })
            
            const result = await response.json()
            return result.hasPermission || false
        } catch (error) {
            console.error('❌ Base-Admin权限检查失败:', error)
            return false
        }
    }

    /**
     * 检查项目权限
     */
    async checkProjectPermission(userId, projectId, module, operation) {
        try {
            const response = await fetch(`${this.smartHomeApi}/projects/${projectId}/permissions/check`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: userId,
                    module: module,
                    operation: operation
                })
            })
            
            const result = await response.json()
            return result.hasPermission || false
        } catch (error) {
            console.error('❌ 项目权限检查失败:', error)
            return false
        }
    }

    /**
     * 创建项目空间
     */
    async createProjectSpace(projectData) {
        try {
            const response = await fetch(`${this.smartHomeApi}/projects`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...projectData,
                    ownerId: this.currentUser.id
                })
            })
            
            if (!response.ok) {
                throw new Error('创建项目空间失败')
            }
            
            const project = await response.json()
            console.log('✅ 项目空间创建成功:', project.name)
            return project
        } catch (error) {
            console.error('❌ 创建项目空间失败:', error)
            throw error
        }
    }

    /**
     * 邀请用户到项目
     */
    async inviteUserToProject(projectId, inviteeId, role, permissions = null) {
        try {
            const response = await fetch(`${this.smartHomeApi}/projects/${projectId}/invitations`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inviterId: this.currentUser.id,
                    inviteeId: inviteeId,
                    role: role,
                    permissions: permissions
                })
            })
            
            if (!response.ok) {
                throw new Error('邀请用户失败')
            }
            
            const invitation = await response.json()
            console.log('📧 用户邀请成功:', invitation.id)
            return invitation
        } catch (error) {
            console.error('❌ 邀请用户失败:', error)
            throw error
        }
    }

    /**
     * 获取用户的项目列表
     */
    async getUserProjects(userId = null) {
        try {
            const targetUserId = userId || this.currentUser.id
            const response = await fetch(`${this.smartHomeApi}/users/${targetUserId}/projects`, {
                headers: { 'Authorization': `Bearer ${this.authToken}` }
            })
            
            if (!response.ok) {
                throw new Error('获取项目列表失败')
            }
            
            const projects = await response.json()
            return projects
        } catch (error) {
            console.error('❌ 获取项目列表失败:', error)
            return []
        }
    }

    /**
     * 获取融合后的用户列表
     */
    async getUsers(filters = {}) {
        try {
            // 1. 获取Base-Admin用户列表
            const baseUsers = await this.getBaseAdminUsers(filters)
            
            // 2. 获取智能家居用户扩展信息
            const enhancedUsers = await Promise.all(
                baseUsers.map(async (user) => {
                    const smartHomeProfile = await this.getSmartHomeProfile(user.id)
                    return {
                        ...user,
                        smartHomeRole: smartHomeProfile.role,
                        projectCount: smartHomeProfile.memberships.length,
                        lastProjectAccess: smartHomeProfile.lastAccess
                    }
                })
            )
            
            return enhancedUsers
        } catch (error) {
            console.error('❌ 获取用户列表失败:', error)
            return []
        }
    }

    /**
     * 获取Base-Admin用户列表
     */
    async getBaseAdminUsers(filters = {}) {
        try {
            const queryParams = new URLSearchParams(filters)
            const response = await fetch(`${this.baseAdminApi}/users?${queryParams}`, {
                headers: { 'Authorization': `Bearer ${this.authToken}` }
            })
            
            if (!response.ok) {
                throw new Error('获取Base-Admin用户列表失败')
            }
            
            const result = await response.json()
            return result.users || []
        } catch (error) {
            console.error('❌ 获取Base-Admin用户列表失败:', error)
            return []
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听用户角色变更
        document.addEventListener('userRoleChanged', (event) => {
            this.handleUserRoleChange(event.detail)
        })
        
        // 监听项目权限变更
        document.addEventListener('projectPermissionChanged', (event) => {
            this.handleProjectPermissionChange(event.detail)
        })
        
        console.log('📡 事件监听器设置完成')
    }

    /**
     * 处理用户角色变更
     */
    async handleUserRoleChange(data) {
        const { userId, oldRole, newRole } = data
        
        try {
            // 同步更新Base-Admin和智能家居系统的角色
            await this.syncUserRole(userId, newRole)
            console.log(`🔄 用户 ${userId} 角色已同步: ${oldRole} → ${newRole}`)
        } catch (error) {
            console.error('❌ 用户角色同步失败:', error)
        }
    }

    /**
     * 同步用户角色
     */
    async syncUserRole(userId, newRole) {
        // 1. 更新Base-Admin角色
        const baseAdminRole = this.roleMapping[newRole]?.baseAdminRole
        if (baseAdminRole) {
            await this.updateBaseAdminUserRole(userId, baseAdminRole)
        }
        
        // 2. 更新智能家居角色
        await this.updateSmartHomeUserRole(userId, newRole)
    }

    /**
     * 获取融合系统统计信息
     */
    getSystemStats() {
        return {
            totalUsers: this.currentUser ? 1 : 0,
            activeProjects: 0,
            systemIntegration: 'Base-Admin + Smart Home',
            version: '1.0',
            lastSync: new Date().toISOString()
        }
    }
}

// 导出融合类
window.BaseAdminSmartHomeIntegration = BaseAdminSmartHomeIntegration

// 使用示例
/*
const integration = new BaseAdminSmartHomeIntegration({
    baseAdminApi: '/api/admin',
    smartHomeApi: '/api/smart-home'
})

// 初始化
await integration.initialize()

// 登录
const loginResult = await integration.login({
    username: 'admin',
    password: 'password'
})

// 检查权限
const canManageProject = await integration.hasPermission(
    'user_123', 'project', 'MANAGE', 'project_456'
)

// 创建项目
const project = await integration.createProjectSpace({
    name: '智能家居改造项目',
    description: '三室两厅智能化改造'
})
*/

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:,">
    <title>智能家居系统 - 用户注册</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="../../../styles/unified-admin-styles.css">
</head>
<body>
    <div class="admin-layout">
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">用户注册</h1>
                            <p class="breadcrumb-description">新用户注册和账户创建</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->

    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1><i class="fas fa-home"></i> 智能家居系统</h1>
                <p>创建您的账户，开启智能生活</p>
            </div>
            
            <div class="register-form">
                <!-- 注册方式选择 -->
                <div class="form-tabs">
                    <button class="tab-btn active" data-tab="email">
                        <i class="fas fa-envelope"></i> 邮箱注册
                    </button>
                    <button class="tab-btn" data-tab="phone">
                        <i class="fas fa-mobile-alt"></i> 手机注册
                    </button>
                </div>
                
                <!-- 消息显示区域 -->
                <div id="successMessage" class="success-message"></div>
                <div id="errorMessage" class="error-message"></div>
                
                <!-- 邮箱注册 -->
                <div id="email" class="tab-content active">
                    <form id="emailRegisterForm">
                        <div class="form-group">
                            <label for="email">邮箱地址 *</label>
                            <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="emailError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="usernameError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">密码 *</label>
                            <input type="password" id="password" name="password" placeholder="请输入密码" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="passwordError"></div>
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text"></div>
                                <div class="strength-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">确认密码 *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="confirmPasswordError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="emailVerificationCode">邮箱验证码 *</label>
                            <div class="verification-group">
                                <input type="text" id="emailVerificationCode" name="verificationCode" placeholder="请输入验证码" required>
                                <button type="button" class="verification-btn" id="sendEmailCodeBtn">发送验证码</button>
                            </div>
                            <div class="field-error" id="emailVerificationCodeError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="nickname">昵称</label>
                            <input type="text" id="nickname" name="nickname" placeholder="请输入昵称（可选）">
                        </div>
                    </form>
                </div>
                
                <!-- 手机注册 -->
                <div id="phone" class="tab-content">
                    <form id="phoneRegisterForm">
                        <div class="form-group">
                            <label for="phoneNumber">手机号码 *</label>
                            <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="请输入手机号码" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="phoneNumberError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phoneVerificationCode">短信验证码 *</label>
                            <div class="verification-group">
                                <input type="text" id="phoneVerificationCode" name="verificationCode" placeholder="请输入验证码" required>
                                <button type="button" class="verification-btn" id="sendPhoneCodeBtn">发送验证码</button>
                            </div>
                            <div class="field-error" id="phoneVerificationCodeError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phoneUsername">用户名 *</label>
                            <input type="text" id="phoneUsername" name="username" placeholder="请输入用户名" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="phoneUsernameError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phonePassword">密码 *</label>
                            <input type="password" id="phonePassword" name="password" placeholder="请输入密码" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="phonePasswordError"></div>
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text"></div>
                                <div class="strength-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phoneConfirmPassword">确认密码 *</label>
                            <input type="password" id="phoneConfirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                            <div class="validation-indicator"></div>
                            <div class="field-error" id="phoneConfirmPasswordError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="phoneNickname">昵称</label>
                            <input type="text" id="phoneNickname" name="nickname" placeholder="请输入昵称（可选）">
                        </div>
                    </form>
                </div>
                
                <!-- GDPR同意 -->
                <div class="gdpr-consent">
                    <input type="checkbox" id="gdprConsent" required>
                    <label for="gdprConsent">
                        我已阅读并同意 <a href="#" onclick="showPrivacyPolicy()">《隐私政策》</a> 和 <a href="#" onclick="showTermsOfService()">《服务条款》</a>，
                        同意智能家居系统收集、使用和处理我的个人信息用于提供服务。
                        根据GDPR，您有权访问、更正、删除您的个人数据。
                    </label>
                </div>
                
                <button type="button" class="register-btn" id="registerBtn" disabled>
                    <i class="fas fa-user-plus"></i> 立即注册
                </button>
                
                <div class="login-link">
                    已有账户？<a href="../07-profile/logout.html">立即登录</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">正在注册，请稍候...</div>
        </div>
    </div>
    
    <!-- 引入实时验证模块 -->
    <script src="../../frontend/js/real-time-validation.js"></script>
    
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
        // 注册页面控制器
        class RegisterController {
            constructor() {
                this.currentTab = 'email';
                this.verificationCodes = {
                    email: { code: null, expiry: null, countdown: 0 },
                    phone: { code: null, expiry: null, countdown: 0 }
                };
                
                this.init();
            }
            
            init() {
                this.setupTabSwitching();
                this.setupFormValidation();
                this.setupVerificationCodes();
                this.setupGDPRConsent();
                this.setupRegisterButton();
            }
            
            setupTabSwitching() {
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const tabId = btn.getAttribute('data-tab');
                        this.switchTab(tabId);
                    });
                });
            }
            
            switchTab(tabId) {
                // 移除所有active类
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                
                // 添加active类
                document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
                document.getElementById(tabId).classList.add('active');
                
                this.currentTab = tabId;
                this.updateRegisterButtonState();
            }
            
            setupFormValidation() {
                // 密码确认验证
                const confirmPasswordInputs = ['confirmPassword', 'phoneConfirmPassword'];
                confirmPasswordInputs.forEach(id => {
                    const input = document.getElementById(id);
                    if (input) {
                        input.addEventListener('input', () => this.validatePasswordConfirmation(id));
                    }
                });
            }
            
            validatePasswordConfirmation(confirmPasswordId) {
                const confirmInput = document.getElementById(confirmPasswordId);
                const passwordInput = confirmPasswordId.includes('phone') ? 
                    document.getElementById('phonePassword') : 
                    document.getElementById('password');
                
                const errorElement = document.getElementById(confirmPasswordId + 'Error');
                
                if (confirmInput.value && passwordInput.value !== confirmInput.value) {
                    this.showFieldError(errorElement, '两次输入的密码不一致');
                    confirmInput.classList.add('error');
                    confirmInput.classList.remove('success');
                } else if (confirmInput.value) {
                    this.hideFieldError(errorElement);
                    confirmInput.classList.remove('error');
                    confirmInput.classList.add('success');
                }
                
                this.updateRegisterButtonState();
            }
            
            setupVerificationCodes() {
                document.getElementById('sendEmailCodeBtn').addEventListener('click', () => {
                    this.sendVerificationCode('email');
                });
                
                document.getElementById('sendPhoneCodeBtn').addEventListener('click', () => {
                    this.sendVerificationCode('phone');
                });
            }
            
            async sendVerificationCode(type) {
                const input = type === 'email' ? 
                    document.getElementById('email') : 
                    document.getElementById('phoneNumber');
                
                const btn = type === 'email' ? 
                    document.getElementById('sendEmailCodeBtn') : 
                    document.getElementById('sendPhoneCodeBtn');
                
                if (!input.value) {
                    this.showError(`请先输入${type === 'email' ? '邮箱地址' : '手机号码'}`);
                    return;
                }
                
                try {
                    btn.disabled = true;
                    btn.textContent = '发送中...';
                    
                    // 模拟发送验证码
                    await this.simulateSendCode(type, input.value);
                    
                    this.showSuccess(`验证码已发送到您的${type === 'email' ? '邮箱' : '手机'}`);
                    this.startCountdown(type, btn);
                    
                } catch (error) {
                    this.showError(`发送验证码失败: ${error.message}`);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }
            
            async simulateSendCode(type, target) {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 生成6位验证码
                const code = Math.floor(100000 + Math.random() * 900000).toString();
                this.verificationCodes[type] = {
                    code: code,
                    expiry: Date.now() + 5 * 60 * 1000, // 5分钟有效期
                    countdown: 60
                };
                
                console.log(`验证码已发送到 ${target}: ${code}`);
            }
            
            startCountdown(type, btn) {
                const countdown = this.verificationCodes[type].countdown;
                let remaining = countdown;
                
                const timer = setInterval(() => {
                    remaining--;
                    btn.textContent = `${remaining}秒后重发`;
                    
                    if (remaining <= 0) {
                        clearInterval(timer);
                        btn.disabled = false;
                        btn.textContent = '发送验证码';
                    }
                }, 1000);
            }
            
            setupGDPRConsent() {
                document.getElementById('gdprConsent').addEventListener('change', () => {
                    this.updateRegisterButtonState();
                });
            }
            
            setupRegisterButton() {
                document.getElementById('registerBtn').addEventListener('click', () => {
                    this.handleRegister();
                });
                
                // 监听表单输入变化
                document.querySelectorAll('input[required]').forEach(input => {
                    input.addEventListener('input', () => {
                        this.updateRegisterButtonState();
                    });
                });
            }
            
            updateRegisterButtonState() {
                const btn = document.getElementById('registerBtn');
                const gdprConsent = document.getElementById('gdprConsent').checked;
                
                let isValid = gdprConsent;
                
                if (this.currentTab === 'email') {
                    const requiredFields = ['email', 'username', 'password', 'confirmPassword', 'emailVerificationCode'];
                    isValid = isValid && requiredFields.every(id => {
                        const input = document.getElementById(id);
                        return input && input.value.trim() && !input.classList.contains('error');
                    });
                } else {
                    const requiredFields = ['phoneNumber', 'phoneUsername', 'phonePassword', 'phoneConfirmPassword', 'phoneVerificationCode'];
                    isValid = isValid && requiredFields.every(id => {
                        const input = document.getElementById(id);
                        return input && input.value.trim() && !input.classList.contains('error');
                    });
                }
                
                btn.disabled = !isValid;
            }
            
            async handleRegister() {
                try {
                    this.showLoading(true);
                    
                    const formData = this.collectFormData();
                    
                    // 验证验证码
                    if (!this.validateVerificationCode(formData)) {
                        throw new Error('验证码错误或已过期');
                    }
                    
                    // 模拟注册API调用
                    await this.simulateRegister(formData);
                    
                    this.showSuccess('注册成功！正在跳转到登录页面...');
                    
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                    
                } catch (error) {
                    this.showError(`注册失败: ${error.message}`);
                } finally {
                    this.showLoading(false);
                }
            }
            
            collectFormData() {
                if (this.currentTab === 'email') {
                    return {
                        type: 'email',
                        email: document.getElementById('email').value,
                        username: document.getElementById('username').value,
                        password: document.getElementById('password').value,
                        confirmPassword: document.getElementById('confirmPassword').value,
                        verificationCode: document.getElementById('emailVerificationCode').value,
                        nickname: document.getElementById('nickname').value,
                        gdprConsent: document.getElementById('gdprConsent').checked
                    };
                } else {
                    return {
                        type: 'phone',
                        phoneNumber: document.getElementById('phoneNumber').value,
                        username: document.getElementById('phoneUsername').value,
                        password: document.getElementById('phonePassword').value,
                        confirmPassword: document.getElementById('phoneConfirmPassword').value,
                        verificationCode: document.getElementById('phoneVerificationCode').value,
                        nickname: document.getElementById('phoneNickname').value,
                        gdprConsent: document.getElementById('gdprConsent').checked
                    };
                }
            }
            
            validateVerificationCode(formData) {
                const type = formData.type;
                const inputCode = formData.verificationCode;
                const storedData = this.verificationCodes[type];
                
                if (!storedData.code) {
                    return false;
                }
                
                if (Date.now() > storedData.expiry) {
                    return false;
                }
                
                return inputCode === storedData.code;
            }
            
            async simulateRegister(formData) {
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 模拟注册逻辑
                console.log('注册数据:', formData);
                
                // 模拟可能的错误
                if (Math.random() < 0.1) {
                    throw new Error('服务器暂时不可用，请稍后重试');
                }
            }
            
            showLoading(show) {
                document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
            }
            
            showSuccess(message) {
                const element = document.getElementById('successMessage');
                element.textContent = message;
                element.style.display = 'block';
                
                setTimeout(() => {
                    element.style.display = 'none';
                }, 5000);
            }
            
            showError(message) {
                const element = document.getElementById('errorMessage');
                element.textContent = message;
                element.style.display = 'block';
                
                setTimeout(() => {
                    element.style.display = 'none';
                }, 5000);
            }
            
            showFieldError(errorElement, message) {
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                }
            }
            
            hideFieldError(errorElement) {
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            }
        }
        
        // 隐私政策和服务条款
        function showPrivacyPolicy() {
            showToast('隐私政策功能即将上线，敬请期待！', 'info');
        }
        
        function showTermsOfService() {
            showToast('服务条款功能即将上线，敬请期待！', 'info');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new RegisterController();
            console.log('✅ 注册页面初始化完成');
        });
    </script>
            </div>
        </main>
    </div>
</body>
</html>

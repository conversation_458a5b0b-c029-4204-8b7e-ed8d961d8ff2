<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">合同管理</h1>
                            <p class="breadcrumb-description">管理智能家居项目合同和协议</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <div class="contract-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalContracts">0</span>
                            <span class="stat-label">合同总数</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="signedContracts">0</span>
                            <span class="stat-label">已签署</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="pendingContracts">0</span>
                            <span class="stat-label">待签署</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalValue">¥0</span>
                            <span class="stat-label">合同总额</span>
                        </div>
                    </div>
                </div>

                <div class="contract-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddContractModal()">
                            <i class="fas fa-plus"></i> 新增合同
                        </button>
                        <button class="btn btn-secondary" onclick="uploadContract()">
                            <i class="fas fa-upload"></i> 上传合同
                        </button>
                        <button class="btn btn-secondary" onclick="exportContracts()">
                            <i class="fas fa-download"></i> 导出合同
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索合同..." onkeyup="searchContracts()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="typeFilter" onchange="filterContracts()">
                            <option value="">所有类型</option>
                            <option value="design">设计合同</option>
                            <option value="construction">施工合同</option>
                            <option value="service">服务合同</option>
                            <option value="purchase">采购合同</option>
                        </select>
                        <select id="statusFilter" onchange="filterContracts()">
                            <option value="">所有状态</option>
                            <option value="draft">草稿</option>
                            <option value="pending">待签署</option>
                            <option value="signed">已签署</option>
                            <option value="executing">执行中</option>
                            <option value="completed">已完成</option>
                            <option value="terminated">已终止</option>
                        </select>
                    </div>
                </div>

                <div class="contract-table-container">
                    <table class="contract-table" id="contractTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortTable('contractNo')">合同编号 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('name')">合同名称 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('client')">客户 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('type')">类型 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('value')">金额 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('status')">状态 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('signDate')">签署日期 <i class="fas fa-sort"></i></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="contractTableBody">
                            <!-- 合同数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div class="page-numbers" id="pageNumbers"></div>
                        <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
        class ContractManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.contracts = [];
                this.filteredContracts = [];
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.init();
            }

            async init() {
                await this.loadContracts();
                this.updateStats();
                this.renderTable();
                this.renderPagination();
                this.bindEvents();
            }

            async loadContracts() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/contracts?page=${this.currentPage}&size=${this.pageSize}`, {
                        headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                    });
                    if (response.ok) {
                        const result = await response.json();
                        this.contracts = result.data || [];
                        this.totalCount = result.total || 0;
                        this.filteredContracts = [...this.contracts];
                    } else {
                        this.loadMockData();
                    }
                } catch (error) {
                    this.loadMockData();
                }
            }

            loadMockData() {
                this.contracts = [
                    { id: 1, contractNo: 'CT20250117001', name: '张先生家智能化设计合同', client: '张先生', type: 'design', value: 15000.00, status: 'signed', signDate: '2025-01-15', endDate: '2025-03-15', description: '全屋智能化设计服务合同' },
                    { id: 2, contractNo: 'CT20250117002', name: '李女士别墅施工合同', client: '李女士', type: 'construction', value: 85000.00, status: 'executing', signDate: '2025-01-10', endDate: '2025-04-10', description: '别墅智能化施工合同' },
                    { id: 3, contractNo: 'CT20250116001', name: '王总办公室改造合同', client: '王总', type: 'service', value: 25000.00, status: 'pending', signDate: null, endDate: '2025-02-28', description: '办公室智能化改造服务合同' },
                    { id: 4, contractNo: 'CT20250115001', name: '智能设备采购合同', client: '赵先生', type: 'purchase', value: 45000.00, status: 'completed', signDate: '2025-01-05', endDate: '2025-01-20', description: '智能家居设备采购合同' },
                    { id: 5, contractNo: 'CT20250114001', name: '孙女士家设计合同', client: '孙女士', type: 'design', value: 12000.00, status: 'draft', signDate: null, endDate: '2025-03-01', description: '公寓智能化设计合同' }
                ];
                this.totalCount = this.contracts.length;
                this.filteredContracts = [...this.contracts];
            }

            updateStats() {
                const totalContracts = this.contracts.length;
                const signedContracts = this.contracts.filter(c => ['signed', 'executing', 'completed'].includes(c.status)).length;
                const pendingContracts = this.contracts.filter(c => c.status === 'pending').length;
                const totalValue = this.contracts.filter(c => c.status !== 'terminated').reduce((sum, c) => sum + c.value, 0);

                document.getElementById('totalContracts').textContent = totalContracts;
                document.getElementById('signedContracts').textContent = signedContracts;
                document.getElementById('pendingContracts').textContent = pendingContracts;
                document.getElementById('totalValue').textContent = `¥${totalValue.toFixed(2)}`;
            }

            renderTable() {
                const tbody = document.getElementById('contractTableBody');
                tbody.innerHTML = '';

                if (this.filteredContracts.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: var(--text-secondary);">暂无合同数据</td></tr>';
                    return;
                }

                this.filteredContracts.forEach(contract => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><input type="checkbox" class="contract-checkbox" value="${contract.id}"></td>
                        <td><a href="#" onclick="contractManager.viewContract(${contract.id})" style="color: var(--accent-blue); text-decoration: none;">${contract.contractNo}</a></td>
                        <td>${contract.name}</td>
                        <td>${contract.client}</td>
                        <td><span class="type-badge">${this.getTypeText(contract.type)}</span></td>
                        <td>¥${contract.value.toFixed(2)}</td>
                        <td><span class="status-badge status-${contract.status}">${this.getStatusText(contract.status)}</span></td>
                        <td>${contract.signDate || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-secondary" onclick="contractManager.editContract(${contract.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="contractManager.viewContract(${contract.id})" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="contractManager.downloadContract(${contract.id})" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="contractManager.signContract(${contract.id})" title="签署">
                                <i class="fas fa-signature"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            renderPagination() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageStart').textContent = start;
                document.getElementById('pageEnd').textContent = end;
                document.getElementById('totalCount').textContent = this.totalCount;

                document.getElementById('prevBtn').disabled = this.currentPage <= 1;
                document.getElementById('nextBtn').disabled = this.currentPage >= totalPages;

                const pageNumbers = document.getElementById('pageNumbers');
                pageNumbers.innerHTML = '';

                for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                    const pageBtn = document.createElement('div');
                    pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    pageNumbers.appendChild(pageBtn);
                }
            }

            bindEvents() {
                document.getElementById('searchInput').addEventListener('input', () => this.searchContracts());
                document.getElementById('typeFilter').addEventListener('change', () => this.filterContracts());
                document.getElementById('statusFilter').addEventListener('change', () => this.filterContracts());
            }

            searchContracts() {
                const keyword = document.getElementById('searchInput').value.toLowerCase();
                this.filteredContracts = this.contracts.filter(contract => 
                    contract.contractNo.toLowerCase().includes(keyword) ||
                    contract.name.toLowerCase().includes(keyword) ||
                    contract.client.toLowerCase().includes(keyword)
                );
                this.renderTable();
            }

            filterContracts() {
                const typeFilter = document.getElementById('typeFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;

                this.filteredContracts = this.contracts.filter(contract => {
                    const typeMatch = !typeFilter || contract.type === typeFilter;
                    const statusMatch = !statusFilter || contract.status === statusFilter;
                    return typeMatch && statusMatch;
                });
                this.renderTable();
            }

            getTypeText(type) {
                const typeMap = { 'design': '设计合同', 'construction': '施工合同', 'service': '服务合同', 'purchase': '采购合同' };
                return typeMap[type] || type;
            }

            getStatusText(status) {
                const statusMap = {
                    'draft': '草稿',
                    'pending': '待签署',
                    'signed': '已签署',
                    'executing': '执行中',
                    'completed': '已完成',
                    'terminated': '已终止'
                };
                return statusMap[status] || status;
            }

            editContract(id) {
                const contract = this.contracts.find(c => c.id === id);
                if (!contract) return;
                showToast(`编辑合同：${contract.name} - 功能即将上线！`, 'info');
            }

            viewContract(id) {
                const contract = this.contracts.find(c => c.id === id);
                if (!contract) return;
                showToast(`合同详情 - ${contract.name}\n编号：${contract.contractNo}\n客户：${contract.client}\n类型：${this.getTypeText(contract.type)}\n金额：¥${contract.value.toFixed(2)}\n状态：${this.getStatusText(contract.status)}`, 'info');
            }

            downloadContract(id) {
                const contract = this.contracts.find(c => c.id === id);
                if (!contract) return;
                showToast(`下载合同：${contract.contractNo} - 功能即将上线！`, 'info');
            }

            signContract(id) {
                const contract = this.contracts.find(c => c.id === id);
                if (!contract) return;
                
                if (contract.status === 'pending' || contract.status === 'draft') {
                    if (confirm(`确定要签署合同 ${contract.contractNo} 吗？`)) {
                        contract.status = 'signed';
                        contract.signDate = new Date().toISOString().split('T')[0];
                        this.updateStats();
                        this.renderTable();
                        showToast('合同签署成功！', 'success');
                    }
                } else {
                    showToast('该合同已签署或状态不允许签署', 'warning');
                }
            }

            goToPage(page) {
                this.currentPage = page;
                this.loadContracts();
            }

            previousPage() {
                if (this.currentPage > 1) this.goToPage(this.currentPage - 1);
            }

            nextPage() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                if (this.currentPage < totalPages) this.goToPage(this.currentPage + 1);
            }

            toggleSelectAll() {
                const selectAll = document.getElementById('selectAll');
                const checkboxes = document.querySelectorAll('.contract-checkbox');
                checkboxes.forEach(cb => cb.checked = selectAll.checked);
            }

            showAddContractModal() { showToast('新增合同功能即将上线，敬请期待！', 'info'); }
            uploadContract() { showToast('上传合同功能即将上线，敬请期待！', 'info'); }
            exportContracts() { showToast('导出合同功能即将上线，敬请期待！', 'info'); }
        }

        let contractManager;
        function showAddContractModal() { contractManager.showAddContractModal(); }
        function searchContracts() { contractManager.searchContracts(); }
        function filterContracts() { contractManager.filterContracts(); }
        function sortTable(field) { contractManager.sortTable(field); }
        function previousPage() { contractManager.previousPage(); }
        function nextPage() { contractManager.nextPage(); }
        function toggleSelectAll() { contractManager.toggleSelectAll(); }
        function uploadContract() { contractManager.uploadContract(); }
        function exportContracts() { contractManager.exportContracts(); }

        document.addEventListener('DOMContentLoaded', function() {
            contractManager = new ContractManager();
        });
    </script>
</body>
</html>

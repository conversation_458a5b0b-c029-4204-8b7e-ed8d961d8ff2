import React from 'react';
import NavBar from './NavBar';

const App: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <NavBar />
      <main className="container mx-auto py-8">
        <h1 className="text-3xl font-bold text-center text-blue-600 mb-6">
          欢迎使用智能设计与施工系统
        </h1>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <p className="text-gray-700">
            Tailwind CSS 已成功配置！这是一个基本组件示例。
          </p>
          <button className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
            示例按钮
          </button>
        </div>
      </main>
    </div>
  );
};

export default App;

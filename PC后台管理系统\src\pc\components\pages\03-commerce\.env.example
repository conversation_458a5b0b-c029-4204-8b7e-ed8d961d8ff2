# 服务器配置
PORT=3001
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=smart_home_products
DB_PORT=3306

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=2h
JWT_REFRESH_EXPIRES_IN=30d

# 前端URL配置
FRONTEND_URL=http://localhost:3000

# MedusaJS配置
MEDUSA_API_URL=http://localhost:9000
MEDUSA_API_KEY=your-medusa-api-key

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Redis配置（可选，用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 邮件配置（可选，用于通知）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 第三方服务配置
ALIYUN_OSS_ACCESS_KEY_ID=
ALIYUN_OSS_ACCESS_KEY_SECRET=
ALIYUN_OSS_BUCKET=
ALIYUN_OSS_REGION=

# 监控配置
SENTRY_DSN=
PROMETHEUS_PORT=9090

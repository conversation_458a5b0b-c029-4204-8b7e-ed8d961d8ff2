/**
 * 批量创建缺失的页面文件
 * 使用标准模板快速生成所有缺失的页面
 */

const fs = require('fs');
const path = require('path');

// 页面配置
const pages = [
    // 知识库模块
    { filename: 'delivery-knowledge.html', title: '交付知识库', subtitle: '项目交付相关的知识文档和流程指南', activeMenu: 'delivery-knowledge.html' },
    { filename: 'market-knowledge.html', title: '市转知识库', subtitle: '市场转化相关的知识文档和策略指南', activeMenu: 'market-knowledge.html' },
    { filename: 'installation-knowledge.html', title: '安装知识库', subtitle: '设备安装相关的技术文档和操作指南', activeMenu: 'installation-knowledge.html' },
    { filename: 'design-knowledge-guide.html', title: '设计指导库', subtitle: '设计指导相关的规范文档和最佳实践', activeMenu: 'design-knowledge-guide.html' },
    { filename: 'product-knowledge.html', title: '产品知识库', subtitle: '产品相关的技术文档和使用指南', activeMenu: 'product-knowledge.html' },
    
    // 数据分析模块
    { filename: 'project-analytics.html', title: '项目分析', subtitle: '项目数据分析和统计报告', activeMenu: 'project-analytics.html' },
    { filename: 'order-analytics.html', title: '订单分析', subtitle: '订单数据分析和趋势统计', activeMenu: 'order-analytics.html' },
    { filename: 'customer-analytics.html', title: '客户分析', subtitle: '客户数据分析和行为统计', activeMenu: 'customer-analytics.html' },
    
    // 系统工具模块
    { filename: 'data-management.html', title: '数据管理', subtitle: '系统数据的管理和维护工具', activeMenu: 'data-management.html' }
];

// 标准页面模板
function generatePageTemplate(config) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.title} - 智能家居管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.95) 0%, 
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(229, 231, 235, 0.8);
            flex-shrink: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        .sidebar-header {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.6);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 12px 0;
        }

        .nav-section {
            margin-bottom: 16px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 4px 16px;
            margin: 4px 8px 2px;
            background: rgba(107, 114, 128, 0.08);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 3px;
            border-left: 2px solid rgba(107, 114, 128, 0.3);
            line-height: 1.2;
        }

        .nav-item {
            display: block;
            padding: 6px 16px;
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 1px 8px;
            position: relative;
            line-height: 1.3;
        }

        .nav-item:hover {
            background: rgba(243, 244, 246, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            color: #1f2937;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            color: #1f2937;
            font-weight: 600;
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateX(6px);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 40px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        .content-container {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 32px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #000;
            color: white;
        }

        .btn-primary:hover {
            background: #374151;
        }

        .placeholder-content {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .placeholder-text {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .placeholder-desc {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">⌂</div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="my-todos.html" class="nav-item">我的代办</a>
                    <a href="my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="design-products.html" class="nav-item">设计商品</a>
                    <a href="requirements-management.html" class="nav-item">需求管理</a>
                    <a href="design-center.html" class="nav-item">设计中心</a>
                    <a href="design-cases.html" class="nav-item">设计案例</a>
                    <a href="project-center.html" class="nav-item">项目中心</a>
                    <a href="construction-management.html" class="nav-item">施工管理</a>
                    <a href="construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products.html" class="nav-item">商品管理</a>
                    <a href="orders.html" class="nav-item">订单管理</a>
                    <a href="customer-management.html" class="nav-item">客户管理</a>
                    <a href="marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="design-knowledge.html" class="nav-item${config.activeMenu === 'design-knowledge.html' ? ' active' : ''}">设计知识库</a>
                    <a href="delivery-knowledge.html" class="nav-item${config.activeMenu === 'delivery-knowledge.html' ? ' active' : ''}">交付知识库</a>
                    <a href="market-knowledge.html" class="nav-item${config.activeMenu === 'market-knowledge.html' ? ' active' : ''}">市转知识库</a>
                    <a href="installation-knowledge.html" class="nav-item${config.activeMenu === 'installation-knowledge.html' ? ' active' : ''}">安装知识库</a>
                    <a href="design-knowledge-guide.html" class="nav-item${config.activeMenu === 'design-knowledge-guide.html' ? ' active' : ''}">设计指导库</a>
                    <a href="product-knowledge.html" class="nav-item${config.activeMenu === 'product-knowledge.html' ? ' active' : ''}">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="api-tools.html" class="nav-item">API 工具</a>
                    <a href="erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="system-settings.html" class="nav-item">系统配置</a>
                    <a href="user-management.html" class="nav-item">用户管理</a>
                    <a href="internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="data-management.html" class="nav-item${config.activeMenu === 'data-management.html' ? ' active' : ''}">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="project-analytics.html" class="nav-item${config.activeMenu === 'project-analytics.html' ? ' active' : ''}">项目分析</a>
                    <a href="order-analytics.html" class="nav-item${config.activeMenu === 'order-analytics.html' ? ' active' : ''}">订单分析</a>
                    <a href="customer-analytics.html" class="nav-item${config.activeMenu === 'customer-analytics.html' ? ' active' : ''}">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="demo.html" class="nav-item">演示展示</a>
                    <a href="user-profile.html" class="nav-item">个人资料</a>
                    <a href="logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">${config.title}</h1>
                <p class="page-subtitle">${config.subtitle}</p>
            </div>

            <div class="content-container">
                <div class="content-header">
                    <h2 class="content-title">${config.title}</h2>
                    <button class="btn btn-primary">+ 新增内容</button>
                </div>

                <div class="placeholder-content">
                    <div class="placeholder-icon">📋</div>
                    <div class="placeholder-text">${config.title}功能开发中</div>
                    <div class="placeholder-desc">该页面正在开发中，敬请期待</div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>`;
}

// 创建页面目录
const pagesDir = path.join(__dirname, '../pages');

// 批量创建页面
function createPages() {
    console.log('🚀 开始批量创建缺失的页面...\n');
    
    let successCount = 0;
    let failCount = 0;
    
    for (const pageConfig of pages) {
        try {
            const filePath = path.join(pagesDir, pageConfig.filename);
            const content = generatePageTemplate(pageConfig);
            
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(\`✅ 已创建: \${pageConfig.filename}\`);
            successCount++;
        } catch (error) {
            console.error(\`❌ 创建失败: \${pageConfig.filename} - \${error.message}\`);
            failCount++;
        }
    }
    
    console.log(\`\n📊 创建统计:\`);
    console.log(\`✅ 成功创建: \${successCount} 个文件\`);
    console.log(\`❌ 创建失败: \${failCount} 个文件\`);
    console.log(\`📁 总计文件: \${pages.length} 个文件\`);
    
    if (successCount > 0) {
        console.log(\`\n🎉 页面批量创建完成！\`);
        console.log(\`💡 所有页面都使用了统一的侧边栏和样式\`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    createPages();
}

module.exports = {
    createPages,
    generatePageTemplate,
    pages
};

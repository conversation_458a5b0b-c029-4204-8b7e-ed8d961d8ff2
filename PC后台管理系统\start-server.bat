@echo off
echo 🚀 正在启动本地服务器...
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查Node.js...
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 可用，启动Node.js服务器...
    echo 📍 访问地址: http://localhost:3000
    echo 🎨 设计管理页面: http://localhost:3000/src/pc/components/pages/design-management.html
    echo.
    node server.js
    goto :end
)

echo 🔍 检查Python...
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python 可用，启动Python服务器...
    echo 📍 访问地址: http://localhost:3000
    echo 🎨 设计管理页面: http://localhost:3000/src/pc/components/pages/design-management.html
    echo.
    python server.py
    goto :end
)

echo ❌ 未找到Node.js或Python，请安装其中一个
echo.
echo 💡 建议安装Node.js: https://nodejs.org/
echo 💡 或者安装Python: https://python.org/
echo.

:end
pause

/**
 * 页面链接验证工具
 * 验证侧边栏中的所有链接是否指向实际存在的页面文件
 */

const fs = require('fs');
const path = require('path');

class PageLinkValidator {
    constructor() {
        this.pagesDir = 'src/pc/components/pages';
        this.sidebarFile = 'src/pc/components/shared/standard-sidebar.html';
        this.errors = [];
        this.warnings = [];
        this.validLinks = [];
    }

    /**
     * 验证所有页面链接
     */
    async validateAllLinks() {
        console.log('🔍 开始验证页面链接...');
        
        try {
            // 1. 读取侧边栏文件
            const sidebarContent = this.readSidebarFile();
            
            // 2. 提取所有链接
            const links = this.extractLinks(sidebarContent);
            
            // 3. 验证每个链接
            this.validateLinks(links);
            
            // 4. 检查遗漏的页面
            this.checkMissingPages();
            
            // 5. 生成验证报告
            this.generateReport();
            
        } catch (error) {
            console.error('❌ 验证失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 读取侧边栏文件
     */
    readSidebarFile() {
        if (!fs.existsSync(this.sidebarFile)) {
            throw new Error(`侧边栏文件不存在: ${this.sidebarFile}`);
        }
        
        return fs.readFileSync(this.sidebarFile, 'utf8');
    }

    /**
     * 提取HTML中的所有链接
     */
    extractLinks(content) {
        const linkRegex = /href="([^"]*\.html)"/g;
        const links = [];
        let match;
        
        while ((match = linkRegex.exec(content)) !== null) {
            const href = match[1];
            // 跳过外部链接和锚点链接
            if (!href.startsWith('http') && !href.startsWith('#')) {
                links.push(href);
            }
        }
        
        console.log(`📋 找到 ${links.length} 个页面链接`);
        return [...new Set(links)]; // 去重
    }

    /**
     * 验证链接是否有效
     */
    validateLinks(links) {
        console.log('🔍 验证链接有效性...');
        
        links.forEach(link => {
            // 处理相对路径
            let filePath;
            if (link.startsWith('../pages/')) {
                // 从shared目录指向pages目录的链接
                filePath = path.join(this.pagesDir, link.replace('../pages/', ''));
            } else if (link.includes('/')) {
                // 其他相对路径
                filePath = link;
            } else {
                // 直接文件名
                filePath = path.join(this.pagesDir, link);
            }
            
            if (fs.existsSync(filePath)) {
                this.validLinks.push({
                    link: link,
                    path: filePath,
                    status: 'valid'
                });
                console.log(`✅ ${link} → ${filePath}`);
            } else {
                this.errors.push({
                    link: link,
                    path: filePath,
                    error: '文件不存在'
                });
                console.log(`❌ ${link} → ${filePath} (文件不存在)`);
            }
        });
    }

    /**
     * 检查是否有页面文件没有在侧边栏中
     */
    checkMissingPages() {
        console.log('🔍 检查遗漏的页面...');
        
        if (!fs.existsSync(this.pagesDir)) {
            console.warn(`⚠️ 页面目录不存在: ${this.pagesDir}`);
            return;
        }
        
        const allPages = fs.readdirSync(this.pagesDir)
            .filter(file => file.endsWith('.html'))
            .filter(file => file !== 'README.md');
        
        const linkedPages = this.validLinks.map(item => 
            path.basename(item.path)
        );
        
        const missingPages = allPages.filter(page => 
            !linkedPages.includes(page)
        );
        
        if (missingPages.length > 0) {
            console.log(`⚠️ 发现 ${missingPages.length} 个页面未在侧边栏中:`);
            missingPages.forEach(page => {
                this.warnings.push({
                    page: page,
                    warning: '页面存在但未在侧边栏中链接'
                });
                console.log(`   📄 ${page}`);
            });
        } else {
            console.log('✅ 所有页面都已在侧边栏中链接');
        }
    }

    /**
     * 生成验证报告
     */
    generateReport() {
        console.log('\n📊 生成验证报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalLinks: this.validLinks.length + this.errors.length,
                validLinks: this.validLinks.length,
                brokenLinks: this.errors.length,
                missingPages: this.warnings.length
            },
            validLinks: this.validLinks,
            errors: this.errors,
            warnings: this.warnings
        };
        
        // 保存报告到文件
        const reportPath = 'docs/page-links-validation-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 输出到控制台
        this.printReport(report);
        
        console.log(`📄 详细报告已保存到: ${reportPath}`);
        
        return report;
    }

    /**
     * 打印报告到控制台
     */
    printReport(report) {
        console.log('\n' + '='.repeat(60));
        console.log('📋 页面链接验证报告');
        console.log('='.repeat(60));
        console.log(`验证时间: ${report.timestamp}`);
        console.log(`总链接数: ${report.summary.totalLinks}`);
        console.log(`有效链接: ${report.summary.validLinks}`);
        console.log(`无效链接: ${report.summary.brokenLinks}`);
        console.log(`遗漏页面: ${report.summary.missingPages}`);
        console.log('');

        if (report.errors.length > 0) {
            console.log('❌ 无效链接列表:');
            report.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.link} → ${error.error}`);
            });
            console.log('');
        }

        if (report.warnings.length > 0) {
            console.log('⚠️ 遗漏页面列表:');
            report.warnings.forEach((warning, index) => {
                console.log(`${index + 1}. ${warning.page} → ${warning.warning}`);
            });
            console.log('');
        }

        if (report.errors.length === 0 && report.warnings.length === 0) {
            console.log('🎉 所有链接验证通过！');
        } else if (report.errors.length === 0) {
            console.log('✅ 所有链接有效，但有页面未链接');
        } else {
            console.log('⚠️ 发现无效链接，需要修复');
        }
    }

    /**
     * 自动修复链接
     */
    async autoFixLinks() {
        console.log('🔧 开始自动修复链接...');
        
        if (this.errors.length === 0) {
            console.log('✅ 没有需要修复的链接');
            return;
        }
        
        let sidebarContent = this.readSidebarFile();
        let fixedCount = 0;
        
        this.errors.forEach(error => {
            const fileName = path.basename(error.path);
            const correctPath = `../pages/${fileName}`;
            
            // 检查正确路径的文件是否存在
            const actualPath = path.join(this.pagesDir, fileName);
            if (fs.existsSync(actualPath)) {
                // 替换错误的链接
                const oldHref = `href="${error.link}"`;
                const newHref = `href="${correctPath}"`;
                
                if (sidebarContent.includes(oldHref)) {
                    sidebarContent = sidebarContent.replace(oldHref, newHref);
                    fixedCount++;
                    console.log(`🔧 修复: ${error.link} → ${correctPath}`);
                }
            }
        });
        
        if (fixedCount > 0) {
            // 备份原文件
            const backupPath = this.sidebarFile + '.backup';
            fs.writeFileSync(backupPath, fs.readFileSync(this.sidebarFile));
            
            // 写入修复后的内容
            fs.writeFileSync(this.sidebarFile, sidebarContent);
            
            console.log(`✅ 已修复 ${fixedCount} 个链接`);
            console.log(`📄 原文件备份到: ${backupPath}`);
        } else {
            console.log('⚠️ 没有可以自动修复的链接');
        }
    }

    /**
     * 生成完整的侧边栏HTML
     */
    generateCompleteNavigation() {
        console.log('🏗️ 生成完整的导航结构...');
        
        const allPages = fs.readdirSync(this.pagesDir)
            .filter(file => file.endsWith('.html'))
            .sort();
        
        // 按功能分组
        const pageGroups = {
            '系统概览': ['admin-dashboard.html', 'index.html'],
            '业务管理': [
                'design-management.html',
                'design-requirements.html', 
                'design-requirements-table.html',
                'design-effects.html',
                'design-progress.html',
                'design-tasks.html',
                'projects.html',
                'construction-management.html',
                'construction-enhanced-demo.html'
            ],
            '商务管理': [
                'products.html',
                'product-materials.html',
                'aqara-product-import.html',
                'orders.html',
                'customer-management.html',
                'marketing-management.html'
            ],
            '内容管理': [
                'knowledge-management.html',
                'contract-management.html'
            ],
            '需求分析': [
                'requirements-analysis.html',
                'requirements-management.html'
            ],
            '系统工具': [
                'analytics.html',
                'api-tester.html',
                '一装ERP-API文档.html',
                'settings.html',
                'user-management.html',
                'user-permissions.html',
                'permissions.html'
            ],
            '个人中心': [
                'user-profile.html',
                'demo.html',
                'register.html',
                'login.html'
            ],
            '开发测试': [
                'design-management-new.html',
                'design-requirements-new.html',
                'design-requirements-fixed.html',
                'design-requirements-test.html'
            ]
        };
        
        // 生成导航HTML
        let navHtml = '<nav class="nav-menu">\n';
        
        Object.entries(pageGroups).forEach(([groupName, pages]) => {
            navHtml += `    <div class="nav-section">\n`;
            navHtml += `        <div class="nav-section-title">${groupName}</div>\n`;
            
            pages.forEach(page => {
                if (fs.existsSync(path.join(this.pagesDir, page))) {
                    const displayName = this.getDisplayName(page);
                    navHtml += `        <a href="../pages/${page}" class="nav-item">${displayName}</a>\n`;
                }
            });
            
            navHtml += `    </div>\n`;
        });
        
        navHtml += '</nav>';
        
        console.log('✅ 导航结构生成完成');
        return navHtml;
    }

    /**
     * 获取页面显示名称
     */
    getDisplayName(fileName) {
        const nameMap = {
            'admin-dashboard.html': '数据概览',
            'index.html': '系统首页',
            'design-management.html': '设计管理',
            'design-requirements.html': '设计需求',
            'design-requirements-table.html': '需求表格',
            'design-effects.html': '设计效果',
            'design-progress.html': '设计进度',
            'design-tasks.html': '设计任务',
            'projects.html': '项目管理',
            'construction-management.html': '施工管理',
            'construction-enhanced-demo.html': '施工演示',
            'products.html': '商品管理',
            'product-materials.html': '产品物料',
            'aqara-product-import.html': 'Aqara导入',
            'orders.html': '订单管理',
            'customer-management.html': '客户管理',
            'marketing-management.html': '营销管理',
            'knowledge-management.html': '知识库管理',
            'contract-management.html': '合同管理',
            'requirements-analysis.html': '需求分析',
            'requirements-management.html': '需求管理',
            'analytics.html': '数据分析',
            'api-tester.html': 'API 工具',
            '一装ERP-API文档.html': 'ERP文档',
            'settings.html': '系统配置',
            'user-management.html': '用户管理',
            'user-permissions.html': '用户权限',
            'permissions.html': '权限管理',
            'user-profile.html': '个人资料',
            'demo.html': '演示展示',
            'register.html': '用户注册',
            'login.html': '退出登录',
            'design-management-new.html': '设计管理v2',
            'design-requirements-new.html': '设计需求v2',
            'design-requirements-fixed.html': '需求修复版',
            'design-requirements-test.html': '需求测试版'
        };
        
        return nameMap[fileName] || fileName.replace('.html', '');
    }
}

// 命令行使用
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const validator = new PageLinkValidator();
    
    if (command === 'fix') {
        validator.validateAllLinks().then(() => {
            validator.autoFixLinks();
        });
    } else if (command === 'generate') {
        const navHtml = validator.generateCompleteNavigation();
        console.log('\n生成的导航HTML:');
        console.log(navHtml);
    } else {
        validator.validateAllLinks();
    }
}

module.exports = PageLinkValidator;

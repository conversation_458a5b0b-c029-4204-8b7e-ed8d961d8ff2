#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能家居系统测试服务器
用于测试注册功能和其他前端页面
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头部，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def start_server(port=8080):
    """启动测试服务器"""
    
    # 切换到项目根目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print("=" * 60)
            print("🚀 智能家居系统测试服务器启动成功！")
            print("=" * 60)
            print(f"📍 服务器地址: http://localhost:{port}")
            print(f"📁 服务目录: {os.getcwd()}")
            print("")
            print("🔗 快速访问链接:")
            print(f"   📝 注册页面: http://localhost:{port}/src/pc/components/pages/register.html")
            print(f"   🔐 登录页面: http://localhost:{port}/src/pc/components/pages/login.html")
            print(f"   🧪 注册功能测试: http://localhost:{port}/test/register-function-test.html")
            print(f"   🔧 联调测试: http://localhost:{port}/test/auth-integration-test.html")
            print(f"   👥 用户管理: http://localhost:{port}/src/pc/components/pages/user-management.html")
            print(f"   🏠 系统首页: http://localhost:{port}/index.html")
            print("")
            print("💡 使用说明:")
            print("   - 按 Ctrl+C 停止服务器")
            print("   - 浏览器会自动打开测试页面")
            print("   - 支持热重载，修改文件后刷新页面即可")
            print("=" * 60)
            
            # 延迟打开浏览器
            def open_browser():
                time.sleep(2)
                test_url = f"http://localhost:{port}/test/register-function-test.html"
                print(f"🌐 正在打开浏览器: {test_url}")
                webbrowser.open(test_url)
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    # 检查命令行参数
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    start_server(port)

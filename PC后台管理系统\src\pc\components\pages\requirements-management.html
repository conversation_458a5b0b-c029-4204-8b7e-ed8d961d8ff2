<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理 - 智能家居后台管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f5f5f5;
            line-height: 1.6;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            font-size: 1.5rem;
            font-weight: 500;
        }
        .breadcrumb {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #eee;
            font-size: 0.9rem;
            color: #666;
        }
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        .toolbar {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .requirements-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: #3498db;
            color: white;
            padding: 1rem 2rem;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .table-content {
            padding: 1.5rem;
        }
        .requirement-item {
            border: 1px solid #eee;
            border-radius: 6px;
            margin-bottom: 1rem;
            padding: 1.5rem;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .requirement-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: white;
        }
        .req-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        .customer-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .req-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .detail-item {
            display: flex;
            align-items: center;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
            margin-right: 0.5rem;
            min-width: 60px;
        }
        .detail-value {
            color: #333;
        }
        .requirements-text {
            background: white;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            margin-top: 1rem;
            font-style: italic;
            color: #555;
        }
        .actions {
            margin-top: 1rem;
            text-align: right;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
            padding: 1rem;
        }
        .page-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #666;
        }
        .empty-state .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
        }
        .modal-close {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        .modal-close:hover {
            color: #000;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            min-height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 需求管理中心</h1>
    </div>
    
    <div class="breadcrumb">
        首页 > 客户管理 > 需求管理
    </div>
    
    <div class="main-content">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="filters">
                <select class="filter-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="待处理">待处理</option>
                    <option value="处理中">处理中</option>
                    <option value="已完成">已完成</option>
                    <option value="已取消">已取消</option>
                </select>
                <select class="filter-select" id="sourceFilter">
                    <option value="">全部来源</option>
                    <option value="H5移动端">H5移动端</option>
                    <option value="PC官网">PC官网</option>
                    <option value="微信小程序">微信小程序</option>
                </select>
            </div>
            <button class="refresh-btn" onclick="loadRequirements()">
                🔄 刷新数据
            </button>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <div class="stat-number" id="totalRequirements">-</div>
                <div class="stat-label">📋 总需求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingRequirements">-</div>
                <div class="stat-label">⏳ 待处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRequirements">-</div>
                <div class="stat-label">📅 今日新增</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgRooms">-</div>
                <div class="stat-label">🏠 平均房间数</div>
            </div>
        </div>
        
        <!-- 需求列表 -->
        <div class="requirements-table">
            <div class="table-header">
                <span>📱 客户需求列表 (实时数据)</span>
                <span id="dataUpdateTime">数据更新时间: -</span>
            </div>
            <div class="table-content">
                <div class="loading" id="loadingContainer">
                    <div class="loading-spinner"></div>
                    <p>正在加载需求数据...</p>
                </div>
                <div id="requirementsContent"></div>
                <div class="pagination" id="paginationContainer"></div>
            </div>
        </div>
    </div>
    
    <!-- 需求详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeModal()">&times;</span>
            <h2>需求详情</h2>
            <div id="detailContent"></div>
        </div>
    </div>
    
    <!-- 编辑需求模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeEditModal()">&times;</span>
            <h2>编辑需求</h2>
            <form id="editForm">
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="editStatus">
                        <option value="待处理">待处理</option>
                        <option value="处理中">处理中</option>
                        <option value="已完成">已完成</option>
                        <option value="已取消">已取消</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">负责人</label>
                    <input type="text" class="form-input" id="editAssignedTo" placeholder="请输入负责人">
                </div>
                <div class="form-group">
                    <label class="form-label">预估预算</label>
                    <input type="number" class="form-input" id="editBudget" placeholder="请输入预估预算">
                </div>
                <div class="form-group">
                    <label class="form-label">优先级</label>
                    <select class="form-select" id="editPriority">
                        <option value="低">低</option>
                        <option value="普通">普通</option>
                        <option value="高">高</option>
                        <option value="紧急">紧急</option>
                    </select>
                </div>
                <div class="actions">
                    <button type="button" class="btn btn-primary" onclick="saveRequirement()">保存</button>
                    <button type="button" class="btn" onclick="closeEditModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // API配置
        const API_BASE_URL = 'http://localhost:8003';
        
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentEditId = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PC端需求管理页面加载完成');
            initializePage();
        });
        
        // 初始化页面
        async function initializePage() {
            try {
                // 检查API连接
                await checkAPIConnection();
                
                // 加载统计数据
                await loadStatistics();
                
                // 加载需求列表
                await loadRequirements();
                
                // 设置筛选器事件
                setupFilters();
                
                console.log('页面初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                showError('页面初始化失败，请检查网络连接');
            }
        }
        
        // 检查API连接
        async function checkAPIConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                console.log('API连接正常:', data);
                return true;
            } catch (error) {
                console.error('API连接失败:', error);
                throw new Error('API服务连接失败');
            }
        }
        
        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements/stats/summary`);
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    
                    document.getElementById('totalRequirements').textContent = stats.total_requirements || 0;
                    document.getElementById('todayRequirements').textContent = stats.today_requirements || 0;
                    document.getElementById('avgRooms').textContent = stats.avg_rooms || '0.0';
                    
                    // 计算待处理数量
                    const pendingCount = stats.status_breakdown.find(s => s.status === '待处理')?.count || 0;
                    document.getElementById('pendingRequirements').textContent = pendingCount;
                    
                    console.log('统计数据加载成功:', stats);
                } else {
                    throw new Error(result.message || '获取统计数据失败');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                // 显示默认值
                document.getElementById('totalRequirements').textContent = '0';
                document.getElementById('pendingRequirements').textContent = '0';
                document.getElementById('todayRequirements').textContent = '0';
                document.getElementById('avgRooms').textContent = '0.0';
            }
        }
        
        // 加载需求列表
        async function loadRequirements(page = 1) {
            const loadingContainer = document.getElementById('loadingContainer');
            const requirementsContent = document.getElementById('requirementsContent');
            
            try {
                // 显示加载状态
                loadingContainer.style.display = 'block';
                requirementsContent.innerHTML = '';
                
                // 获取筛选条件
                const status = document.getElementById('statusFilter').value;
                const source = document.getElementById('sourceFilter').value;
                
                // 构建查询参数
                const params = new URLSearchParams({
                    page: page,
                    limit: 10
                });
                
                if (status) params.append('status', status);
                if (source) params.append('source', source);
                
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    const requirements = result.data;
                    const pagination = result.pagination;
                    
                    // 隐藏加载状态
                    loadingContainer.style.display = 'none';
                    
                    if (requirements.length === 0) {
                        requirementsContent.innerHTML = `
                            <div class="empty-state">
                                <div class="icon">📭</div>
                                <h3>暂无需求数据</h3>
                                <p>还没有客户提交需求，等待客户提交...</p>
                            </div>
                        `;
                    } else {
                        // 渲染需求列表
                        renderRequirements(requirements);
                        
                        // 渲染分页
                        renderPagination(pagination);
                        
                        // 更新数据时间
                        document.getElementById('dataUpdateTime').textContent = 
                            `数据更新时间: ${new Date().toLocaleString('zh-CN')}`;
                    }
                    
                    console.log(`加载需求列表成功: ${requirements.length}条数据`);
                } else {
                    throw new Error(result.message || '获取需求列表失败');
                }
            } catch (error) {
                console.error('加载需求列表失败:', error);
                loadingContainer.style.display = 'none';
                requirementsContent.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">❌</div>
                        <h3>加载失败</h3>
                        <p>无法加载需求数据: ${error.message}</p>
                        <button class="btn btn-primary" onclick="loadRequirements()">重试</button>
                    </div>
                `;
            }
        }
        
        // 渲染需求列表
        function renderRequirements(requirements) {
            const container = document.getElementById('requirementsContent');
            let html = '';
            
            requirements.forEach((req, index) => {
                const statusClass = getStatusClass(req.status);
                
                html += `
                    <div class="requirement-item">
                        <div class="req-header">
                            <div class="customer-name">👤 ${req.customer_name}</div>
                            <div class="status-badge ${statusClass}">${req.status}</div>
                        </div>
                        
                        <div class="req-details">
                            <div class="detail-item">
                                <span class="detail-label">📞 电话:</span>
                                <span class="detail-value">${req.customer_phone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🏠 房型:</span>
                                <span class="detail-value">${req.house_rooms}室${req.house_halls}厅${req.house_bathrooms}卫</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📅 提交:</span>
                                <span class="detail-value">${req.created_at}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📱 来源:</span>
                                <span class="detail-value">${req.source || 'H5移动端'}</span>
                            </div>
                            ${req.assigned_to ? `
                            <div class="detail-item">
                                <span class="detail-label">👨‍💼 负责人:</span>
                                <span class="detail-value">${req.assigned_to}</span>
                            </div>
                            ` : ''}
                            ${req.estimated_budget ? `
                            <div class="detail-item">
                                <span class="detail-label">💰 预算:</span>
                                <span class="detail-value">¥${req.estimated_budget}</span>
                            </div>
                            ` : ''}
                        </div>
                        
                        ${req.address ? `
                        <div style="margin: 1rem 0;">
                            <span class="detail-label">📍 地址:</span>
                            <span class="detail-value">${req.address}</span>
                        </div>
                        ` : ''}
                        
                        <div class="requirements-text">
                            💡 客户需求: ${req.requirements}
                        </div>
                        
                        <div class="actions">
                            <button class="btn btn-primary" onclick="viewDetails(${req.id})">查看详情</button>
                            <button class="btn btn-warning" onclick="editRequirement(${req.id})">编辑需求</button>
                            <button class="btn btn-success" onclick="contactCustomer('${req.customer_phone}')">联系客户</button>
                            ${req.status === '待处理' ? `
                                <button class="btn btn-success" onclick="processRequirement(${req.id})">开始处理</button>
                            ` : ''}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '待处理': return 'status-pending';
                case '处理中': return 'status-processing';
                case '已完成': return 'status-completed';
                case '已取消': return 'status-cancelled';
                default: return 'status-pending';
            }
        }
        
        // 渲染分页
        function renderPagination(pagination) {
            const container = document.getElementById('paginationContainer');
            const { page, pages, total } = pagination;
            
            if (pages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let html = '<div class="pagination-info">共 ' + total + ' 条记录，第 ' + page + ' / ' + pages + ' 页</div>';
            
            // 上一页
            if (page > 1) {
                html += `<button class="page-btn" onclick="loadRequirements(${page - 1})">上一页</button>`;
            }
            
            // 页码
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(pages, page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === page ? 'active' : '';
                html += `<button class="page-btn ${activeClass}" onclick="loadRequirements(${i})">${i}</button>`;
            }
            
            // 下一页
            if (page < pages) {
                html += `<button class="page-btn" onclick="loadRequirements(${page + 1})">下一页</button>`;
            }
            
            container.innerHTML = html;
            currentPage = page;
            totalPages = pages;
        }
        
        // 设置筛选器事件
        function setupFilters() {
            document.getElementById('statusFilter').addEventListener('change', () => {
                currentPage = 1;
                loadRequirements(1);
            });
            
            document.getElementById('sourceFilter').addEventListener('change', () => {
                currentPage = 1;
                loadRequirements(1);
            });
        }
        
        // 查看详情
        async function viewDetails(id) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements/${id}`);
                const result = await response.json();
                
                if (result.success) {
                    const req = result.data;
                    const detailContent = document.getElementById('detailContent');
                    
                    detailContent.innerHTML = `
                        <div class="detail-info">
                            <h3>客户信息</h3>
                            <p><strong>姓名:</strong> ${req.customer_name}</p>
                            <p><strong>电话:</strong> ${req.customer_phone}</p>
                            <p><strong>地址:</strong> ${req.address || '未提供'}</p>
                            
                            <h3>房屋信息</h3>
                            <p><strong>房型:</strong> ${req.house_rooms}室${req.house_halls}厅${req.house_bathrooms}卫</p>
                            
                            <h3>需求详情</h3>
                            <p>${req.requirements}</p>
                            
                            <h3>处理信息</h3>
                            <p><strong>状态:</strong> ${req.status}</p>
                            <p><strong>来源:</strong> ${req.source}</p>
                            <p><strong>负责人:</strong> ${req.assigned_to || '未分配'}</p>
                            <p><strong>预估预算:</strong> ${req.estimated_budget ? '¥' + req.estimated_budget : '未设置'}</p>
                            <p><strong>优先级:</strong> ${req.priority || '普通'}</p>
                            <p><strong>提交时间:</strong> ${req.created_at}</p>
                            <p><strong>更新时间:</strong> ${req.updated_at}</p>
                        </div>
                    `;
                    
                    document.getElementById('detailModal').style.display = 'block';
                } else {
                    showToast('获取需求详情失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('获取需求详情失败:', error);
                showToast('获取需求详情失败: ' + error.message, 'error');
            }
        }
        
        // 编辑需求
        async function editRequirement(id) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements/${id}`);
                const result = await response.json();
                
                if (result.success) {
                    const req = result.data;
                    currentEditId = id;
                    
                    // 填充表单
                    document.getElementById('editStatus').value = req.status;
                    document.getElementById('editAssignedTo').value = req.assigned_to || '';
                    document.getElementById('editBudget').value = req.estimated_budget || '';
                    document.getElementById('editPriority').value = req.priority || '普通';
                    
                    document.getElementById('editModal').style.display = 'block';
                } else {
                    showToast('获取需求信息失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('获取需求信息失败:', error);
                showToast('获取需求信息失败: ' + error.message, 'error');
            }
        }
        
        // 保存需求
        async function saveRequirement() {
            if (!currentEditId) return;
            
            try {
                const updateData = {
                    status: document.getElementById('editStatus').value,
                    assigned_to: document.getElementById('editAssignedTo').value,
                    estimated_budget: parseFloat(document.getElementById('editBudget').value) || null,
                    priority: document.getElementById('editPriority').value
                };
                
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements/${currentEditId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showToast('需求更新成功！', 'success');
                    closeEditModal();
                    loadRequirements(currentPage);
                    loadStatistics();
                } else {
                    showToast('更新失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('更新需求失败:', error);
                showToast('更新需求失败: ' + error.message, 'error');
            }
        }
        
        // 开始处理需求
        async function processRequirement(id) {
            if (confirm('确认开始处理此需求吗？')) {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/v1/requirements/${id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            status: '处理中',
                            assigned_to: '系统管理员'
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showToast('已开始处理此需求！', 'success');
                        loadRequirements(currentPage);
                        loadStatistics();
                    } else {
                        showToast('处理失败: ' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('处理需求失败:', error);
                    showToast('处理需求失败: ' + error.message, 'error');
                }
            }
        }
        
        // 联系客户
        function contactCustomer(phone) {
            if (confirm(`确认联系客户 ${phone} 吗？`)) {
                // 这里可以集成拨号功能或跳转到客服系统
                window.open(`tel:${phone}`, '_self');
            }
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('detailModal').style.display = 'none';
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            currentEditId = null;
        }
        
        // 显示错误信息
        function showError(message) {
            showToast('错误: ' + message, 'error');
        }
        
        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const detailModal = document.getElementById('detailModal');
            const editModal = document.getElementById('editModal');
            
            if (event.target === detailModal) {
                detailModal.style.display = 'none';
            }
            if (event.target === editModal) {
                editModal.style.display = 'none';
            }
        });
        
        // 定时刷新数据
        setInterval(() => {
            loadStatistics();
        }, 30000); // 每30秒刷新统计数据
    </script>
</body>
</html>
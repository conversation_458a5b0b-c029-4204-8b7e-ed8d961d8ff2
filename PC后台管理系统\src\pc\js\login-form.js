/**
 * 登录表单处理逻辑
 * 处理表单验证、提交、错误显示等功能
 */

class LoginForm {
  constructor() {
    console.log('🔧 LoginForm 初始化开始')
    this.form = null
    this.submitButton = null
    this.errorContainer = null
    this.loadingOverlay = null

    // 表单字段
    this.usernameField = null
    this.passwordField = null
    this.rememberMeField = null

    // 登录类型
    this.currentLoginType = 'password' // password, email, wechat, google

    this.init()
  }

  /**
   * 初始化登录表单
   */
  init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupForm())
    } else {
      this.setupForm()
    }
  }

  /**
   * 设置表单元素和事件监听
   */
  setupForm() {
    // 获取表单元素
    this.form = document.getElementById('loginForm')
    this.submitButton = document.getElementById('loginButton')
    this.errorContainer = document.getElementById('errorMessage')
    
    // 获取表单字段
    this.usernameField = document.getElementById('username')
    this.passwordField = document.getElementById('password')
    this.rememberMeField = document.getElementById('rememberMe')
    
    if (!this.form) {
      console.error('登录表单未找到')
      return
    }

    // 创建加载遮罩
    this.createLoadingOverlay()
    
    // 绑定事件监听器
    this.bindEvents()
    
    // 初始化表单状态
    this.initFormState()
    
    console.log('登录表单初始化完成')
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 表单提交事件
    this.form.addEventListener('submit', (e) => this.handleSubmit(e))
    
    // 输入字段实时验证
    if (this.usernameField) {
      this.usernameField.addEventListener('input', () => this.validateField('username'))
      this.usernameField.addEventListener('blur', () => this.validateField('username'))
    }
    
    if (this.passwordField) {
      this.passwordField.addEventListener('input', () => this.validateField('password'))
      this.passwordField.addEventListener('blur', () => this.validateField('password'))
    }
    
    // 密码显示/隐藏切换
    const passwordToggle = document.getElementById('passwordToggle')
    if (passwordToggle) {
      passwordToggle.addEventListener('click', () => this.togglePasswordVisibility())
    }
    
    // 登录类型切换
    const loginTypeTabs = document.querySelectorAll('.login-type-tab')
    loginTypeTabs.forEach(tab => {
      tab.addEventListener('click', (e) => this.switchLoginType(e.target.dataset.type))
    })
    
    // 第三方登录按钮
    const wechatLoginBtn = document.getElementById('wechatLoginBtn')
    if (wechatLoginBtn) {
      wechatLoginBtn.addEventListener('click', () => this.handleWechatLogin())
    }
    
    const googleLoginBtn = document.getElementById('googleLoginBtn')
    if (googleLoginBtn) {
      googleLoginBtn.addEventListener('click', () => this.handleGoogleLogin())
    }
    
    // 忘记密码链接
    const forgotPasswordLink = document.getElementById('forgotPassword')
    if (forgotPasswordLink) {
      forgotPasswordLink.addEventListener('click', (e) => this.handleForgotPassword(e))
    }
  }

  /**
   * 初始化表单状态
   */
  initFormState() {
    // 检查是否有记住的用户名
    const rememberedUsername = localStorage.getItem('remembered_username')
    if (rememberedUsername && this.usernameField) {
      this.usernameField.value = rememberedUsername
      if (this.rememberMeField) {
        this.rememberMeField.checked = true
      }
    }
    
    // 清除之前的错误信息
    this.clearError()
  }

  /**
   * 处理表单提交
   */
  async handleSubmit(event) {
    event.preventDefault()
    
    // 验证表单
    if (!this.validateForm()) {
      return
    }
    
    // 显示加载状态
    this.showLoading()
    
    try {
      const formData = this.getFormData()
      let result
      
      // 根据登录类型调用相应的登录方法
      switch (this.currentLoginType) {
        case 'password':
          result = await window.authService.passwordLogin(formData.username, formData.password)
          break
        case 'email':
          result = await window.authService.emailLogin(formData.username, formData.password)
          break
        default:
          throw new Error('不支持的登录类型')
      }
      
      // 处理记住用户名
      this.handleRememberMe(formData.username)
      
      // 登录成功
      this.handleLoginSuccess(result)
      
    } catch (error) {
      console.error('登录失败:', error)
      this.handleLoginError(error)
    } finally {
      this.hideLoading()
    }
  }

  /**
   * 获取表单数据
   */
  getFormData() {
    return {
      username: this.usernameField?.value?.trim() || '',
      password: this.passwordField?.value || '',
      rememberMe: this.rememberMeField?.checked || false
    }
  }

  /**
   * 验证表单
   */
  validateForm() {
    let isValid = true
    
    // 验证用户名/邮箱
    if (!this.validateField('username')) {
      isValid = false
    }
    
    // 验证密码
    if (!this.validateField('password')) {
      isValid = false
    }
    
    return isValid
  }

  /**
   * 验证单个字段
   */
  validateField(fieldName) {
    const field = this[fieldName + 'Field']
    if (!field) return true
    
    const value = field.value.trim()
    let isValid = true
    let errorMessage = ''
    
    switch (fieldName) {
      case 'username':
        if (!value) {
          isValid = false
          errorMessage = '请输入用户名或邮箱'
        } else if (this.currentLoginType === 'email' && !this.isValidEmail(value)) {
          isValid = false
          errorMessage = '请输入有效的邮箱地址'
        }
        break
        
      case 'password':
        if (!value) {
          isValid = false
          errorMessage = '请输入密码'
        } else if (value.length < 6) {
          isValid = false
          errorMessage = '密码至少6个字符'
        }
        break
    }
    
    // 显示字段错误
    this.showFieldError(field, isValid ? '' : errorMessage)
    
    return isValid
  }

  /**
   * 验证邮箱格式
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 显示字段错误
   */
  showFieldError(field, message) {
    // 移除之前的错误样式
    field.classList.remove('error')
    
    // 查找或创建错误提示元素
    let errorElement = field.parentNode.querySelector('.field-error')
    
    if (message) {
      // 添加错误样式
      field.classList.add('error')
      
      if (!errorElement) {
        errorElement = document.createElement('div')
        errorElement.className = 'field-error'
        field.parentNode.appendChild(errorElement)
      }
      
      errorElement.textContent = message
      errorElement.style.display = 'block'
    } else if (errorElement) {
      errorElement.style.display = 'none'
    }
  }

  /**
   * 处理记住用户名
   */
  handleRememberMe(username) {
    if (this.rememberMeField?.checked) {
      localStorage.setItem('remembered_username', username)
    } else {
      localStorage.removeItem('remembered_username')
    }
  }

  /**
   * 处理登录成功
   */
  handleLoginSuccess(result) {
    console.log('登录成功:', result)

    // 显示成功消息
    this.showSuccess('登录成功，正在跳转...')

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      // 跳转到管理仪表板或之前访问的页面
      const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || './admin-dashboard.html'
      console.log('🔄 准备跳转到:', redirectUrl)

      // 确保跳转到正确的管理页面
      if (redirectUrl.includes('index.html') || redirectUrl.includes('admin-dashboard-modern.html')) {
        window.location.href = './admin-dashboard.html'
      } else {
        window.location.href = redirectUrl
      }
    }, 1500)
  }

  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    let errorMessage = error.message || '登录失败，请重试'
    
    // 处理特定错误类型
    if (error.message.includes('账户已被锁定')) {
      const identifier = this.usernameField?.value?.trim()
      if (identifier) {
        const remainingTime = window.authService.getRemainingLockoutTime(identifier)
        const minutes = Math.ceil(remainingTime / (60 * 1000))
        errorMessage = `账户已被锁定，请 ${minutes} 分钟后再试`
      }
    }
    
    this.showError(errorMessage)
  }

  /**
   * 微信登录处理
   */
  async handleWechatLogin() {
    try {
      this.showLoading()
      
      // TODO: 集成微信登录SDK
      // 这里需要根据实际的微信登录流程来实现
      console.log('微信登录功能开发中...')
      this.showError('微信登录功能开发中，请使用密码登录')
      
    } catch (error) {
      console.error('微信登录失败:', error)
      this.showError('微信登录失败: ' + error.message)
    } finally {
      this.hideLoading()
    }
  }

  /**
   * Google登录处理
   */
  async handleGoogleLogin() {
    try {
      this.showLoading()
      
      // TODO: 集成Google登录SDK
      // 这里需要根据实际的Google登录流程来实现
      console.log('Google登录功能开发中...')
      this.showError('Google登录功能开发中，请使用密码登录')
      
    } catch (error) {
      console.error('Google登录失败:', error)
      this.showError('Google登录失败: ' + error.message)
    } finally {
      this.hideLoading()
    }
  }

  /**
   * 忘记密码处理
   */
  handleForgotPassword(event) {
    event.preventDefault()
    
    // TODO: 实现忘记密码功能
    alert('忘记密码功能开发中，请联系管理员重置密码')
  }

  /**
   * 切换登录类型
   */
  switchLoginType(type) {
    this.currentLoginType = type
    
    // 更新UI状态
    document.querySelectorAll('.login-type-tab').forEach(tab => {
      tab.classList.toggle('active', tab.dataset.type === type)
    })
    
    // 更新表单字段标签和验证规则
    if (this.usernameField) {
      const label = this.usernameField.parentNode.querySelector('label')
      if (label) {
        label.textContent = type === 'email' ? '邮箱地址' : '用户名/邮箱'
      }
      this.usernameField.placeholder = type === 'email' ? '请输入邮箱地址' : '请输入用户名或邮箱'
    }
    
    // 清除之前的验证错误
    this.clearFieldErrors()
  }

  /**
   * 切换密码可见性
   */
  togglePasswordVisibility() {
    if (!this.passwordField) return
    
    const isPassword = this.passwordField.type === 'password'
    this.passwordField.type = isPassword ? 'text' : 'password'
    
    const toggleIcon = document.getElementById('passwordToggle')
    if (toggleIcon) {
      toggleIcon.textContent = isPassword ? '🙈' : '👁️'
    }
  }

  /**
   * 创建加载遮罩
   */
  createLoadingOverlay() {
    this.loadingOverlay = document.createElement('div')
    this.loadingOverlay.className = 'loading-overlay'
    this.loadingOverlay.innerHTML = `
      <div class="loading-spinner">
        <div class="spinner"></div>
        <div class="loading-text">登录中...</div>
      </div>
    `
    this.loadingOverlay.style.display = 'none'
    document.body.appendChild(this.loadingOverlay)
  }

  /**
   * 显示加载状态
   */
  showLoading() {
    if (this.submitButton) {
      this.submitButton.disabled = true
      this.submitButton.textContent = '登录中...'
    }
    
    if (this.loadingOverlay) {
      this.loadingOverlay.style.display = 'flex'
    }
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    if (this.submitButton) {
      this.submitButton.disabled = false
      this.submitButton.textContent = '登录'
    }
    
    if (this.loadingOverlay) {
      this.loadingOverlay.style.display = 'none'
    }
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    if (this.errorContainer) {
      this.errorContainer.textContent = message
      this.errorContainer.className = 'error-message show'
    } else {
      alert(message)
    }
  }

  /**
   * 显示成功信息
   */
  showSuccess(message) {
    if (this.errorContainer) {
      this.errorContainer.textContent = message
      this.errorContainer.className = 'success-message show'
    } else {
      alert(message)
    }
  }

  /**
   * 清除错误信息
   */
  clearError() {
    if (this.errorContainer) {
      this.errorContainer.textContent = ''
      this.errorContainer.className = 'error-message'
    }
  }

  /**
   * 清除所有字段错误
   */
  clearFieldErrors() {
    const errorElements = document.querySelectorAll('.field-error')
    errorElements.forEach(element => {
      element.style.display = 'none'
    })
    
    const errorFields = document.querySelectorAll('.error')
    errorFields.forEach(field => {
      field.classList.remove('error')
    })
  }
}

// 初始化登录表单
window.loginForm = new LoginForm()

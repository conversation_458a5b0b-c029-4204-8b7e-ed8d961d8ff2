<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <button class="mobile-menu-btn" onclick="toggleMobileMenu()" style="display: none;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">设计管理</h1>
                            <p class="breadcrumb-description">智能家居设计业务的统一管理平台</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="design-management-container">
                <!-- 主模块表格 -->
                <div class="main-modules-table">
                    <table>
                        <thead>
                            <tr>
                                <th>设计案例</th>
                                <th>设计商品</th>
                                <th>需求管理</th>
                                <th>设计中心</th>
                                <th>数据分析</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="module-card" onclick="selectModule('design_cases')" data-module="design_cases">
                                        <div class="module-icon">
                                            <i class="fas fa-image"></i>
                                        </div>
                                        <div class="module-title">设计案例</div>
                                        <div class="module-desc">案例展示管理</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-card" onclick="selectModule('design_products')" data-module="design_products">
                                        <div class="module-icon">
                                            <i class="fas fa-cube"></i>
                                        </div>
                                        <div class="module-title">设计商品</div>
                                        <div class="module-desc">商品服务管理</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-card" onclick="selectModule('requirements_management')" data-module="requirements_management">
                                        <div class="module-icon">
                                            <i class="fas fa-clipboard-list"></i>
                                        </div>
                                        <div class="module-title">需求管理</div>
                                        <div class="module-desc">客户需求处理</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-card" onclick="selectModule('design_center')" data-module="design_center">
                                        <div class="module-icon">
                                            <i class="fas fa-drafting-compass"></i>
                                        </div>
                                        <div class="module-title">设计中心</div>
                                        <div class="module-desc">设计流程管理</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="module-card" onclick="selectModule('data_analysis')" data-module="data_analysis">
                                        <div class="module-icon">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div class="module-title">数据分析</div>
                                        <div class="module-desc">统计分析报告</div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 子功能表格 -->
                <div class="sub-functions-container">
                    <div class="sub-functions-header">
                        <div>
                            <h2 class="sub-functions-title" id="subFunctionsTitle">请选择上方模块查看功能</h2>
                            <p class="sub-functions-desc" id="subFunctionsDesc">点击上方模块卡片查看对应的功能列表</p>
                        </div>
                    </div>

                    <div id="subFunctionsContent">
                        <div style="text-align: center; padding: 60px 20px; color: #9ca3af;">
                            <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                            <p style="font-size: 16px; margin: 0;">请点击上方模块卡片查看功能详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../../js/admin-common.js"></script>
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 模块数据定义 - 使用数据库友好的命名规范
        const moduleData = {
            'design_cases': {
                title: '设计案例管理',
                desc: '管理和展示各类设计案例，包含户型分类、品牌分类、装修风格分类和商业案例',
                apiEndpoint: '/api/design/cases',
                dbTable: 'design_cases',
                functions: [
                    {
                        id: 'case_category_apartment',
                        name: '户型分类',
                        desc: '按户型结构分类管理设计案例',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/cases/categories/apartment',
                        dbTable: 'design_case_categories'
                    },
                    {
                        id: 'case_category_brand',
                        name: '品牌分类',
                        desc: '按品牌类型分类管理设计案例',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/cases/categories/brand',
                        dbTable: 'design_case_categories'
                    },
                    {
                        id: 'case_category_style',
                        name: '装修风格分类',
                        desc: '按装修风格分类管理设计案例',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/cases/categories/style',
                        dbTable: 'design_case_categories'
                    },
                    {
                        id: 'case_business',
                        name: '商业案例',
                        desc: '商业项目设计案例管理',
                        status: 'development',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/cases/business',
                        dbTable: 'design_business_cases'
                    }
                ]
            },
            'design_products': {
                title: '设计商品管理',
                desc: '管理设计服务商品，包含公寓、别墅/平层、商业空间等不同类型的设计服务',
                apiEndpoint: '/api/design/products',
                dbTable: 'design_products',
                functions: [
                    {
                        id: 'product_apartment',
                        name: '公寓',
                        desc: '公寓类型设计服务管理',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/products/apartment',
                        dbTable: 'design_product_apartment'
                    },
                    {
                        id: 'product_villa',
                        name: '别墅/平层',
                        desc: '别墅和平层设计服务管理',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/products/villa',
                        dbTable: 'design_product_villa'
                    },
                    {
                        id: 'product_commercial',
                        name: '商业空间',
                        desc: '商业空间设计服务管理',
                        status: 'development',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/products/commercial',
                        dbTable: 'design_product_commercial'
                    }
                ]
            },
            'requirements_management': {
                title: '需求管理',
                desc: '客户需求的全流程管理，包含需求分析、需求列表、新建需求、设计师分配和需求设置',
                apiEndpoint: '/api/requirements',
                dbTable: 'customer_requirements',
                functions: [
                    {
                        id: 'req_analysis',
                        name: '需求分析',
                        desc: '查看需求统计和分析报告',
                        status: 'active',
                        actions: ['查看', '导出'],
                        apiPath: '/api/requirements/analysis',
                        dbTable: 'requirement_analysis'
                    },
                    {
                        id: 'req_insights',
                        name: '数据洞察',
                        desc: '需求数据深度分析和洞察',
                        status: 'active',
                        actions: ['查看', '分析'],
                        apiPath: '/api/requirements/insights',
                        dbTable: 'requirement_insights'
                    },
                    {
                        id: 'req_list',
                        name: '需求列表',
                        desc: '查看所有客户需求列表',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/requirements/list',
                        dbTable: 'customer_requirements'
                    },
                    {
                        id: 'req_new_opportunities',
                        name: '新的用户商机',
                        desc: '新客户商机管理',
                        status: 'development',
                        actions: ['查看', '跟进'],
                        apiPath: '/api/requirements/opportunities/new',
                        dbTable: 'customer_opportunities'
                    },
                    {
                        id: 'req_history_opportunities',
                        name: '历史的商机',
                        desc: '历史商机记录管理',
                        status: 'active',
                        actions: ['查看', '分析'],
                        apiPath: '/api/requirements/opportunities/history',
                        dbTable: 'customer_opportunities'
                    },
                    {
                        id: 'req_create',
                        name: '新建需求',
                        desc: '创建新的客户需求',
                        status: 'active',
                        actions: ['新建', '保存'],
                        apiPath: '/api/requirements/create',
                        dbTable: 'customer_requirements'
                    },
                    {
                        id: 'req_user_info',
                        name: '用户信息',
                        desc: '客户基本信息管理',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/requirements/users',
                        dbTable: 'customer_info'
                    },
                    {
                        id: 'req_product_needs',
                        name: '产品需求',
                        desc: '产品相关需求管理',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/requirements/products',
                        dbTable: 'product_requirements'
                    },
                    {
                        id: 'req_drawings',
                        name: '图纸提交',
                        desc: '设计图纸提交管理',
                        status: 'active',
                        actions: ['上传', '审核'],
                        apiPath: '/api/requirements/drawings',
                        dbTable: 'requirement_drawings'
                    },
                    {
                        id: 'req_preferences',
                        name: '喜好需求',
                        desc: '客户喜好偏好管理',
                        status: 'active',
                        actions: ['记录', '分析'],
                        apiPath: '/api/requirements/preferences',
                        dbTable: 'customer_preferences'
                    },
                    {
                        id: 'req_designer_assign',
                        name: '设计师分配',
                        desc: '为需求分配合适的设计师',
                        status: 'active',
                        actions: ['分配', '调整'],
                        apiPath: '/api/requirements/assign',
                        dbTable: 'designer_assignments'
                    },
                    {
                        id: 'req_pending_assign',
                        name: '3个待分配',
                        desc: '当前待分配的需求数量',
                        status: 'planning',
                        actions: ['分配', '查看'],
                        apiPath: '/api/requirements/pending',
                        dbTable: 'designer_assignments'
                    },
                    {
                        id: 'req_assign_management',
                        name: '管理需求与设计师的分配关系',
                        desc: '管理需求和设计师的匹配关系',
                        status: 'active',
                        actions: ['管理', '优化'],
                        apiPath: '/api/requirements/assign/manage',
                        dbTable: 'designer_assignments'
                    },
                    {
                        id: 'req_settings',
                        name: '需求设置',
                        desc: '需求管理相关设置',
                        status: 'active',
                        actions: ['设置', '保存'],
                        apiPath: '/api/requirements/settings',
                        dbTable: 'requirement_settings'
                    },
                    {
                        id: 'req_config',
                        name: '配置需求管理相关设置',
                        desc: '配置需求处理流程和规则',
                        status: 'active',
                        actions: ['配置', '应用'],
                        apiPath: '/api/requirements/config',
                        dbTable: 'requirement_config'
                    },
                    {
                        id: 'req_templates',
                        name: '需求模板',
                        desc: '管理常用的需求模板，提高效率',
                        status: 'development',
                        actions: ['创建', '编辑'],
                        apiPath: '/api/requirements/templates',
                        dbTable: 'requirement_templates'
                    }
                ]
            },
            'design_center': {
                title: '设计中心',
                desc: '设计任务和流程的核心管理中心，包含任务管理、反馈管理和支付管理',
                apiEndpoint: '/api/design/center',
                dbTable: 'design_tasks',
                functions: [
                    {
                        id: 'task_management',
                        name: '设计任务',
                        desc: '设计任务的创建和管理',
                        status: 'active',
                        actions: ['查看', '管理'],
                        apiPath: '/api/design/tasks',
                        dbTable: 'design_tasks'
                    },
                    {
                        id: 'task_new',
                        name: '新任务',
                        desc: '创建新的设计任务',
                        status: 'active',
                        actions: ['创建', '分配'],
                        apiPath: '/api/design/tasks/new',
                        dbTable: 'design_tasks'
                    },
                    {
                        id: 'task_in_progress',
                        name: '进行中',
                        desc: '正在进行的设计任务',
                        status: 'active',
                        actions: ['查看', '跟进'],
                        apiPath: '/api/design/tasks/in-progress',
                        dbTable: 'design_tasks'
                    },
                    {
                        id: 'task_history',
                        name: '历史任务',
                        desc: '已完成的历史设计任务',
                        status: 'active',
                        actions: ['查看', '归档'],
                        apiPath: '/api/design/tasks/history',
                        dbTable: 'design_tasks'
                    },
                    {
                        id: 'feedback_management',
                        name: '反馈管理',
                        desc: '客户反馈的收集和处理',
                        status: 'active',
                        actions: ['查看', '处理'],
                        apiPath: '/api/design/feedback',
                        dbTable: 'customer_feedback'
                    },
                    {
                        id: 'customer_communication',
                        name: '客户沟通',
                        desc: '与客户的沟通记录管理',
                        status: 'active',
                        actions: ['查看', '回复'],
                        apiPath: '/api/design/communication',
                        dbTable: 'customer_communications'
                    },
                    {
                        id: 'payment_management',
                        name: '支付管理',
                        desc: '设计费用和支付管理',
                        status: 'active',
                        actions: ['查看', '处理'],
                        apiPath: '/api/design/payments',
                        dbTable: 'design_payments'
                    },
                    {
                        id: 'design_progress',
                        name: '设计进度',
                        desc: '设计项目进度跟踪',
                        status: 'active',
                        actions: ['查看', '更新'],
                        apiPath: '/api/design/progress',
                        dbTable: 'design_progress'
                    },
                    {
                        id: 'design_drawings',
                        name: '设计图纸',
                        desc: '设计图纸文件管理',
                        status: 'active',
                        actions: ['上传', '下载'],
                        apiPath: '/api/design/drawings',
                        dbTable: 'design_drawings'
                    },
                    {
                        id: 'design_effects',
                        name: '设计效果',
                        desc: '设计效果图管理',
                        status: 'active',
                        actions: ['查看', '编辑'],
                        apiPath: '/api/design/effects',
                        dbTable: 'design_effects'
                    },
                    {
                        id: 'construction_guide',
                        name: '施工说明',
                        desc: '施工指导说明文档',
                        status: 'development',
                        actions: ['编写', '审核'],
                        apiPath: '/api/design/construction-guide',
                        dbTable: 'construction_guides'
                    },
                    {
                        id: 'project_budget',
                        name: '项目预算',
                        desc: '设计项目预算管理',
                        status: 'development',
                        actions: ['制定', '审核'],
                        apiPath: '/api/design/budget',
                        dbTable: 'project_budgets'
                    },
                    {
                        id: 'design_versions',
                        name: '方案版本',
                        desc: '设计方案版本控制',
                        status: 'planning',
                        actions: ['管理', '对比'],
                        apiPath: '/api/design/versions',
                        dbTable: 'design_versions'
                    }
                ]
            },
            'data_analysis': {
                title: '数据分析',
                desc: '设计业务的数据统计和分析报告，为业务决策提供数据支持',
                apiEndpoint: '/api/analytics',
                dbTable: 'analytics_reports',
                functions: [
                    {
                        id: 'req_statistics',
                        name: '需求统计分析报告',
                        desc: '客户需求的统计分析和趋势报告',
                        status: 'active',
                        actions: ['查看', '导出'],
                        apiPath: '/api/analytics/requirements',
                        dbTable: 'requirement_analytics'
                    },
                    {
                        id: 'designer_workload',
                        name: '设计师工作量分析',
                        desc: '设计师工作负荷和效率分析',
                        status: 'development',
                        actions: ['分析', '优化'],
                        apiPath: '/api/analytics/designer-workload',
                        dbTable: 'designer_analytics'
                    },
                    {
                        id: 'customer_satisfaction',
                        name: '客户满意度分析',
                        desc: '客户满意度调查和分析报告',
                        status: 'planning',
                        actions: ['调查', '分析'],
                        apiPath: '/api/analytics/satisfaction',
                        dbTable: 'satisfaction_analytics'
                    },
                    {
                        id: 'revenue_analysis',
                        name: '业务收入分析',
                        desc: '设计业务收入统计和分析',
                        status: 'development',
                        actions: ['统计', '分析'],
                        apiPath: '/api/analytics/revenue',
                        dbTable: 'revenue_analytics'
                    },
                    {
                        id: 'completion_rate',
                        name: '项目完成率分析',
                        desc: '设计项目完成情况统计',
                        status: 'active',
                        actions: ['统计', '报告'],
                        apiPath: '/api/analytics/completion',
                        dbTable: 'completion_analytics'
                    }
                ]
            }
        };

        // 当前选中的模块
        let currentModule = null;

        // 选择模块
        function selectModule(moduleId) {
            console.log('选择模块:', moduleId);

            // 更新模块卡片状态
            document.querySelectorAll('.module-card').forEach(card => {
                card.classList.remove('active');
            });

            const selectedCard = document.querySelector(`[data-module="${moduleId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }

            // 更新当前模块
            currentModule = moduleId;
            const moduleInfo = moduleData[moduleId];

            if (moduleInfo) {
                // 更新标题和描述
                document.getElementById('subFunctionsTitle').textContent = moduleInfo.title;
                document.getElementById('subFunctionsDesc').textContent = moduleInfo.desc;

                // 渲染功能列表
                renderFunctions(moduleInfo.functions);
            } else {
                console.error('模块数据未找到:', moduleId);
            }
        }

        // 渲染功能列表
        function renderFunctions(functions) {
            const container = document.getElementById('subFunctionsContent');

            if (!functions || functions.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #9ca3af;">
                        <i class="fas fa-exclamation-circle" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                        <p style="font-size: 16px; margin: 0;">该模块暂无功能数据</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="sub-functions-table">
                    <thead>
                        <tr>
                            <th>功能ID</th>
                            <th>功能名称</th>
                            <th>功能描述</th>
                            <th>API路径</th>
                            <th>数据表</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${functions.map(func => `
                            <tr>
                                <td><code style="font-size: 11px;">${func.id}</code></td>
                                <td class="function-name">${func.name}</td>
                                <td class="function-desc">${func.desc}</td>
                                <td><code style="font-size: 11px;">${func.apiPath}</code></td>
                                <td><code style="font-size: 11px;">${func.dbTable}</code></td>
                                <td>
                                    <span class="function-status status-${func.status}">
                                        ${getStatusText(func.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="function-actions">
                                        ${func.actions.map(action => `
                                            <button class="btn-sm btn-primary" onclick="handleAction('${func.id}', '${action}', '${func.apiPath}')" title="${action} ${func.name}">
                                                ${action}
                                            </button>
                                        `).join('')}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '已启用',
                'development': '开发中',
                'planning': '规划中'
            };
            return statusMap[status] || status;
        }

        // 处理操作
        function handleAction(functionId, action, apiPath) {
            console.log(`执行操作: ${action} - ${functionId} - API: ${apiPath}`);

            // 获取功能名称
            const functionName = getFunctionName(functionId);

            // 根据不同的操作执行不同的逻辑
            switch(action) {
                case '查看':
                    showActionDialog('查看功能', functionName, functionId, apiPath);
                    break;
                case '编辑':
                    showActionDialog('编辑功能', functionName, functionId, apiPath);
                    break;
                case '新建':
                case '创建':
                    showActionDialog('创建新的', functionName, functionId, apiPath);
                    break;
                case '分配':
                    showActionDialog('分配功能', functionName, functionId, apiPath);
                    break;
                case '设置':
                case '配置':
                    showActionDialog('配置功能', functionName, functionId, apiPath);
                    break;
                default:
                    showActionDialog(`${action}操作`, functionName, functionId, apiPath);
            }
        }

        // 获取功能名称
        function getFunctionName(functionId) {
            for (const moduleId in moduleData) {
                const module = moduleData[moduleId];
                const func = module.functions.find(f => f.id === functionId);
                if (func) {
                    return func.name;
                }
            }
            return functionId;
        }

        // 显示操作对话框
        function showActionDialog(actionType, functionName, functionId, apiPath) {
            showToast(`${actionType}: ${functionName} - 功能即将上线，敬请期待！`, 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('设计管理页面已加载');

            // 检查模块数据是否正确加载
            console.log('模块数据:', Object.keys(moduleData));

            // 默认选中第一个模块
            setTimeout(() => {
                selectModule('design_cases');
            }, 100);
        });

        // 添加移动端菜单切换功能
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-open');
            }
        }
    </script>
</body>
</html>

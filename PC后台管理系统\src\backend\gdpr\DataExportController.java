package com.smarthome.gdpr;

import com.smarthome.auth.service.AuthService;
import com.smarthome.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * GDPR数据导出控制器
 * 提供用户数据导出API，符合GDPR数据可携带性要求
 */
@RestController
@RequestMapping("/api/gdpr")
@Tag(name = "GDPR数据导出", description = "用户数据导出和隐私权管理")
public class DataExportController {

    @Autowired
    private DataExportService dataExportService;
    
    @Autowired
    private AuthService authService;

    /**
     * 导出用户个人数据
     * 
     * @param userId 用户ID
     * @param request HTTP请求
     * @return ZIP格式的用户数据包
     */
    @GetMapping("/export/{userId}")
    @Operation(
        summary = "导出用户个人数据",
        description = "根据GDPR第20条，用户有权以结构化、常用和机器可读的格式获取其个人数据"
    )
    @PreAuthorize("hasRole('USER') and #userId == authentication.principal.businessUID")
    public ResponseEntity<?> exportUserData(
            @Parameter(description = "用户业务ID", required = true)
            @PathVariable String userId,
            HttpServletRequest request) {
        
        try {
            // 获取当前认证用户
            String currentUserId = authService.getCurrentUserBusinessUID();
            
            // 验证权限：只能导出自己的数据
            if (!dataExportService.canExportData(userId, currentUserId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限导出该用户数据"));
            }
            
            // 记录导出操作
            dataExportService.logDataExport(userId, currentUserId);
            
            // 导出数据
            byte[] zipData = dataExportService.exportUserData(userId);
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("personal_data_export_%s_%s.zip", userId, timestamp);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(zipData.length);
            
            return ResponseEntity.ok()
                .headers(headers)
                .body(zipData);
                
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在: " + e.getMessage()));
                
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("数据导出失败: " + e.getMessage()));
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统错误: " + e.getMessage()));
        }
    }

    /**
     * 检查数据导出权限
     * 
     * @param userId 用户ID
     * @return 权限检查结果
     */
    @GetMapping("/export/{userId}/check")
    @Operation(
        summary = "检查数据导出权限",
        description = "检查当前用户是否有权限导出指定用户的数据"
    )
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Boolean>> checkExportPermission(
            @Parameter(description = "用户业务ID", required = true)
            @PathVariable String userId) {
        
        try {
            String currentUserId = authService.getCurrentUserBusinessUID();
            boolean canExport = dataExportService.canExportData(userId, currentUserId);
            
            return ResponseEntity.ok(ApiResponse.success(canExport));
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限检查失败: " + e.getMessage()));
        }
    }

    /**
     * 获取GDPR权利说明
     * 
     * @return GDPR权利说明
     */
    @GetMapping("/rights")
    @Operation(
        summary = "获取GDPR权利说明",
        description = "获取用户在GDPR框架下享有的数据权利说明"
    )
    public ResponseEntity<ApiResponse<GDPRRights>> getGDPRRights() {
        
        GDPRRights rights = new GDPRRights();
        rights.setAccessRight("您有权获取我们处理的关于您的个人数据的副本");
        rights.setRectificationRight("您有权要求我们更正关于您的不准确个人数据");
        rights.setErasureRight("在某些情况下，您有权要求我们删除您的个人数据");
        rights.setRestrictionRight("在某些情况下，您有权要求我们限制对您个人数据的处理");
        rights.setPortabilityRight("您有权以结构化、常用和机器可读的格式接收您的个人数据");
        rights.setObjectionRight("您有权反对我们处理您的个人数据");
        rights.setContactInfo("如需行使这些权利，请联系: <EMAIL>");
        
        return ResponseEntity.ok(ApiResponse.success(rights));
    }

    /**
     * 请求删除个人数据
     * 
     * @param userId 用户ID
     * @param reason 删除原因
     * @return 删除请求结果
     */
    @PostMapping("/delete-request/{userId}")
    @Operation(
        summary = "请求删除个人数据",
        description = "根据GDPR第17条被遗忘权，用户可以请求删除其个人数据"
    )
    @PreAuthorize("hasRole('USER') and #userId == authentication.principal.businessUID")
    public ResponseEntity<ApiResponse<String>> requestDataDeletion(
            @Parameter(description = "用户业务ID", required = true)
            @PathVariable String userId,
            @Parameter(description = "删除原因")
            @RequestParam(required = false) String reason) {
        
        try {
            String currentUserId = authService.getCurrentUserBusinessUID();
            
            // 验证权限
            if (!userId.equals(currentUserId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限删除该用户数据"));
            }
            
            // 记录删除请求
            // 这里应该创建一个删除请求记录，由管理员审核
            String requestId = "DEL_" + System.currentTimeMillis();
            
            // 实际实现中，这里应该调用数据删除服务
            // dataDeletionService.createDeletionRequest(userId, reason, requestId);
            
            return ResponseEntity.ok(ApiResponse.success(
                "数据删除请求已提交，请求ID: " + requestId + 
                "。我们将在30天内处理您的请求。"));
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除请求提交失败: " + e.getMessage()));
        }
    }

    /**
     * GDPR权利信息类
     */
    public static class GDPRRights {
        private String accessRight;
        private String rectificationRight;
        private String erasureRight;
        private String restrictionRight;
        private String portabilityRight;
        private String objectionRight;
        private String contactInfo;

        // Getters and Setters
        public String getAccessRight() { return accessRight; }
        public void setAccessRight(String accessRight) { this.accessRight = accessRight; }

        public String getRectificationRight() { return rectificationRight; }
        public void setRectificationRight(String rectificationRight) { this.rectificationRight = rectificationRight; }

        public String getErasureRight() { return erasureRight; }
        public void setErasureRight(String erasureRight) { this.erasureRight = erasureRight; }

        public String getRestrictionRight() { return restrictionRight; }
        public void setRestrictionRight(String restrictionRight) { this.restrictionRight = restrictionRight; }

        public String getPortabilityRight() { return portabilityRight; }
        public void setPortabilityRight(String portabilityRight) { this.portabilityRight = portabilityRight; }

        public String getObjectionRight() { return objectionRight; }
        public void setObjectionRight(String objectionRight) { this.objectionRight = objectionRight; }

        public String getContactInfo() { return contactInfo; }
        public void setContactInfo(String contactInfo) { this.contactInfo = contactInfo; }
    }
}

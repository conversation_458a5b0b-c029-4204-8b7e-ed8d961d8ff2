<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居管理系统</title>
    <link rel="stylesheet" href="styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 确保基本样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #1a1a1a;
        }
        
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: #1a1a1a;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .logo-text {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-section {
            margin-bottom: 24px;
        }
        
        .nav-section-title {
            padding: 0 20px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .nav-item:hover {
            background: #f3f4f6;
            color: #1a1a1a;
        }
        
        .nav-item.active {
            background: #1a1a1a;
            color: white;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        .page-description {
            color: #6b7280;
            font-size: 14px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            background: #1a1a1a;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .stat-info {
            flex: 1;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .content-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #1a1a1a;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #374151;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #1a1a1a;
            border: 1px solid #e5e7eb;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">系统概览</div>
                    <a href="index.html" class="nav-item active">数据概览</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="src/pc/components/pages/design-management.html" class="nav-item">设计管理</a>
                    <a href="src/pc/components/pages/projects.html" class="nav-item">项目管理</a>
                    <a href="src/pc/components/pages/construction-management.html" class="nav-item">施工管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务管理</div>
                    <a href="src/pc/components/pages/products.html" class="nav-item">商品管理</a>
                    <a href="src/pc/components/pages/orders.html" class="nav-item">订单管理</a>
                    <a href="src/pc/components/pages/customer-management.html" class="nav-item">客户管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="src/pc/components/pages/user-management.html" class="nav-item">用户管理</a>
                    <a href="src/pc/components/pages/permissions.html" class="nav-item">权限管理</a>
                    <a href="src/pc/components/pages/settings.html" class="nav-item">系统配置</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="src/pc/components/pages/user-profile.html" class="nav-item">个人资料</a>
                    <a href="src/pc/components/pages/login.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">数据概览</h1>
                <p class="page-description">智能家居管理系统运营数据总览</p>
            </div>

            <!-- 统计卡片 -->
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-value">1,234</span>
                        <div class="stat-label">总用户数</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-value">567</span>
                        <div class="stat-label">订单总数</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-value">¥89,456</span>
                        <div class="stat-label">总收入</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-value">23</span>
                        <div class="stat-label">活跃项目</div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-card">
                <h2 class="card-title">快速操作</h2>
                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <a href="src/pc/components/pages/orders.html" class="btn">
                        <i class="fas fa-plus"></i>
                        新增订单
                    </a>
                    <a href="src/pc/components/pages/products.html" class="btn btn-secondary">
                        <i class="fas fa-box"></i>
                        商品管理
                    </a>
                    <a href="src/pc/components/pages/user-management.html" class="btn btn-secondary">
                        <i class="fas fa-users"></i>
                        用户管理
                    </a>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 简单的页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('智能家居管理系统已加载');
            
            // 添加导航点击事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 移除所有active类
                    document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                    // 添加active类到当前项
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>

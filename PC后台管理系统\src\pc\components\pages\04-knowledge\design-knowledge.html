<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计知识库 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 强制横向布局样式 - 最高优先级 -->
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>

    <style>
        /* CSS变量兼容性定义 */
        :root {
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-4: 1rem;
            --radius-lg: 8px;
            --duration-fast: 0.15s;
            --ease-in-out: ease-in-out;
            --bg-primary: #ffffff;
            --border-primary: #e5e7eb;
            --color-primary: #3b82f6;
            --color-warning: #f59e0b;
            --color-info: #06b6d4;
            --color-success: #10b981;
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
        }
        
        /* 知识库特定样式 */
        .knowledge-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .knowledge-category-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            transition: all var(--duration-fast) var(--ease-in-out);
            cursor: pointer;
        }

        .knowledge-category-card:hover {
            border-color: var(--color-primary);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-bottom: var(--space-4);
        }

        .category-icon.design { background: var(--color-primary); }
        .category-icon.tutorial { background: var(--color-info); }
        .category-icon.best-practice { background: var(--color-success); }
        .category-icon.tools { background: var(--color-warning); }

        .knowledge-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            transition: all var(--duration-fast) var(--ease-in-out);
        }

        .knowledge-item:hover {
            border-color: var(--color-primary-200);
            box-shadow: var(--shadow-sm);
        }

        .knowledge-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .knowledge-meta {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--space-3);
        }

        .knowledge-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .knowledge-tag {
            padding: var(--space-1) var(--space-2);
            background: var(--bg-secondary);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
        }

        .knowledge-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .tabs-container {
            border-bottom: 1px solid var(--border-primary);
            margin-bottom: var(--space-6);
        }

        .tabs-nav {
            display: flex;
            gap: var(--space-1);
        }

        .tab-button {
            padding: var(--space-3) var(--space-4);
            border: none;
            background: none;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--duration-fast) var(--ease-in-out);
        }

        .tab-button:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        .tab-button.active {
            color: var(--color-primary);
            border-bottom-color: var(--color-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .search-highlight {
            background: var(--color-warning-light);
            color: var(--color-warning-dark);
            padding: 1px 2px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <!-- 立即执行的横向布局强制脚本 -->
    <script>
        console.log('强制应用横向布局...');

        // 立即隐藏标签页相关元素
        const hideElements = () => {
            const selectors = [
                '.tabs-container',
                '.tabs-nav',
                '.tab-button',
                '.tab-content[data-tab-content]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                    el.style.opacity = '0';
                });
            });
        };

        // 立即显示横向模块
        const showModules = () => {
            const modules = document.querySelector('.knowledge-modules');
            if (modules) {
                modules.style.display = 'grid';
                modules.style.gridTemplateColumns = '2fr 1fr 1fr 1fr';
                modules.style.gap = '1.5rem';
                modules.style.minHeight = '600px';
                modules.style.visibility = 'visible';
                modules.style.opacity = '1';
            }
        };

        // 页面加载时立即执行
        hideElements();
        showModules();

        // DOM加载完成后再次执行
        document.addEventListener('DOMContentLoaded', () => {
            hideElements();
            showModules();
            console.log('横向布局已强制应用');
        });
    </script>

    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item active">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="content-header">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">设计知识库</h1>
                            <p class="breadcrumb-description">智能家居设计相关知识、教程和最佳实践</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-body">
                <!-- 统计卡片 -->
                <div class="knowledge-stats-grid">
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-articles">0</div>
                                    <div class="text-sm text-secondary">知识文章</div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-categories">0</div>
                                    <div class="text-sm text-secondary">知识分类</div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-folder fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-views">0</div>
                                    <div class="text-sm text-secondary">总浏览量</div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="new-articles">0</div>
                                    <div class="text-sm text-secondary">本月新增</div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-plus fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 横向模块布局 -->
                <div class="knowledge-modules" style="display: grid !important; grid-template-columns: 2fr 1fr 1fr 1fr !important; gap: 1.5rem !important; min-height: 600px !important; width: 100% !important;">

                    <!-- 知识文章模块 -->
                    <div class="module-card" style="background: #ffffff !important; border: 1px solid #e5e7eb !important; border-radius: 8px !important; overflow: hidden !important; display: flex !important; flex-direction: column !important; height: 100% !important;">
                        <div class="module-header" style="padding: 1rem 1.5rem !important; border-bottom: 1px solid #e5e7eb !important; background: #f8fafc !important; display: flex !important; justify-content: space-between !important; align-items: center !important;">
                            <h3 style="margin: 0 !important; font-size: 16px !important; font-weight: 600 !important; color: #1f2937 !important;">知识文章</h3>
                            <div class="module-actions">
                                <button class="btn btn-primary" style="padding: 0.5rem 1rem; border: 1px solid #1f2937; border-radius: 4px; background: #1f2937; color: #ffffff; font-size: 12px; cursor: pointer;">
                                    <i class="fas fa-plus"></i> 新增
                                </button>
                            </div>
                        </div>
                        <div class="module-content" style="padding: 1rem !important; flex: 1 !important; overflow-y: auto !important;">
                            <div class="search-toolbar" style="display: flex !important; align-items: center !important; gap: 0.75rem !important; margin-bottom: 1rem !important; padding: 0.75rem !important; background: #f8fafc !important; border-radius: 6px !important; border: 1px solid #e5e7eb !important;">
                                <div class="search-box" style="position: relative !important; flex: 1 !important;">
                                    <input type="text" placeholder="搜索知识文章..." style="width: 100% !important; padding: 0.5rem 2rem 0.5rem 0.75rem !important; border: 1px solid #e5e7eb !important; border-radius: 4px !important; font-size: 12px !important; background: #ffffff !important;">
                                    <i class="fas fa-search" style="position: absolute !important; right: 8px !important; top: 50% !important; transform: translateY(-50%) !important; color: #6b7280 !important;"></i>
                                </div>
                                <select style="padding: 0.5rem 0.75rem !important; border: 1px solid #e5e7eb !important; border-radius: 4px !important; font-size: 12px !important; min-width: 120px !important; background: #ffffff !important;">
                                    <option value="">所有类型</option>
                                    <option value="design-principles">设计原则</option>
                                    <option value="color-theory">色彩理论</option>
                                    <option value="space-planning">空间规划</option>
                                    <option value="lighting-design">照明设计</option>
                                </select>
                            </div>
                            <div class="knowledge-list">
                                <div style="padding: 0.75rem; margin-bottom: 0.75rem; border: 1px solid #e5e7eb; border-radius: 6px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 13px; font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">智能家居设计原则</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px; color: #6b7280;">
                                        <span>设计原则</span>
                                        <span>2025-01-15</span>
                                    </div>
                                </div>
                                <div style="padding: 0.75rem; margin-bottom: 0.75rem; border: 1px solid #e5e7eb; border-radius: 6px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 13px; font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">色彩搭配技巧</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px; color: #6b7280;">
                                        <span>色彩理论</span>
                                        <span>2025-01-14</span>
                                    </div>
                                </div>
                                <div style="padding: 0.75rem; margin-bottom: 0.75rem; border: 1px solid #e5e7eb; border-radius: 6px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 13px; font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">空间布局规划</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px; color: #6b7280;">
                                        <span>空间规划</span>
                                        <span>2025-01-13</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类管理模块 -->
                    <div class="module-card" style="background: #ffffff !important; border: 1px solid #e5e7eb !important; border-radius: 8px !important; overflow: hidden !important; display: flex !important; flex-direction: column !important; height: 100% !important;">
                        <div class="module-header" style="padding: 1rem 1.5rem !important; border-bottom: 1px solid #e5e7eb !important; background: #f8fafc !important; display: flex !important; justify-content: space-between !important; align-items: center !important;">
                            <h3 style="margin: 0 !important; font-size: 16px !important; font-weight: 600 !important; color: #1f2937 !important;">分类管理</h3>
                            <div class="module-actions">
                                <button class="btn btn-primary" style="padding: 0.5rem 1rem; border: 1px solid #1f2937; border-radius: 4px; background: #1f2937; color: #ffffff; font-size: 12px; cursor: pointer;">
                                    <i class="fas fa-plus"></i> 新增分类
                                </button>
                            </div>
                        </div>
                        <div class="module-content" style="padding: 1rem !important; flex: 1 !important; overflow-y: auto !important;">
                            <div class="category-tree">
                                <div style="padding: 0.5rem 0.75rem; margin-bottom: 0.25rem; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; justify-content: space-between; align-items: center; background: #1f2937; color: #ffffff;">
                                    <span>全部知识</span>
                                    <span style="font-size: 10px; background: rgba(255, 255, 255, 0.2); padding: 2px 6px; border-radius: 10px;">45</span>
                                </div>
                                <div style="padding: 0.5rem 0.75rem; margin-bottom: 0.25rem; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <span>设计原则</span>
                                    <span style="font-size: 10px; color: #6b7280; background: #f3f4f6; padding: 2px 6px; border-radius: 10px;">12</span>
                                </div>
                                <div style="padding: 0.5rem 0.75rem; margin-bottom: 0.25rem; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <span>色彩理论</span>
                                    <span style="font-size: 10px; color: #6b7280; background: #f3f4f6; padding: 2px 6px; border-radius: 10px;">8</span>
                                </div>
                                <div style="padding: 0.5rem 0.75rem; margin-bottom: 0.25rem; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <span>空间规划</span>
                                    <span style="font-size: 10px; color: #6b7280; background: #f3f4f6; padding: 2px 6px; border-radius: 10px;">15</span>
                                </div>
                                <div style="padding: 0.5rem 0.75rem; margin-bottom: 0.25rem; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <span>照明设计</span>
                                    <span style="font-size: 10px; color: #6b7280; background: #f3f4f6; padding: 2px 6px; border-radius: 10px;">10</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 热门内容模块 -->
                    <div class="module-card" style="background: #ffffff !important; border: 1px solid #e5e7eb !important; border-radius: 8px !important; overflow: hidden !important; display: flex !important; flex-direction: column !important; height: 100% !important;">
                        <div class="module-header" style="padding: 1rem 1.5rem !important; border-bottom: 1px solid #e5e7eb !important; background: #f8fafc !important; display: flex !important; justify-content: space-between !important; align-items: center !important;">
                            <h3 style="margin: 0 !important; font-size: 16px !important; font-weight: 600 !important; color: #1f2937 !important;">热门内容</h3>
                            <div class="module-actions">
                                <span style="color: #6b7280; font-size: 12px;">本周浏览</span>
                            </div>
                        </div>
                        <div class="module-content" style="padding: 1rem !important; flex: 1 !important; overflow-y: auto !important;">
                            <div class="popular-list">
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">智能家居设计原则详解</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>设计原则</span>
                                        <span style="color: #3b82f6; font-weight: 500;">345 次浏览</span>
                                    </div>
                                </div>
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">色彩搭配实用技巧</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>色彩理论</span>
                                        <span style="color: #3b82f6; font-weight: 500;">234 次浏览</span>
                                    </div>
                                </div>
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">空间布局优化方案</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>空间规划</span>
                                        <span style="color: #3b82f6; font-weight: 500;">189 次浏览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近更新模块 -->
                    <div class="module-card" style="background: #ffffff !important; border: 1px solid #e5e7eb !important; border-radius: 8px !important; overflow: hidden !important; display: flex !important; flex-direction: column !important; height: 100% !important;">
                        <div class="module-header" style="padding: 1rem 1.5rem !important; border-bottom: 1px solid #e5e7eb !important; background: #f8fafc !important; display: flex !important; justify-content: space-between !important; align-items: center !important;">
                            <h3 style="margin: 0 !important; font-size: 16px !important; font-weight: 600 !important; color: #1f2937 !important;">最近更新</h3>
                            <div class="module-actions">
                                <span style="color: #6b7280; font-size: 12px;">近7天</span>
                            </div>
                        </div>
                        <div class="module-content" style="padding: 1rem !important; flex: 1 !important; overflow-y: auto !important;">
                            <div class="recent-list">
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">智能照明设计新趋势</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>设计部</span>
                                        <span>2025-01-23</span>
                                    </div>
                                </div>
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">材料选择指导手册</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>技术部</span>
                                        <span>2025-01-22</span>
                                    </div>
                                </div>
                                <div style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; cursor: pointer; transition: all 0.15s ease-in-out;">
                                    <div style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 0.25rem; line-height: 1.3;">全屋智能设计案例</div>
                                    <div style="font-size: 10px; color: #6b7280; display: flex; justify-content: space-between; align-items: center;">
                                        <span>设计部</span>
                                        <span>2025-01-21</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增文章模态框 -->
    <div class="modal" id="new-article-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">新增知识文章</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="new-article-form" data-form-validate>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label required">文章标题</label>
                        <input type="text" class="form-control" id="article-title" required placeholder="请输入文章标题">
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label required">知识分类</label>
                            <select class="form-control" id="article-category" required>
                                <option value="">请选择分类</option>
                                <option value="design-principles">设计原则</option>
                                <option value="color-theory">色彩理论</option>
                                <option value="space-planning">空间规划</option>
                                <option value="lighting-design">照明设计</option>
                                <option value="material-selection">材料选择</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">难度等级</label>
                            <select class="form-control" id="article-difficulty">
                                <option value="beginner">初级</option>
                                <option value="intermediate">中级</option>
                                <option value="advanced">高级</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">文章摘要</label>
                        <textarea class="form-control" id="article-summary" rows="3" placeholder="请输入文章摘要"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">文章内容</label>
                        <textarea class="form-control" id="article-content" rows="10" required placeholder="请输入文章内容"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">标签</label>
                        <input type="text" class="form-control" id="article-tags" placeholder="请输入标签，用逗号分隔">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="ds.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">发布文章</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../../design-system/components.js"></script>
    <script>
        // 设计知识库页面逻辑
        class DesignKnowledgePage {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 10;
                this.totalCount = 0;
                this.currentFilters = {};

                this.init();
            }

            async init() {
                await this.loadStatistics();
                await this.loadArticles();
                await this.loadPopularArticles();
                await this.loadRecentArticles();
                this.bindEvents();
            }

            bindEvents() {
                // 搜索输入防抖
                const searchInput = document.getElementById('search-knowledge');
                if (searchInput) {
                    searchInput.addEventListener('input', ds.debounce(() => {
                        this.searchKnowledge();
                    }, 500));
                }

                // 筛选器变化
                ['category-filter', 'difficulty-filter'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('change', () => {
                            this.searchKnowledge();
                        });
                    }
                });

                // 新增文章表单提交
                const newArticleForm = document.getElementById('new-article-form');
                if (newArticleForm) {
                    newArticleForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.createArticle();
                    });
                }
            }

            async loadStatistics() {
                try {
                    const response = await api.get('/knowledge/design/statistics');
                    const stats = response.data || {};

                    document.getElementById('total-articles').textContent = stats.total_articles || 0;
                    document.getElementById('total-categories').textContent = stats.total_categories || 0;
                    document.getElementById('total-views').textContent = stats.total_views || 0;
                    document.getElementById('new-articles').textContent = stats.new_articles || 0;

                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            async loadArticles(page = 1) {
                try {
                    ds.showLoading('#knowledge-articles');

                    const params = {
                        page: page,
                        page_size: this.pageSize,
                        type: 'design',
                        ...this.currentFilters
                    };

                    const response = await api.get('/knowledge/articles', params);

                    this.renderArticles(response.data || []);
                    this.renderArticlesPagination(response.pagination || {});
                    this.totalCount = response.pagination?.total || 0;

                } catch (error) {
                    console.error('加载知识文章失败:', error);
                    ds.showToast('加载知识文章失败，请重试', 'error');
                    this.renderArticles([]);
                } finally {
                    ds.hideLoading('#knowledge-articles');
                }
            }

            renderArticles(articles) {
                const container = document.getElementById('knowledge-articles');

                if (articles.length === 0) {
                    container.innerHTML = `
                        <div class="text-center text-secondary" style="padding: 40px;">
                            <i class="fas fa-book fa-3x mb-4" style="display: block; opacity: 0.3;"></i>
                            暂无知识文章
                        </div>
                    `;
                    return;
                }

                container.innerHTML = articles.map(article => `
                    <div class="knowledge-item">
                        <div class="knowledge-title">${article.title}</div>
                        <div class="knowledge-meta">
                            <span><i class="fas fa-folder"></i> ${this.getCategoryText(article.category)}</span>
                            <span><i class="fas fa-signal"></i> ${this.getDifficultyText(article.difficulty)}</span>
                            <span><i class="fas fa-eye"></i> ${article.views || 0} 浏览</span>
                            <span><i class="fas fa-clock"></i> ${ds.formatDate(article.updated_at)}</span>
                        </div>
                        <div class="text-secondary">${article.summary || '暂无摘要'}</div>
                        ${article.tags && article.tags.length > 0 ? `
                            <div class="knowledge-tags">
                                ${article.tags.map(tag => `<span class="knowledge-tag">${tag}</span>`).join('')}
                            </div>
                        ` : ''}
                        <div class="knowledge-actions">
                            <button class="btn btn-outline btn-sm" onclick="designKnowledgePage.viewArticle('${article.id}')">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="designKnowledgePage.editArticle('${article.id}')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="designKnowledgePage.shareArticle('${article.id}')">
                                <i class="fas fa-share"></i> 分享
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            renderArticlesPagination(pagination) {
                const paginationEl = document.getElementById('articles-pagination');
                const { current_page = 1, total_pages = 1, total = 0 } = pagination;

                // 更新统计信息
                const start = (current_page - 1) * this.pageSize + 1;
                const end = Math.min(current_page * this.pageSize, total);

                document.getElementById('articles-page-start').textContent = total > 0 ? start : 0;
                document.getElementById('articles-page-end').textContent = end;
                document.getElementById('articles-total-count').textContent = total;

                // 生成分页按钮
                let paginationHTML = '';

                if (current_page > 1) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="designKnowledgePage.loadArticles(${current_page - 1})">上一页</button>`;
                }

                const startPage = Math.max(1, current_page - 2);
                const endPage = Math.min(total_pages, current_page + 2);

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === current_page ? 'btn-primary' : 'btn-outline';
                    paginationHTML += `<button class="btn ${isActive} btn-sm" onclick="designKnowledgePage.loadArticles(${i})">${i}</button>`;
                }

                if (current_page < total_pages) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="designKnowledgePage.loadArticles(${current_page + 1})">下一页</button>`;
                }

                paginationEl.innerHTML = paginationHTML;
                this.currentPage = current_page;
            }

            async loadPopularArticles() {
                try {
                    const response = await api.get('/knowledge/articles/popular', { type: 'design', limit: 10 });
                    const articles = response.data || [];
                    this.renderPopularArticles(articles);
                } catch (error) {
                    console.error('加载热门文章失败:', error);
                }
            }

            renderPopularArticles(articles) {
                const container = document.getElementById('popular-articles');

                if (articles.length === 0) {
                    container.innerHTML = '<div class="text-center text-secondary">暂无热门文章</div>';
                    return;
                }

                container.innerHTML = articles.map((article, index) => `
                    <div class="knowledge-item">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-semibold">
                                ${index + 1}
                            </div>
                            <div class="flex-1">
                                <div class="knowledge-title">${article.title}</div>
                                <div class="knowledge-meta">
                                    <span><i class="fas fa-eye"></i> ${article.views || 0} 浏览</span>
                                    <span><i class="fas fa-thumbs-up"></i> ${article.likes || 0} 点赞</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            async loadRecentArticles() {
                try {
                    const response = await api.get('/knowledge/articles/recent', { type: 'design', limit: 10 });
                    const articles = response.data || [];
                    this.renderRecentArticles(articles);
                } catch (error) {
                    console.error('加载最近文章失败:', error);
                }
            }

            renderRecentArticles(articles) {
                const container = document.getElementById('recent-articles');

                if (articles.length === 0) {
                    container.innerHTML = '<div class="text-center text-secondary">暂无最近更新</div>';
                    return;
                }

                container.innerHTML = articles.map(article => `
                    <div class="knowledge-item">
                        <div class="knowledge-title">${article.title}</div>
                        <div class="knowledge-meta">
                            <span><i class="fas fa-clock"></i> ${ds.formatDate(article.updated_at)}</span>
                            <span><i class="fas fa-user"></i> ${article.author || '系统'}</span>
                        </div>
                        <div class="text-secondary">${article.summary || '暂无摘要'}</div>
                    </div>
                `).join('');
            }

        .logo-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 12px 0;
        }

        .nav-section {
            margin-bottom: 16px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 4px 16px;
            margin: 4px 8px 2px;
            background: rgba(107, 114, 128, 0.08);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 3px;
            border-left: 2px solid rgba(107, 114, 128, 0.3);
            line-height: 1.2;
        }

                .nav-item {
            display: block;
            padding: 10px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 14px;
            line-height: 1.4;
            font-weight: 400;
        }

        .nav-item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
            font-weight: 500;
        }

        .nav-section-title {
            font-size: 11px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 8px 20px 4px;
            margin-top: 16px;
            line-height: 1.4;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 8px;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
        }

        

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        .knowledge-container {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 32px;
        }

        .knowledge-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .knowledge-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #000;
            color: white;
        }

        .btn-primary:hover {
            background: #374151;
        }

        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .knowledge-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: #f9fafb;
            transition: all 0.2s;
        }

        .knowledge-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .knowledge-card-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .knowledge-card-desc {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .knowledge-card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #9ca3af;
        }

        .tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .tag-design {
            background: #dbeafe;
            color: #1e40af;
        }

        .tag-guide {
            background: #d1fae5;
            color: #065f46;
        }

        .tag-case {
            background: #fef3c7;
            color: #92400e;
        }

                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="design-knowledge.html" class="nav-item active">设计知识库</a>
                    <a href="delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">设计知识库</h1>
                <p class="page-subtitle">智能家居设计相关的知识文档和指南</p>
            </div>

            <div class="knowledge-container">
                <div class="knowledge-header">
                    <h2 class="knowledge-title">知识文档</h2>
                    <button class="btn btn-primary">+ 新增文档</button>
                </div>

                <div class="knowledge-grid">
                    <div class="knowledge-card">
                        <div class="knowledge-card-title">全屋智能设计规范</div>
                        <div class="knowledge-card-desc">包含智能家居系统设计的基本原则、布局要求和技术标准</div>
                        <div class="knowledge-card-meta">
                            <span class="tag tag-design">设计规范</span>
                            <span>更新时间: 2025-01-20</span>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <div class="knowledge-card-title">户型优化设计指南</div>
                        <div class="knowledge-card-desc">针对不同户型的空间优化设计方法和实践案例</div>
                        <div class="knowledge-card-meta">
                            <span class="tag tag-guide">设计指南</span>
                            <span>更新时间: 2025-01-18</span>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <div class="knowledge-card-title">智能照明设计案例</div>
                        <div class="knowledge-card-desc">经典的智能照明设计案例分析和设计要点总结</div>
                        <div class="knowledge-card-meta">
                            <span class="tag tag-case">设计案例</span>
                            <span>更新时间: 2025-01-15</span>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <div class="knowledge-card-title">设备选型指导</div>
                        <div class="knowledge-card-desc">智能家居设备的选型原则、性能对比和推荐方案</div>
                        <div class="knowledge-card-meta">
                            <span class="tag tag-guide">选型指南</span>
                            <span>更新时间: 2025-01-12</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 补充JavaScript功能 -->
    <script>
        // 继续添加剩余的JavaScript方法
        if (typeof designKnowledgePage !== 'undefined') {
            // 添加剩余方法到现有类
            Object.assign(designKnowledgePage.constructor.prototype, {
                async createArticle() {
                    try {
                        const formData = {
                            title: document.getElementById('article-title').value,
                            category: document.getElementById('article-category').value,
                            difficulty: document.getElementById('article-difficulty').value,
                            summary: document.getElementById('article-summary').value,
                            content: document.getElementById('article-content').value,
                            tags: document.getElementById('article-tags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                            type: 'design'
                        };

                        const response = await api.post('/knowledge/articles', formData);

                        if (response.success) {
                            ds.showToast('文章发布成功', 'success');
                            ds.closeModal();
                            this.loadArticles(1);
                            this.loadStatistics();

                            // 重置表单
                            document.getElementById('new-article-form').reset();
                        } else {
                            ds.showToast(response.message || '发布失败', 'error');
                        }

                    } catch (error) {
                        console.error('发布文章失败:', error);
                        ds.showToast('发布文章失败，请重试', 'error');
                    }
                },

                searchKnowledge() {
                    const searchTerm = document.getElementById('search-knowledge').value.trim();
                    const category = document.getElementById('category-filter').value;
                    const difficulty = document.getElementById('difficulty-filter').value;

                    this.currentFilters = {};

                    if (searchTerm) this.currentFilters.search = searchTerm;
                    if (category) this.currentFilters.category = category;
                    if (difficulty) this.currentFilters.difficulty = difficulty;

                    this.loadArticles(1);
                },

                resetKnowledgeFilters() {
                    document.getElementById('search-knowledge').value = '';
                    document.getElementById('category-filter').value = '';
                    document.getElementById('difficulty-filter').value = '';

                    this.currentFilters = {};
                    this.loadArticles(1);
                },

                viewArticle(articleId) {
                    // 打开文章详情页面或模态框
                    window.open(`/knowledge/article/${articleId}`, '_blank');
                },

                editArticle(articleId) {
                    const article = this.articles.find(a => a.id === articleId);
                    if (article) {
                        // 填充表单数据
                        document.getElementById('article-title').value = article.title;
                        document.getElementById('article-category').value = article.category;
                        document.getElementById('article-difficulty').value = article.difficulty || 'beginner';
                        document.getElementById('article-summary').value = article.summary || '';
                        document.getElementById('article-content').value = article.content || '';
                        document.getElementById('article-tags').value = article.tags ? article.tags.join(', ') : '';
                        
                        // 设置编辑模式
                        const form = document.getElementById('new-article-form');
                        form.dataset.editMode = 'true';
                        form.dataset.editId = articleId;
                        
                        // 修改模态框标题
                        document.querySelector('#new-article-modal .modal-title').textContent = '编辑知识文章';
                        document.querySelector('#new-article-form button[type="submit"]').textContent = '保存修改';
                        
                        // 显示模态框
                        ds.openModal('new-article-modal');
                    }
                },

                shareArticle(articleId) {
                    // 复制分享链接到剪贴板
                    const shareUrl = `${window.location.origin}/knowledge/article/${articleId}`;
                    navigator.clipboard.writeText(shareUrl).then(() => {
                        ds.showToast('分享链接已复制到剪贴板', 'success');
                    }).catch(() => {
                        ds.showToast('复制失败，请手动复制链接', 'error');
                    });
                },

                // 工具方法
                getCategoryText(category) {
                    const texts = {
                        'design-principles': '设计原则',
                        'color-theory': '色彩理论',
                        'space-planning': '空间规划',
                        'lighting-design': '照明设计',
                        'material-selection': '材料选择'
                    };
                    return texts[category] || category;
                },

                getDifficultyText(difficulty) {
                    const texts = {
                        'beginner': '初级',
                        'intermediate': '中级',
                        'advanced': '高级'
                    };
                    return texts[difficulty] || difficulty;
                }
            });
        }

        // 全局函数
        function searchKnowledge() {
            if (typeof designKnowledgePage !== 'undefined') {
                designKnowledgePage.searchKnowledge();
            }
        }

        function resetKnowledgeFilters() {
            if (typeof designKnowledgePage !== 'undefined') {
                designKnowledgePage.resetKnowledgeFilters();
            }
        }

        function exportKnowledge() {
            if (typeof designKnowledgePage !== 'undefined' && designKnowledgePage.articles.length > 0) {
                // 导出所有文章为JSON格式
                const dataStr = JSON.stringify(designKnowledgePage.articles, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `design_knowledge_export_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                ds.showToast('导出成功', 'success');
            } else {
                ds.showToast('暂无数据可导出', 'warning');
            }
        }

        function addNewArticle() {
            // 重置表单
            const form = document.getElementById('new-article-form');
            form.reset();
            delete form.dataset.editMode;
            delete form.dataset.editId;
            
            // 重置模态框标题
            document.querySelector('#new-article-modal .modal-title').textContent = '新增知识文章';
            document.querySelector('#new-article-form button[type="submit"]').textContent = '发布文章';
            
            ds.openModal('new-article-modal');
        }

        function viewCategory(categoryId) {
            // 切换到文章标签页并筛选该分类
            ds.switchTab('design-knowledge', 'articles');
            document.getElementById('category-filter').value = categoryId;
            if (typeof designKnowledgePage !== 'undefined') {
                designKnowledgePage.searchKnowledge();
            }
        }

        // 初始化页面（如果还没有初始化）
        let designKnowledgePage;
        if (typeof designKnowledgePage === 'undefined') {
            designKnowledgePage = new DesignKnowledgePage();
        }

        // 表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('new-article-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // 获取表单数据
                    const formData = new FormData(form);
                    const articleData = {
                        title: document.getElementById('article-title').value.trim(),
                        category: document.getElementById('article-category').value,
                        difficulty: document.getElementById('article-difficulty').value,
                        summary: document.getElementById('article-summary').value.trim(),
                        content: document.getElementById('article-content').value.trim(),
                        tags: document.getElementById('article-tags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    };
                    
                    // 基本验证
                    if (!articleData.title) {
                        ds.showToast('请输入文章标题', 'error');
                        return;
                    }
                    
                    if (!articleData.category) {
                        ds.showToast('请选择知识分类', 'error');
                        return;
                    }
                    
                    if (!articleData.content) {
                        ds.showToast('请输入文章内容', 'error');
                        return;
                    }
                    
                    // 检查是否为编辑模式
                    const isEditMode = form.dataset.editMode === 'true';
                    const editId = form.dataset.editId;
                    
                    if (isEditMode && editId) {
                        // 编辑现有文章
                        const index = designKnowledgePage.articles.findIndex(a => a.id === editId);
                        if (index !== -1) {
                            // 更新文章数据，保持原有的id和创建时间
                            designKnowledgePage.articles[index] = {
                                ...designKnowledgePage.articles[index],
                                ...articleData,
                                updated_at: new Date().toISOString(),
                                author: '当前用户'
                            };
                            ds.showToast('文章更新成功', 'success');
                        }
                    } else {
                        // 新增文章
                        const newArticle = {
                            id: 'article_' + Date.now(),
                            ...articleData,
                            author: '当前用户',
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                            views: 0,
                            likes: 0
                        };
                        
                        designKnowledgePage.articles.unshift(newArticle);
                        ds.showToast('文章发布成功', 'success');
                    }
                    
                    // 重新渲染文章列表
                    designKnowledgePage.loadArticles(1);
                    
                    // 关闭模态框
                    ds.closeModal();
                    
                    // 重置表单
                    form.reset();
                    delete form.dataset.editMode;
                    delete form.dataset.editId;
                });
            }
        });
    </script>

    <!-- 模态框样式 -->
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: var(--z-modal, 1000);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.modal-open {
            opacity: 1;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            z-index: 1;
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        .modal.modal-open .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .grid {
            display: grid;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(4, 1fr);
        }

        .gap-4 {
            gap: 16px;
        }

        .gap-6 {
            gap: 24px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-content {
                margin: 16px;
                max-width: calc(100vw - 32px);
            }

            .grid-cols-2,
            .grid-cols-4 {
                grid-template-columns: 1fr;
            }

            .knowledge-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>

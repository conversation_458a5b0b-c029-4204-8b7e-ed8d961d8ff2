/**
 * 需求管理选项卡流程管理器
 * 实现选项卡间的顺序切换逻辑：需求列表 → 新建需求 → 产品选择 → 图纸管理
 * 
 * 功能特性：
 * - 自动激活和切换逻辑
 * - 数据自动保存
 * - 进度指示器
 * - 导航按钮控制
 * - 最终提交和返回逻辑
 */

class TabFlowManager {
    constructor() {
        // 选项卡流程配置
        this.config = {
            tabs: [
                {
                    id: 'requirements-list',
                    name: '需求列表',
                    icon: 'fas fa-list',
                    required: false,
                    validator: null
                },
                {
                    id: 'new-requirement',
                    name: '新建需求',
                    icon: 'fas fa-plus-circle',
                    required: true,
                    validator: 'validateNewRequirementForm'
                },
                {
                    id: 'product-selection',
                    name: '产品选择',
                    icon: 'fas fa-cube',
                    required: true,
                    validator: 'validateProductSelection'
                },
                {
                    id: 'drawing-management',
                    name: '图纸管理',
                    icon: 'fas fa-file-image',
                    required: false,
                    validator: null
                }
            ]
        };

        // 流程状态
        this.state = {
            isActive: false,
            currentStep: 0,
            completedSteps: new Set(),
            data: {},
            autoSave: true,
            navigationEnabled: true
        };

        // 事件监听器
        this.eventListeners = {
            onStepChange: [],
            onValidation: [],
            onDataSave: [],
            onComplete: []
        };

        this.init();
    }

    /**
     * 初始化流程管理器
     */
    init() {
        this.createProgressIndicator();
        this.createNavigationButtons();
        this.bindEvents();
        this.loadSavedState();
        
        console.log('🚀 选项卡流程管理器初始化完成');
    }

    /**
     * 创建进度指示器
     */
    createProgressIndicator() {
        const progressHTML = `
            <div id="tab-progress-indicator" class="tab-progress-container" style="display: none;">
                <div class="progress-header">
                    <h3>需求创建流程</h3>
                    <div class="progress-stats">
                        <span id="current-step-info">步骤 1 / ${this.config.tabs.length}</span>
                    </div>
                </div>
                <div class="progress-steps">
                    ${this.config.tabs.map((tab, index) => `
                        <div class="progress-step" data-step="${index}" data-tab-id="${tab.id}">
                            <div class="step-indicator">
                                <div class="step-number">${index + 1}</div>
                                <div class="step-icon">
                                    <i class="${tab.icon}"></i>
                                </div>
                            </div>
                            <div class="step-label">${tab.name}</div>
                            <div class="step-status">
                                <i class="fas fa-check completed-icon"></i>
                                <i class="fas fa-exclamation-triangle warning-icon"></i>
                            </div>
                        </div>
                        ${index < this.config.tabs.length - 1 ? '<div class="step-connector"></div>' : ''}
                    `).join('')}
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-percentage" id="progress-percentage">0%</div>
                </div>
            </div>
        `;

        // 插入进度指示器到页面
        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
            contentArea.insertAdjacentHTML('afterbegin', progressHTML);
        }

        this.addProgressStyles();
    }

    /**
     * 创建导航按钮
     */
    createNavigationButtons() {
        const navigationHTML = `
            <div id="tab-navigation-controls" class="tab-navigation-container" style="display: none;">
                <div class="navigation-buttons">
                    <button id="prev-step-btn" class="nav-btn prev-btn" disabled>
                        <i class="fas fa-chevron-left"></i>
                        上一步
                    </button>
                    <button id="save-draft-btn" class="nav-btn save-btn">
                        <i class="fas fa-save"></i>
                        保存草稿
                    </button>
                    <button id="next-step-btn" class="nav-btn next-btn">
                        下一步
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="complete-flow-btn" class="nav-btn complete-btn" style="display: none;">
                        <i class="fas fa-check-circle"></i>
                        完成创建
                    </button>
                    <button id="cancel-flow-btn" class="nav-btn cancel-btn">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                </div>
                <div class="navigation-info">
                    <div class="auto-save-status" id="auto-save-status">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>自动保存已开启</span>
                    </div>
                </div>
            </div>
        `;

        // 插入导航按钮到页面底部
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.insertAdjacentHTML('beforeend', navigationHTML);
        }

        this.addNavigationStyles();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 导航按钮事件
        document.getElementById('prev-step-btn')?.addEventListener('click', () => this.previousStep());
        document.getElementById('next-step-btn')?.addEventListener('click', () => this.nextStep());
        document.getElementById('save-draft-btn')?.addEventListener('click', () => this.saveDraft());
        document.getElementById('complete-flow-btn')?.addEventListener('click', () => this.completeFlow());
        document.getElementById('cancel-flow-btn')?.addEventListener('click', () => this.cancelFlow());

        // 进度步骤点击事件
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            step.addEventListener('click', () => this.goToStep(index));
        });

        // 自动保存监听器
        if (this.state.autoSave) {
            this.setupAutoSave();
        }
    }

    /**
     * 开始流程
     */
    startFlow(startStep = 1) {
        this.state.isActive = true;
        this.state.currentStep = startStep;
        
        // 显示进度指示器和导航按钮
        document.getElementById('tab-progress-indicator').style.display = 'block';
        document.getElementById('tab-navigation-controls').style.display = 'block';
        
        // 切换到指定步骤
        this.goToStep(startStep);
        
        // 更新UI状态
        this.updateProgressDisplay();
        this.updateNavigationButtons();
        
        // 触发事件
        this.trigger('onStepChange', { step: startStep, tab: this.config.tabs[startStep] });
        
        console.log(`🎯 开始需求创建流程，当前步骤: ${startStep + 1}`);
    }

    /**
     * 下一步
     */
    async nextStep() {
        if (!this.canGoToNext()) {
            return;
        }

        // 验证当前步骤
        const isValid = await this.validateCurrentStep();
        if (!isValid) {
            this.showValidationError();
            return;
        }

        // 保存当前步骤数据
        await this.saveCurrentStepData();
        
        // 标记当前步骤为完成
        this.state.completedSteps.add(this.state.currentStep);
        
        // 移动到下一步
        if (this.state.currentStep < this.config.tabs.length - 1) {
            this.state.currentStep++;
            this.goToStep(this.state.currentStep);
        } else {
            // 最后一步，显示完成按钮
            this.showCompleteButton();
        }
    }

    /**
     * 上一步
     */
    previousStep() {
        if (!this.canGoToPrevious()) {
            return;
        }

        // 保存当前数据
        this.saveCurrentStepData();
        
        if (this.state.currentStep > 0) {
            this.state.currentStep--;
            this.goToStep(this.state.currentStep);
        } else {
            // 返回需求列表
            this.cancelFlow();
        }
    }

    /**
     * 跳转到指定步骤
     */
    async goToStep(stepIndex) {
        if (stepIndex < 0 || stepIndex >= this.config.tabs.length) {
            console.warn(`⚠️ 无效的步骤索引: ${stepIndex}`);
            return;
        }

        const tab = this.config.tabs[stepIndex];
        
        // 保存当前数据
        if (this.state.isActive) {
            await this.saveCurrentStepData();
        }
        
        // 更新状态
        this.state.currentStep = stepIndex;
        
        // 切换选项卡
        this.switchTab(tab.id);
        
        // 更新UI
        this.updateProgressDisplay();
        this.updateNavigationButtons();
        
        // 触发事件
        this.trigger('onStepChange', { step: stepIndex, tab });
        
        console.log(`📍 切换到步骤 ${stepIndex + 1}: ${tab.name}`);
    }

    /**
     * 切换选项卡
     */
    switchTab(tabId) {
        // 隐藏所有模块
        document.querySelectorAll('.module-content').forEach(module => {
            module.classList.remove('active');
            module.style.display = 'none';
        });

        // 显示目标模块
        const targetModule = document.getElementById(tabId);
        if (targetModule) {
            targetModule.classList.add('active');
            targetModule.style.display = 'block';
        }

        // 更新选项卡激活状态
        this.updateTabActiveState(tabId);
        
        // 特殊处理
        this.handleSpecialTabLogic(tabId);
    }

    /**
     * 更新选项卡激活状态
     */
    updateTabActiveState(activeTabId) {
        const cards = document.querySelectorAll('.module-card');
        const moduleCardMap = {
            'requirements-list': 0,
            'new-requirement': 1,
            'product-selection': 2,
            'drawing-management': 3
        };

        cards.forEach((card, index) => {
            card.classList.remove('active', 'completed', 'disabled');
            
            if (index === moduleCardMap[activeTabId]) {
                card.classList.add('active');
            } else if (this.state.completedSteps.has(index)) {
                card.classList.add('completed');
            } else if (this.state.isActive && index > this.state.currentStep) {
                card.classList.add('disabled');
            }
        });
    }

    /**
     * 验证当前步骤
     */
    async validateCurrentStep() {
        const currentTab = this.config.tabs[this.state.currentStep];
        
        if (!currentTab.required || !currentTab.validator) {
            return true;
        }

        try {
            const validatorFunction = window[currentTab.validator];
            if (typeof validatorFunction === 'function') {
                const result = await validatorFunction();
                this.trigger('onValidation', { step: this.state.currentStep, valid: result });
                return result;
            }
        } catch (error) {
            console.error(`❌ 验证步骤 ${this.state.currentStep + 1} 时出错:`, error);
        }
        
        return false;
    }

    /**
     * 保存当前步骤数据
     */
    async saveCurrentStepData() {
        const currentTab = this.config.tabs[this.state.currentStep];
        const tabData = this.extractTabData(currentTab.id);
        
        this.state.data[currentTab.id] = tabData;
        
        // 保存到本地存储
        if (this.state.autoSave) {
            this.saveToStorage();
        }
        
        // 触发保存事件
        this.trigger('onDataSave', { step: this.state.currentStep, data: tabData });
        
        // 更新自动保存状态
        this.updateAutoSaveStatus('已保存');
    }

    /**
     * 提取选项卡数据
     */
    extractTabData(tabId) {
        const data = {};
        
        switch (tabId) {
            case 'new-requirement':
                data.title = document.getElementById('requirement-title')?.value || '';
                data.description = document.getElementById('requirement-description')?.value || '';
                data.priority = document.querySelector('input[name="priority"]:checked')?.value || 'medium';
                data.deadline = document.getElementById('requirement-deadline')?.value || '';
                data.budget = document.getElementById('requirement-budget')?.value || '';
                data.contact = document.getElementById('requirement-contact')?.value || '';
                break;
                
            case 'product-selection':
                data.selectedProducts = this.getSelectedProducts();
                data.selectedScenes = this.getSelectedScenes();
                data.customization = document.getElementById('customization-notes')?.value || '';
                break;
                
            case 'drawing-management':
                data.uploadedFiles = this.getUploadedFiles();
                data.drawingNotes = document.getElementById('drawing-notes')?.value || '';
                break;
        }
        
        data.timestamp = new Date().toISOString();
        return data;
    }

    /**
     * 完成流程
     */
    async completeFlow() {
        // 最终验证
        const isValid = await this.validateAllSteps();
        if (!isValid) {
            this.showError('请完成所有必填步骤');
            return;
        }

        // 保存最终数据
        await this.saveCurrentStepData();
        
        // 提交数据
        const success = await this.submitRequirement();
        
        if (success) {
            // 触发完成事件
            this.trigger('onComplete', { data: this.state.data });
            
            // 清理状态
            this.cleanup();
            
            // 返回需求列表
            this.switchTab('requirements-list');
            
            // 显示成功消息
            this.showSuccess('需求创建成功！');
        } else {
            this.showError('提交失败，请重试');
        }
    }

    /**
     * 取消流程
     */
    cancelFlow() {
        if (this.state.isActive && Object.keys(this.state.data).length > 0) {
            const confirmed = confirm('当前有未保存的数据，确定要取消吗？');
            if (!confirmed) {
                return;
            }
        }
        
        // 清理状态
        this.cleanup();
        
        // 返回需求列表
        this.switchTab('requirements-list');
        
        console.log('🚫 流程已取消');
    }

    /**
     * 清理状态
     */
    cleanup() {
        this.state.isActive = false;
        this.state.currentStep = 0;
        this.state.completedSteps.clear();
        this.state.data = {};
        
        // 隐藏UI组件
        document.getElementById('tab-progress-indicator').style.display = 'none';
        document.getElementById('tab-navigation-controls').style.display = 'none';
        
        // 重置选项卡状态
        document.querySelectorAll('.module-card').forEach(card => {
            card.classList.remove('active', 'completed', 'disabled');
        });
        
        // 清理本地存储
        this.clearStorage();
    }

    /**
     * 更新进度显示
     */
    updateProgressDisplay() {
        // 更新步骤指示器
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            step.classList.remove('active', 'completed');
            
            if (index === this.state.currentStep) {
                step.classList.add('active');
            } else if (this.state.completedSteps.has(index)) {
                step.classList.add('completed');
            }
        });
        
        // 更新进度条
        const progress = ((this.state.currentStep + 1) / this.config.tabs.length) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;
        document.getElementById('progress-percentage').textContent = `${Math.round(progress)}%`;
        
        // 更新步骤信息
        document.getElementById('current-step-info').textContent = 
            `步骤 ${this.state.currentStep + 1} / ${this.config.tabs.length}`;
    }

    /**
     * 更新导航按钮状态
     */
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prev-step-btn');
        const nextBtn = document.getElementById('next-step-btn');
        const completeBtn = document.getElementById('complete-flow-btn');
        
        // 上一步按钮
        if (prevBtn) {
            prevBtn.disabled = !this.canGoToPrevious();
        }
        
        // 下一步按钮
        if (nextBtn) {
            if (this.state.currentStep >= this.config.tabs.length - 1) {
                nextBtn.style.display = 'none';
                completeBtn.style.display = 'inline-flex';
            } else {
                nextBtn.style.display = 'inline-flex';
                completeBtn.style.display = 'none';
                nextBtn.disabled = !this.canGoToNext();
            }
        }
    }

    /**
     * 检查是否可以前进
     */
    canGoToNext() {
        return this.state.isActive && 
               this.state.currentStep < this.config.tabs.length - 1 &&
               this.state.navigationEnabled;
    }

    /**
     * 检查是否可以后退
     */
    canGoToPrevious() {
        return this.state.isActive && 
               this.state.currentStep >= 0 &&
               this.state.navigationEnabled;
    }

    // 事件系统
    on(eventName, callback) {
        if (this.eventListeners[eventName]) {
            this.eventListeners[eventName].push(callback);
        }
    }

    trigger(eventName, data) {
        if (this.eventListeners[eventName]) {
            this.eventListeners[eventName].forEach(callback => callback(data));
        }
    }

    // 存储相关方法
    saveToStorage() {
        const state = {
            isActive: this.state.isActive,
            currentStep: this.state.currentStep,
            completedSteps: Array.from(this.state.completedSteps),
            data: this.state.data,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('tab-flow-state', JSON.stringify(state));
    }

    loadSavedState() {
        try {
            const saved = localStorage.getItem('tab-flow-state');
            if (saved) {
                const state = JSON.parse(saved);
                this.state.isActive = state.isActive || false;
                this.state.currentStep = state.currentStep || 0;
                this.state.completedSteps = new Set(state.completedSteps || []);
                this.state.data = state.data || {};
                
                if (this.state.isActive) {
                    this.startFlow(this.state.currentStep);
                }
            }
        } catch (error) {
            console.error('❌ 加载保存状态失败:', error);
        }
    }

    clearStorage() {
        localStorage.removeItem('tab-flow-state');
    }

    // 样式添加方法
    addProgressStyles() {
        const styles = `
            <style id="tab-progress-styles">
                .tab-progress-container {
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    margin: 20px 0;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    border: 1px solid #e5e7eb;
                }
                
                .progress-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                }
                
                .progress-header h3 {
                    margin: 0;
                    color: #1f2937;
                    font-weight: 600;
                }
                
                .progress-stats {
                    color: #6b7280;
                    font-size: 14px;
                }
                
                .progress-steps {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                
                .progress-step {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    padding: 10px;
                    border-radius: 8px;
                }
                
                .progress-step:hover {
                    background: #f9fafb;
                }
                
                .step-indicator {
                    position: relative;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: #f3f4f6;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 8px;
                    transition: all 0.3s ease;
                }
                
                .progress-step.active .step-indicator {
                    background: #3b82f6;
                    color: white;
                }
                
                .progress-step.completed .step-indicator {
                    background: #10b981;
                    color: white;
                }
                
                .step-number {
                    font-weight: 600;
                    font-size: 14px;
                }
                
                .step-icon {
                    display: none;
                    font-size: 16px;
                }
                
                .progress-step.active .step-icon,
                .progress-step.completed .step-icon {
                    display: block;
                }
                
                .progress-step.active .step-number,
                .progress-step.completed .step-number {
                    display: none;
                }
                
                .step-label {
                    font-size: 12px;
                    text-align: center;
                    color: #6b7280;
                    font-weight: 500;
                }
                
                .progress-step.active .step-label {
                    color: #3b82f6;
                    font-weight: 600;
                }
                
                .step-connector {
                    flex: 1;
                    height: 2px;
                    background: #e5e7eb;
                    margin: 0 10px;
                }
                
                .progress-bar-container {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                
                .progress-bar {
                    flex: 1;
                    height: 6px;
                    background: #f3f4f6;
                    border-radius: 3px;
                    overflow: hidden;
                }
                
                .progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                    transition: width 0.5s ease;
                }
                
                .progress-percentage {
                    font-size: 14px;
                    font-weight: 600;
                    color: #3b82f6;
                    min-width: 40px;
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }

    addNavigationStyles() {
        const styles = `
            <style id="tab-navigation-styles">
                .tab-navigation-container {
                    position: fixed;
                    bottom: 0;
                    left: 200px;
                    right: 0;
                    background: white;
                    border-top: 1px solid #e5e7eb;
                    padding: 16px 24px;
                    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
                    z-index: 998;
                }
                
                .navigation-buttons {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    justify-content: center;
                }
                
                .nav-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    text-decoration: none;
                }
                
                .nav-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .prev-btn {
                    background: #f3f4f6;
                    color: #374151;
                }
                
                .prev-btn:hover:not(:disabled) {
                    background: #e5e7eb;
                }
                
                .next-btn, .complete-btn {
                    background: #3b82f6;
                    color: white;
                }
                
                .next-btn:hover:not(:disabled), .complete-btn:hover:not(:disabled) {
                    background: #2563eb;
                }
                
                .save-btn {
                    background: #10b981;
                    color: white;
                }
                
                .save-btn:hover {
                    background: #059669;
                }
                
                .cancel-btn {
                    background: #ef4444;
                    color: white;
                }
                
                .cancel-btn:hover {
                    background: #dc2626;
                }
                
                .navigation-info {
                    display: flex;
                    justify-content: center;
                    margin-top: 8px;
                }
                
                .auto-save-status {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    color: #6b7280;
                }
                
                .auto-save-status i {
                    color: #10b981;
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 设置自动保存
     */
    setupAutoSave() {
        // 每30秒自动保存
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setInterval(() => {
            if (this.state.isActive) {
                this.saveCurrentStepData();
                this.updateAutoSaveStatus('已自动保存');
                
                setTimeout(() => {
                    this.updateAutoSaveStatus('自动保存已开启');
                }, 2000);
            }
        }, 30000);
        
        console.log('🔄 自动保存已启用 (30秒间隔)');
    }

    /**
     * 保存草稿
     */
    saveDraft() {
        if (this.state.isActive) {
            this.saveCurrentStepData();
            this.updateAutoSaveStatus('草稿已保存');
            this.showSuccess('草稿已保存');
            
            setTimeout(() => {
                this.updateAutoSaveStatus('自动保存已开启');
            }, 3000);
        }
    }

    /**
     * 获取选中的产品
     */
    getSelectedProducts() {
        const selectedProducts = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
            const productCard = checkbox.closest('.product-card');
            if (productCard) {
                selectedProducts.push({
                    id: checkbox.value,
                    name: productCard.querySelector('.product-name')?.textContent || '',
                    category: productCard.dataset.category || '',
                    price: productCard.dataset.price || ''
                });
            }
        });
        return selectedProducts;
    }

    /**
     * 获取选中的场景
     */
    getSelectedScenes() {
        const selectedScenes = [];
        document.querySelectorAll('.scene-checkbox:checked').forEach(checkbox => {
            const sceneCard = checkbox.closest('.scene-card');
            if (sceneCard) {
                selectedScenes.push({
                    id: checkbox.value,
                    name: sceneCard.querySelector('.scene-name')?.textContent || '',
                    description: sceneCard.querySelector('.scene-description')?.textContent || ''
                });
            }
        });
        return selectedScenes;
    }

    /**
     * 获取上传的文件
     */
    getUploadedFiles() {
        const uploadedFiles = [];
        document.querySelectorAll('.uploaded-file-item').forEach(fileItem => {
            if (fileItem.classList.contains('upload-complete')) {
                uploadedFiles.push({
                    name: fileItem.dataset.filename || '',
                    size: fileItem.dataset.filesize || '',
                    type: fileItem.dataset.filetype || '',
                    url: fileItem.dataset.fileurl || ''
                });
            }
        });
        return uploadedFiles;
    }

    /**
     * 验证所有步骤
     */
    async validateAllSteps() {
        for (let i = 0; i < this.config.tabs.length; i++) {
            const tab = this.config.tabs[i];
            if (tab.required && tab.validator) {
                try {
                    const validatorFunction = window[tab.validator];
                    if (typeof validatorFunction === 'function') {
                        const result = await validatorFunction();
                        if (!result) {
                            console.log(`❌ 步骤 ${i + 1} (${tab.name}) 验证失败`);
                            return false;
                        }
                    }
                } catch (error) {
                    console.error(`❌ 验证步骤 ${i + 1} 时出错:`, error);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 提交需求
     */
    async submitRequirement() {
        try {
            // 模拟提交过程
            console.log('📤 正在提交需求数据...');
            console.log('📋 需求数据:', this.state.data);
            
            // 模拟网络请求延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 这里应该调用实际的API提交数据
            // const response = await fetch('/api/requirements', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(this.state.data)
            // });
            // return response.ok;
            
            return true; // 模拟成功
        } catch (error) {
            console.error('❌ 提交需求失败:', error);
            return false;
        }
    }

    /**
     * 显示完成按钮
     */
    showCompleteButton() {
        const nextBtn = document.getElementById('next-step-btn');
        const completeBtn = document.getElementById('complete-flow-btn');
        
        if (nextBtn) nextBtn.style.display = 'none';
        if (completeBtn) completeBtn.style.display = 'inline-flex';
    }

    /**
     * 处理特殊选项卡逻辑
     */
    handleSpecialTabLogic(tabId) {
        switch (tabId) {
            case 'requirements-list':
                // 刷新需求列表
                if (typeof renderTable === 'function') {
                    renderTable();
                }
                break;
                
            case 'product-selection':
                // 初始化产品选择
                if (typeof initializeProductSelection === 'function') {
                    initializeProductSelection();
                }
                break;
                
            case 'drawing-management':
                // 初始化文件上传
                if (typeof initializeFileUpload === 'function') {
                    initializeFileUpload();
                }
                break;
        }
    }

    // 工具方法
    showSuccess(message) {
        // 实现成功提示
        console.log(`✅ ${message}`);
    }

    showError(message) {
        // 实现错误提示
        console.error(`❌ ${message}`);
    }

    showValidationError() {
        this.showError('请完成当前步骤的必填信息');
    }

    updateAutoSaveStatus(status) {
        const statusElement = document.querySelector('#auto-save-status span');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }
}

// 导出全局实例
window.TabFlowManager = TabFlowManager;
window.tabFlowManager = new TabFlowManager();

// 与现有系统集成
window.startRequirementFlow = function() {
    window.tabFlowManager.startFlow(1); // 从新建需求开始
};

console.log('📋 选项卡流程管理器已加载');
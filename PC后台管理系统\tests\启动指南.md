# 前后端协同测试启动指南

## 🚀 快速开始

### 第一步：环境准备

确保您的开发环境已安装：
- **Node.js** (用于前端服务器)
- **Python** (可选，用于静态服务器)
- **后端API服务** (运行在端口8080)

### 第二步：启动后端服务

首先需要启动后端API服务器，确保运行在 `http://localhost:8080`

```bash
# 如果您有后端项目
cd 后端项目目录
npm start
# 或
python app.py
```

### 第三步：启动前端测试服务器

在PC后台管理系统目录下运行：

```powershell
# 使用Python启动
python -m http.server 3000

# 或使用Node.js启动
npx http-server -p 3000 -c-1

# 或运行预置脚本
.\启动测试服务器.ps1
```

### 第四步：打开测试控制台

在浏览器中访问：
```
http://localhost:3000/tests/测试控制台.html
```

### 第五步：执行测试

1. 检查测试环境配置（默认配置通常可用）
2. 点击 **🚀 运行完整测试** 开始全部测试
3. 或分别点击其他按钮进行单独测试：
   - **🔌 API接口测试**
   - **🎨 前端功能测试**
   - **🔄 端到端测试**

---

## 📋 测试项目说明

### API接口测试 (7项)
- ✅ 环境连通性检查
- ⚠️ 微信登录接口 (可能未实现)
- ⚠️ 用户信息更新 (可能未实现)
- ⚠️ 图纸上传 (可能未实现)
- ⚠️ 图纸列表 (可能未实现)
- ⚠️ 需求提交 (可能未实现)
- ⚠️ 管理员用户列表 (可能未实现)
- ⚠️ 管理员需求列表 (可能未实现)

### 前端功能测试 (3项)
- ✅ 页面加载测试
- ✅ 用户交互测试
- ✅ 数据绑定测试

### 端到端测试 (1项)
- ✅ 完整业务流程模拟

---

## 🔧 配置说明

测试控制台允许您配置：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| 后端API地址 | http://localhost:8080 | 后端服务器地址 |
| 前端服务地址 | http://localhost:3000 | 前端服务器地址 |
| 测试环境 | development | 测试环境类型 |

---

## 📊 测试结果说明

### 状态类型
- **等待中** - 测试尚未开始
- **测试中...** - 正在执行测试
- **✅ 成功** - 测试通过
- **❌ 失败** - 测试失败，通常是接口未实现

### 预期结果
由于这是前后端协同测试，许多API接口可能尚未实现，出现失败是正常的。重点关注：

1. **环境连通性** - 应该成功
2. **前端功能** - 应该全部成功
3. **API接口** - 失败表示需要开发

---

## 🐛 常见问题解决

### 问题1：无法启动服务器
**症状**：执行启动命令时报错
**解决**：
- 检查端口3000是否被占用
- 尝试使用其他端口：`python -m http.server 3001`
- 确保已安装Node.js或Python

### 问题2：CORS跨域错误
**症状**：浏览器控制台显示CORS错误
**解决**：
- 确保后端API配置了CORS
- 使用相同协议和端口
- 检查浏览器安全设置

### 问题3：所有API测试失败
**症状**：所有API测试都显示"接口未实现"
**解决**：
- 检查后端服务是否正常运行
- 确认API地址配置正确
- 查看浏览器网络面板确认请求

### 问题4：页面无法加载
**症状**：浏览器显示无法访问页面
**解决**：
- 确认前端服务器已启动
- 检查防火墙设置
- 尝试使用127.0.0.1而不是localhost

---

## 📞 技术支持

如遇到其他问题，请：

1. 查看浏览器控制台错误信息
2. 检查测试日志输出
3. 确认网络连接正常
4. 联系开发团队获取支持

---

## 🎯 测试重点

本次协同测试主要验证：

✅ **已验证项目**
- 前端页面正常加载和交互
- 测试框架运行正常
- 环境配置正确

⚠️ **待验证项目**
- 微信用户管理API接口
- 图纸上传下载功能
- 用户需求管理流程
- 前后端数据同步

🔄 **持续改进**
- 根据测试结果优化接口设计
- 完善错误处理机制
- 提升用户体验

---

**祝测试顺利！** 🎉 
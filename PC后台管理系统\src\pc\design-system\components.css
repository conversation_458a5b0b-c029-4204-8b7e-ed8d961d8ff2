/**
 * 智能家居管理系统 - 组件库
 * 基于设计令牌的现代化组件系统
 */

/* ========== 基础重置 ========== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  min-width: 1200px;
}

/* ========== 布局组件 ========== */

.admin-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.sidebar {
  width: var(--sidebar-width);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-primary);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: var(--z-fixed);
  transition: transform var(--duration-normal) var(--ease-in-out);
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  background: var(--bg-secondary);
  transition: margin-left var(--duration-normal) var(--ease-in-out);
}

.main-content.sidebar-collapsed {
  margin-left: var(--sidebar-width-collapsed);
}

.content-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6) var(--space-8);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.content-body {
  padding: var(--space-8);
  max-width: 100%;
}

/* ========== 卡片组件 ========== */

.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-in-out);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-primary);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--space-1);
}

.card-body {
  padding: var(--card-padding);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

/* ========== 按钮组件 ========== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: 0 var(--button-padding-x-md);
  height: var(--button-height-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-in-out);
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 按钮变体 */
.btn-primary {
  background: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-800);
  border-color: var(--color-primary-800);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--hover-bg);
  border-color: var(--hover-border);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--hover-bg);
}

.btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--hover-bg);
}

/* 按钮尺寸 */
.btn-sm {
  height: var(--button-height-sm);
  padding: 0 var(--button-padding-x-sm);
  font-size: var(--font-size-xs);
}

.btn-lg {
  height: var(--button-height-lg);
  padding: 0 var(--button-padding-x-lg);
  font-size: var(--font-size-base);
}

/* ========== 表单组件 ========== */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-label.required::after {
  content: " *";
  color: var(--color-error);
}

.form-control {
  width: 100%;
  height: var(--input-height-md);
  padding: 0 var(--input-padding-x);
  font-family: inherit;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-in-out);
}

.form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background: var(--disabled-bg);
  color: var(--disabled-text);
  cursor: not-allowed;
}

.form-control.error {
  border-color: var(--border-error);
}

.form-control.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--space-1);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: var(--space-1);
}

/* ========== 表格组件 ========== */

.table-container {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.table th,
.table td {
  padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table th {
  background: var(--bg-secondary);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tbody tr:hover {
  background: var(--hover-bg);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ========== 导航组件 ========== */

.nav-menu {
  list-style: none;
  padding: var(--space-4) 0;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  margin: 0 var(--space-4);
  transition: all var(--duration-fast) var(--ease-in-out);
}

.nav-link:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.nav-link.active {
  background: var(--selected-bg);
  color: var(--selected-text);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* ========== 状态组件 ========== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-success {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.badge-warning {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.badge-error {
  background: var(--color-error-light);
  color: var(--color-error-dark);
}

.badge-info {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

/* ========== 加载状态 ========== */

.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

/* ========== 工具类 ========== */

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

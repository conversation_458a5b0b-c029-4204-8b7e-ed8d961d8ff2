/**
 * 智能家居管理系统 - 通用JavaScript库
 * 版本: v3.0
 * 更新时间: 2025-01-17
 */

// 全局配置
window.AdminConfig = {
    apiBase: '/api/v1',
    version: 'v3.0',
    debug: true,
    pageSize: 20,
    timeout: 30000
};

// 通用工具函数
window.AdminUtils = {
    /**
     * 格式化日期
     */
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '-';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    },

    /**
     * 格式化金额
     */
    formatMoney: function(amount, currency = '¥') {
        if (amount === null || amount === undefined) return '-';
        return currency + Number(amount).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    },

    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 生成UUID
     */
    generateUUID: function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    /**
     * 深拷贝对象
     */
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 验证邮箱格式
     */
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    /**
     * 验证手机号格式
     */
    validatePhone: function(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    },

    /**
     * 获取URL参数
     */
    getUrlParam: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 设置URL参数
     */
    setUrlParam: function(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    }
};

// 通用API请求类
window.AdminAPI = {
    /**
     * 发送GET请求
     */
    get: async function(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        try {
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: this.getHeaders()
            });
            return await this.handleResponse(response);
        } catch (error) {
            console.error('GET请求失败:', error);
            throw error;
        }
    },

    /**
     * 发送POST请求
     */
    post: async function(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            console.error('POST请求失败:', error);
            throw error;
        }
    },

    /**
     * 发送PUT请求
     */
    put: async function(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: this.getHeaders(),
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            console.error('PUT请求失败:', error);
            throw error;
        }
    },

    /**
     * 发送DELETE请求
     */
    delete: async function(url) {
        try {
            const response = await fetch(url, {
                method: 'DELETE',
                headers: this.getHeaders()
            });
            return await this.handleResponse(response);
        } catch (error) {
            console.error('DELETE请求失败:', error);
            throw error;
        }
    },

    /**
     * 获取请求头
     */
    getHeaders: function() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        const token = localStorage.getItem('token');
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        return headers;
    },

    /**
     * 处理响应
     */
    handleResponse: async function(response) {
        if (!response.ok) {
            if (response.status === 401) {
                // Token过期，跳转到登录页
                localStorage.removeItem('token');
                window.location.href = 'login.html';
                return;
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        
        return await response.text();
    }
};

// 通用消息提示
window.AdminMessage = {
    /**
     * 成功消息
     */
    success: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },

    /**
     * 错误消息
     */
    error: function(message, duration = 5000) {
        this.show(message, 'error', duration);
    },

    /**
     * 警告消息
     */
    warning: function(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },

    /**
     * 信息消息
     */
    info: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    },

    /**
     * 显示消息
     */
    show: function(message, type = 'info', duration = 3000) {
        // 创建消息容器
        let container = document.getElementById('admin-message-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'admin-message-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }

        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.style.cssText = `
            background: ${this.getTypeColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            pointer-events: auto;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
        `;
        messageEl.textContent = message;

        container.appendChild(messageEl);

        // 显示动画
        setTimeout(() => {
            messageEl.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, duration);
    },

    /**
     * 获取类型颜色
     */
    getTypeColor: function(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
};

// 通用加载状态
window.AdminLoading = {
    /**
     * 显示加载状态
     */
    show: function(target = document.body, message = '加载中...') {
        const loadingEl = document.createElement('div');
        loadingEl.className = 'admin-loading';
        loadingEl.innerHTML = `
            <div class="admin-loading-backdrop"></div>
            <div class="admin-loading-content">
                <div class="admin-loading-spinner"></div>
                <div class="admin-loading-text">${message}</div>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('admin-loading-styles')) {
            const style = document.createElement('style');
            style.id = 'admin-loading-styles';
            style.textContent = `
                .admin-loading {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 9999;
                }
                .admin-loading-backdrop {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.8);
                }
                .admin-loading-content {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                }
                .admin-loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #1a1a1a;
                    border-radius: 50%;
                    animation: admin-spin 1s linear infinite;
                    margin: 0 auto 10px;
                }
                .admin-loading-text {
                    color: #666;
                    font-size: 14px;
                }
                @keyframes admin-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        target.style.position = 'relative';
        target.appendChild(loadingEl);
        return loadingEl;
    },

    /**
     * 隐藏加载状态
     */
    hide: function(target = document.body) {
        const loadingEl = target.querySelector('.admin-loading');
        if (loadingEl) {
            loadingEl.remove();
        }
    }
};

// 页面初始化完成后的通用处理
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    const token = localStorage.getItem('token');
    const currentPage = window.location.pathname.split('/').pop();
    
    if (!token && currentPage !== 'login.html') {
        // 未登录且不在登录页面，跳转到登录页
        window.location.href = 'login.html';
        return;
    }

    // 设置全局错误处理
    window.addEventListener('error', function(e) {
        if (AdminConfig.debug) {
            console.error('页面错误:', e.error);
        }
    });

    // 设置全局未处理的Promise拒绝处理
    window.addEventListener('unhandledrejection', function(e) {
        if (AdminConfig.debug) {
            console.error('未处理的Promise拒绝:', e.reason);
        }
    });

    console.log('智能家居管理系统通用库已加载 - 版本:', AdminConfig.version);
});

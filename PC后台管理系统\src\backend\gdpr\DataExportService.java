package com.smarthome.gdpr;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smarthome.auth.entity.User;
import com.smarthome.auth.repository.UserRepository;
import com.smarthome.project.entity.Project;
import com.smarthome.project.repository.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * GDPR数据可携带性服务
 * 实现用户数据导出功能，符合GDPR第20条数据可携带权要求
 */
@Service
@Transactional(readOnly = true)
public class DataExportService {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 导出用户的所有个人数据
     * @param userId 用户ID
     * @return 包含所有用户数据的ZIP文件字节数组
     */
    public byte[] exportUserData(String userId) throws IOException {
        User user = userRepository.findByBusinessUID(userId)
            .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));

        // 创建数据导出包
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);

        try {
            // 1. 导出用户基本信息
            exportUserBasicInfo(zos, user);
            
            // 2. 导出用户项目数据
            exportUserProjects(zos, user);
            
            // 3. 导出用户认证信息
            exportUserAuthInfo(zos, user);
            
            // 4. 导出用户活动日志
            exportUserActivityLogs(zos, user);
            
            // 5. 生成数据导出报告
            generateExportReport(zos, user);
            
        } finally {
            zos.close();
        }

        return baos.toByteArray();
    }

    /**
     * 导出用户基本信息
     */
    private void exportUserBasicInfo(ZipOutputStream zos, User user) throws IOException {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("businessUID", user.getBusinessUID());
        userInfo.put("email", user.getEmail());
        userInfo.put("phoneNumber", user.getPhoneNumber());
        userInfo.put("displayName", user.getDisplayName());
        userInfo.put("authStatus", user.getAuthStatus());
        userInfo.put("createdAt", user.getCreatedAt());
        userInfo.put("updatedAt", user.getUpdatedAt());
        userInfo.put("lastLoginAt", user.getLastLoginAt());
        
        // 移除敏感信息
        userInfo.put("note", "密码等敏感信息已被排除以保护安全");

        addJsonFileToZip(zos, "user_basic_info.json", userInfo);
    }

    /**
     * 导出用户项目数据
     */
    private void exportUserProjects(ZipOutputStream zos, User user) throws IOException {
        List<Project> projects = projectRepository.findByOwnerId(user.getId());
        
        List<Map<String, Object>> projectsData = new ArrayList<>();
        for (Project project : projects) {
            Map<String, Object> projectInfo = new HashMap<>();
            projectInfo.put("id", project.getId());
            projectInfo.put("projectName", project.getProjectName());
            projectInfo.put("projectType", project.getProjectType());
            projectInfo.put("status", project.getStatus());
            projectInfo.put("createdAt", project.getCreatedAt());
            projectInfo.put("updatedAt", project.getUpdatedAt());
            
            // 导出项目详细数据（如果有的话）
            if (project.getProjectData() != null) {
                projectInfo.put("projectData", project.getProjectData());
            }
            
            projectsData.add(projectInfo);
        }

        addJsonFileToZip(zos, "user_projects.json", projectsData);
    }

    /**
     * 导出用户认证信息
     */
    private void exportUserAuthInfo(ZipOutputStream zos, User user) throws IOException {
        Map<String, Object> authInfo = new HashMap<>();
        authInfo.put("businessUID", user.getBusinessUID());
        authInfo.put("authMethods", getAuthMethods(user));
        authInfo.put("lastLoginAt", user.getLastLoginAt());
        authInfo.put("authStatus", user.getAuthStatus());
        authInfo.put("accountCreatedAt", user.getCreatedAt());
        
        // 添加微信信息（如果有）
        if (user.getWechatInfo() != null) {
            Map<String, Object> wechatInfo = new HashMap<>();
            wechatInfo.put("openId", maskSensitiveData(user.getWechatInfo().getOpenId()));
            wechatInfo.put("unionId", maskSensitiveData(user.getWechatInfo().getUnionId()));
            wechatInfo.put("nickname", user.getWechatInfo().getNickname());
            wechatInfo.put("avatarUrl", user.getWechatInfo().getAvatarUrl());
            authInfo.put("wechatInfo", wechatInfo);
        }

        addJsonFileToZip(zos, "user_auth_info.json", authInfo);
    }

    /**
     * 导出用户活动日志
     */
    private void exportUserActivityLogs(ZipOutputStream zos, User user) throws IOException {
        // 这里可以从审计日志表中获取用户活动记录
        List<Map<String, Object>> activityLogs = new ArrayList<>();
        
        // 示例活动日志
        Map<String, Object> sampleLog = new HashMap<>();
        sampleLog.put("timestamp", LocalDateTime.now());
        sampleLog.put("action", "数据导出请求");
        sampleLog.put("description", "用户请求导出个人数据");
        sampleLog.put("ipAddress", "已脱敏");
        activityLogs.add(sampleLog);

        addJsonFileToZip(zos, "user_activity_logs.json", activityLogs);
    }

    /**
     * 生成数据导出报告
     */
    private void generateExportReport(ZipOutputStream zos, User user) throws IOException {
        Map<String, Object> report = new HashMap<>();
        report.put("exportDate", LocalDateTime.now());
        report.put("userId", user.getBusinessUID());
        report.put("exportedDataTypes", Arrays.asList(
            "用户基本信息",
            "项目数据", 
            "认证信息",
            "活动日志"
        ));
        report.put("gdprCompliance", true);
        report.put("dataRetentionPolicy", "根据GDPR要求，用户可以要求删除个人数据");
        report.put("contactInfo", "如有疑问，请联系: <EMAIL>");
        
        addJsonFileToZip(zos, "export_report.json", report);
        
        // 添加README文件
        String readme = "# 个人数据导出包\n\n" +
                       "此ZIP文件包含您在智能家居系统中的所有个人数据。\n\n" +
                       "## 文件说明:\n" +
                       "- user_basic_info.json: 用户基本信息\n" +
                       "- user_projects.json: 项目数据\n" +
                       "- user_auth_info.json: 认证信息\n" +
                       "- user_activity_logs.json: 活动日志\n" +
                       "- export_report.json: 导出报告\n\n" +
                       "## GDPR权利:\n" +
                       "根据GDPR，您有权要求:\n" +
                       "- 访问您的个人数据\n" +
                       "- 更正不准确的数据\n" +
                       "- 删除您的个人数据\n" +
                       "- 限制数据处理\n" +
                       "- 数据可携带性\n\n" +
                       "如需行使这些权利，请联系: <EMAIL>";
        
        addTextFileToZip(zos, "README.md", readme);
    }

    /**
     * 获取用户的认证方式
     */
    private List<String> getAuthMethods(User user) {
        List<String> methods = new ArrayList<>();
        
        if (user.getEmail() != null) {
            methods.add("邮箱认证");
        }
        
        if (user.getPhoneNumber() != null) {
            methods.add("手机号认证");
        }
        
        if (user.getWechatInfo() != null) {
            methods.add("微信认证");
        }
        
        return methods;
    }

    /**
     * 脱敏敏感数据
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 2) + "****" + data.substring(data.length() - 2);
    }

    /**
     * 添加JSON文件到ZIP
     */
    private void addJsonFileToZip(ZipOutputStream zos, String fileName, Object data) throws IOException {
        ZipEntry entry = new ZipEntry(fileName);
        zos.putNextEntry(entry);
        
        String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(data);
        zos.write(jsonContent.getBytes("UTF-8"));
        zos.closeEntry();
    }

    /**
     * 添加文本文件到ZIP
     */
    private void addTextFileToZip(ZipOutputStream zos, String fileName, String content) throws IOException {
        ZipEntry entry = new ZipEntry(fileName);
        zos.putNextEntry(entry);
        zos.write(content.getBytes("UTF-8"));
        zos.closeEntry();
    }

    /**
     * 检查用户是否有权限导出数据
     */
    public boolean canExportData(String userId, String requesterId) {
        // 只有用户本人可以导出自己的数据
        return userId.equals(requesterId);
    }

    /**
     * 记录数据导出操作
     */
    @Transactional
    public void logDataExport(String userId, String requesterId) {
        // 记录到审计日志
        // 这里可以调用审计服务记录操作
        System.out.println("数据导出操作已记录: 用户=" + userId + ", 请求者=" + requesterId);
    }
}

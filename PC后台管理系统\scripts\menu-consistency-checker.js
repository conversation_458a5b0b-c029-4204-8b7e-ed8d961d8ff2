/**
 * PC后台管理系统菜单一致性检查工具
 * 检查所有页面的左侧菜单是否与标准配置一致
 */

const fs = require('fs');
const path = require('path');

// 加载标准菜单配置
function loadStandardMenuConfig() {
    try {
        const configPath = path.join(__dirname, 'menu-config.json');
        const configContent = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configContent);
    } catch (error) {
        console.error('❌ 无法加载标准菜单配置:', error.message);
        process.exit(1);
    }
}

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 提取页面中的菜单内容
function extractMenuFromPage(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找所有侧边栏
        const sidebarRegex = /<aside[^>]*class="sidebar"[^>]*>([\s\S]*?)<\/aside>/g;
        const sidebars = [];
        let match;
        
        while ((match = sidebarRegex.exec(content)) !== null) {
            sidebars.push(match[1]);
        }
        
        if (sidebars.length === 0) {
            return { fileName, hasSidebar: false, sidebars: [], issues: ['无侧边栏'] };
        }
        
        // 分析每个侧边栏
        const menuAnalysis = sidebars.map((sidebar, index) => {
            return analyzeSidebarContent(sidebar, fileName);
        });
        
        return {
            fileName,
            hasSidebar: true,
            sidebars: menuAnalysis,
            issues: []
        };
        
    } catch (error) {
        return {
            fileName: path.basename(filePath),
            hasSidebar: false,
            sidebars: [],
            issues: [`读取文件失败: ${error.message}`]
        };
    }
}

// 分析侧边栏内容
function analyzeSidebarContent(sidebarContent, fileName) {
    const analysis = {
        sections: [],
        menuItems: [],
        activeItems: [],
        issues: []
    };
    
    // 提取菜单分组
    const sectionRegex = /<div class="nav-section-title">([^<]+)<\/div>/g;
    let sectionMatch;
    while ((sectionMatch = sectionRegex.exec(sidebarContent)) !== null) {
        analysis.sections.push(sectionMatch[1].trim());
    }
    
    // 提取菜单项
    const menuItemRegex = /<a href="([^"]+)" class="nav-item([^"]*)"[^>]*>(?:<i[^>]*><\/i>\s*)?([^<]+)<\/a>/g;
    let itemMatch;
    while ((itemMatch = menuItemRegex.exec(sidebarContent)) !== null) {
        const href = itemMatch[1];
        const classes = itemMatch[2];
        const text = itemMatch[3].trim();
        const isActive = classes.includes('active');
        
        analysis.menuItems.push({ href, text, isActive });
        
        if (isActive) {
            analysis.activeItems.push({ href, text });
        }
    }
    
    // 检查特定问题
    if (sidebarContent.includes('交底知识库')) {
        analysis.issues.push('包含旧的"交底知识库"文本');
    }
    
    if (sidebarContent.includes('知识库管理') && sidebarContent.includes('交底知识库')) {
        analysis.issues.push('同时包含"知识库管理"和"交底知识库"');
    }
    
    return analysis;
}

// 验证菜单与标准配置的一致性
function validateMenuConsistency(pageAnalysis, standardConfig) {
    const issues = [];
    
    if (!pageAnalysis.hasSidebar) {
        return ['页面无侧边栏'];
    }
    
    // 检查每个侧边栏
    pageAnalysis.sidebars.forEach((sidebar, index) => {
        const sidebarPrefix = pageAnalysis.sidebars.length > 1 ? `侧边栏${index + 1}: ` : '';
        
        // 检查菜单分组数量
        const expectedSections = standardConfig.sections.length;
        if (sidebar.sections.length !== expectedSections) {
            issues.push(`${sidebarPrefix}菜单分组数量不匹配 (期望${expectedSections}个，实际${sidebar.sections.length}个)`);
        }
        
        // 检查菜单分组名称
        standardConfig.sections.forEach((section, i) => {
            if (sidebar.sections[i] !== section.title) {
                issues.push(`${sidebarPrefix}菜单分组${i + 1}不匹配 (期望"${section.title}"，实际"${sidebar.sections[i] || '缺失'}")`);
            }
        });
        
        // 检查知识库管理菜单项
        const knowledgeManagementItem = sidebar.menuItems.find(item => 
            item.href === 'delivery-knowledge.html' || item.href.endsWith('/delivery-knowledge.html')
        );
        
        if (knowledgeManagementItem) {
            if (knowledgeManagementItem.text !== '知识库管理') {
                issues.push(`${sidebarPrefix}delivery-knowledge.html菜单文本错误 (期望"知识库管理"，实际"${knowledgeManagementItem.text}")`);
            }
        } else {
            issues.push(`${sidebarPrefix}缺少delivery-knowledge.html菜单项`);
        }
        
        // 检查激活状态
        const currentFileName = pageAnalysis.fileName;
        const expectedActiveHref = currentFileName;
        
        const correctActiveItems = sidebar.activeItems.filter(item => {
            const itemFileName = path.basename(item.href);
            return itemFileName === currentFileName;
        });
        
        if (currentFileName === 'delivery-knowledge.html') {
            if (correctActiveItems.length === 0) {
                issues.push(`${sidebarPrefix}delivery-knowledge.html页面的菜单项未标记为active`);
            }
        }
        
        // 添加侧边栏特有的问题
        issues.push(...sidebar.issues.map(issue => `${sidebarPrefix}${issue}`));
    });
    
    return issues;
}

// 主函数
function main() {
    console.log('🔍 开始PC后台管理系统菜单一致性检查...\n');
    
    // 加载标准配置
    const standardConfig = loadStandardMenuConfig();
    console.log('✅ 标准菜单配置加载成功');
    console.log(`📋 标准配置包含 ${standardConfig.sections.length} 个菜单分组\n`);
    
    // 获取所有HTML文件
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    // 检查结果统计
    const results = {
        totalFiles: htmlFiles.length,
        checkedFiles: 0,
        filesWithSidebar: 0,
        filesWithIssues: 0,
        totalIssues: 0,
        issuesByType: {},
        detailedResults: []
    };
    
    // 逐个检查文件
    for (const filePath of htmlFiles) {
        const relativePath = path.relative(pagesDir, filePath);
        const pageAnalysis = extractMenuFromPage(filePath);
        const issues = validateMenuConsistency(pageAnalysis, standardConfig);
        
        results.checkedFiles++;
        
        if (pageAnalysis.hasSidebar) {
            results.filesWithSidebar++;
        }
        
        if (issues.length > 0) {
            results.filesWithIssues++;
            results.totalIssues += issues.length;
            
            console.log(`❌ ${relativePath}:`);
            issues.forEach(issue => {
                console.log(`   - ${issue}`);
                
                // 统计问题类型
                const issueType = issue.split(':')[0];
                results.issuesByType[issueType] = (results.issuesByType[issueType] || 0) + 1;
            });
            console.log('');
        } else if (pageAnalysis.hasSidebar) {
            console.log(`✅ ${relativePath}: 菜单一致性正常`);
        } else {
            console.log(`⏭️  ${relativePath}: 跳过（无侧边栏）`);
        }
        
        results.detailedResults.push({
            file: relativePath,
            hasSidebar: pageAnalysis.hasSidebar,
            issues: issues
        });
    }
    
    // 输出统计报告
    console.log('\n📊 检查统计报告:');
    console.log(`📁 总文件数: ${results.totalFiles}`);
    console.log(`🔍 已检查文件: ${results.checkedFiles}`);
    console.log(`📋 包含侧边栏: ${results.filesWithSidebar}`);
    console.log(`❌ 有问题文件: ${results.filesWithIssues}`);
    console.log(`🚨 问题总数: ${results.totalIssues}`);
    
    if (results.totalIssues > 0) {
        console.log('\n🔍 问题类型统计:');
        Object.entries(results.issuesByType).forEach(([type, count]) => {
            console.log(`   ${type}: ${count} 个`);
        });
    }
    
    // 生成详细报告文件
    const reportPath = path.join(__dirname, '../docs/菜单一致性检查报告.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2), 'utf8');
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    
    if (results.totalIssues === 0) {
        console.log('\n🎉 所有页面菜单一致性检查通过！');
    } else {
        console.log(`\n⚠️  发现 ${results.totalIssues} 个问题需要修复`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    loadStandardMenuConfig,
    extractMenuFromPage,
    validateMenuConsistency,
    main
};

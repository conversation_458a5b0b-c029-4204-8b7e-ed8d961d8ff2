<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品素材管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">产品素材管理</h1>
                            <p class="breadcrumb-description">多品牌产品素材上传、管理和组织</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 素材统计 -->
                <div class="material-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalMaterials">0</span>
                            <span class="stat-label">总素材数</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="uploadedToday">0</span>
                            <span class="stat-label">今日上传</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="activeBatches">0</span>
                            <span class="stat-label">处理中批次</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="storageUsed">0 MB</span>
                            <span class="stat-label">存储使用</span>
                        </div>
                    </div>
                </div>

                <!-- 素材管理工具栏 -->
                <div class="material-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showSingleUploadModal()">
                            <i class="fas fa-plus"></i> 单一上传
                        </button>
                        <button class="btn btn-success" onclick="showBatchUploadModal()">
                            <i class="fas fa-upload"></i> 批量上传
                        </button>
                        <button class="btn btn-info" onclick="showBrandUploadModal()">
                            <i class="fas fa-layer-group"></i> 品牌导入
                        </button>
                        <button class="btn btn-secondary" onclick="showBatchProgress()">
                            <i class="fas fa-tasks"></i> 任务进度
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <select id="brandFilter" onchange="filterMaterials()">
                            <option value="">所有品牌</option>
                            <option value="aqara">Aqara</option>
                            <option value="yeelight">Yeelight</option>
                            <option value="mijia">Mijia</option>
                            <option value="tuya">Tuya</option>
                        </select>
                        <select id="typeFilter" onchange="filterMaterials()">
                            <option value="">所有类型</option>
                            <option value="overview">概览文档</option>
                            <option value="image">产品图片</option>
                            <option value="video">演示视频</option>
                            <option value="manual">产品手册</option>
                            <option value="specification">技术规格</option>
                        </select>
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索素材..." onkeyup="searchMaterials()">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>

                <!-- 素材列表 -->
                <div class="material-grid" id="materialGrid">
                    <!-- 素材卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 分页控件 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div class="page-numbers" id="pageNumbers"></div>
                        <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 单一上传模态框 -->
    <div class="modal" id="singleUploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>单一文件上传</h3>
                <button class="modal-close" onclick="closeSingleUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="singleUploadForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productSelect">关联产品</label>
                            <select id="productSelect" name="product_id">
                                <option value="">选择产品（可选）</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="materialType">素材类型 *</label>
                            <select id="materialType" name="material_type" required>
                                <option value="">请选择类型</option>
                                <option value="overview">概览文档</option>
                                <option value="image">产品图片</option>
                                <option value="video">演示视频</option>
                                <option value="manual">产品手册</option>
                                <option value="specification">技术规格</option>
                                <option value="faq">常见问题</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="fileInput">选择文件 *</label>
                        <input type="file" id="fileInput" name="file" required accept="image/*,video/*,.pdf,.md,.txt,.json">
                        <div class="file-info" id="fileInfo"></div>
                    </div>
                    <div class="form-group">
                        <label for="fileDescription">文件描述</label>
                        <textarea id="fileDescription" name="description" rows="3" placeholder="可选的文件描述信息"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeSingleUploadModal()">取消</button>
                <button class="btn btn-primary" onclick="uploadSingleFile()">上传</button>
            </div>
        </div>
    </div>

    <!-- 批量上传模态框 -->
    <div class="modal large-modal" id="batchUploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量文件上传</h3>
                <button class="modal-close" onclick="closeBatchUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-config">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="batchName">批次名称 *</label>
                            <input type="text" id="batchName" required placeholder="例：智能开关系列素材">
                        </div>
                        <div class="form-group">
                            <label for="batchProduct">关联产品</label>
                            <select id="batchProduct">
                                <option value="">选择产品（可选）</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="batchDescription">批次描述</label>
                        <textarea id="batchDescription" rows="2" placeholder="可选的批次描述"></textarea>
                    </div>
                    
                    <div class="upload-options">
                        <h4>上传选项</h4>
                        <div class="option-grid">
                            <label class="option-item">
                                <input type="checkbox" id="autoCateg

orize" checked>
                                <span>自动分类</span>
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="autoExtract" checked>
                                <span>自动提取内容</span>
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="compressImages">
                                <span>压缩图片</span>
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="generateThumbnails" checked>
                                <span>生成缩略图</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="drop-zone" id="dropZone">
                    <div class="drop-zone-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>拖拽文件到此处</h4>
                        <p>或 <span class="link" onclick="selectBatchFiles()">点击选择文件</span></p>
                        <p class="note">支持：图片、视频、PDF、Markdown、文本文件</p>
                    </div>
                    <input type="file" id="batchFileInput" multiple accept="image/*,video/*,.pdf,.md,.txt,.json" style="display: none;">
                </div>

                <div class="file-list" id="fileList" style="display: none;">
                    <h4>待上传文件 (<span id="fileCount">0</span>)</h4>
                    <div class="file-items" id="fileItems"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeBatchUploadModal()">取消</button>
                <button class="btn btn-primary" onclick="startBatchUpload()" id="batchUploadBtn" disabled>开始上传</button>
            </div>
        </div>
    </div>

    <!-- 品牌导入模态框 -->
    <div class="modal large-modal" id="brandUploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>品牌结构化导入</h3>
                <button class="modal-close" onclick="closeBrandUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="brand-selection">
                    <h4>选择品牌模板</h4>
                    <div class="brand-grid">
                        <div class="brand-card" data-brand="aqara">
                            <div class="brand-icon"><i class="fas fa-home"></i></div>
                            <h5>Aqara</h5>
                            <p>概览 + 产品支持 + 产品参数</p>
                            <span class="structure-info">3层标准结构</span>
                        </div>
                        <div class="brand-card" data-brand="yeelight">
                            <div class="brand-icon"><i class="fas fa-lightbulb"></i></div>
                            <h5>Yeelight</h5>
                            <p>产品概览 + 演示视频 + 技术文档</p>
                            <span class="structure-info">视频重点型</span>
                        </div>
                        <div class="brand-card" data-brand="mijia">
                            <div class="brand-icon"><i class="fas fa-cog"></i></div>
                            <h5>Mijia</h5>
                            <p>产品信息 + 生态集成 + 安装指南</p>
                            <span class="structure-info">生态集成型</span>
                        </div>
                        <div class="brand-card" data-brand="tuya">
                            <div class="brand-icon"><i class="fas fa-code"></i></div>
                            <h5>Tuya</h5>
                            <p>产品规格 + API文档 + 集成示例</p>
                            <span class="structure-info">开发者友好型</span>
                        </div>
                    </div>
                </div>

                <div class="import-config" id="importConfig" style="display: none;">
                    <h4>导入配置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="importPath">文档路径 *</label>
                            <input type="text" id="importPath" required placeholder="F:\AI编程\网站\网站爬取------aqara第二版\aqara产品文档">
                        </div>
                        <div class="form-group">
                            <label for="selectedBrand">选中品牌</label>
                            <input type="text" id="selectedBrand" readonly>
                        </div>
                    </div>
                    
                    <div class="template-preview" id="templatePreview">
                        <!-- 模板结构预览 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeBrandUploadModal()">取消</button>
                <button class="btn btn-primary" onclick="startBrandImport()" id="brandImportBtn" disabled>开始导入</button>
            </div>
        </div>
    </div>

    <!-- 进度模态框 -->
    <div class="modal" id="progressModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>上传进度</h3>
                <button class="modal-close" onclick="closeProgressModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="progress-info">
                    <div class="progress-item">
                        <span class="progress-label">当前任务:</span>
                        <span class="progress-value" id="currentTask">准备中...</span>
                    </div>
                    <div class="progress-item">
                        <span class="progress-label">进度:</span>
                        <span class="progress-value" id="progressPercent">0%</span>
                    </div>
                    <div class="progress-item">
                        <span class="progress-label">文件:</span>
                        <span class="progress-value" id="fileProgress">0/0</span>
                    </div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div class="progress-logs" id="progressLogs">
                    <!-- 进度日志 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeProgressModal()">关闭</button>
            </div>
        </div>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        class MaterialManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.materials = [];
                this.filteredMaterials = [];
                this.uploadTasks = [];
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.selectedBrand = null;
                this.batchFiles = [];
                this.init();
            }

            async init() {
                await this.loadMaterials();
                await this.loadProducts();
                this.updateStats();
                this.renderMaterials();
                this.bindEvents();
            }

            async loadMaterials() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/materials?page=${this.currentPage}&limit=${this.pageSize}`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    if (response.ok) {
                        const result = await response.json();
                        this.materials = result.materials || [];
                        this.totalCount = result.total || 0;
                        this.filteredMaterials = [...this.materials];
                    } else {
                        this.loadMockData();
                    }
                } catch (error) {
                    this.loadMockData();
                }
            }

            loadMockData() {
                this.materials = [
                    { id: 1, file_name: 'aqara_gateway_m3_overview.md', display_name: 'Aqara网关M3概览', material_type: 'overview', file_type: 'md', file_size: 15632, status: 'completed', created_at: '2024-01-15T10:30:00Z', tags: ['Aqara', '网关'] },
                    { id: 2, file_name: 'yeelight_bulb_demo.mp4', display_name: 'Yeelight灯泡演示', material_type: 'video', file_type: 'mp4', file_size: 25478963, status: 'completed', created_at: '2024-01-14T15:20:00Z', tags: ['Yeelight', '灯泡'] },
                    { id: 3, file_name: 'smart_switch_product_image.jpg', display_name: '智能开关产品图', material_type: 'image', file_type: 'jpg', file_size: 2847512, status: 'completed', created_at: '2024-01-13T09:15:00Z', tags: ['智能开关'] },
                    { id: 4, file_name: 'mijia_sensor_manual.pdf', display_name: 'Mijia传感器手册', material_type: 'manual', file_type: 'pdf', file_size: 8964237, status: 'processing', created_at: '2024-01-12T14:45:00Z', tags: ['Mijia', '传感器'] },
                    { id: 5, file_name: 'tuya_api_specification.json', display_name: 'Tuya API规格', material_type: 'specification', file_type: 'json', file_size: 45821, status: 'completed', created_at: '2024-01-11T11:30:00Z', tags: ['Tuya', 'API'] }
                ];
                this.totalCount = this.materials.length;
                this.filteredMaterials = [...this.materials];
            }

            async loadProducts() {
                // 加载产品列表用于关联
                const productSelect = document.getElementById('productSelect');
                const batchProduct = document.getElementById('batchProduct');
                
                const mockProducts = [
                    { id: 1, name: 'Aqara智能开关' },
                    { id: 2, name: 'Yeelight智能灯泡' },
                    { id: 3, name: 'Mijia温湿度传感器' },
                    { id: 4, name: 'Tuya智能插座' }
                ];

                mockProducts.forEach(product => {
                    const option1 = document.createElement('option');
                    option1.value = product.id;
                    option1.textContent = product.name;
                    productSelect.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = product.id;
                    option2.textContent = product.name;
                    batchProduct.appendChild(option2);
                });
            }

            updateStats() {
                const totalMaterials = this.materials.length;
                const totalImages = this.materials.filter(m => m.material_type === 'image').length;
                const totalVideos = this.materials.filter(m => m.material_type === 'video').length;
                const pendingUploads = this.materials.filter(m => m.status === 'processing').length;

                document.getElementById('totalMaterials').textContent = totalMaterials;
                document.getElementById('totalImages').textContent = totalImages;
                document.getElementById('totalVideos').textContent = totalVideos;
                document.getElementById('pendingUploads').textContent = pendingUploads;
            }

            renderMaterials() {
                const grid = document.getElementById('materialGrid');
                grid.innerHTML = '';

                if (this.filteredMaterials.length === 0) {
                    grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: var(--text-secondary);">暂无素材数据</div>';
                    return;
                }

                this.filteredMaterials.forEach(material => {
                    const card = document.createElement('div');
                    card.className = 'material-card';
                    
                    const previewContent = this.getPreviewContent(material);
                    const typeText = this.getTypeText(material.material_type);
                    
                    card.innerHTML = `
                        <div class="material-preview">
                            ${previewContent}
                            <div class="material-type-badge">${typeText}</div>
                        </div>
                        <div class="material-info">
                            <div class="material-name">${material.display_name}</div>
                            <div class="material-meta">
                                <span>大小: ${this.formatFileSize(material.file_size)}</span>
                                <span class="status-${material.status}">${this.getStatusText(material.status)}</span>
                            </div>
                            <div class="material-meta">
                                <span>类型: ${material.file_type.toUpperCase()}</span>
                                <span>${this.formatDate(material.created_at)}</span>
                            </div>
                            <div class="material-actions">
                                <button class="btn btn-sm btn-secondary" onclick="materialManager.viewMaterial(${material.id})" title="查看">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="materialManager.downloadMaterial(${material.id})" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="materialManager.editMaterial(${material.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="materialManager.deleteMaterial(${material.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    grid.appendChild(card);
                });
            }

            getPreviewContent(material) {
                if (material.material_type === 'image') {
                    return `<img src="/uploads/materials/thumbnails/${material.id}.jpg" alt="${material.display_name}" onerror="this.style.display='none'; this.nextSibling.style.display='flex';">
                            <div style="display: none;"><i class="fas fa-image" style="font-size: 48px;"></i></div>`;
                } else if (material.material_type === 'video') {
                    return `<div><i class="fas fa-video" style="font-size: 48px;"></i></div>`;
                } else if (material.file_type === 'pdf') {
                    return `<div><i class="fas fa-file-pdf" style="font-size: 48px; color: #dc2626;"></i></div>`;
                } else if (material.file_type === 'md') {
                    return `<div><i class="fab fa-markdown" style="font-size: 48px; color: #059669;"></i></div>`;
                } else {
                    return `<div><i class="fas fa-file" style="font-size: 48px;"></i></div>`;
                }
            }

            getTypeText(type) {
                const typeMap = {
                    'overview': '概览',
                    'image': '图片',
                    'video': '视频',
                    'manual': '手册',
                    'specification': '规格',
                    'faq': '问答',
                    'product_support': '支持',
                    'product_params': '参数'
                };
                return typeMap[type] || type;
            }

            getStatusText(status) {
                const statusMap = {
                    'completed': '完成',
                    'processing': '处理中',
                    'uploading': '上传中',
                    'failed': '失败',
                    'archived': '归档'
                };
                return statusMap[status] || status;
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN');
            }

            bindEvents() {
                // 文件选择事件
                document.getElementById('fileInput').addEventListener('change', (e) => this.previewSingleFile(e));
                document.getElementById('batchFileInput').addEventListener('change', (e) => this.handleBatchFiles(e));
                
                // 拖拽事件
                const dropZone = document.getElementById('dropZone');
                dropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dropZone.classList.add('dragover');
                });
                dropZone.addEventListener('dragleave', () => dropZone.classList.remove('dragover'));
                dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropZone.classList.remove('dragover');
                    this.handleBatchFiles({ target: { files: e.dataTransfer.files } });
                });

                // 品牌选择事件
                document.querySelectorAll('.brand-card').forEach(card => {
                    card.addEventListener('click', () => this.selectBrand(card.dataset.brand));
                });
            }

            // 单一文件上传方法
            showSingleUploadModal() {
                document.getElementById('singleUploadModal').style.display = 'block';
            }

            closeSingleUploadModal() {
                document.getElementById('singleUploadModal').style.display = 'none';
                document.getElementById('singleUploadForm').reset();
                document.getElementById('fileInfo').innerHTML = '';
            }

            previewSingleFile(event) {
                const file = event.target.files[0];
                if (!file) return;

                const fileInfo = document.getElementById('fileInfo');
                fileInfo.innerHTML = `
                    <div style="margin-top: 8px; font-size: 12px; color: var(--text-secondary);">
                        <div>文件名: ${file.name}</div>
                        <div>大小: ${this.formatFileSize(file.size)}</div>
                        <div>类型: ${file.type || '未知'}</div>
                    </div>
                `;
            }

            async uploadSingleFile() {
                const form = document.getElementById('singleUploadForm');
                const formData = new FormData(form);
                
                if (!formData.get('file') || !formData.get('material_type')) {
                    showToast('请选择文件和素材类型！', 'warning');
                    return;
                }

                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/materials/upload`, {
                        method: 'POST',
                        headers: { 'Authorization': `Bearer ${token}` },
                        body: formData
                    });

                    if (response.ok) {
                        showToast('文件上传成功！', 'success');
                        this.closeSingleUploadModal();
                        this.loadMaterials();
                    } else {
                        showToast('上传失败，请稍后重试', 'error');
                    }
                } catch (error) {
                    showToast('上传失败：' + error.message, 'error');
                }
            }

            // 批量上传方法
            showBatchUploadModal() {
                document.getElementById('batchUploadModal').style.display = 'block';
            }

            closeBatchUploadModal() {
                document.getElementById('batchUploadModal').style.display = 'none';
                this.resetBatchUpload();
            }

            selectBatchFiles() {
                document.getElementById('batchFileInput').click();
            }

            handleBatchFiles(event) {
                const files = Array.from(event.target.files);
                this.batchFiles = files;
                this.renderFileList();
            }

            renderFileList() {
                const fileList = document.getElementById('fileList');
                const fileItems = document.getElementById('fileItems');
                const fileCount = document.getElementById('fileCount');
                const uploadBtn = document.getElementById('batchUploadBtn');

                if (this.batchFiles.length === 0) {
                    fileList.style.display = 'none';
                    uploadBtn.disabled = true;
                    return;
                }

                fileList.style.display = 'block';
                fileCount.textContent = this.batchFiles.length;
                uploadBtn.disabled = false;

                fileItems.innerHTML = '';
                this.batchFiles.forEach((file, index) => {
                    const item = document.createElement('div');
                    item.className = 'file-item';
                    item.innerHTML = `
                        <div class="file-item-info">
                            <div class="file-item-icon">
                                <i class="fas ${this.getFileIcon(file)}"></i>
                            </div>
                            <div class="file-item-details">
                                <div class="file-item-name">${file.name}</div>
                                <div class="file-item-size">${this.formatFileSize(file.size)}</div>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-secondary" onclick="materialManager.removeBatchFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    fileItems.appendChild(item);
                });
            }

            getFileIcon(file) {
                const ext = file.name.split('.').pop().toLowerCase();
                if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) return 'fa-image';
                if (['mp4', 'avi', 'mov', 'wmv'].includes(ext)) return 'fa-video';
                if (ext === 'pdf') return 'fa-file-pdf';
                if (['md', 'txt'].includes(ext)) return 'fa-file-text';
                if (['json', 'yaml'].includes(ext)) return 'fa-file-code';
                return 'fa-file';
            }

            removeBatchFile(index) {
                this.batchFiles.splice(index, 1);
                this.renderFileList();
            }

            async startBatchUpload() {
                const batchName = document.getElementById('batchName').value;
                if (!batchName.trim()) {
                    showToast('请输入批次名称！', 'warning');
                    return;
                }

                const uploadConfig = {
                    batch_name: batchName,
                    description: document.getElementById('batchDescription').value,
                    product_id: document.getElementById('batchProduct').value || null,
                    config: {
                        storage_type: 'local',
                        auto_categorize: document.getElementById('autoCategorize').checked,
                        auto_extract: document.getElementById('autoExtract').checked,
                        compress_images: document.getElementById('compressImages').checked,
                        generate_thumbnails: document.getElementById('generateThumbnails').checked,
                        max_file_size: 100 * 1024 * 1024,
                        allowed_extensions: ['jpg', 'jpeg', 'png', 'pdf', 'md', 'mp4', 'txt', 'json']
                    }
                };

                this.closeBatchUploadModal();
                this.showProgressModal();
                
                // 模拟批量上传进度
                this.simulateBatchUpload(uploadConfig);
            }

            simulateBatchUpload(config) {
                let processed = 0;
                const total = this.batchFiles.length;
                
                const updateProgress = () => {
                    processed++;
                    const percent = Math.round((processed / total) * 100);
                    
                    document.getElementById('currentTask').textContent = `处理文件 ${processed}/${total}`;
                    document.getElementById('progressPercent').textContent = `${percent}%`;
                    document.getElementById('fileProgress').textContent = `${processed}/${total}`;
                    document.getElementById('progressFill').style.width = `${percent}%`;
                    
                    const logs = document.getElementById('progressLogs');
                    const log = document.createElement('div');
                    log.className = 'log-entry';
                    log.textContent = `✓ 已处理: ${this.batchFiles[processed-1].name}`;
                    logs.appendChild(log);
                    logs.scrollTop = logs.scrollHeight;
                    
                    if (processed < total) {
                        setTimeout(updateProgress, 500 + Math.random() * 1000);
                    } else {
                        document.getElementById('currentTask').textContent = '批量上传完成！';
                        setTimeout(() => {
                            this.closeProgressModal();
                            this.loadMaterials();
                        }, 2000);
                    }
                };
                
                setTimeout(updateProgress, 1000);
            }

            resetBatchUpload() {
                this.batchFiles = [];
                document.getElementById('batchName').value = '';
                document.getElementById('batchDescription').value = '';
                document.getElementById('batchProduct').value = '';
                document.getElementById('fileList').style.display = 'none';
                document.getElementById('batchUploadBtn').disabled = true;
            }

            // 品牌导入方法
            showBrandUploadModal() {
                document.getElementById('brandUploadModal').style.display = 'block';
            }

            closeBrandUploadModal() {
                document.getElementById('brandUploadModal').style.display = 'none';
                this.resetBrandUpload();
            }

            selectBrand(brand) {
                // 移除之前的选择
                document.querySelectorAll('.brand-card').forEach(card => card.classList.remove('selected'));
                
                // 选择当前品牌
                document.querySelector(`[data-brand="${brand}"]`).classList.add('selected');
                this.selectedBrand = brand;
                
                // 显示配置区域
                document.getElementById('importConfig').style.display = 'block';
                document.getElementById('selectedBrand').value = brand.toUpperCase();
                document.getElementById('brandImportBtn').disabled = false;
                
                // 显示模板预览
                this.showTemplatePreview(brand);
            }

            showTemplatePreview(brand) {
                const templates = {
                    aqara: [
                        { name: '概览', types: ['概览文档', '产品图片'] },
                        { name: '产品支持', types: ['安装视频', '产品手册', '常见问题'] },
                        { name: '产品参数', types: ['技术规格', '参数文档'] }
                    ],
                    yeelight: [
                        { name: '产品概览', types: ['基本信息', '产品图片'] },
                        { name: '演示视频', types: ['产品演示', '使用视频'] },
                        { name: '技术文档', types: ['技术规格', '使用手册'] },
                        { name: '增强内容', types: ['扩展支持'] }
                    ],
                    mijia: [
                        { name: '产品信息', types: ['基础信息', '规格参数'] },
                        { name: '生态集成', types: ['Mi Home集成', '生态联动'] },
                        { name: '安装指南', types: ['安装配置', '使用指南'] },
                        { name: '智能场景', types: ['场景配置'] }
                    ],
                    tuya: [
                        { name: '产品规格', types: ['详细规格', 'IoT参数'] },
                        { name: 'API文档', types: ['接口文档', '开发文档'] },
                        { name: '集成示例', types: ['代码示例', '平台集成'] },
                        { name: '媒体资源', types: ['产品图片', '演示视频'] }
                    ]
                };

                const preview = document.getElementById('templatePreview');
                const folders = templates[brand] || [];
                
                preview.innerHTML = `
                    <h5>模板结构预览</h5>
                    <div class="template-structure">
                        ${folders.map(folder => `
                            <div class="structure-folder">
                                <div class="folder-icon"><i class="fas fa-folder"></i></div>
                                <div class="folder-name">${folder.name}</div>
                                <div class="folder-types">${folder.types.join(', ')}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            async startBrandImport() {
                const importPath = document.getElementById('importPath').value;
                if (!importPath.trim()) {
                    showToast('请输入文档路径！', 'warning');
                    return;
                }

                this.closeBrandUploadModal();
                this.showProgressModal();
                
                // 模拟品牌导入进度
                this.simulateBrandImport();
            }

            simulateBrandImport() {
                const steps = [
                    '扫描目录结构...',
                    '验证文件格式...',
                    '创建导入任务...',
                    '处理概览文档...',
                    '处理产品图片...',
                    '处理支持文档...',
                    '处理技术规格...',
                    '生成索引和标签...',
                    '完成导入任务...'
                ];

                let currentStep = 0;
                const updateStep = () => {
                    if (currentStep < steps.length) {
                        const percent = Math.round(((currentStep + 1) / steps.length) * 100);
                        document.getElementById('currentTask').textContent = steps[currentStep];
                        document.getElementById('progressPercent').textContent = `${percent}%`;
                        document.getElementById('progressFill').style.width = `${percent}%`;
                        
                        const logs = document.getElementById('progressLogs');
                        const log = document.createElement('div');
                        log.className = 'log-entry';
                        log.textContent = `✓ ${steps[currentStep]}`;
                        logs.appendChild(log);
                        logs.scrollTop = logs.scrollHeight;
                        
                        currentStep++;
                        setTimeout(updateStep, 800 + Math.random() * 400);
                    } else {
                        document.getElementById('currentTask').textContent = '品牌导入完成！';
                        setTimeout(() => {
                            this.closeProgressModal();
                            this.loadMaterials();
                        }, 2000);
                    }
                };
                
                setTimeout(updateStep, 1000);
            }

            resetBrandUpload() {
                this.selectedBrand = null;
                document.querySelectorAll('.brand-card').forEach(card => card.classList.remove('selected'));
                document.getElementById('importConfig').style.display = 'none';
                document.getElementById('importPath').value = '';
                document.getElementById('brandImportBtn').disabled = true;
            }

            // 进度模态框方法
            showProgressModal() {
                document.getElementById('progressModal').style.display = 'block';
                // 重置进度
                document.getElementById('currentTask').textContent = '准备中...';
                document.getElementById('progressPercent').textContent = '0%';
                document.getElementById('fileProgress').textContent = '0/0';
                document.getElementById('progressFill').style.width = '0%';
                document.getElementById('progressLogs').innerHTML = '';
            }

            closeProgressModal() {
                document.getElementById('progressModal').style.display = 'none';
            }

            showBatchProgress() {
                // 显示批量任务进度列表
                showToast('批量任务进度功能开发中...', 'info');
            }

            // 素材操作方法
            viewMaterial(id) {
                const material = this.materials.find(m => m.id === id);
                if (!material) return;
                showToast(`查看素材：${material.display_name}`, 'info');
            }

            downloadMaterial(id) {
                const material = this.materials.find(m => m.id === id);
                if (!material) return;
                // 模拟下载
                showToast(`开始下载：${material.display_name}`, 'success');
            }

            editMaterial(id) {
                showToast('编辑素材功能开发中...', 'info');
            }

            deleteMaterial(id) {
                if (confirm('确定要删除这个素材吗？')) {
                    this.materials = this.materials.filter(m => m.id !== id);
                    this.filteredMaterials = [...this.materials];
                    this.updateStats();
                    this.renderMaterials();
                    showToast('素材删除成功！', 'success');
                }
            }

            // 筛选和搜索方法
            filterMaterials() {
                const brandFilter = document.getElementById('brandFilter').value;
                const typeFilter = document.getElementById('typeFilter').value;
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();

                this.filteredMaterials = this.materials.filter(material => {
                    const brandMatch = !brandFilter || (material.tags && material.tags.some(tag => tag.toLowerCase().includes(brandFilter)));
                    const typeMatch = !typeFilter || material.material_type === typeFilter;
                    const searchMatch = !searchTerm || 
                        material.display_name.toLowerCase().includes(searchTerm) ||
                        material.file_name.toLowerCase().includes(searchTerm);
                    
                    return brandMatch && typeMatch && searchMatch;
                });

                this.renderMaterials();
            }

            searchMaterials() {
                this.filterMaterials();
            }

            // 分页方法
            previousPage() {
                if (this.currentPage > 1) {
                    this.currentPage--;
                    this.loadMaterials();
                }
            }

            nextPage() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                if (this.currentPage < totalPages) {
                    this.currentPage++;
                    this.loadMaterials();
                }
            }
        }

        // 全局方法
        let materialManager;
        
        function showSingleUploadModal() { materialManager.showSingleUploadModal(); }
        function closeSingleUploadModal() { materialManager.closeSingleUploadModal(); }
        function uploadSingleFile() { materialManager.uploadSingleFile(); }
        
        function showBatchUploadModal() { materialManager.showBatchUploadModal(); }
        function closeBatchUploadModal() { materialManager.closeBatchUploadModal(); }
        function selectBatchFiles() { materialManager.selectBatchFiles(); }
        function startBatchUpload() { materialManager.startBatchUpload(); }
        
        function showBrandUploadModal() { materialManager.showBrandUploadModal(); }
        function closeBrandUploadModal() { materialManager.closeBrandUploadModal(); }
        function startBrandImport() { materialManager.startBrandImport(); }
        
        function showBatchProgress() { materialManager.showBatchProgress(); }
        function closeProgressModal() { materialManager.closeProgressModal(); }
        
        function searchMaterials() { materialManager.searchMaterials(); }
        function filterMaterials() { materialManager.filterMaterials(); }
        function previousPage() { materialManager.previousPage(); }
        function nextPage() { materialManager.nextPage(); }

        document.addEventListener('DOMContentLoaded', function() {
            materialManager = new MaterialManager();
        });
    </script>
</body>
</html>
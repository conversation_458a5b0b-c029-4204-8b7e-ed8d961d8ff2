/**
 * 批量修复关键问题脚本
 * 1. 修复旧链接问题 (118个)
 * 2. 修复知识库命名错误 (56个)
 */

const fs = require('fs');
const path = require('path');

// 文件重命名映射
const FILE_RENAMES = {
    "api-tester.html": "api-tools.html",
    "user-permissions.html": "internal-permissions.html", 
    "permissions.html": "customer-permissions.html",
    "settings.html": "system-settings.html",
    "requirements-analysis.html": "requirements-analytics.html",
    "一装ERP-API文档.html": "erp-documentation.html",
    "login.html": "logout.html"
};

// 知识库命名修正
const KNOWLEDGE_FIXES = {
    "交付知识库": "交底知识库",
    "安装知识库": "布线知识库"
};

// 需要添加的水电交底知识库链接
const ADD_ELECTRICAL_DELIVERY = true;

// 需要移除的设计指导库
const REMOVE_DESIGN_GUIDE = true;

// 批量修复单个文件
function fixFile(filePath) {
    const fileName = path.basename(filePath);
    console.log(`🔧 修复文件: ${fileName}`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let changes = [];
        
        // 1. 修复旧文件链接
        Object.keys(FILE_RENAMES).forEach(oldFile => {
            const newFile = FILE_RENAMES[oldFile];
            const oldPattern = new RegExp(`href="${oldFile}"`, 'g');
            const newPattern = `href="${newFile}"`;
            
            if (content.includes(`href="${oldFile}"`)) {
                content = content.replace(oldPattern, newPattern);
                changes.push(`链接: ${oldFile} → ${newFile}`);
            }
        });
        
        // 2. 修复知识库命名
        Object.keys(KNOWLEDGE_FIXES).forEach(oldName => {
            const newName = KNOWLEDGE_FIXES[oldName];
            if (content.includes(oldName)) {
                content = content.replace(new RegExp(oldName, 'g'), newName);
                changes.push(`命名: ${oldName} → ${newName}`);
            }
        });
        
        // 3. 添加水电交底知识库（如果缺少且有知识库部分）
        if (ADD_ELECTRICAL_DELIVERY && 
            content.includes('知识库') && 
            content.includes('nav-section-title') &&
            !content.includes('electrical-delivery-knowledge.html')) {
            
            // 查找知识库部分并添加水电交底知识库
            const knowledgePattern = /(<a href="delivery-knowledge\.html"[^>]*>交底知识库<\/a>)/;
            if (knowledgePattern.test(content)) {
                content = content.replace(
                    knowledgePattern,
                    '$1\n                    <a href="electrical-delivery-knowledge.html" class="nav-item">水电交底知识库</a>'
                );
                changes.push('添加: 水电交底知识库');
            }
        }
        
        // 4. 移除设计指导库（如果存在）
        if (REMOVE_DESIGN_GUIDE && content.includes('design-knowledge-guide.html')) {
            const designGuidePattern = /\s*<a href="design-knowledge-guide\.html"[^>]*>设计指导库<\/a>\s*/g;
            content = content.replace(designGuidePattern, '');
            changes.push('移除: 设计指导库');
        }
        
        // 保存修改后的文件
        if (changes.length > 0) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`   ✅ 完成修复: ${changes.join(', ')}`);
            return { fileName, changes, success: true };
        } else {
            console.log(`   ℹ️  无需修复`);
            return { fileName, changes: [], success: true };
        }
        
    } catch (error) {
        console.log(`   ❌ 修复失败: ${error.message}`);
        return { fileName, changes: [], success: false, error: error.message };
    }
}

// 批量修复所有文件
function batchFixAllFiles() {
    console.log('🚀 开始批量修复关键问题...\n');
    
    const pagesDir = path.join(__dirname, '../pages');
    const results = {
        total: 0,
        processed: 0,
        fixed: 0,
        errors: 0,
        changes: []
    };
    
    // 获取所有HTML文件
    const files = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.html'))
        .sort();
    
    results.total = files.length;
    console.log(`📊 发现 ${files.length} 个HTML文件\n`);
    
    // 处理每个文件
    files.forEach(file => {
        const filePath = path.join(pagesDir, file);
        if (fs.existsSync(filePath)) {
            results.processed++;
            const result = fixFile(filePath);
            
            if (result.success) {
                if (result.changes.length > 0) {
                    results.fixed++;
                    results.changes.push(result);
                }
            } else {
                results.errors++;
            }
        }
    });
    
    return results;
}

// 生成修复报告
function generateFixReport(results) {
    console.log('\n📊 批量修复完成报告');
    console.log('='.repeat(50));
    
    console.log(`\n📈 修复统计:`);
    console.log(`   总文件数: ${results.total}`);
    console.log(`   已处理: ${results.processed}`);
    console.log(`   已修复: ${results.fixed}`);
    console.log(`   错误数: ${results.errors}`);
    console.log(`   成功率: ${(results.fixed/results.processed*100).toFixed(1)}%`);
    
    if (results.changes.length > 0) {
        console.log(`\n🔧 修复详情:`);
        results.changes.forEach(change => {
            console.log(`   📄 ${change.fileName}:`);
            change.changes.forEach(detail => {
                console.log(`      - ${detail}`);
            });
        });
    }
    
    // 统计修复类型
    const fixTypes = {
        links: 0,
        naming: 0,
        additions: 0,
        removals: 0
    };
    
    results.changes.forEach(change => {
        change.changes.forEach(detail => {
            if (detail.includes('链接:')) fixTypes.links++;
            if (detail.includes('命名:')) fixTypes.naming++;
            if (detail.includes('添加:')) fixTypes.additions++;
            if (detail.includes('移除:')) fixTypes.removals++;
        });
    });
    
    console.log(`\n📋 修复类型统计:`);
    console.log(`   旧链接修复: ${fixTypes.links}个`);
    console.log(`   命名修正: ${fixTypes.naming}个`);
    console.log(`   功能添加: ${fixTypes.additions}个`);
    console.log(`   内容移除: ${fixTypes.removals}个`);
    
    console.log(`\n✅ 阶段一关键问题修复完成！`);
    console.log(`🎯 已解决影响用户体验的主要问题`);
    console.log(`🚀 可以继续进行阶段二的结构标准化工作`);
}

// 主函数
function main() {
    const results = batchFixAllFiles();
    generateFixReport(results);
    
    // 保存修复结果
    const reportPath = path.join(__dirname, '../../docs/critical-fixes-report.json');
    try {
        fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
        console.log(`\n💾 修复报告已保存到: ${reportPath}`);
    } catch (error) {
        console.log(`\n⚠️  保存报告失败: ${error.message}`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixFile,
    batchFixAllFiles,
    generateFixReport,
    FILE_RENAMES,
    KNOWLEDGE_FIXES
};

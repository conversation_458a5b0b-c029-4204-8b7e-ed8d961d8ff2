/**
 * 认证服务
 * 处理用户登录、注册、令牌管理等功能
 */

class AuthService {
  constructor() {
    // 动态设置API基础URL
    this.baseURL = this.getBaseURL()
    this.tokenKey = 'token'
    this.refreshTokenKey = 'refresh_token'
    this.userKey = 'user'
    
    // 登录尝试限制
    this.maxAttempts = 5
    this.lockoutDuration = 15 * 60 * 1000 // 15分钟
  }

  /**
   * 获取API基础URL
   */
  getBaseURL() {
    // 如果有环境变量或配置，优先使用
    if (window.API_BASE_URL) {
      return window.API_BASE_URL
    }
    
    // 检查是否有本地运行的服务器
    if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
      return 'http://localhost:8001/api/auth'
    }
    
    // 生产环境或其他情况，使用相对路径
    return '/api/auth'
  }

  /**
   * 统一登录接口
   */
  async login(loginData) {
    try {
      // 检查登录尝试限制
      if (this.isAccountLocked(loginData.username || loginData.email)) {
        throw new Error('账户已被锁定，请稍后再试')
      }

      const response = await this.makeRequest('/login', {
        method: 'POST',
        body: JSON.stringify(loginData)
      })

      if (response.success) {
        // 保存令牌和用户信息
        this.saveTokens(response.data.accessToken, response.data.refreshToken)
        this.saveUser(response.data.user)
        
        // 重置登录尝试计数
        this.resetLoginAttempts(loginData.username || loginData.email)
        
        return response.data
      } else {
        // 记录失败尝试
        this.recordFailedAttempt(loginData.username || loginData.email)
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  /**
   * 密码登录
   */
  async passwordLogin(username, password) {
    return this.login({
      loginType: 'password',
      username,
      password
    })
  }

  /**
   * 邮箱登录
   */
  async emailLogin(email, password) {
    return this.login({
      loginType: 'email',
      email,
      password
    })
  }

  /**
   * 微信登录
   */
  async wechatLogin(code, encryptedData, iv) {
    return this.login({
      loginType: 'wechat',
      code,
      encryptedData,
      iv
    })
  }

  /**
   * Google登录
   */
  async googleLogin(token) {
    return this.login({
      loginType: 'google',
      token
    })
  }

  /**
   * 手机号登录
   */
  async phoneLogin(phone, code) {
    return this.login({
      loginType: 'phone',
      phone,
      code
    })
  }

  /**
   * 刷新令牌
   */
  async refreshToken() {
    try {
      const refreshToken = this.getRefreshToken()
      if (!refreshToken) {
        throw new Error('没有刷新令牌')
      }

      const response = await this.makeRequest('/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken })
      })

      if (response.success) {
        this.saveTokens(response.data.accessToken, refreshToken)
        return response.data.accessToken
      } else {
        throw new Error(response.message || '令牌刷新失败')
      }
    } catch (error) {
      console.error('令牌刷新失败:', error)
      this.logout()
      throw error
    }
  }

  /**
   * 登出
   */
  async logout() {
    try {
      const token = this.getAccessToken()
      if (token) {
        await this.makeRequest('/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地存储
      this.clearTokens()
      this.clearUser()
      
      // 跳转到登录页
      window.location.href = './login.html'
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser() {
    try {
      const token = this.getAccessToken()
      if (!token) {
        throw new Error('没有访问令牌')
      }

      const response = await this.makeRequest('/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.success) {
        this.saveUser(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    const token = this.getAccessToken()
    const user = this.getUser()
    return !!(token && user)
  }

  /**
   * 获取访问令牌
   */
  getAccessToken() {
    return localStorage.getItem(this.tokenKey)
  }

  /**
   * 获取刷新令牌
   */
  getRefreshToken() {
    return localStorage.getItem(this.refreshTokenKey)
  }

  /**
   * 获取用户信息
   */
  getUser() {
    const userStr = localStorage.getItem(this.userKey)
    return userStr ? JSON.parse(userStr) : null
  }

  /**
   * 保存令牌
   */
  saveTokens(accessToken, refreshToken) {
    localStorage.setItem(this.tokenKey, accessToken)
    if (refreshToken) {
      localStorage.setItem(this.refreshTokenKey, refreshToken)
    }

    // 为了兼容性，也保存其他可能的token名称
    localStorage.setItem('accessToken', accessToken)
    localStorage.setItem('access_token', accessToken)
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken)
      localStorage.setItem('refresh_token', refreshToken)
    }

    console.log('💾 Token已保存:', {
      smart_home_access_token: accessToken ? '✅' : '❌',
      accessToken: accessToken ? '✅' : '❌',
      refreshToken: refreshToken ? '✅' : '❌'
    })
  }

  /**
   * 保存用户信息
   */
  saveUser(user) {
    localStorage.setItem(this.userKey, JSON.stringify(user))

    // 为了兼容性，也保存其他可能的用户信息名称
    localStorage.setItem('userInfo', JSON.stringify(user))
    localStorage.setItem('user', JSON.stringify(user))

    console.log('👤 用户信息已保存:', user.name || user.username || '未知用户')
  }

  /**
   * 清除令牌
   */
  clearTokens() {
    localStorage.removeItem(this.tokenKey)
    localStorage.removeItem(this.refreshTokenKey)
  }

  /**
   * 清除用户信息
   */
  clearUser() {
    localStorage.removeItem(this.userKey)
  }

  /**
   * 记录失败的登录尝试
   */
  recordFailedAttempt(identifier) {
    const key = `login_attempts_${identifier}`
    const attempts = JSON.parse(localStorage.getItem(key) || '[]')
    attempts.push(Date.now())
    
    // 只保留最近的尝试记录
    const recentAttempts = attempts.filter(time => 
      Date.now() - time < this.lockoutDuration
    )
    
    localStorage.setItem(key, JSON.stringify(recentAttempts))
  }

  /**
   * 重置登录尝试计数
   */
  resetLoginAttempts(identifier) {
    const key = `login_attempts_${identifier}`
    localStorage.removeItem(key)
  }

  /**
   * 检查账户是否被锁定
   */
  isAccountLocked(identifier) {
    const key = `login_attempts_${identifier}`
    const attempts = JSON.parse(localStorage.getItem(key) || '[]')
    
    // 过滤出最近的尝试
    const recentAttempts = attempts.filter(time => 
      Date.now() - time < this.lockoutDuration
    )
    
    return recentAttempts.length >= this.maxAttempts
  }

  /**
   * 获取剩余锁定时间
   */
  getRemainingLockoutTime(identifier) {
    const key = `login_attempts_${identifier}`
    const attempts = JSON.parse(localStorage.getItem(key) || '[]')
    
    if (attempts.length === 0) return 0
    
    const oldestAttempt = Math.min(...attempts)
    const elapsed = Date.now() - oldestAttempt
    const remaining = this.lockoutDuration - elapsed
    
    return remaining > 0 ? remaining : 0
  }

  /**
   * 发送HTTP请求
   */
  async makeRequest(endpoint, options = {}) {
    const url = this.baseURL + endpoint
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }

    const config = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers
      }
    }

    try {
      console.log(`🌐 尝试请求: ${url}`)
      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`✅ API请求成功: ${endpoint}`)
      return result
    } catch (error) {
      console.warn(`❌ API请求失败: ${error.message}`)
      console.log('🔍 请求详情:', { endpoint, url, options })

      // 如果是登录请求且是开发模式，使用模拟登录
      if (endpoint === '/login' && this.isDevelopmentMode()) {
        console.warn('🔄 后端服务不可用，使用模拟登录模式')
        console.log('📝 登录数据:', options.body)
        return this.mockLogin(options.body ? JSON.parse(options.body) : {})
      }
      throw error
    }
  }

  /**
   * 检查是否为开发模式
   */
  isDevelopmentMode() {
    // 强制启用开发模式进行测试
    console.log('🔍 检查开发模式:', {
      hostname: location.hostname,
      isLocalhost: location.hostname === 'localhost',
      is127: location.hostname === '127.0.0.1',
      hasGithub: location.hostname.includes('github.io'),
      devMode: localStorage.getItem('dev_mode')
    });

    return true || // 临时强制启用开发模式
           location.hostname === 'localhost' ||
           location.hostname === '127.0.0.1' ||
           location.hostname.includes('github.io') ||
           localStorage.getItem('dev_mode') === 'true'
  }

  /**
   * 模拟登录功能
   */
  async mockLogin(loginData) {
    console.log('🧪 开始模拟登录:', loginData);

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const { username, email, password } = loginData
    const loginField = username || email

    console.log('🔍 登录字段:', { username, email, password, loginField });

    // 预定义的测试账号
    const testAccounts = {
      'admin': { password: 'admin123', role: 'admin', name: '系统管理员' },
      '<EMAIL>': { password: '123456', role: 'admin', name: '系统管理员' },
      '<EMAIL>': { password: 'supersecret', role: 'admin', name: '系统管理员' },
      '<EMAIL>': { password: 'adminpassword', role: 'admin', name: '系统管理员' },
      'designer1': { password: '123456', role: 'designer', name: '设计师' },
      '<EMAIL>': { password: '123456', role: 'designer', name: '设计师' },
      'owner1': { password: '123456', role: 'owner', name: '业主' },
      '<EMAIL>': { password: '123456', role: 'owner', name: '业主' },
      'test': { password: '123456', role: 'user', name: '测试用户' },
      'demo': { password: 'demo123', role: 'user', name: '演示用户' }
    }

    console.log('📋 可用测试账号:', Object.keys(testAccounts));
    console.log('🔑 查找账号:', loginField?.toLowerCase());

    // 验证账号
    const account = testAccounts[loginField?.toLowerCase()]
    console.log('👤 找到账号:', account);

    if (!account) {
      console.error('❌ 账号不存在:', loginField);
      throw new Error(`账号不存在: ${loginField}`)
    }

    if (account.password !== password) {
      console.error('❌ 密码错误:', { expected: account.password, actual: password });
      throw new Error(`密码错误，期望: ${account.password}, 实际: ${password}`)
    }

    console.log('✅ 账号验证通过:', account);

    // 生成模拟令牌
    const accessToken = 'mock_access_token_' + Date.now()
    const refreshToken = 'mock_refresh_token_' + Date.now()

    // 返回成功响应
    return {
      success: true,
      data: {
        accessToken,
        refreshToken,
        user: {
          id: Date.now(),
          username: loginField,
          email: loginField.includes('@') ? loginField : `${loginField}@smarthome.com`,
          name: account.name,
          role: account.role,
          avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          permissions: this.getMockPermissions(account.role)
        }
      },
      message: '登录成功'
    }
  }

  /**
   * 获取模拟权限
   */
  getMockPermissions(role) {
    const permissions = {
      admin: ['*'], // 管理员拥有所有权限
      designer: ['design:*', 'project:read', 'customer:read'],
      owner: ['project:read', 'project:update', 'space:*'],
      user: ['project:read', 'profile:update']
    }
    return permissions[role] || permissions.user
  }

  /**
   * 自动刷新令牌的请求包装器
   */
  async makeAuthenticatedRequest(endpoint, options = {}) {
    let token = this.getAccessToken()
    
    if (!token) {
      throw new Error('用户未登录')
    }

    try {
      return await this.makeRequest(endpoint, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${token}`
        }
      })
    } catch (error) {
      // 如果是令牌过期错误，尝试刷新令牌
      if (error.message.includes('TOKEN_EXPIRED') || error.message.includes('401')) {
        try {
          token = await this.refreshToken()
          return await this.makeRequest(endpoint, {
            ...options,
            headers: {
              ...options.headers,
              'Authorization': `Bearer ${token}`
            }
          })
        } catch (refreshError) {
          console.error('令牌刷新失败:', refreshError)
          this.logout()
          throw refreshError
        }
      }
      throw error
    }
  }
}

// 注意：不要在这里创建全局实例，应该在页面中按需创建
// window.authService = new AuthService()

// 注意：移除了自动跳转逻辑，避免页面闪跳问题
// 登录状态检查应该在具体页面中按需处理

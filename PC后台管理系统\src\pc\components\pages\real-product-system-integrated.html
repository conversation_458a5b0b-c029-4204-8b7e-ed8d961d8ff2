<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居产品管理系统 - 集成版</title>
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="../../../styles/unified-admin-styles.css">
</head>
<body>
    <div class="admin-layout">
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">产品系统集成</h1>
                            <p class="breadcrumb-description">产品系统集成管理</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->

    <div id="connectionStatus" class="connection-status connection-offline">
        🔴 离线模式
    </div>

    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>智能家居产品管理系统</h1>
            <p>管理Aqara、Yeelight等智能家居品牌产品，实现真实的产品上架、图片管理、库存控制</p>
        </div>

        <!-- 统计面板 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalCount">0</div>
                <div class="stat-label">📦 总产品数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="onlineCount">0</div>
                <div class="stat-label">🟢 上架产品</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="stockValue">¥0</div>
                <div class="stat-label">💰 库存总值</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="brandCount">0</div>
                <div class="stat-label">🏷️ 品牌数量</div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-top">
                <div class="search-filters">
                    <input type="text" class="search-input" id="searchInput" 
                           placeholder="🔍 搜索产品名称、SKU或品牌...">
                    <select class="filter-select" id="brandFilter">
                        <option value="">所有品牌</option>
                        <option value="Aqara">Aqara</option>
                        <option value="Yeelight">Yeelight</option>
                        <option value="Xiaomi">小米</option>
                        <option value="Philips">飞利浦</option>
                    </select>
                    <select class="filter-select" id="statusFilter">
                        <option value="">所有状态</option>
                        <option value="online">已上架</option>
                        <option value="offline">已下架</option>
                        <option value="pending">待审核</option>
                    </select>
                </div>
                <div style="display: flex; gap: 12px;">
                    <button class="btn btn-primary" onclick="addProduct()">
                        ➕ 添加产品
                    </button>
                    <button class="btn btn-success" onclick="syncWithServer()">
                        🔄 同步数据
                    </button>
                    <button class="btn btn-warning" onclick="exportProducts()">
                        📁 导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loadingState">
            <div class="spinner"></div>
            <div>加载中...</div>
        </div>

        <!-- 产品网格 -->
        <div class="product-grid" id="productGrid">
            <!-- 产品卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">添加产品</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            
            <div id="alertContainer"></div>
            
            <form id="productForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">产品名称 *</label>
                        <input type="text" class="form-control" id="productName" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">产品SKU *</label>
                        <input type="text" class="form-control" id="productSku" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">品牌 *</label>
                        <select class="form-control" id="productBrand" required>
                            <option value="">选择品牌</option>
                            <option value="Aqara">Aqara</option>
                            <option value="Yeelight">Yeelight</option>
                            <option value="Xiaomi">小米</option>
                            <option value="Philips">飞利浦</option>
                            <option value="其他">其他品牌</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">产品分类 *</label>
                        <select class="form-control" id="productCategory" required>
                            <option value="">选择分类</option>
                            <option value="传感器">传感器</option>
                            <option value="照明设备">照明设备</option>
                            <option value="开关面板">开关面板</option>
                            <option value="智能插座">智能插座</option>
                            <option value="安防设备">安防设备</option>
                            <option value="网关设备">网关设备</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">售价 (¥) *</label>
                        <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">库存数量 *</label>
                        <input type="number" class="form-control" id="productStock" min="0" required>
                    </div>
                </div>
                
                <div class="form-group full-width">
                    <label class="form-label">产品描述</label>
                    <textarea class="form-control form-textarea" id="productDescription" 
                              placeholder="请输入产品详细描述..."></textarea>
                </div>
                
                <div class="form-group full-width">
                    <label class="form-label">产品图片</label>
                    <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                        <input type="file" id="imageInput" style="display:none" multiple accept="image/*">
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">点击上传产品图片</div>
                        <div class="upload-hint">支持JPG、PNG、WebP格式，建议尺寸800x600px，最大5MB</div>
                    </div>
                    <div class="image-preview" id="imagePreview"></div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 16px; margin-top: 32px;">
                    <button type="button" class="btn" onclick="closeModal()" 
                            style="background: #e2e8f0; color: #4a5568;">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        💾 保存产品
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let products = [];
        let currentEditingId = null;
        let uploadedImages = [];
        let isOnlineMode = false;

        // API配置
        const API_BASE_URL = 'http://localhost:3000/api';
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
            checkServerConnection();
        });

        // 初始化应用
        async function initializeApp() {
            showLoading(true);
            
            try {
                // 尝试从服务器获取数据
                await syncWithServer();
            } catch (error) {
                console.log('服务器连接失败，使用本地数据模式');
                loadLocalProducts();
            }
            
            showLoading(false);
        }

        // 检查服务器连接
        async function checkServerConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/real-products/stats`);
                if (response.ok) {
                    updateConnectionStatus(true);
                } else {
                    updateConnectionStatus(false);
                }
            } catch (error) {
                updateConnectionStatus(false);
            }
        }

        // 更新连接状态
        function updateConnectionStatus(isOnline) {
            const statusEl = document.getElementById('connectionStatus');
            isOnlineMode = isOnline;
            
            if (isOnline) {
                statusEl.textContent = '🟢 在线模式';
                statusEl.className = 'connection-status connection-online';
            } else {
                statusEl.textContent = '🔴 离线模式';
                statusEl.className = 'connection-status connection-offline';
            }
        }

        // 从服务器同步数据
        async function syncWithServer() {
            try {
                const response = await fetch(`${API_BASE_URL}/real-products`);
                if (!response.ok) throw new Error('服务器响应错误');
                
                const data = await response.json();
                if (data.success) {
                    products = data.data;
                    updateConnectionStatus(true);
                    renderProducts();
                    updateStats();
                    showAlert('数据同步成功', 'success');
                } else {
                    throw new Error(data.message || '数据获取失败');
                }
            } catch (error) {
                console.error('同步失败:', error);
                updateConnectionStatus(false);
                loadLocalProducts();
                showAlert('服务器连接失败，使用本地数据', 'error');
            }
        }

        // 加载本地产品数据
        function loadLocalProducts() {
            // 如果有本地存储的数据，优先使用
            const localData = localStorage.getItem('smartHomeProducts');
            if (localData) {
                try {
                    products = JSON.parse(localData);
                } catch (error) {
                    console.error('本地数据解析失败:', error);
                    loadDefaultProducts();
                }
            } else {
                loadDefaultProducts();
            }
            
            renderProducts();
            updateStats();
        }

        // 加载默认产品数据
        function loadDefaultProducts() {
            products = [
                {
                    id: 1,
                    name: 'Aqara人体传感器',
                    sku: 'RTCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 89.00,
                    stock: 150,
                    status: 'online',
                    description: '高精度人体移动检测，支持光照度检测，超低功耗设计，检测角度170°，检测距离7米，电池续航2年。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/motion_sensor_p3/4.webp'],
                    createTime: '2024-01-15',
                    updateTime: '2024-01-20'
                },
                {
                    id: 2,
                    name: 'Aqara温湿度传感器',
                    sku: 'WSDCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 69.00,
                    stock: 200,
                    status: 'online',
                    description: '瑞士进口传感器芯片，±0.3°C温度精度，±3%RH湿度精度，实时监测环境温湿度变化。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/temperature_humidity_sensor/1.webp'],
                    createTime: '2024-01-16',
                    updateTime: '2024-01-21'
                },
                {
                    id: 3,
                    name: 'Yeelight LED彩光灯泡',
                    sku: 'YLDP06YL',
                    brand: 'Yeelight',
                    category: '照明设备',
                    price: 79.00,
                    stock: 120,
                    status: 'online',
                    description: 'Wi-Fi智能彩光灯泡，1600万种颜色可调，亮度无级调节，支持语音控制和场景模式。',
                    images: ['https://cdn.yeelight.com/assets/uploads/2021/01/YLDP06YL_01.jpg'],
                    createTime: '2024-01-17',
                    updateTime: '2024-01-22'
                },
                {
                    id: 4,
                    name: 'Aqara智能墙壁开关（单火版）',
                    sku: 'QBKG04LM',
                    brand: 'Aqara',
                    category: '开关面板',
                    price: 129.00,
                    stock: 80,
                    status: 'online',
                    description: '86型单火线智能开关，无需零线，支持传统灯具改造，过载保护和记忆功能。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/wall_switch_h1_single_rocker/1.webp'],
                    createTime: '2024-01-18',
                    updateTime: '2024-01-23'
                },
                {
                    id: 5,
                    name: 'Aqara门窗传感器',
                    sku: 'MCCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 49.00,
                    stock: 180,
                    status: 'online',
                    description: '超小型无线门窗传感器，实时监测门窗开关状态，支持安防模式，安装简便。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/door_window_sensor/1.webp'],
                    createTime: '2024-01-19',
                    updateTime: '2024-01-24'
                },
                {
                    id: 6,
                    name: 'Aqara智能插座',
                    sku: 'SP-EUC01',
                    brand: 'Aqara',
                    category: '智能插座',
                    price: 99.00,
                    stock: 90,
                    status: 'online',
                    description: 'Wi-Fi智能插座，支持远程开关控制，实时功耗监测，过载保护，兼容主流智能音箱。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/smart_plug_eu/1.webp'],
                    createTime: '2024-01-20',
                    updateTime: '2024-01-25'
                }
            ];
        }

        // 渲染产品列表
        function renderProducts(productsToRender = products) {
            const grid = document.getElementById('productGrid');
            
            if (productsToRender.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📦</div>
                        <h3>暂无产品</h3>
                        <p>点击"添加产品"开始管理您的智能家居商品库</p>
                        <button class="btn btn-primary" onclick="addProduct()" style="margin-top: 16px;">
                            ➕ 立即添加
                        </button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = productsToRender.map(product => `
                <div class="product-card">
                    <div class="product-image">
                        ${product.images && product.images.length > 0 ? 
                            `<img src="${product.images[0]}" alt="${product.name}" 
                                 onerror="this.parentElement.innerHTML='<div class=\\"placeholder\\">📷</div>'">` :
                            '<div class="placeholder">📷</div>'
                        }
                    </div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-meta">
                            <span class="product-brand">${product.brand}</span>
                            <span class="product-sku">${product.sku}</span>
                        </div>
                        <div class="product-price">¥${product.price.toFixed(2)}</div>
                        <div class="product-stock">
                            库存: ${product.stock} 件 | ${product.category}
                        </div>
                        <div class="product-actions">
                            <span class="status-badge status-${product.status}">
                                ${getStatusText(product.status)}
                            </span>
                            <button class="btn btn-sm btn-warning" onclick="editProduct(${product.id})">
                                ✏️ 编辑
                            </button>
                            <button class="btn btn-sm ${product.status === 'online' ? 'btn-danger' : 'btn-success'}" 
                                    onclick="toggleProductStatus(${product.id})">
                                ${product.status === 'online' ? '📤 下架' : '📥 上架'}
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'online': '🟢 已上架',
                'offline': '🔴 已下架',
                'pending': '🟡 待审核'
            };
            return statusMap[status] || status;
        }

        // 更新统计数据
        function updateStats() {
            const total = products.length;
            const online = products.filter(p => p.status === 'online').length;
            const stockValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);
            const brands = new Set(products.map(p => p.brand)).size;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('onlineCount').textContent = online;
            document.getElementById('stockValue').textContent = `¥${stockValue.toLocaleString()}`;
            document.getElementById('brandCount').textContent = brands;
        }

        // 搜索和筛选
        function searchProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const brandFilter = document.getElementById('brandFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = products.filter(product => {
                const matchesSearch = !searchTerm || 
                    product.name.toLowerCase().includes(searchTerm) ||
                    product.sku.toLowerCase().includes(searchTerm) ||
                    product.brand.toLowerCase().includes(searchTerm);
                
                const matchesBrand = !brandFilter || product.brand === brandFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesBrand && matchesStatus;
            });

            renderProducts(filtered);
        }

        // 添加产品
        function addProduct() {
            currentEditingId = null;
            uploadedImages = [];
            document.getElementById('modalTitle').textContent = '添加产品';
            document.getElementById('productForm').reset();
            document.getElementById('imagePreview').innerHTML = '';
            document.getElementById('alertContainer').innerHTML = '';
            document.getElementById('productModal').style.display = 'block';
        }

        // 编辑产品
        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            currentEditingId = id;
            uploadedImages = [...(product.images || [])];
            
            document.getElementById('modalTitle').textContent = '编辑产品';
            document.getElementById('productName').value = product.name;
            document.getElementById('productSku').value = product.sku;
            document.getElementById('productBrand').value = product.brand;
            document.getElementById('productCategory').value = product.category;
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productStock').value = product.stock;
            document.getElementById('productDescription').value = product.description || '';

            renderImagePreview();
            document.getElementById('alertContainer').innerHTML = '';
            document.getElementById('productModal').style.display = 'block';
        }

        // 切换产品状态
        async function toggleProductStatus(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            const newStatus = product.status === 'online' ? 'offline' : 'online';
            
            if (isOnlineMode) {
                try {
                    const response = await fetch(`${API_BASE_URL}/real-products/${id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ status: newStatus })
                    });

                    if (!response.ok) throw new Error('服务器更新失败');
                    
                    const data = await response.json();
                    if (data.success) {
                        // 更新本地数据
                        const productIndex = products.findIndex(p => p.id === id);
                        products[productIndex] = data.data;
                        showAlert(`产品已${newStatus === 'online' ? '上架' : '下架'}`, 'success');
                    }
                } catch (error) {
                    showAlert('状态更新失败: ' + error.message, 'error');
                    return;
                }
            } else {
                // 离线模式，本地更新
                product.status = newStatus;
                product.updateTime = new Date().toISOString().split('T')[0];
                saveToLocalStorage();
            }
            
            renderProducts();
            updateStats();
        }

        // 表单提交
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('productName').value,
                sku: document.getElementById('productSku').value,
                brand: document.getElementById('productBrand').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                stock: parseInt(document.getElementById('productStock').value),
                description: document.getElementById('productDescription').value,
                images: uploadedImages,
                status: currentEditingId ? products.find(p => p.id === currentEditingId).status : 'pending'
            };

            try {
                if (isOnlineMode) {
                    // 在线模式，提交到服务器
                    await submitToServer(formData);
                } else {
                    // 离线模式，本地保存
                    saveLocally(formData);
                }
                
                closeModal();
                renderProducts();
                updateStats();
                showAlert(`产品${currentEditingId ? '更新' : '添加'}成功`, 'success');
                
            } catch (error) {
                showAlert('保存失败: ' + error.message, 'error');
            }
        });

        // 提交到服务器
        async function submitToServer(formData) {
            const url = currentEditingId 
                ? `${API_BASE_URL}/real-products/${currentEditingId}`
                : `${API_BASE_URL}/real-products`;
            
            const method = currentEditingId ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) throw new Error('服务器响应错误');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || '操作失败');
            
            // 更新本地数据
            if (currentEditingId) {
                const productIndex = products.findIndex(p => p.id === currentEditingId);
                products[productIndex] = data.data;
            } else {
                products.push(data.data);
            }
        }

        // 本地保存
        function saveLocally(formData) {
            if (currentEditingId) {
                const productIndex = products.findIndex(p => p.id === currentEditingId);
                if (productIndex !== -1) {
                    products[productIndex] = { 
                        ...products[productIndex], 
                        ...formData,
                        updateTime: new Date().toISOString().split('T')[0]
                    };
                }
            } else {
                const newProduct = {
                    id: Date.now(),
                    createTime: new Date().toISOString().split('T')[0],
                    updateTime: new Date().toISOString().split('T')[0],
                    ...formData
                };
                products.push(newProduct);
            }
            
            saveToLocalStorage();
        }

        // 保存到本地存储
        function saveToLocalStorage() {
            localStorage.setItem('smartHomeProducts', JSON.stringify(products));
        }

        // 图片上传处理
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                if (file.type.startsWith('image/') && file.size <= 5 * 1024 * 1024) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedImages.push(e.target.result);
                        renderImagePreview();
                    };
                    reader.readAsDataURL(file);
                } else if (file.size > 5 * 1024 * 1024) {
                    showAlert('图片大小不能超过5MB', 'error');
                }
            });
        });

        // 渲染图片预览
        function renderImagePreview() {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = uploadedImages.map((image, index) => `
                <div class="image-preview-item">
                    <img src="${image}" alt="预览图">
                    <button type="button" class="image-remove" onclick="removeImage(${index})">&times;</button>
                </div>
            `).join('');
        }

        // 移除图片
        function removeImage(index) {
            uploadedImages.splice(index, 1);
            renderImagePreview();
        }

        // 导出产品数据
        function exportProducts() {
            const dataStr = JSON.stringify(products, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `smart_home_products_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            showAlert('产品数据导出成功', 'success');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        // 显示加载状态
        function showLoading(show) {
            const loading = document.getElementById('loadingState');
            const grid = document.getElementById('productGrid');
            
            if (show) {
                loading.classList.add('show');
                grid.style.display = 'none';
            } else {
                loading.classList.remove('show');
                grid.style.display = 'grid';
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);
            
            // 自动消失
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索框实时搜索
            document.getElementById('searchInput').addEventListener('input', searchProducts);
            document.getElementById('brandFilter').addEventListener('change', searchProducts);
            document.getElementById('statusFilter').addEventListener('change', searchProducts);
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modal = document.getElementById('productModal');
                if (e.target === modal) {
                    closeModal();
                }
            });

            // 定期检查服务器连接
            setInterval(checkServerConnection, 30000); // 每30秒检查一次
        }
    </script>
            </div>
        </main>
    </div>
</body>
</html>
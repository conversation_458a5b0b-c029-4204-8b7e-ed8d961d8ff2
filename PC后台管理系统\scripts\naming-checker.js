/**
 * PC端页面命名规范检查工具
 * 用于验证文件命名、API路径、数据库表名是否符合规范
 */

const fs = require('fs');
const path = require('path');

class NamingChecker {
    constructor() {
        this.rules = {
            // 页面文件命名规则
            pages: /^[a-z]+(-[a-z]+)*\.html$/,
            // JavaScript文件命名规则
            scripts: /^[a-z]+(-[a-z]+)*\.js$/,
            // CSS文件命名规则
            styles: /^[a-z]+(-[a-z]+)*\.css$/,
            // API路径命名规则
            apiPaths: /^\/api\/admin\/[a-z]+([\/a-z-]*[a-z])?$/,
            // 数据库表命名规则
            dbTables: /^[a-z]+(_[a-z]+)*$/,
            // 组件类名命名规则
            cssClasses: /^[a-z]+(-[a-z]+)*(__[a-z]+(-[a-z]+)*)?$/
        };

        this.errors = [];
        this.warnings = [];
        this.suggestions = [];
    }

    /**
     * 检查页面文件命名
     */
    checkPageFiles(directory = 'src/pc/components/pages') {
        console.log('🔍 检查页面文件命名...');
        
        try {
            const files = fs.readdirSync(directory);
            const htmlFiles = files.filter(file => file.endsWith('.html'));

            htmlFiles.forEach(file => {
                if (!this.rules.pages.test(file)) {
                    this.errors.push({
                        type: 'PAGE_NAMING',
                        file: file,
                        message: `页面文件命名不规范: ${file}`,
                        suggestion: this.suggestPageName(file)
                    });
                } else {
                    console.log(`✅ ${file} - 命名规范`);
                }
            });

            // 检查重复功能页面
            this.checkDuplicatePages(htmlFiles);

        } catch (error) {
            console.error(`❌ 无法读取目录 ${directory}:`, error.message);
        }
    }

    /**
     * 检查重复功能页面
     */
    checkDuplicatePages(files) {
        const duplicates = {};
        
        files.forEach(file => {
            const baseName = file.replace(/(-new|-fixed|-test|-v\d+)?\.html$/, '');
            if (!duplicates[baseName]) {
                duplicates[baseName] = [];
            }
            duplicates[baseName].push(file);
        });

        Object.entries(duplicates).forEach(([baseName, fileList]) => {
            if (fileList.length > 1) {
                this.warnings.push({
                    type: 'DUPLICATE_PAGES',
                    files: fileList,
                    message: `发现重复功能页面: ${fileList.join(', ')}`,
                    suggestion: `建议保留主版本，其他版本重命名为 ${baseName}-v2.html, ${baseName}-test.html 等`
                });
            }
        });
    }

    /**
     * 检查JavaScript文件命名
     */
    checkScriptFiles(directory = 'src/pc/js') {
        console.log('🔍 检查JavaScript文件命名...');
        
        try {
            const files = fs.readdirSync(directory);
            const jsFiles = files.filter(file => file.endsWith('.js'));

            jsFiles.forEach(file => {
                if (!this.rules.scripts.test(file)) {
                    this.errors.push({
                        type: 'SCRIPT_NAMING',
                        file: file,
                        message: `JavaScript文件命名不规范: ${file}`,
                        suggestion: this.suggestScriptName(file)
                    });
                } else {
                    console.log(`✅ ${file} - 命名规范`);
                }
            });

        } catch (error) {
            console.error(`❌ 无法读取目录 ${directory}:`, error.message);
        }
    }

    /**
     * 检查API路径命名
     */
    checkApiPaths(apiList = []) {
        console.log('🔍 检查API路径命名...');
        
        // 默认API路径列表（可以从配置文件读取）
        const defaultApiPaths = [
            '/api/admin/design',
            '/api/admin/design/projects',
            '/api/admin/design/requirements',
            '/api/admin/construction',
            '/api/admin/construction/projects',
            '/api/admin/products',
            '/api/admin/orders',
            '/api/admin/users',
            '/api/admin/knowledge'
        ];

        const paths = apiList.length > 0 ? apiList : defaultApiPaths;

        paths.forEach(apiPath => {
            if (!this.rules.apiPaths.test(apiPath)) {
                this.errors.push({
                    type: 'API_NAMING',
                    path: apiPath,
                    message: `API路径命名不规范: ${apiPath}`,
                    suggestion: this.suggestApiPath(apiPath)
                });
            } else {
                console.log(`✅ ${apiPath} - 命名规范`);
            }
        });
    }

    /**
     * 检查数据库表命名
     */
    checkDatabaseTables(tableList = []) {
        console.log('🔍 检查数据库表命名...');
        
        // 默认表名列表（可以从数据库schema读取）
        const defaultTables = [
            'design_projects',
            'design_services',
            'design_requirements',
            'construction_projects',
            'construction_teams',
            'products',
            'product_categories',
            'orders',
            'order_items',
            'users',
            'user_profiles',
            'knowledge_articles'
        ];

        const tables = tableList.length > 0 ? tableList : defaultTables;

        tables.forEach(tableName => {
            if (!this.rules.dbTables.test(tableName)) {
                this.errors.push({
                    type: 'DB_TABLE_NAMING',
                    table: tableName,
                    message: `数据库表命名不规范: ${tableName}`,
                    suggestion: this.suggestTableName(tableName)
                });
            } else {
                console.log(`✅ ${tableName} - 命名规范`);
            }
        });
    }

    /**
     * 建议页面文件名
     */
    suggestPageName(fileName) {
        return fileName
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    /**
     * 建议脚本文件名
     */
    suggestScriptName(fileName) {
        return this.suggestPageName(fileName.replace('.js', '')) + '.js';
    }

    /**
     * 建议API路径
     */
    suggestApiPath(apiPath) {
        return apiPath
            .toLowerCase()
            .replace(/[^a-z0-9\/]/g, '')
            .replace(/\/+/g, '/');
    }

    /**
     * 建议表名
     */
    suggestTableName(tableName) {
        return tableName
            .toLowerCase()
            .replace(/[^a-z0-9_]/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    }

    /**
     * 生成H5与PC端对应关系检查
     */
    checkH5PCMapping() {
        console.log('🔍 检查H5与PC端页面对应关系...');
        
        const h5Pages = [
            'home.html',
            'design.html',
            'spaces.html',
            'mall.html',
            'me.html',
            'design-service-detail.html',
            'design-project-detail.html',
            'mall-detail.html',
            'knowledge.html'
        ];

        const expectedPCPages = [
            'admin-dashboard.html',
            'design-management.html',
            'construction-management.html',
            'products-management.html',
            'user-management.html',
            'design-services.html',
            'design-projects.html',
            'product-detail.html',
            'knowledge-management.html'
        ];

        const pcPagesDir = 'src/pc/components/pages';
        let existingPCPages = [];
        
        try {
            existingPCPages = fs.readdirSync(pcPagesDir).filter(file => file.endsWith('.html'));
        } catch (error) {
            console.error('无法读取PC页面目录:', error.message);
            return;
        }

        expectedPCPages.forEach((expectedPage, index) => {
            if (!existingPCPages.includes(expectedPage)) {
                this.warnings.push({
                    type: 'MISSING_PC_PAGE',
                    h5Page: h5Pages[index],
                    expectedPCPage: expectedPage,
                    message: `缺少对应的PC端页面: ${expectedPage}`,
                    suggestion: `为H5页面 ${h5Pages[index]} 创建对应的PC端管理页面 ${expectedPage}`
                });
            }
        });
    }

    /**
     * 生成完整检查报告
     */
    generateReport() {
        console.log('\n📊 生成命名规范检查报告...\n');

        // 执行所有检查
        this.checkPageFiles();
        this.checkScriptFiles();
        this.checkApiPaths();
        this.checkDatabaseTables();
        this.checkH5PCMapping();

        // 生成报告
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalErrors: this.errors.length,
                totalWarnings: this.warnings.length,
                totalSuggestions: this.suggestions.length
            },
            errors: this.errors,
            warnings: this.warnings,
            suggestions: this.suggestions
        };

        // 输出到控制台
        this.printReport(report);

        // 保存到文件
        const reportPath = 'docs/naming-check-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`📄 详细报告已保存到: ${reportPath}`);

        return report;
    }

    /**
     * 打印报告到控制台
     */
    printReport(report) {
        console.log('=' * 60);
        console.log('📋 命名规范检查报告');
        console.log('=' * 60);
        console.log(`检查时间: ${report.timestamp}`);
        console.log(`错误数量: ${report.summary.totalErrors}`);
        console.log(`警告数量: ${report.summary.totalWarnings}`);
        console.log(`建议数量: ${report.summary.totalSuggestions}`);
        console.log('');

        if (report.errors.length > 0) {
            console.log('❌ 错误列表:');
            report.errors.forEach((error, index) => {
                console.log(`${index + 1}. [${error.type}] ${error.message}`);
                if (error.suggestion) {
                    console.log(`   建议: ${error.suggestion}`);
                }
                console.log('');
            });
        }

        if (report.warnings.length > 0) {
            console.log('⚠️  警告列表:');
            report.warnings.forEach((warning, index) => {
                console.log(`${index + 1}. [${warning.type}] ${warning.message}`);
                if (warning.suggestion) {
                    console.log(`   建议: ${warning.suggestion}`);
                }
                console.log('');
            });
        }

        if (report.errors.length === 0 && report.warnings.length === 0) {
            console.log('🎉 恭喜！所有检查项都符合命名规范！');
        }
    }
}

// 命令行使用
if (require.main === module) {
    const checker = new NamingChecker();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--check-pages')) {
        checker.checkPageFiles();
    } else if (args.includes('--check-scripts')) {
        checker.checkScriptFiles();
    } else if (args.includes('--check-api')) {
        checker.checkApiPaths();
    } else if (args.includes('--check-database')) {
        checker.checkDatabaseTables();
    } else if (args.includes('--check-mapping')) {
        checker.checkH5PCMapping();
    } else {
        // 默认执行完整检查
        checker.generateReport();
    }
}

module.exports = NamingChecker;

/**
 * 智能设计与施工管理系统 - 后台管理核心功能
 * 版本: v2.0
 */

// 全局状态管理
window.AdminApp = {
  currentModule: 'dashboard',
  sidebarCollapsed: false,
  modules: new Map(),
  
  // 初始化应用
  init() {
    this.initEventListeners();
    this.initModules();
    this.initSearch();
    console.log('Admin系统已初始化');
  },
  
  // 初始化事件监听器
  initEventListeners() {
    // 窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 全局点击事件 - 关闭下拉菜单
    document.addEventListener('click', this.handleGlobalClick.bind(this));
    
    // 键盘快捷键
    document.addEventListener('keydown', this.handleKeyboard.bind(this));
  },
  
  // 初始化模块
  initModules() {
    // 注册模块
    this.registerModule('dashboard', this.loadDashboard.bind(this));
    this.registerModule('requirements', this.loadRequirements.bind(this));
    this.registerModule('wechat-users', this.loadWechatUsers.bind(this));
    this.registerModule('create-requirement', this.loadCreateRequirement.bind(this));
    this.registerModule('analytics', this.loadAnalytics.bind(this));
  },
  
  // 注册模块
  registerModule(name, loader) {
    this.modules.set(name, loader);
  },
  
  // 初始化搜索功能
  initSearch() {
    const searchInput = document.getElementById('globalSearchInput');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
    }
  }
};

// 侧边栏控制
function toggleSidebar() {
  const layout = document.getElementById('adminApp');
  const sidebar = document.getElementById('adminSidebar');
  
  if (window.innerWidth <= 1024) {
    // 移动端：显示/隐藏侧边栏
    sidebar.classList.toggle('open');
  } else {
    // 桌面端：折叠/展开侧边栏
    layout.classList.toggle('sidebar-collapsed');
    AdminApp.sidebarCollapsed = !AdminApp.sidebarCollapsed;
  }
}

// 模块加载器
function loadModule(moduleName) {
  if (AdminApp.currentModule === moduleName) return;
  
  // 隐藏当前模块
  const currentContent = document.querySelector('.module-content.active');
  if (currentContent) {
    currentContent.classList.remove('active');
  }
  
  // 更新导航状态
  document.querySelectorAll('.nav-link').forEach(link => {
    link.classList.remove('active');
  });
  
  const newActiveLink = document.querySelector(`[onclick="loadModule('${moduleName}')"]`);
  if (newActiveLink) {
    newActiveLink.classList.add('active');
  }
  
  // 显示目标模块
  let targetContent = document.getElementById(`${moduleName}Module`);
  
  if (!targetContent) {
    // 创建模块容器
    targetContent = document.createElement('div');
    targetContent.id = `${moduleName}Module`;
    targetContent.className = 'module-content';
    document.getElementById('pageContent').appendChild(targetContent);
  }
  
  targetContent.classList.add('active');
  
  // 加载模块内容
  const loader = AdminApp.modules.get(moduleName);
  if (loader) {
    loader(targetContent);
  }
  
  AdminApp.currentModule = moduleName;
  
  // 在移动端自动关闭侧边栏
  if (window.innerWidth <= 1024) {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.remove('open');
  }
}

// 模块加载器函数
AdminApp.loadDashboard = function(container) {
  // 仪表盘内容已经在HTML中，无需额外加载
  console.log('仪表盘模块已加载');
};

AdminApp.loadRequirements = function(container) {
  container.innerHTML = `
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-gray-900">需求管理</h2>
            <p class="text-sm text-gray-600 mt-1">管理用户提交的设计需求</p>
          </div>
          <button class="btn btn-primary" onclick="loadModule('create-requirement')">
            <i class="fas fa-plus mr-2"></i>新建需求
          </button>
        </div>
      </div>
      
      <!-- 筛选栏 -->
      <div class="p-4 bg-gray-50 border-b border-gray-200">
        <div class="flex flex-wrap gap-4">
          <div class="flex-1 min-w-64">
            <input type="text" placeholder="搜索需求..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>
          <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            <option>全部状态</option>
            <option>待处理</option>
            <option>处理中</option>
            <option>已完成</option>
          </select>
          <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            <option>全部优先级</option>
            <option>紧急</option>
            <option>高</option>
            <option>普通</option>
          </select>
        </div>
      </div>
      
      <!-- 需求列表 -->
      <div class="divide-y divide-gray-200">
        ${generateRequirementList()}
      </div>
      
      <!-- 分页 -->
      <div class="p-4 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">显示 1-10 条，共 156 条记录</span>
          <div class="flex gap-2">
            <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">上一页</button>
            <button class="px-3 py-1 text-sm bg-black text-white rounded">1</button>
            <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">2</button>
            <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">3</button>
            <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">下一页</button>
          </div>
        </div>
      </div>
    </div>
  `;
};

AdminApp.loadWechatUsers = function(container) {
  // 加载微信用户管理页面
  fetch('./src/pages/user-management/wechat-users.html')
    .then(response => response.text())
    .then(html => {
      // 提取主要内容部分
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const mainContent = doc.querySelector('.main-content');
      
      if (mainContent) {
        container.innerHTML = mainContent.innerHTML;
        // 重新初始化页面脚本
        initWechatUsersModule();
      } else {
        container.innerHTML = '<div class="p-8 text-center text-gray-500">微信用户管理模块加载失败</div>';
      }
    })
    .catch(error => {
      console.error('加载微信用户模块失败:', error);
      container.innerHTML = '<div class="p-8 text-center text-red-500">模块加载失败，请刷新重试</div>';
    });
};

AdminApp.loadCreateRequirement = function(container) {
  container.innerHTML = `
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">新建需求</h2>
        <p class="text-sm text-gray-600 mt-1">录入新的客户设计需求</p>
      </div>
      
      <form class="p-6 space-y-6">
        <!-- 客户信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">客户姓名</label>
            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入客户姓名">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
            <input type="tel" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入联系电话">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">设计风格</label>
            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>现代简约</option>
              <option>新中式</option>
              <option>欧式</option>
              <option>美式</option>
              <option>北欧</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">预算范围</label>
            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>10万以下</option>
              <option>10-20万</option>
              <option>20-50万</option>
              <option>50-100万</option>
              <option>100万以上</option>
            </select>
          </div>
        </div>
        
        <!-- 需求描述 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">需求描述</label>
          <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请详细描述设计需求..."></textarea>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-end gap-4">
          <button type="button" class="btn" onclick="loadModule('requirements')">取消</button>
          <button type="submit" class="btn btn-primary">保存需求</button>
        </div>
      </form>
    </div>
  `;
};

AdminApp.loadAnalytics = function(container) {
  container.innerHTML = `
    <div class="space-y-6">
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">数据分析</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-medium text-gray-800 mb-4">需求趋势</h3>
            <canvas id="trendChart" width="400" height="200"></canvas>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-800 mb-4">客户分布</h3>
            <canvas id="distributionChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // 初始化图表
  setTimeout(() => {
    initAnalyticsCharts();
  }, 100);
};

// 生成需求列表HTML
function generateRequirementList() {
  const requirements = [
    {
      id: 'REQ001',
      customer: '张先生',
      phone: '138****8888',
      style: '现代简约',
      budget: '15-20万',
      status: 'pending',
      priority: 'high',
      createTime: '2024-12-20 14:30'
    },
    {
      id: 'REQ002', 
      customer: '李女士',
      phone: '139****6666',
      style: '新中式',
      budget: '25-30万',
      status: 'processing',
      priority: 'normal',
      createTime: '2024-12-19 10:15'
    },
    {
      id: 'REQ003',
      customer: '王总',
      phone: '137****9999', 
      style: '欧式',
      budget: '50-80万',
      status: 'completed',
      priority: 'urgent',
      createTime: '2024-12-18 16:20'
    }
  ];
  
  return requirements.map(req => `
    <div class="p-4 hover:bg-gray-50 transition-colors">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="flex items-center gap-4">
            <div class="font-medium text-gray-900">${req.id}</div>
            <div class="text-sm text-gray-600">${req.customer} | ${req.phone}</div>
            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">${req.style}</span>
            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">${req.budget}</span>
          </div>
          <div class="mt-2 text-sm text-gray-500">${req.createTime}</div>
        </div>
        <div class="flex items-center gap-3">
          <span class="px-2 py-1 text-xs font-medium rounded ${getStatusClass(req.status)}">${getStatusText(req.status)}</span>
          <span class="px-2 py-1 text-xs font-medium rounded ${getPriorityClass(req.priority)}">${getPriorityText(req.priority)}</span>
          <button class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </div>
    </div>
  `).join('');
}

// 状态样式
function getStatusClass(status) {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800', 
    completed: 'bg-green-100 text-green-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
}

function getStatusText(status) {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成'
  };
  return texts[status] || '未知';
}

// 优先级样式
function getPriorityClass(priority) {
  const classes = {
    urgent: 'bg-red-100 text-red-800',
    high: 'bg-orange-100 text-orange-800',
    normal: 'bg-gray-100 text-gray-800'
  };
  return classes[priority] || 'bg-gray-100 text-gray-800';
}

function getPriorityText(priority) {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通'
  };
  return texts[priority] || '普通';
}

// 搜索处理
AdminApp.handleSearch = function(event) {
  const query = event.target.value.trim();
  if (query.length === 0) return;
  
  console.log('搜索:', query);
  // 这里实现搜索逻辑
};

// 全局点击处理
AdminApp.handleGlobalClick = function(event) {
  // 关闭所有下拉菜单
  const dropdowns = document.querySelectorAll('.dropdown-open');
  dropdowns.forEach(dropdown => {
    if (!dropdown.contains(event.target)) {
      dropdown.classList.remove('dropdown-open');
    }
  });
};

// 窗口大小变化处理
AdminApp.handleResize = function() {
  const sidebar = document.getElementById('adminSidebar');
  if (window.innerWidth > 1024) {
    sidebar.classList.remove('open');
  }
};

// 键盘快捷键处理
AdminApp.handleKeyboard = function(event) {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault();
    const searchInput = document.getElementById('globalSearchInput');
    if (searchInput) {
      searchInput.focus();
    }
  }
  
  // ESC 关闭模态框和下拉菜单
  if (event.key === 'Escape') {
    const dropdowns = document.querySelectorAll('.dropdown-open');
    dropdowns.forEach(dropdown => {
      dropdown.classList.remove('dropdown-open');
    });
  }
};

// 通知中心切换
function toggleNotifications() {
  console.log('切换通知中心');
  // 实现通知中心逻辑
}

// 快速操作切换  
function toggleQuickActions() {
  console.log('切换快速操作');
  // 实现快速操作逻辑
}

// 用户菜单切换
function toggleUserMenu() {
  console.log('切换用户菜单');
  // 实现用户菜单逻辑
}

// 工具函数：防抖
AdminApp.debounce = function(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 微信用户模块初始化（当模块加载后调用）
function initWechatUsersModule() {
  console.log('微信用户模块已初始化');
  // 这里可以重新绑定事件监听器等
}

// 分析图表初始化
function initAnalyticsCharts() {
  // 趋势图表
  const trendCtx = document.getElementById('trendChart');
  if (trendCtx) {
    new Chart(trendCtx, {
      type: 'line',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '需求数量',
          data: [12, 19, 15, 25, 22, 30],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });
  }
  
  // 分布图表
  const distributionCtx = document.getElementById('distributionChart');
  if (distributionCtx) {
    new Chart(distributionCtx, {
      type: 'doughnut',
      data: {
        labels: ['现代简约', '新中式', '欧式', '美式', '北欧'],
        datasets: [{
          data: [35, 25, 15, 15, 10],
          backgroundColor: [
            '#3b82f6',
            '#10b981', 
            '#f59e0b',
            '#ef4444',
            '#8b5cf6'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });
  }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
  AdminApp.init();
}); 
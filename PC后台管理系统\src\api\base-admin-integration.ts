/**
 * Base-Admin API整合模块
 * 将我们已完成的React权限管理界面与Base-Admin的API逻辑对接
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

// API基础配置
const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  baseAdminURL: process.env.BASE_ADMIN_API_URL || 'http://localhost:8001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
}

// 集成状态管理
class BaseAdminIntegrationManager {
  private static instance: BaseAdminIntegrationManager;
  private isConnected: boolean = false;
  private authToken: string | null = null;
  private currentUser: any = null;

  static getInstance(): BaseAdminIntegrationManager {
    if (!BaseAdminIntegrationManager.instance) {
      BaseAdminIntegrationManager.instance = new BaseAdminIntegrationManager();
    }
    return BaseAdminIntegrationManager.instance;
  }

  // 初始化Base-Admin连接
  async initialize(): Promise<boolean> {
    try {
      console.log('🔧 初始化Base-Admin集成...');

      // 检查Base-Admin服务可用性
      const healthCheck = await this.checkBaseAdminHealth();
      if (!healthCheck) {
        console.warn('⚠️ Base-Admin服务不可用，使用本地权限系统');
        return false;
      }

      // 同步权限配置
      await this.syncPermissionConfig();

      this.isConnected = true;
      console.log('✅ Base-Admin集成初始化成功');
      return true;
    } catch (error) {
      console.error('❌ Base-Admin集成初始化失败:', error);
      return false;
    }
  }

  // 检查Base-Admin服务健康状态
  private async checkBaseAdminHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_CONFIG.baseAdminURL}/health`, {
        method: 'GET',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // 同步权限配置到Base-Admin
  private async syncPermissionConfig(): Promise<void> {
    const config = {
      roles: ROLE_MAPPING,
      modules: MODULE_MAPPING,
      timestamp: new Date().toISOString()
    };

    await this.makeBaseAdminRequest('/permissions/sync', {
      method: 'POST',
      body: JSON.stringify(config)
    });
  }

  // Base-Admin API请求封装
  private async makeBaseAdminRequest(endpoint: string, options: any = {}): Promise<any> {
    const url = `${API_CONFIG.baseAdminURL}${endpoint}`;
    const headers = {
      ...API_CONFIG.headers,
      ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` }),
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers,
      timeout: API_CONFIG.timeout
    });

    if (!response.ok) {
      throw new Error(`Base-Admin API错误: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// 用户角色映射（我们的React系统 ↔ Base-Admin系统）
export const ROLE_MAPPING = {
  // 我们的智能家居角色 → Base-Admin角色
  'OWNER': {
    baseAdminRole: 'admin',
    systemPermissions: ['user:manage', 'project:manage', 'system:config'],
    description: '业主/空间所有者，拥有项目空间的完全控制权'
  },
  'FAMILY_MEMBER': {
    baseAdminRole: 'user',
    systemPermissions: ['project:view', 'comment:create'],
    description: '家庭成员，由业主邀请，权限由业主设定和控制'
  },
  'HOME_DESIGNER': {
    baseAdminRole: 'designer',
    systemPermissions: ['design:manage', 'project:edit'],
    description: '家装设计师，被邀请进项目空间参与设计工作'
  },
  'SMART_HOME_DESIGNER': {
    baseAdminRole: 'super_designer',
    systemPermissions: ['design:*', 'project:*', 'system:view'],
    description: '智能家居设计师，可以在设计模块中看到用户所有的项目文件'
  },
  'CONSTRUCTOR': {
    baseAdminRole: 'constructor',
    systemPermissions: ['construction:manage', 'file:upload'],
    description: '施工人员，被邀请参与施工阶段，由用户决定可以看到的内容'
  }
}

// 权限模块映射（我们的模块 ↔ Base-Admin模块）
export const MODULE_MAPPING = {
  // 我们的业务模块 → Base-Admin权限资源
  'project': {
    baseAdminResource: 'project_management',
    operations: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE']
  },
  'design': {
    baseAdminResource: 'design_management',
    operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT']
  },
  'construction': {
    baseAdminResource: 'construction_management',
    operations: ['VIEW', 'UPDATE', 'COMMENT', 'INSPECT', 'APPROVE']
  },
  'cost': {
    baseAdminResource: 'budget_management',
    operations: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE']
  },
  'files': {
    baseAdminResource: 'file_management',
    operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE', 'MANAGE']
  },
  'comments': {
    baseAdminResource: 'comment_management',
    operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
  },
  'marketing': {
    baseAdminResource: 'marketing_management',
    operations: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS']
  },
  'system': {
    baseAdminResource: 'system_management',
    operations: ['VIEW', 'EDIT', 'MANAGE', 'CONFIGURE']
  }
}

// API客户端类
export class BaseAdminAPIClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string = API_CONFIG.baseURL) {
    this.baseURL = baseURL
    this.token = localStorage.getItem('auth_token')
  }

  // 设置认证令牌
  setToken(token: string) {
    this.token = token
    localStorage.setItem('auth_token', token)
  }

  // 清除认证令牌
  clearToken() {
    this.token = null
    localStorage.removeItem('auth_token')
  }

  // 通用请求方法
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const headers = {
      ...API_CONFIG.headers,
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        timeout: API_CONFIG.timeout
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('API请求错误:', error)
      throw error
    }
  }

  // 用户认证API
  async login(credentials: { username: string; password: string }) {
    try {
      const response = await this.request<{
        success: boolean
        data: {
          user: any
          access_token: string
          refresh_token: string
        }
      }>('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials)
      })

      if (response.success && response.data) {
        this.setToken(response.data.access_token)
        return {
          success: true,
          user: response.data.user,
          token: response.data.access_token
        }
      }

      throw new Error('登录失败')
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    }
  }

  // 获取用户列表
  async getUsers(filters: any = {}) {
    try {
      const queryParams = new URLSearchParams(filters).toString()
      const response = await this.request<{
        success: boolean
        data: {
          users: any[]
          total: number
        }
      }>(`/users?${queryParams}`)

      if (response.success) {
        // 将Base-Admin用户数据转换为我们的格式
        const enhancedUsers = response.data.users.map(user => ({
          ...user,
          smartHomeRole: this.mapBaseAdminRoleToSmartHome(user.role),
          permissions: this.getUserPermissions(user.role)
        }))

        return {
          users: enhancedUsers,
          total: response.data.total
        }
      }

      throw new Error('获取用户列表失败')
    } catch (error) {
      console.error('获取用户列表错误:', error)
      throw error
    }
  }

  // 获取角色权限配置
  async getRolePermissions(role: string) {
    try {
      const response = await this.request<{
        success: boolean
        data: {
          role: string
          permissions: string[]
          modules: any
        }
      }>(`/roles/${role}/permissions`)

      if (response.success) {
        // 将Base-Admin权限格式转换为我们的权限格式
        return this.convertPermissionsFormat(response.data)
      }

      throw new Error('获取角色权限失败')
    } catch (error) {
      console.error('获取角色权限错误:', error)
      throw error
    }
  }

  // 更新角色权限
  async updateRolePermissions(role: string, permissions: any) {
    try {
      // 将我们的权限格式转换为Base-Admin格式
      const baseAdminPermissions = this.convertToBaseAdminFormat(permissions)

      const response = await this.request<{
        success: boolean
        message: string
      }>(`/roles/${role}/permissions`, {
        method: 'PUT',
        body: JSON.stringify({
          permissions: baseAdminPermissions
        })
      })

      if (response.success) {
        return {
          success: true,
          message: response.message
        }
      }

      throw new Error('更新角色权限失败')
    } catch (error) {
      console.error('更新角色权限错误:', error)
      throw error
    }
  }

  // 检查用户权限
  async checkPermission(userId: string, module: string, operation: string, projectId?: string) {
    try {
      const baseAdminResource = MODULE_MAPPING[module]?.baseAdminResource || module
      
      const response = await this.request<{
        success: boolean
        data: {
          hasPermission: boolean
          reason?: string
        }
      }>('/permissions/check', {
        method: 'POST',
        body: JSON.stringify({
          userId,
          resource: baseAdminResource,
          action: operation,
          context: projectId ? { projectId } : undefined
        })
      })

      if (response.success) {
        return response.data.hasPermission
      }

      return false
    } catch (error) {
      console.error('权限检查错误:', error)
      return false
    }
  }

  // 创建项目空间
  async createProject(projectData: any) {
    try {
      const response = await this.request<{
        success: boolean
        data: any
      }>('/projects', {
        method: 'POST',
        body: JSON.stringify(projectData)
      })

      if (response.success) {
        return response.data
      }

      throw new Error('创建项目失败')
    } catch (error) {
      console.error('创建项目错误:', error)
      throw error
    }
  }

  // 邀请用户到项目
  async inviteUserToProject(projectId: string, inviteeId: string, role: string, permissions?: string[]) {
    try {
      const response = await this.request<{
        success: boolean
        data: any
      }>(`/projects/${projectId}/invitations`, {
        method: 'POST',
        body: JSON.stringify({
          inviteeId,
          role,
          permissions
        })
      })

      if (response.success) {
        return response.data
      }

      throw new Error('邀请用户失败')
    } catch (error) {
      console.error('邀请用户错误:', error)
      throw error
    }
  }

  // 辅助方法：将Base-Admin角色映射为智能家居角色
  private mapBaseAdminRoleToSmartHome(baseAdminRole: string): string {
    const mapping = {
      'admin': 'OWNER',
      'user': 'FAMILY_MEMBER',
      'designer': 'HOME_DESIGNER',
      'super_designer': 'SMART_HOME_DESIGNER',
      'constructor': 'CONSTRUCTOR'
    }
    return mapping[baseAdminRole] || 'FAMILY_MEMBER'
  }

  // 辅助方法：获取用户权限
  private getUserPermissions(role: string): string[] {
    const smartHomeRole = this.mapBaseAdminRoleToSmartHome(role)
    return ROLE_MAPPING[smartHomeRole]?.systemPermissions || []
  }

  // 辅助方法：转换权限格式（Base-Admin → 我们的格式）
  private convertPermissionsFormat(baseAdminData: any) {
    const permissions = {}
    
    // 遍历我们的模块，映射Base-Admin的权限
    Object.keys(MODULE_MAPPING).forEach(module => {
      const baseAdminResource = MODULE_MAPPING[module].baseAdminResource
      const operations = MODULE_MAPPING[module].operations
      
      permissions[module] = operations.filter(op => 
        baseAdminData.permissions.includes(`${baseAdminResource}:${op}`) ||
        baseAdminData.permissions.includes(`${baseAdminResource}:*`)
      )
    })

    return {
      role: baseAdminData.role,
      permissions,
      modules: MODULE_MAPPING
    }
  }

  // 辅助方法：转换权限格式（我们的格式 → Base-Admin）
  private convertToBaseAdminFormat(permissions: any): string[] {
    const baseAdminPermissions: string[] = []
    
    Object.keys(permissions).forEach(module => {
      const baseAdminResource = MODULE_MAPPING[module]?.baseAdminResource
      if (baseAdminResource && permissions[module]) {
        permissions[module].forEach(operation => {
          baseAdminPermissions.push(`${baseAdminResource}:${operation}`)
        })
      }
    })

    return baseAdminPermissions
  }
}

// 导出API客户端实例
export const apiClient = new BaseAdminAPIClient()

// 导出类型定义
export interface User {
  id: string
  username: string
  email: string
  role: string
  smartHomeRole: string
  permissions: string[]
  createdAt: string
  updatedAt: string
}

export interface Permission {
  module: string
  operations: string[]
}

export interface RolePermissions {
  role: string
  permissions: { [module: string]: string[] }
  modules: any
}

// 导出常量
export { API_CONFIG }

/**
 * 商品管理API客户端
 * 支持MedusaJS兼容的API接口
 */

class ProductsAPI {
    constructor(config = {}) {
        this.baseURL = config.baseURL || '/api/admin';
        this.medusaURL = config.medusaURL || '/api/medusa';
        this.timeout = config.timeout || 10000;
        this.retryCount = config.retryCount || 3;
        this.retryDelay = config.retryDelay || 1000;
    }

    /**
     * 安全的fetch请求
     */
    async secureFetch(url, options = {}) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                ...options.headers
            },
            timeout: this.timeout
        };

        const finalOptions = { ...defaultOptions, ...options };
        
        // 添加超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        finalOptions.signal = controller.signal;

        try {
            const response = await fetch(url, finalOptions);
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * 带重试机制的请求
     */
    async requestWithRetry(url, options = {}, retryCount = this.retryCount) {
        try {
            return await this.secureFetch(url, options);
        } catch (error) {
            if (retryCount > 0 && this.shouldRetry(error)) {
                console.warn(`请求失败，${this.retryDelay}ms后重试 (剩余${retryCount}次):`, error.message);
                await this.delay(this.retryDelay);
                return this.requestWithRetry(url, options, retryCount - 1);
            }
            throw error;
        }
    }

    /**
     * 判断是否应该重试
     */
    shouldRetry(error) {
        // 网络错误或5xx服务器错误可以重试
        return error.name === 'TypeError' || 
               error.message.includes('fetch') ||
               (error.message.includes('HTTP 5'));
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取商品列表
     */
    async getProducts(params = {}) {
        const queryParams = new URLSearchParams();
        
        // 标准参数
        if (params.page) queryParams.append('page', params.page);
        if (params.limit) queryParams.append('limit', params.limit);
        if (params.search) queryParams.append('search', params.search);
        if (params.category) queryParams.append('category', params.category);
        if (params.brand) queryParams.append('brand', params.brand);
        if (params.status) queryParams.append('status', params.status);
        
        // MedusaJS兼容参数
        if (params.offset) queryParams.append('offset', params.offset);
        if (params.order) queryParams.append('order', params.order);
        if (params.expand) queryParams.append('expand', params.expand);

        const url = `${this.baseURL}/products?${queryParams.toString()}`;
        
        try {
            const response = await this.requestWithRetry(url);
            const data = await response.json();
            
            // 标准化响应格式
            return {
                success: true,
                data: {
                    products: data.products || data.data || [],
                    total: data.total || data.count || 0,
                    page: data.page || 1,
                    limit: data.limit || 20,
                    hasMore: data.hasMore || false
                },
                message: '获取商品列表成功'
            };
        } catch (error) {
            console.error('获取商品列表失败:', error);
            return {
                success: false,
                data: null,
                message: `获取商品列表失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 获取单个商品详情
     */
    async getProduct(id) {
        const url = `${this.baseURL}/products/${id}`;
        
        try {
            const response = await this.requestWithRetry(url);
            const data = await response.json();
            
            return {
                success: true,
                data: data.product || data.data || data,
                message: '获取商品详情成功'
            };
        } catch (error) {
            console.error('获取商品详情失败:', error);
            return {
                success: false,
                data: null,
                message: `获取商品详情失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 创建商品
     */
    async createProduct(productData) {
        const url = `${this.baseURL}/products`;
        
        // 数据验证
        const validationResult = this.validateProductData(productData);
        if (!validationResult.valid) {
            return {
                success: false,
                data: null,
                message: `数据验证失败: ${validationResult.errors.join(', ')}`,
                errors: validationResult.errors
            };
        }

        try {
            const response = await this.requestWithRetry(url, {
                method: 'POST',
                body: JSON.stringify(productData)
            });
            
            const data = await response.json();
            
            return {
                success: true,
                data: data.product || data.data || data,
                message: '商品创建成功'
            };
        } catch (error) {
            console.error('创建商品失败:', error);
            return {
                success: false,
                data: null,
                message: `创建商品失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 更新商品
     */
    async updateProduct(id, productData) {
        const url = `${this.baseURL}/products/${id}`;
        
        // 数据验证
        const validationResult = this.validateProductData(productData, false);
        if (!validationResult.valid) {
            return {
                success: false,
                data: null,
                message: `数据验证失败: ${validationResult.errors.join(', ')}`,
                errors: validationResult.errors
            };
        }

        try {
            const response = await this.requestWithRetry(url, {
                method: 'PUT',
                body: JSON.stringify(productData)
            });
            
            const data = await response.json();
            
            return {
                success: true,
                data: data.product || data.data || data,
                message: '商品更新成功'
            };
        } catch (error) {
            console.error('更新商品失败:', error);
            return {
                success: false,
                data: null,
                message: `更新商品失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 删除商品
     */
    async deleteProduct(id) {
        const url = `${this.baseURL}/products/${id}`;
        
        try {
            const response = await this.requestWithRetry(url, {
                method: 'DELETE'
            });
            
            return {
                success: true,
                data: { id },
                message: '商品删除成功'
            };
        } catch (error) {
            console.error('删除商品失败:', error);
            return {
                success: false,
                data: null,
                message: `删除商品失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 批量操作
     */
    async batchOperation(operation, ids, data = {}) {
        const url = `${this.baseURL}/products/batch`;
        
        try {
            const response = await this.requestWithRetry(url, {
                method: 'POST',
                body: JSON.stringify({
                    operation,
                    ids,
                    data
                })
            });
            
            const result = await response.json();
            
            return {
                success: true,
                data: result,
                message: `批量${operation}操作成功`
            };
        } catch (error) {
            console.error('批量操作失败:', error);
            return {
                success: false,
                data: null,
                message: `批量操作失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 数据验证
     */
    validateProductData(data, isCreate = true) {
        const errors = [];
        
        // 必填字段验证
        if (isCreate || data.name !== undefined) {
            if (!data.name || data.name.trim().length < 2) {
                errors.push('商品名称至少2个字符');
            }
        }
        
        if (isCreate || data.sku !== undefined) {
            if (!data.sku || !/^[A-Z]{2}\d{3,}$/.test(data.sku)) {
                errors.push('SKU格式不正确，应为2个大写字母+3位以上数字');
            }
        }
        
        if (isCreate || data.price !== undefined) {
            if (!data.price || isNaN(data.price) || data.price <= 0) {
                errors.push('价格必须为正数');
            }
        }
        
        if (isCreate || data.category !== undefined) {
            if (!data.category) {
                errors.push('商品分类不能为空');
            }
        }
        
        // 可选字段验证
        if (data.stock !== undefined && (isNaN(data.stock) || data.stock < 0)) {
            errors.push('库存数量不能为负数');
        }
        
        if (data.description && data.description.length > 1000) {
            errors.push('商品描述不能超过1000字符');
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * MedusaJS同步
     */
    async syncToMedusa(productId = null) {
        const url = productId 
            ? `${this.medusaURL}/sync/product/${productId}`
            : `${this.medusaURL}/sync/products`;
        
        try {
            const response = await this.requestWithRetry(url, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            return {
                success: true,
                data: data,
                message: 'MedusaJS同步成功'
            };
        } catch (error) {
            console.error('MedusaJS同步失败:', error);
            return {
                success: false,
                data: null,
                message: `MedusaJS同步失败: ${error.message}`,
                error: error
            };
        }
    }
}

// 导出API实例
window.ProductsAPI = ProductsAPI;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后系统测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .status-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .status-label {
            color: #6b7280;
            font-size: 14px;
        }
        
        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .result-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .status-pass {
            color: #10b981;
            font-weight: bold;
        }
        
        .status-fail {
            color: #ef4444;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> 修复后系统测试</h1>
            <p>验证修复后的施工管理系统功能</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-number" style="color: #3b82f6;" id="moduleCount">0</div>
                <div class="status-label">已加载模块</div>
            </div>
            <div class="status-card">
                <div class="status-number" style="color: #10b981;" id="testCount">0</div>
                <div class="status-label">通过测试</div>
            </div>
            <div class="status-card">
                <div class="status-number" style="color: #ef4444;" id="errorCount">0</div>
                <div class="status-label">错误数量</div>
            </div>
            <div class="status-card">
                <div class="status-number" style="color: #f59e0b;" id="successRate">0%</div>
                <div class="status-label">成功率</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="runFixedTest()">
                <i class="fas fa-play"></i> 开始测试
            </button>
            <button class="btn btn-success" onclick="clearLog()">
                <i class="fas fa-trash"></i> 清空日志
            </button>
        </div>
        
        <div class="test-log" id="testLog">
            <div style="color: #10b981; font-weight: bold;">🔧 修复后系统测试控制台</div>
            <div style="color: #6b7280; margin-top: 8px;">点击"开始测试"验证修复效果...</div>
        </div>
        
        <div class="test-results" id="testResults" style="display: none;">
            <!-- 测试结果将在这里显示 -->
        </div>
    </div>

    <!-- 引入修复后的脚本 -->
    <script src="../src/pc/js/admin-common.js"></script>
    <script src="../src/pc/components/js/file-manager.js"></script>
    <script src="../src/pc/components/js/knowledge-manager.js"></script>
    <script src="../src/pc/components/js/record-manager.js"></script>
    <script src="../src/pc/components/js/issue-manager.js"></script>
    <script src="../src/pc/components/js/acceptance-manager.js"></script>
    <script src="../src/pc/components/js/construction-enhanced.js"></script>

    <script>
        class FixedSystemTester {
            constructor() {
                this.testResults = [];
                this.moduleCount = 0;
                this.testCount = 0;
                this.errorCount = 0;
                this.setupErrorCapture();
            }

            setupErrorCapture() {
                window.addEventListener('error', (event) => {
                    this.errorCount++;
                    this.updateCounters();
                    this.log(`❌ JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`);
                });

                window.addEventListener('unhandledrejection', (event) => {
                    this.errorCount++;
                    this.updateCounters();
                    this.log(`❌ Promise错误: ${event.reason}`);
                });
            }

            async runFixedTest() {
                this.log('🔧 开始修复后系统测试...');
                this.log('');

                // 重置计数器
                this.moduleCount = 0;
                this.testCount = 0;
                this.errorCount = 0;
                this.testResults = [];

                // 1. 检查模块加载
                await this.checkModuleLoading();
                
                // 2. 检查模块初始化
                await this.checkModuleInitialization();
                
                // 3. 检查核心功能
                await this.checkCoreFunctions();
                
                // 4. 生成测试报告
                this.generateTestReport();
            }

            async checkModuleLoading() {
                this.log('📋 检查模块加载状态...');
                
                const modules = [
                    { name: 'FileManager', class: window.FileManager },
                    { name: 'KnowledgeManager', class: window.KnowledgeManager },
                    { name: 'RecordManager', class: window.RecordManager },
                    { name: 'IssueManager', class: window.IssueManager },
                    { name: 'AcceptanceManager', class: window.AcceptanceManager },
                    { name: 'ConstructionManager', class: window.ConstructionManager }
                ];

                for (const module of modules) {
                    if (module.class && typeof module.class === 'function') {
                        this.moduleCount++;
                        this.addTestResult(`${module.name}类定义`, true);
                        this.log(`✅ ${module.name} 类已加载`);
                    } else {
                        this.addTestResult(`${module.name}类定义`, false);
                        this.log(`❌ ${module.name} 类未加载`);
                    }
                    await this.sleep(100);
                }
                
                this.updateCounters();
            }

            async checkModuleInitialization() {
                this.log('📋 检查模块初始化状态...');
                
                // 等待constructionManager初始化
                let attempts = 0;
                while (!window.constructionManager && attempts < 50) {
                    await this.sleep(100);
                    attempts++;
                }

                if (window.constructionManager) {
                    this.addTestResult('ConstructionManager初始化', true);
                    this.log('✅ ConstructionManager 已初始化');
                    
                    // 检查子模块
                    const subModules = [
                        { name: 'fileManager', instance: window.constructionManager.fileManager },
                        { name: 'knowledgeManager', instance: window.constructionManager.knowledgeManager },
                        { name: 'recordManager', instance: window.constructionManager.recordManager },
                        { name: 'issueManager', instance: window.constructionManager.issueManager },
                        { name: 'acceptanceManager', instance: window.constructionManager.acceptanceManager }
                    ];

                    for (const subModule of subModules) {
                        if (subModule.instance && typeof subModule.instance === 'object') {
                            this.addTestResult(`${subModule.name}初始化`, true);
                            this.log(`✅ ${subModule.name} 已初始化`);
                        } else {
                            this.addTestResult(`${subModule.name}初始化`, false);
                            this.log(`❌ ${subModule.name} 未初始化`);
                        }
                    }
                } else {
                    this.addTestResult('ConstructionManager初始化', false);
                    this.log('❌ ConstructionManager 未初始化');
                }
                
                this.updateCounters();
            }

            async checkCoreFunctions() {
                this.log('📋 检查核心功能...');
                
                if (window.constructionManager) {
                    // 检查核心方法
                    const coreMethods = [
                        'switchPhase',
                        'loadPhaseContent',
                        'savePhaseData',
                        'loadPhaseData'
                    ];

                    for (const method of coreMethods) {
                        if (typeof window.constructionManager[method] === 'function') {
                            this.addTestResult(`${method}方法`, true);
                            this.log(`✅ ${method} 方法存在`);
                        } else {
                            this.addTestResult(`${method}方法`, false);
                            this.log(`❌ ${method} 方法不存在`);
                        }
                    }

                    // 测试阶段切换
                    try {
                        window.constructionManager.switchPhase('briefing');
                        this.addTestResult('阶段切换功能', true);
                        this.log('✅ 阶段切换功能正常');
                    } catch (error) {
                        this.addTestResult('阶段切换功能', false);
                        this.log(`❌ 阶段切换功能异常: ${error.message}`);
                    }

                    // 测试数据保存
                    try {
                        window.constructionManager.savePhaseData();
                        this.addTestResult('数据保存功能', true);
                        this.log('✅ 数据保存功能正常');
                    } catch (error) {
                        this.addTestResult('数据保存功能', false);
                        this.log(`❌ 数据保存功能异常: ${error.message}`);
                    }
                }
                
                this.updateCounters();
            }

            generateTestReport() {
                this.log('');
                this.log('📊 生成测试报告...');
                this.log('='.repeat(50));
                
                const totalTests = this.testResults.length;
                const passedTests = this.testResults.filter(r => r.passed).length;
                const failedTests = totalTests - passedTests;
                const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
                
                this.log(`总测试数: ${totalTests}`);
                this.log(`通过测试: ${passedTests}`);
                this.log(`失败测试: ${failedTests}`);
                this.log(`成功率: ${successRate}%`);
                this.log(`错误数量: ${this.errorCount}`);
                
                // 显示详细结果
                this.displayDetailedResults();
                
                this.log('');
                if (successRate >= 90) {
                    this.log('🎉 系统修复成功！功能正常运行');
                } else if (successRate >= 70) {
                    this.log('⚠️ 系统部分修复，仍有问题需要解决');
                } else {
                    this.log('❌ 系统修复不完整，需要进一步修复');
                }
                
                this.updateCounters();
            }

            displayDetailedResults() {
                const resultsContainer = document.getElementById('testResults');
                resultsContainer.style.display = 'grid';
                
                const moduleResults = this.testResults.filter(r => r.name.includes('类定义') || r.name.includes('初始化'));
                const functionResults = this.testResults.filter(r => r.name.includes('方法') || r.name.includes('功能'));
                
                resultsContainer.innerHTML = `
                    <div class="result-card">
                        <h3><i class="fas fa-cubes"></i> 模块加载结果</h3>
                        ${moduleResults.map(result => `
                            <div class="result-item">
                                <span>${result.name}</span>
                                <span class="${result.passed ? 'status-pass' : 'status-fail'}">
                                    ${result.passed ? '✅ 通过' : '❌ 失败'}
                                </span>
                            </div>
                        `).join('')}
                    </div>
                    <div class="result-card">
                        <h3><i class="fas fa-cogs"></i> 功能测试结果</h3>
                        ${functionResults.map(result => `
                            <div class="result-item">
                                <span>${result.name}</span>
                                <span class="${result.passed ? 'status-pass' : 'status-fail'}">
                                    ${result.passed ? '✅ 通过' : '❌ 失败'}
                                </span>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            addTestResult(name, passed) {
                this.testResults.push({ name, passed });
                if (passed) this.testCount++;
            }

            updateCounters() {
                document.getElementById('moduleCount').textContent = this.moduleCount;
                document.getElementById('testCount').textContent = this.testCount;
                document.getElementById('errorCount').textContent = this.errorCount;
                
                const totalTests = this.testResults.length;
                const successRate = totalTests > 0 ? Math.round((this.testCount / totalTests) * 100) : 0;
                document.getElementById('successRate').textContent = successRate + '%';
            }

            log(message) {
                const output = document.getElementById('testLog');
                const div = document.createElement('div');
                div.style.marginBottom = '4px';
                
                if (message.includes('✅')) {
                    div.style.color = '#10b981';
                } else if (message.includes('❌')) {
                    div.style.color = '#ef4444';
                } else if (message.includes('📋') || message.includes('🔧') || message.includes('📊')) {
                    div.style.color = '#3b82f6';
                    div.style.fontWeight = 'bold';
                } else if (message.includes('🎉')) {
                    div.style.color = '#10b981';
                    div.style.fontWeight = 'bold';
                } else if (message.includes('=')) {
                    div.style.color = '#6b7280';
                }
                
                div.textContent = message;
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 全局测试实例
        const fixedTester = new FixedSystemTester();

        // 全局函数
        async function runFixedTest() {
            await fixedTester.runFixedTest();
        }

        function clearLog() {
            const output = document.getElementById('testLog');
            output.innerHTML = `
                <div style="color: #10b981; font-weight: bold;">🔧 修复后系统测试控制台</div>
                <div style="color: #6b7280; margin-top: 8px;">日志已清空，准备开始新的测试...</div>
            `;
            
            document.getElementById('testResults').style.display = 'none';
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('🔧 修复后测试页面已加载');
            setTimeout(() => {
                runFixedTest();
            }, 2000);
        });
    </script>
</body>
</html>

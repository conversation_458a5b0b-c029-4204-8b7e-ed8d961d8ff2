<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居产品上架管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 18px;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 3px dashed #bdc3c7;
            border-radius: 12px;
            padding: 60px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .upload-area.dragover {
            border-color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }

        .upload-icon {
            font-size: 64px;
            color: #bdc3c7;
            margin-bottom: 20px;
        }

        .upload-text {
            color: #7f8c8d;
            font-size: 18px;
            margin-bottom: 15px;
        }

        .upload-hint {
            color: #95a5a6;
            font-size: 14px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .product-image {
            height: 240px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-image:hover img {
            transform: scale(1.05);
        }

        .image-placeholder {
            color: #bdc3c7;
            font-size: 48px;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .product-details {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .product-price {
            font-size: 24px;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .product-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
            align-items: center;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
            margin: 0;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 600;
        }

        .upload-progress {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #2c3e50;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-timestamp {
            color: #7f8c8d;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            max-width: 600px;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #bdc3c7;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-textarea {
            height: 100px;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-panel {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🏠 智能家居产品上架管理</h1>
            <p>专业的产品图片和内容管理系统，支持Aqara、Yeelight等品牌产品上架</p>
        </div>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">总产品数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uploadedImages">0</div>
                <div class="stat-label">已上传图片</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeProducts">0</div>
                <div class="stat-label">已上架产品</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingProducts">0</div>
                <div class="stat-label">待审核产品</div>
            </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">📤 产品图片上传</h2>
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📷</div>
                <div class="upload-text">拖拽产品图片到此处或点击上传</div>
                <div class="upload-hint">支持 JPG、PNG、WebP 格式，建议尺寸 800x600px，最大 5MB</div>
                <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
                <button class="btn" onclick="document.getElementById('fileInput').click()">选择图片文件</button>
                <button class="btn" onclick="addNewProduct()">手动添加产品</button>
                <button class="btn" onclick="importBrandProducts()">导入品牌产品</button>
            </div>
            
            <!-- 上传进度 -->
            <div class="upload-progress" id="uploadProgress">
                <h3>上传进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="uploadStatus">准备上传...</div>
            </div>
        </div>

        <!-- 产品网格 -->
        <div class="product-grid" id="productGrid">
            <!-- 产品卡片将在这里动态生成 -->
        </div>

        <!-- 操作日志 -->
        <div class="log-panel">
            <h3 style="margin-bottom: 15px; color: #2c3e50;">📋 操作日志</h3>
            <div id="logContainer">
                <!-- 日志条目将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">添加新产品</h2>
            <form id="productForm">
                <div class="form-group">
                    <label class="form-label">产品名称</label>
                    <input type="text" id="productName" class="form-input" placeholder="例：Aqara人体传感器" required>
                </div>
                <div class="form-group">
                    <label class="form-label">产品SKU</label>
                    <input type="text" id="productSku" class="form-input" placeholder="例：RTCGQ11LM" required>
                </div>
                <div class="form-group">
                    <label class="form-label">品牌</label>
                    <select id="productBrand" class="form-input" required>
                        <option value="">选择品牌</option>
                        <option value="Aqara">Aqara</option>
                        <option value="Yeelight">Yeelight</option>
                        <option value="Xiaomi">小米</option>
                        <option value="Philips">飞利浦</option>
                        <option value="Other">其他品牌</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">产品分类</label>
                    <select id="productCategory" class="form-input" required>
                        <option value="">选择分类</option>
                        <option value="sensor">传感器</option>
                        <option value="lighting">照明设备</option>
                        <option value="switch">开关面板</option>
                        <option value="socket">智能插座</option>
                        <option value="security">安防设备</option>
                        <option value="gateway">网关设备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">售价 (¥)</label>
                    <input type="number" id="productPrice" class="form-input" placeholder="例：89.00" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label class="form-label">库存数量</label>
                    <input type="number" id="productStock" class="form-input" placeholder="例：150" min="0" required>
                </div>
                <div class="form-group">
                    <label class="form-label">产品描述</label>
                    <textarea id="productDescription" class="form-input form-textarea" placeholder="描述产品特色功能和技术参数" required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">产品图片URL（多个用逗号分隔）</label>
                    <textarea id="productImages" class="form-input form-textarea" placeholder="https://example.com/image1.jpg,https://example.com/image2.jpg"></textarea>
                </div>
                <div style="text-align: right; margin-top: 30px;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #95a5a6;">取消</button>
                    <button type="submit" class="btn">保存产品</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let products = [];
        let currentEditingId = null;

        // 日志记录函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // 更新统计数据
        function updateStats() {
            const total = products.length;
            const active = products.filter(p => p.status === 'active').length;
            const pending = products.filter(p => p.status === 'pending').length;
            const totalImages = products.reduce((sum, p) => sum + (p.product_images ? p.product_images.length : 0), 0);

            document.getElementById('totalProducts').textContent = total;
            document.getElementById('activeProducts').textContent = active;
            document.getElementById('pendingProducts').textContent = pending;
            document.getElementById('uploadedImages').textContent = totalImages;
        }

        // 渲染产品网格
        function renderProducts() {
            const grid = document.getElementById('productGrid');
            grid.innerHTML = '';

            if (products.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 60px; color: #7f8c8d;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📦</div>
                        <h3>暂无产品</h3>
                        <p>请上传产品图片或手动添加产品信息</p>
                    </div>
                `;
                return;
            }

            products.forEach(product => {
                const card = document.createElement('div');
                card.className = 'product-card';
                
                const hasImages = product.product_images && product.product_images.length > 0;
                const mainImage = hasImages ? product.product_images[0] : null;

                card.innerHTML = `
                    <div class="product-image">
                        ${mainImage ? 
                            `<img src="${mainImage}" alt="${product.name}" onerror="this.parentElement.innerHTML='<div class=\\"image-placeholder\\">📷</div>'">` :
                            '<div class="image-placeholder">📷</div>'
                        }
                    </div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-details">
                            品牌: ${product.brand} | SKU: ${product.sku}<br>
                            分类: ${getCategoryName(product.category)} | 库存: ${product.stock}
                        </div>
                        <div class="product-price">¥${product.price.toFixed(2)}</div>
                        <div class="product-actions">
                            <span class="status-badge status-${product.status}">
                                ${product.status === 'active' ? '已上架' : '待审核'}
                            </span>
                            <div>
                                <button class="btn btn-small btn-warning" onclick="editProduct(${product.id})">编辑</button>
                                <button class="btn btn-small btn-success" onclick="toggleProductStatus(${product.id})">
                                    ${product.status === 'active' ? '下架' : '上架'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        // 获取分类中文名称
        function getCategoryName(category) {
            const categoryMap = {
                'sensor': '传感器',
                'lighting': '照明设备',
                'switch': '开关面板',
                'socket': '智能插座',
                'security': '安防设备',
                'gateway': '网关设备'
            };
            return categoryMap[category] || category;
        }

        // 添加新产品
        function addNewProduct() {
            currentEditingId = null;
            document.getElementById('modalTitle').textContent = '添加新产品';
            document.getElementById('productForm').reset();
            document.getElementById('productModal').style.display = 'block';
        }

        // 编辑产品
        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            currentEditingId = id;
            document.getElementById('modalTitle').textContent = '编辑产品信息';
            document.getElementById('productName').value = product.name;
            document.getElementById('productSku').value = product.sku;
            document.getElementById('productBrand').value = product.brand;
            document.getElementById('productCategory').value = product.category;
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productStock').value = product.stock;
            document.getElementById('productDescription').value = product.description;
            document.getElementById('productImages').value = product.product_images ? product.product_images.join(',') : '';
            document.getElementById('productModal').style.display = 'block';
        }

        // 切换产品状态
        function toggleProductStatus(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            product.status = product.status === 'active' ? 'pending' : 'active';
            log(`${product.status === 'active' ? '上架' : '下架'}产品: ${product.name}`);
            renderProducts();
            updateStats();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        // 导入品牌产品
        function importBrandProducts() {
            const brandProducts = [
                {
                    id: Date.now() + 1,
                    name: 'Aqara人体传感器',
                    sku: 'RTCGQ11LM',
                    category: 'sensor',
                    brand: 'Aqara',
                    price: 89.00,
                    stock: 150,
                    status: 'active',
                    description: '高精度人体移动检测，支持光照度检测，超低功耗设计',
                    product_images: [
                        'https://cdn.aqara.com/uploads/product/aqara_motion_sensor_main.jpg',
                        'https://cdn.aqara.com/uploads/product/aqara_motion_sensor_side.jpg'
                    ]
                },
                {
                    id: Date.now() + 2,
                    name: 'Aqara温湿度传感器',
                    sku: 'WSDCGQ11LM',
                    category: 'sensor',
                    brand: 'Aqara',
                    price: 69.00,
                    stock: 200,
                    status: 'active',
                    description: '高精度温湿度监测，实时数据推送，小巧设计',
                    product_images: [
                        'https://cdn.aqara.com/uploads/product/temperature_humidity_sensor.jpg'
                    ]
                },
                {
                    id: Date.now() + 3,
                    name: 'Yeelight LED智能灯泡',
                    sku: 'YLDP05YL',
                    category: 'lighting',
                    brand: 'Yeelight',
                    price: 79.00,
                    stock: 120,
                    status: 'active',
                    description: '1600万色彩调节，智能调光调色，APP远程控制',
                    product_images: [
                        'https://www.yeelight.com/uploads/product/color_bulb_main.jpg',
                        'https://www.yeelight.com/uploads/product/rgb_color_effect.jpg'
                    ]
                },
                {
                    id: Date.now() + 4,
                    name: 'Aqara智能开关（单火版）',
                    sku: 'QBKG04LM',
                    category: 'switch',
                    brand: 'Aqara',
                    price: 129.00,
                    stock: 80,
                    status: 'active',
                    description: '单火线智能开关，支持语音控制，定时场景联动',
                    product_images: [
                        'https://cdn.aqara.com/uploads/product/wall_switch_single.jpg'
                    ]
                },
                {
                    id: Date.now() + 5,
                    name: 'Aqara门窗传感器',
                    sku: 'MCCGQ11LM',
                    category: 'sensor',
                    brand: 'Aqara',
                    price: 49.00,
                    stock: 180,
                    status: 'active',
                    description: '门窗开关状态实时监测，安防报警，小巧隐蔽',
                    product_images: [
                        'https://cdn.aqara.com/uploads/product/door_window_sensor.jpg'
                    ]
                },
                {
                    id: Date.now() + 6,
                    name: 'Aqara智能插座',
                    sku: 'SP-EUC01',
                    category: 'socket',
                    brand: 'Aqara',
                    price: 99.00,
                    stock: 90,
                    status: 'active',
                    description: '远程控制家电开关，功耗统计，过载保护',
                    product_images: [
                        'https://cdn.aqara.com/uploads/product/smart_plug_main.jpg'
                    ]
                }
            ];

            products = products.concat(brandProducts);
            log(`成功导入 ${brandProducts.length} 个品牌产品`);
            renderProducts();
            updateStats();
        }

        // 表单提交处理
        document.getElementById('productForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('productName').value,
                sku: document.getElementById('productSku').value,
                brand: document.getElementById('productBrand').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                stock: parseInt(document.getElementById('productStock').value),
                description: document.getElementById('productDescription').value,
                product_images: document.getElementById('productImages').value.split(',').map(url => url.trim()).filter(url => url),
                status: 'pending'
            };

            if (currentEditingId) {
                // 编辑现有产品
                const productIndex = products.findIndex(p => p.id === currentEditingId);
                if (productIndex !== -1) {
                    products[productIndex] = { ...products[productIndex], ...formData };
                    log(`更新产品信息: ${formData.name}`);
                }
            } else {
                // 添加新产品
                const newProduct = {
                    id: Date.now(),
                    ...formData
                };
                products.push(newProduct);
                log(`添加新产品: ${formData.name}`);
            }

            closeModal();
            renderProducts();
            updateStats();
        });

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                handleFileUpload(files);
            }
        });

        // 拖拽上传处理
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFileUpload(files.filter(file => file.type.startsWith('image/')));
        });

        // 处理文件上传
        function handleFileUpload(files) {
            if (files.length === 0) return;

            log(`开始处理 ${files.length} 个图片文件`);
            const progressSection = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const uploadStatus = document.getElementById('uploadStatus');

            progressSection.style.display = 'block';
            
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 模拟上传进度
                    const progress = ((index + 1) / files.length) * 100;
                    progressFill.style.width = progress + '%';
                    uploadStatus.textContent = `上传中... ${index + 1}/${files.length}`;

                    // 这里可以集成实际的图片上传API
                    // 目前使用本地预览
                    log(`处理图片: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`);

                    if (index === files.length - 1) {
                        setTimeout(() => {
                            uploadStatus.textContent = '上传完成！';
                            log(`所有图片处理完成`);
                            setTimeout(() => {
                                progressSection.style.display = 'none';
                            }, 2000);
                        }, 500);
                    }
                };
                reader.readAsDataURL(file);
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('智能家居产品上架管理系统初始化完成');
            updateStats();
            renderProducts();

            // 模拟加载一些示例数据
            setTimeout(() => {
                log('点击"导入品牌产品"按钮可快速添加Aqara和Yeelight产品示例');
            }, 1000);
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('productModal');
            if (e.target === modal) {
                closeModal();
            }
        });
    </script>
</body>
</html>
/**
 * 商品模态框组件
 * 支持创建和编辑商品
 */

class ProductModal {
    constructor(options = {}) {
        this.options = {
            title: '商品信息',
            size: 'large',
            ...options
        };
        
        this.modal = null;
        this.form = null;
        this.isEdit = false;
        this.currentProduct = null;
        
        // 事件回调
        this.onSave = options.onSave || (() => {});
        this.onCancel = options.onCancel || (() => {});
        
        this.init();
    }

    init() {
        this.createModal();
        this.setupEventListeners();
    }

    createModal() {
        // 创建模态框HTML
        const modalHTML = `
            <div class="modal fade" id="productModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-${this.options.size}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${this.options.title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="productForm" class="product-form">
                                <div class="row">
                                    <!-- 基本信息 -->
                                    <div class="col-md-8">
                                        <div class="form-section">
                                            <h6 class="section-title">基本信息</h6>
                                            
                                            <div class="mb-3">
                                                <label for="productName" class="form-label">商品名称 <span class="required">*</span></label>
                                                <input type="text" class="form-control" id="productName" name="name" required>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productSku" class="form-label">SKU <span class="required">*</span></label>
                                                        <input type="text" class="form-control" id="productSku" name="sku" required>
                                                        <div class="form-text">格式：2个大写字母+3位以上数字</div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productBrand" class="form-label">品牌</label>
                                                        <input type="text" class="form-control" id="productBrand" name="brand">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="productCategory" class="form-label">商品分类 <span class="required">*</span></label>
                                                <select class="form-select" id="productCategory" name="category" required>
                                                    <option value="">请选择分类</option>
                                                    <option value="switch">智能开关</option>
                                                    <option value="lighting">智能照明</option>
                                                    <option value="security">安防设备</option>
                                                    <option value="sensor">传感器</option>
                                                    <option value="environment">环境控制</option>
                                                </select>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="productDescription" class="form-label">商品描述</label>
                                                <textarea class="form-control" id="productDescription" name="description" rows="4" maxlength="1000"></textarea>
                                                <div class="form-text">最多1000字符</div>
                                            </div>
                                        </div>
                                        
                                        <!-- 价格库存 -->
                                        <div class="form-section">
                                            <h6 class="section-title">价格与库存</h6>
                                            
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="productPrice" class="form-label">销售价格 <span class="required">*</span></label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">¥</span>
                                                            <input type="number" class="form-control" id="productPrice" name="price" step="0.01" min="0" required>
                                                        </div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="productStock" class="form-label">库存数量</label>
                                                        <input type="number" class="form-control" id="productStock" name="stock" min="0" value="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="productWeight" class="form-label">重量(kg)</label>
                                                        <input type="number" class="form-control" id="productWeight" name="weight" step="0.1" min="0">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 状态设置 -->
                                        <div class="form-section">
                                            <h6 class="section-title">状态设置</h6>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productStatus" class="form-label">商品状态</label>
                                                        <select class="form-select" id="productStatus" name="status">
                                                            <option value="active">上架中</option>
                                                            <option value="inactive">已下架</option>
                                                            <option value="draft">草稿</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check mt-4">
                                                            <input class="form-check-input" type="checkbox" id="syncToMedusa" name="sync_to_medusa">
                                                            <label class="form-check-label" for="syncToMedusa">
                                                                同步到MedusaJS
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 图片上传 -->
                                    <div class="col-md-4">
                                        <div class="form-section">
                                            <h6 class="section-title">商品图片</h6>
                                            
                                            <div class="image-upload-area">
                                                <div class="upload-zone" id="imageUploadZone">
                                                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                                    <p>点击或拖拽上传图片</p>
                                                    <p class="upload-hint">支持 JPG、PNG 格式，最大 5MB</p>
                                                    <input type="file" id="imageInput" accept="image/*" multiple style="display: none;">
                                                </div>
                                                
                                                <div class="image-preview-list" id="imagePreviewList">
                                                    <!-- 图片预览将在这里显示 -->
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 快速操作 -->
                                        <div class="form-section">
                                            <h6 class="section-title">快速操作</h6>
                                            
                                            <div class="quick-actions">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="generateSku">
                                                    <i class="fas fa-magic"></i> 生成SKU
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="copyFromTemplate">
                                                    <i class="fas fa-copy"></i> 从模板复制
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="previewProduct">
                                                    <i class="fas fa-eye"></i> 预览
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveProduct">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('productModal');
        this.form = document.getElementById('productForm');
    }

    setupEventListeners() {
        // 保存按钮
        const saveBtn = this.modal.querySelector('#saveProduct');
        saveBtn.addEventListener('click', () => this.handleSave());
        
        // 表单验证
        this.form.addEventListener('input', (e) => this.validateField(e.target));
        this.form.addEventListener('change', (e) => this.validateField(e.target));
        
        // 图片上传
        this.setupImageUpload();
        
        // 快速操作
        this.setupQuickActions();
        
        // 模态框事件
        this.modal.addEventListener('hidden.bs.modal', () => {
            this.reset();
        });
    }

    setupImageUpload() {
        const uploadZone = this.modal.querySelector('#imageUploadZone');
        const imageInput = this.modal.querySelector('#imageInput');
        const previewList = this.modal.querySelector('#imagePreviewList');
        
        // 点击上传
        uploadZone.addEventListener('click', () => {
            imageInput.click();
        });
        
        // 文件选择
        imageInput.addEventListener('change', (e) => {
            this.handleImageUpload(e.target.files);
        });
        
        // 拖拽上传
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });
        
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('drag-over');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
            this.handleImageUpload(e.dataTransfer.files);
        });
    }

    setupQuickActions() {
        // 生成SKU
        this.modal.querySelector('#generateSku').addEventListener('click', () => {
            const category = this.form.category.value;
            const sku = this.generateSKU(category);
            this.form.sku.value = sku;
            this.validateField(this.form.sku);
        });
        
        // 从模板复制
        this.modal.querySelector('#copyFromTemplate').addEventListener('click', () => {
            this.showTemplateSelector();
        });
        
        // 预览
        this.modal.querySelector('#previewProduct').addEventListener('click', () => {
            this.previewProduct();
        });
    }

    show(product = null) {
        this.isEdit = !!product;
        this.currentProduct = product;
        
        if (product) {
            this.populateForm(product);
            this.modal.querySelector('.modal-title').textContent = '编辑商品';
        } else {
            this.reset();
            this.modal.querySelector('.modal-title').textContent = '新增商品';
        }
        
        // 显示模态框
        const bsModal = new bootstrap.Modal(this.modal);
        bsModal.show();
    }

    hide() {
        const bsModal = bootstrap.Modal.getInstance(this.modal);
        if (bsModal) {
            bsModal.hide();
        }
    }

    populateForm(product) {
        Object.keys(product).forEach(key => {
            const field = this.form.elements[key];
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = !!product[key];
                } else {
                    field.value = product[key] || '';
                }
            }
        });
        
        // 处理图片
        if (product.images && product.images.length > 0) {
            this.displayImages(product.images);
        }
    }

    reset() {
        this.form.reset();
        this.clearValidation();
        this.clearImages();
        this.isEdit = false;
        this.currentProduct = null;
    }

    handleSave() {
        if (!this.validateForm()) {
            return;
        }
        
        const formData = new FormData(this.form);
        const productData = Object.fromEntries(formData.entries());
        
        // 处理复选框
        productData.sync_to_medusa = this.form.sync_to_medusa.checked;
        
        // 处理数字字段
        ['price', 'stock', 'weight'].forEach(field => {
            if (productData[field]) {
                productData[field] = parseFloat(productData[field]);
            }
        });
        
        // 添加图片数据
        productData.images = this.getImageData();
        
        // 如果是编辑模式，添加ID
        if (this.isEdit && this.currentProduct) {
            productData.id = this.currentProduct.id;
        }
        
        // 调用保存回调
        this.onSave(productData, this.isEdit);
    }

    validateForm() {
        let isValid = true;
        const requiredFields = this.form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        // 必填验证
        if (field.required && !value) {
            isValid = false;
            message = '此字段为必填项';
        }
        
        // 特定字段验证
        switch (field.name) {
            case 'name':
                if (value && value.length < 2) {
                    isValid = false;
                    message = '商品名称至少2个字符';
                }
                break;
                
            case 'sku':
                if (value && !/^[A-Z]{2}\d{3,}$/.test(value)) {
                    isValid = false;
                    message = 'SKU格式不正确';
                }
                break;
                
            case 'price':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) {
                    isValid = false;
                    message = '价格必须为正数';
                }
                break;
                
            case 'stock':
                if (value && (isNaN(value) || parseInt(value) < 0)) {
                    isValid = false;
                    message = '库存不能为负数';
                }
                break;
        }
        
        // 显示验证结果
        this.showFieldValidation(field, isValid, message);
        
        return isValid;
    }

    showFieldValidation(field, isValid, message) {
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
        }
    }

    clearValidation() {
        const fields = this.form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        const feedbacks = this.form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });
    }

    handleImageUpload(files) {
        Array.from(files).forEach(file => {
            if (this.validateImage(file)) {
                this.addImagePreview(file);
            }
        });
    }

    validateImage(file) {
        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件');
            return false;
        }
        
        // 检查文件大小 (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('图片大小不能超过5MB');
            return false;
        }
        
        return true;
    }

    addImagePreview(file) {
        const previewList = this.modal.querySelector('#imagePreviewList');
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'image-preview-item';
            previewItem.innerHTML = `
                <img src="${e.target.result}" alt="预览">
                <div class="image-actions">
                    <button type="button" class="btn btn-sm btn-danger remove-image">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // 删除图片
            previewItem.querySelector('.remove-image').addEventListener('click', () => {
                previewItem.remove();
            });
            
            previewList.appendChild(previewItem);
        };
        
        reader.readAsDataURL(file);
    }

    displayImages(images) {
        const previewList = this.modal.querySelector('#imagePreviewList');
        previewList.innerHTML = '';
        
        images.forEach(imageUrl => {
            const previewItem = document.createElement('div');
            previewItem.className = 'image-preview-item';
            previewItem.innerHTML = `
                <img src="${imageUrl}" alt="商品图片">
                <div class="image-actions">
                    <button type="button" class="btn btn-sm btn-danger remove-image">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            previewItem.querySelector('.remove-image').addEventListener('click', () => {
                previewItem.remove();
            });
            
            previewList.appendChild(previewItem);
        });
    }

    clearImages() {
        const previewList = this.modal.querySelector('#imagePreviewList');
        previewList.innerHTML = '';
    }

    getImageData() {
        const images = [];
        const previewItems = this.modal.querySelectorAll('.image-preview-item img');
        
        previewItems.forEach(img => {
            images.push(img.src);
        });
        
        return images;
    }

    generateSKU(category = '') {
        const categoryMap = {
            'switch': 'SW',
            'lighting': 'LT',
            'security': 'SC',
            'sensor': 'SN',
            'environment': 'EN'
        };
        
        const prefix = categoryMap[category] || 'PD';
        const timestamp = Date.now().toString().slice(-6);
        
        return `${prefix}${timestamp}`;
    }

    showTemplateSelector() {
        // 显示模板选择器（简化实现）
        alert('模板功能开发中...');
    }

    previewProduct() {
        const formData = new FormData(this.form);
        const productData = Object.fromEntries(formData.entries());
        
        // 简化的预览功能
        console.log('商品预览:', productData);
        alert('预览功能开发中...');
    }

    destroy() {
        if (this.modal) {
            this.modal.remove();
        }
    }
}

// 导出组件
window.ProductModal = ProductModal;

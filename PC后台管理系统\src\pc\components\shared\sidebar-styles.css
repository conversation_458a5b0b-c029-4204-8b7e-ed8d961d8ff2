/* 侧边栏样式 - 可复用的CSS文件 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    color: #1f2937;
    line-height: 1.6;
}

.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 240px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid rgba(229, 231, 235, 0.8);
    flex-shrink: 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 自定义滚动条 */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.sidebar-header {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 28px;
    height: 28px;
    background: #000;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.logo-text {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
}

.nav-menu {
    padding: 12px 0;
}

.nav-section {
    margin-bottom: 16px;
}

.nav-section-title {
    font-size: 10px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 4px 16px;
    margin: 4px 8px 2px;
    background: rgba(107, 114, 128, 0.08);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-radius: 3px;
    border-left: 2px solid rgba(107, 114, 128, 0.3);
    line-height: 1.2;
}

.nav-item {
    display: block;
    padding: 6px 16px;
    color: #6b7280;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 1px 8px;
    position: relative;
    line-height: 1.3;
}

.nav-item:hover {
    background: rgba(243, 244, 246, 0.8);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    color: #1f2937;
    transform: translateX(4px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: #1f2937;
    font-weight: 600;
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateX(6px);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        padding: 20px;
    }
}

@media (max-width: 640px) {
    .admin-layout {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
    }
    
    .main-content {
        padding: 16px;
    }
}

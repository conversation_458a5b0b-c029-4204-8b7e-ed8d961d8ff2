/**
 * 修复页面编码问题脚本
 * 检查和修复HTML页面中的中文乱码问题
 */

const fs = require('fs');
const path = require('path');

// 常见的乱码映射
const encodingFixes = {
    // 一装相关乱码
    'һװ': '一装',
    'װ��': '装修',
    '��Ʒ': '产品',
    '�ĵ�': '文档',
    '����': '系统',
    '����': '管理',
    'ϵͳ': '系统',
    '������': '管理系统',
    
    // 其他常见乱码
    '����': '中文',
    '����': '页面',
    '�û�': '用户',
    '����': '设置',
    '����': '配置',
    '����': '信息',
    '����': '数据',
    '����': '服务',
    '����': '接口',
    'API': 'API'
};

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 修复单个文件的编码问题
function fixEncodingIssues(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        let hasChanges = false;
        
        // 检查是否缺少charset声明
        if (!content.includes('<meta charset="UTF-8">') && 
            !content.includes('charset=utf-8') && 
            !content.includes('charset="utf-8"')) {
            
            // 在<head>标签后添加charset声明
            const headIndex = content.indexOf('<head>');
            if (headIndex !== -1) {
                const insertIndex = headIndex + 6; // '<head>'.length
                const beforeHead = content.substring(0, insertIndex);
                const afterHead = content.substring(insertIndex);
                content = beforeHead + '\n    <meta charset="UTF-8">' + afterHead;
                hasChanges = true;
            }
        }
        
        // 确保html标签有lang属性
        if (!content.includes('lang="zh-CN"') && !content.includes("lang='zh-CN'")) {
            content = content.replace(/<html[^>]*>/, '<html lang="zh-CN">');
            hasChanges = true;
        }
        
        // 修复乱码文本
        for (const [garbled, correct] of Object.entries(encodingFixes)) {
            if (content.includes(garbled)) {
                content = content.replace(new RegExp(garbled, 'g'), correct);
                hasChanges = true;
            }
        }
        
        // 修复常见的title乱码
        const titleMatch = content.match(/<title>([^<]*)<\/title>/);
        if (titleMatch && titleMatch[1]) {
            const title = titleMatch[1];
            if (title.includes('һװ') || title.includes('װ��') || title.includes('��Ʒ')) {
                let newTitle = title;
                for (const [garbled, correct] of Object.entries(encodingFixes)) {
                    newTitle = newTitle.replace(new RegExp(garbled, 'g'), correct);
                }
                // 如果还有乱码，使用通用标题
                if (/[^\u4e00-\u9fa5\u0000-\u007f\s\-]/.test(newTitle)) {
                    newTitle = '智能家居管理系统';
                }
                content = content.replace(titleMatch[0], `<title>${newTitle}</title>`);
                hasChanges = true;
            }
        }
        
        // 修复meta标签中的乱码
        const metaMatches = content.match(/<meta[^>]*content="[^"]*"[^>]*>/g);
        if (metaMatches) {
            for (const metaTag of metaMatches) {
                let newMetaTag = metaTag;
                for (const [garbled, correct] of Object.entries(encodingFixes)) {
                    newMetaTag = newMetaTag.replace(new RegExp(garbled, 'g'), correct);
                }
                if (newMetaTag !== metaTag) {
                    content = content.replace(metaTag, newMetaTag);
                    hasChanges = true;
                }
            }
        }
        
        // 检查CSS语法错误
        const cssErrors = [
            /\}\/\*[^*]*\*\/\s*[^{}]*\{/g, // }/* comment */ { 这种错误
            /\}[^{}]*-ms-overflow-style[^{}]*\}/g, // 孤立的CSS属性
        ];
        
        for (const errorPattern of cssErrors) {
            if (errorPattern.test(content)) {
                // 简单的修复：移除这些错误的CSS片段
                content = content.replace(errorPattern, '}');
                hasChanges = true;
            }
        }
        
        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已修复: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (无编码问题)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🔧 开始修复编码问题...\n');
    console.log('📋 修复内容:');
    console.log('   - 添加UTF-8字符编码声明');
    console.log('   - 修复中文乱码文本');
    console.log('   - 修复title和meta标签乱码');
    console.log('   - 修复CSS语法错误\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const file of htmlFiles) {
        if (fixEncodingIssues(file)) {
            successCount++;
        } else {
            skipCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 已修复: ${successCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 编码问题修复完成！');
        console.log('📝 所有页面都有正确的字符编码');
        console.log('🔤 中文乱码已修复');
        console.log('✨ 页面显示正常');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixEncodingIssues,
    main
};

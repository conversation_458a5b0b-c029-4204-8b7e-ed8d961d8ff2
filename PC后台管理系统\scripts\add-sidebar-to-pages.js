/**
 * 为缺少侧边栏的页面添加标准菜单
 */

const fs = require('fs');
const path = require('path');

// 加载标准菜单配置
function loadMenuConfig() {
    try {
        const configPath = path.join(__dirname, 'menu-config.json');
        const configContent = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configContent);
    } catch (error) {
        console.error('❌ 无法加载菜单配置:', error.message);
        process.exit(1);
    }
}

// 生成标准侧边栏HTML
function generateSidebarHTML(menuConfig, currentFileName = '') {
    const { logo, sections } = menuConfig;
    
    // 计算相对路径前缀
    const getRelativePath = (targetHref, currentFile) => {
        // 如果当前文件在子目录中，需要添加../前缀
        if (currentFile.includes('/')) {
            const depth = currentFile.split('/').length - 1;
            const prefix = '../'.repeat(depth);
            
            // 如果目标文件也在子目录中，需要调整路径
            if (targetHref.includes('/')) {
                return prefix + targetHref;
            } else {
                return prefix + targetHref;
            }
        }
        return targetHref;
    };
    
    let sidebarHTML = `        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="${logo.icon}"></i>
                    </div>
                    <div>
                        <div class="logo-text">${logo.title}</div>
                        <div style="font-size: 10px; color: #6b7280;">${logo.subtitle}</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">`;

    // 生成菜单分组
    sections.forEach(section => {
        sidebarHTML += `
                <div class="nav-section">
                    <div class="nav-section-title">${section.title}</div>`;
        
        // 生成菜单项
        section.items.forEach(item => {
            const href = getRelativePath(item.href, currentFileName);
            const isActive = path.basename(item.href) === path.basename(currentFileName) ? ' active' : '';
            const iconHTML = item.icon ? `<i class="${item.icon}"></i> ` : '';
            
            sidebarHTML += `
                    <a href="${href}" class="nav-item${isActive}">${iconHTML}${item.text}</a>`;
        });
        
        sidebarHTML += `
                </div>`;
    });

    sidebarHTML += `
            </nav>
        </aside>`;

    return sidebarHTML;
}

// 为页面添加侧边栏
function addSidebarToPage(filePath, menuConfig) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        const relativePath = path.relative(path.join(__dirname, '../src/pc/components/pages'), filePath);
        
        console.log(`🔧 处理页面: ${relativePath}`);
        
        // 检查是否已经有侧边栏
        if (content.includes('class="sidebar"')) {
            console.log(`⏭️  跳过: ${fileName} (已有侧边栏)`);
            return false;
        }
        
        // 检查页面结构
        if (!content.includes('<body>')) {
            console.log(`⚠️  警告: ${fileName} (页面结构不标准)`);
            return false;
        }
        
        // 生成侧边栏HTML
        const sidebarHTML = generateSidebarHTML(menuConfig, relativePath);
        
        // 查找插入位置
        let insertPosition = -1;
        let layoutType = 'unknown';
        
        // 检查是否有admin-layout结构
        if (content.includes('class="admin-layout"')) {
            layoutType = 'admin-layout';
            const adminLayoutMatch = content.match(/<div class="admin-layout"[^>]*>/);
            if (adminLayoutMatch) {
                insertPosition = adminLayoutMatch.index + adminLayoutMatch[0].length;
            }
        }
        // 检查是否有其他容器结构
        else if (content.includes('<div class="main-container"') || content.includes('<div class="container"')) {
            layoutType = 'container';
            // 在body开始后插入
            const bodyMatch = content.match(/<body[^>]*>/);
            if (bodyMatch) {
                insertPosition = bodyMatch.index + bodyMatch[0].length;
            }
        }
        // 默认在body后插入
        else {
            layoutType = 'body';
            const bodyMatch = content.match(/<body[^>]*>/);
            if (bodyMatch) {
                insertPosition = bodyMatch.index + bodyMatch[0].length;
            }
        }
        
        if (insertPosition === -1) {
            console.log(`❌ 错误: ${fileName} (无法找到插入位置)`);
            return false;
        }
        
        // 根据不同的布局类型进行不同的处理
        let newContent;
        
        if (layoutType === 'admin-layout') {
            // 直接在admin-layout内插入侧边栏
            newContent = content.slice(0, insertPosition) + '\n' + sidebarHTML + '\n' + content.slice(insertPosition);
        } else {
            // 需要包装整个页面结构
            const beforeBody = content.slice(0, insertPosition);
            const afterBody = content.slice(insertPosition);
            
            newContent = beforeBody + `
    <div class="admin-layout">
${sidebarHTML}

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">${getPageTitle(fileName)}</h1>
                            <p class="breadcrumb-description">${getPageDescription(fileName)}</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->
` + afterBody.replace('</body>', `            </div>
        </main>
    </div>
</body>`);
        }
        
        // 确保引入了统一样式
        if (!newContent.includes('unified-admin-styles.css')) {
            // 计算样式文件的相对路径
            const depth = relativePath.split('/').length - 1;
            const stylePrefix = '../'.repeat(depth);
            const stylePath = `${stylePrefix}../../../styles/unified-admin-styles.css`;
            
            // 在head中添加样式引用
            newContent = newContent.replace(
                '</head>',
                `    <link rel="stylesheet" href="${stylePath}">
</head>`
            );
        }
        
        // 保存文件
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ 已添加侧边栏: ${fileName} (${layoutType}布局)`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 获取页面标题
function getPageTitle(fileName) {
    const titleMap = {
        'erp-documentation.html': 'ERP文档',
        'demo.html': '演示展示',
        'logout.html': '系统登录',
        'aqara-product-import-demo.html': 'Aqara产品导入演示',
        'aqara-product-import.html': 'Aqara产品导入',
        'construction-enhanced-demo.html': '施工增强演示',
        'index.html': '系统首页',
        'real-product-system-integrated.html': '产品系统集成',
        'register.html': '用户注册'
    };
    return titleMap[fileName] || '系统页面';
}

// 获取页面描述
function getPageDescription(fileName) {
    const descMap = {
        'erp-documentation.html': 'ERP系统文档和API说明',
        'demo.html': '系统功能演示和介绍',
        'logout.html': '用户登录和身份验证',
        'aqara-product-import-demo.html': 'Aqara产品批量导入演示',
        'aqara-product-import.html': 'Aqara产品数据导入工具',
        'construction-enhanced-demo.html': '施工管理增强功能演示',
        'index.html': '系统主页和导航',
        'real-product-system-integrated.html': '产品系统集成管理',
        'register.html': '新用户注册和账户创建'
    };
    return descMap[fileName] || '系统功能页面';
}

// 主函数
function main() {
    console.log('🚀 开始为缺少侧边栏的页面添加标准菜单...\n');
    
    // 加载菜单配置
    const menuConfig = loadMenuConfig();
    console.log('✅ 菜单配置加载成功\n');
    
    // 需要处理的页面列表
    const targetPages = [
        '05-tools/erp-documentation.html',
        '07-profile/demo.html',
        '07-profile/logout.html',
        'aqara-product-import-demo.html',
        'aqara-product-import.html',
        'construction-enhanced-demo.html',
        'index.html',
        'real-product-system-integrated.html',
        'register.html'
    ];
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (const pagePath of targetPages) {
        const fullPath = path.join(pagesDir, pagePath);
        
        if (!fs.existsSync(fullPath)) {
            console.log(`⚠️  文件不存在: ${pagePath}`);
            errorCount++;
            continue;
        }
        
        const result = addSidebarToPage(fullPath, menuConfig);
        if (result === true) {
            successCount++;
        } else if (result === false) {
            skipCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n📊 处理统计:');
    console.log(`✅ 成功添加: ${successCount} 个页面`);
    console.log(`⏭️  跳过页面: ${skipCount} 个页面`);
    console.log(`❌ 处理失败: ${errorCount} 个页面`);
    console.log(`📁 总计页面: ${targetPages.length} 个页面`);
    
    if (successCount > 0) {
        console.log('\n🎉 侧边栏添加完成！');
        console.log('💡 建议运行菜单一致性检查验证结果');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    loadMenuConfig,
    generateSidebarHTML,
    addSidebarToPage,
    main
};

/**
 * 修复04-knowledge目录下页面的样式引用问题
 */

const fs = require('fs');
const path = require('path');

// 需要修复的页面列表
const knowledgePages = [
    'debugging-knowledge.html',
    'delivery-knowledge.html', 
    'design-knowledge.html',
    'installation-knowledge.html',
    'knowledge-template.html',
    'product-knowledge.html',
    'wiring-knowledge.html'
];

// 修复单个页面的样式引用
function fixPageStyles(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        console.log(`🔧 修复页面: ${fileName}`);
        
        // 检查是否需要修复
        if (content.includes('unified-admin-styles.css')) {
            console.log(`⏭️  跳过: ${fileName} (已使用统一样式)`);
            return false;
        }
        
        // 检查是否使用了设计系统样式
        if (!content.includes('design-system/design-tokens.css') && 
            !content.includes('design-system/components.css')) {
            console.log(`⏭️  跳过: ${fileName} (无需修复)`);
            return false;
        }
        
        // 替换样式引用
        let newContent = content;
        
        // 移除设计系统样式引用
        newContent = newContent.replace(
            /\s*<link rel="stylesheet" href="\.\.\/\.\.\/\.\.\/design-system\/design-tokens\.css">\s*/g,
            ''
        );
        newContent = newContent.replace(
            /\s*<link rel="stylesheet" href="\.\.\/\.\.\/\.\.\/design-system\/components\.css">\s*/g,
            ''
        );
        
        // 添加统一样式引用
        if (!newContent.includes('unified-admin-styles.css')) {
            // 在title后添加统一样式
            newContent = newContent.replace(
                /(<title>[^<]*<\/title>)/,
                '$1\n    <link rel="stylesheet" href="../../../../../styles/unified-admin-styles.css">'
            );
        }
        
        // 确保Font Awesome在统一样式之后
        if (newContent.includes('font-awesome')) {
            // 移除现有的Font Awesome引用
            newContent = newContent.replace(
                /\s*<link rel="stylesheet" href="https:\/\/cdnjs\.cloudflare\.com\/ajax\/libs\/font-awesome\/[^"]*">\s*/g,
                ''
            );
            
            // 在统一样式后添加Font Awesome
            newContent = newContent.replace(
                /(unified-admin-styles\.css">)/,
                '$1\n    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">'
            );
        }
        
        // 保存文件
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ 已修复: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🚀 开始修复04-knowledge目录页面样式引用...\n');
    
    const knowledgeDir = path.join(__dirname, '../src/pc/components/pages/04-knowledge');
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (const pageName of knowledgePages) {
        const fullPath = path.join(knowledgeDir, pageName);
        
        if (!fs.existsSync(fullPath)) {
            console.log(`⚠️  文件不存在: ${pageName}`);
            errorCount++;
            continue;
        }
        
        const result = fixPageStyles(fullPath);
        if (result === true) {
            successCount++;
        } else if (result === false) {
            skipCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 成功修复: ${successCount} 个页面`);
    console.log(`⏭️  跳过页面: ${skipCount} 个页面`);
    console.log(`❌ 修复失败: ${errorCount} 个页面`);
    console.log(`📁 总计页面: ${knowledgePages.length} 个页面`);
    
    if (successCount > 0) {
        console.log('\n🎉 样式修复完成！');
        console.log('💡 建议刷新浏览器查看修复效果');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixPageStyles,
    main
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="../../../../styles/mobile-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item active">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <button class="mobile-menu-btn" onclick="toggleMobileMenu()" style="display: none;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">权限管理</h1>
                            <p class="breadcrumb-description">管理系统角色权限和访问控制策略</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 权限管理主界面 -->
                <div class="permissions-container">
                    <!-- 左侧角色列表 -->
                    <div class="roles-panel">
                        <div class="panel-header">
                            <h3>角色管理</h3>
                            <button class="btn btn-primary" onclick="addRole()">
                                <i class="fas fa-plus"></i> 新增角色
                            </button>
                        </div>
                        <div class="roles-list" id="rolesList">
                            <!-- 智能家居系统角色列表 -->
                            <div class="role-item active" data-role="owner" onclick="selectRole('owner')">
                                <div class="role-info">
                                    <div class="role-details">
                                        <div class="role-name">业主/空间所有者</div>
                                        <div class="role-desc">拥有项目空间的完全控制权，可邀请成员、管理进度、审批方案</div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editRole('owner')" title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="role-item" data-role="family_member" onclick="selectRole('family_member')">
                                <div class="role-info">
                                    <div class="role-details">
                                        <div class="role-name">家庭成员</div>
                                        <div class="role-desc">项目参与者，可查看进度、参与讨论、提出建议</div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editRole('family_member')" title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="role-item" data-role="home_designer" onclick="selectRole('home_designer')">
                                <div class="role-info">
                                    <div class="role-details">
                                        <div class="role-name">家装设计师</div>
                                        <div class="role-desc">负责室内设计方案、装修风格、空间布局设计</div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editRole('home_designer')" title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="role-item" data-role="smart_home_designer" onclick="selectRole('smart_home_designer')">
                                <div class="role-info">
                                    <div class="role-details">
                                        <div class="role-name">智能家居设计师</div>
                                        <div class="role-desc">专业智能家居系统设计，包括灯光、安防、控制系统</div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editRole('smart_home_designer')" title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="role-item" data-role="constructor" onclick="selectRole('constructor')">
                                <div class="role-info">
                                    <div class="role-details">
                                        <div class="role-name">施工方</div>
                                        <div class="role-desc">负责施工管理、进度更新、质量控制、现场协调</div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editRole('constructor')" title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧权限配置 -->
                    <div class="permissions-panel">
                        <div class="panel-header">
                            <h3 id="permissionTitle">项目业主 - 权限配置</h3>
                            <div class="permission-actions">
                                <button class="btn btn-secondary" onclick="showTemplateManager()" title="权限模板管理">
                                    <i class="fas fa-layer-group"></i> 模板
                                </button>
                                <label class="permission-item global-select">
                                    <input type="checkbox" id="selectAllPermissions" onchange="toggleAllPermissions(this)">
                                    <span>全选所有权限</span>
                                </label>
                                <button class="btn btn-secondary" onclick="resetPermissions()" id="resetBtn">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button class="btn btn-success" onclick="savePermissions()" id="saveBtn">
                                    <i class="fas fa-save"></i> 保存权限
                                </button>
                            </div>
                        </div>
                        <div class="permissions-tree" id="permissionsTree">
                            <!-- 智能家居系统权限模块 -->

                            <!-- 1. 项目空间管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="project-module" checked onchange="toggleModule('project')">
                                        <label for="project-module">
                                            <i class="fas fa-home" style="color: #1a1a1a;"></i>
                                            项目空间管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="project-select-all" onchange="toggleModulePermissions('project', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="project-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>基础权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:view" data-module="project"> 查看项目概览
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:edit" data-module="project"> 编辑项目信息
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:comment" data-module="project"> 项目评论讨论
                                            </label>
                                        </div>
                                    </div>
                                    <div class="permission-group">
                                        <h4>管理权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:invite" data-module="project"> 邀请项目成员
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:manage_members" data-module="project"> 管理成员权限
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="project:config" data-module="project"> 项目配置设置
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 2. 设计管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="design-module" checked onchange="toggleModule('design')">
                                        <label for="design-module">
                                            <i class="fas fa-drafting-compass" style="color: #374151;"></i>
                                            设计管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="design-select-all" onchange="toggleModulePermissions('design', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="design-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>设计权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="design:view" data-module="design"> 查看设计方案
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="design:create" data-module="design"> 创建设计方案
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="design:edit" data-module="design"> 编辑设计方案
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="design:comment" data-module="design"> 设计方案评论
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="design:approve" data-module="design"> 审批设计方案
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="design:delete" data-module="design"> 删除设计方案
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 3. 施工管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="construction-module" checked onchange="toggleModule('construction')">
                                        <label for="construction-module">
                                            <i class="fas fa-hard-hat" style="color: #6b7280;"></i>
                                            施工管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="construction-select-all" onchange="toggleModulePermissions('construction', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="construction-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>施工权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="construction:view" data-module="construction"> 查看施工进度
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="construction:update" data-module="construction"> 更新施工进度
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="construction:quality" data-module="construction"> 施工质量检查
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="construction:feedback" data-module="construction"> 施工问题反馈
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="construction:plan" data-module="construction"> 施工计划管理
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 4. 成本管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="cost-module" checked onchange="toggleModule('cost')">
                                        <label for="cost-module">
                                            <i class="fas fa-calculator" style="color: #4b5563;"></i>
                                            成本管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="cost-select-all" onchange="toggleModulePermissions('cost', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="cost-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>成本权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="cost:view" data-module="cost"> 查看项目预算
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="cost:edit" data-module="cost"> 编辑预算方案
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="cost:approve" data-module="cost"> 审批预算变更
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="cost:report" data-module="cost"> 成本分析报告
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 5. 文件管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="files-module" checked onchange="toggleModule('files')">
                                        <label for="files-module">
                                            <i class="fas fa-folder-open" style="color: #6b7280;"></i>
                                            文件管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="files-select-all" onchange="toggleModulePermissions('files', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="files-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>文件权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="files:view" data-module="files"> 查看项目文件
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="files:upload" data-module="files"> 上传项目文件
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="files:download" data-module="files"> 下载项目文件
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="files:delete" data-module="files"> 删除项目文件
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 6. 评论管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="comments-module" checked onchange="toggleModule('comments')">
                                        <label for="comments-module">
                                            <i class="fas fa-comments" style="color: #4b5563;"></i>
                                            评论管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="comments-select-all" onchange="toggleModulePermissions('comments', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="comments-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>评论权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="comments:view" data-module="comments"> 查看所有评论
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="comments:create" data-module="comments"> 创建新评论
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="comments:edit" data-module="comments"> 编辑自己评论
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="comments:delete" data-module="comments"> 删除自己评论
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 7. 营销管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="marketing-module" checked onchange="toggleModule('marketing')">
                                        <label for="marketing-module">
                                            <i class="fas fa-bullhorn" style="color: #6b7280;"></i>
                                            营销管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="marketing-select-all" onchange="toggleModulePermissions('marketing', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="marketing-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>营销权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="marketing:view" data-module="marketing"> 查看营销活动
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="marketing:share" data-module="marketing"> 分享项目案例
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="marketing:reward" data-module="marketing"> 获得推荐奖励
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" checked data-permission="marketing:stats" data-module="marketing"> 查看营销统计
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 8. 系统管理 -->
                            <div class="permission-module">
                                <div class="module-header">
                                    <div class="module-toggle">
                                        <input type="checkbox" id="system-module" onchange="toggleModule('system')">
                                        <label for="system-module">
                                            <i class="fas fa-cog" style="color: #1a1a1a;"></i>
                                            系统管理
                                        </label>
                                    </div>
                                    <div class="module-actions">
                                        <label class="permission-item module-select-all">
                                            <input type="checkbox" id="system-select-all" onchange="toggleModulePermissions('system', this)">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="module-permissions" id="system-permissions" style="display: block;">
                                    <div class="permission-group">
                                        <h4>系统权限</h4>
                                        <div class="permission-items">
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="system:view" data-module="system"> 查看系统设置
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="system:config" data-module="system"> 配置系统参数
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="system:user_manage" data-module="system"> 用户管理
                                            </label>
                                            <label class="permission-item">
                                                <input type="checkbox" data-permission="system:permission_manage" data-module="system"> 权限管理
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 权限模板管理模态框 -->
    <div id="templateModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>权限模板管理</h3>
                <button class="modal-close" onclick="closeModal('templateModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="template-section">
                    <h4>预设模板</h4>
                    <div class="template-list">
                        <div class="template-item" onclick="applyTemplate('owner_template')">
                            <div class="template-info">
                                <div class="template-name">业主完整权限模板</div>
                                <div class="template-desc">包含所有项目管理和审批权限</div>
                            </div>
                            <button class="btn btn-primary">应用</button>
                        </div>
                        <div class="template-item" onclick="applyTemplate('designer_template')">
                            <div class="template-info">
                                <div class="template-name">设计师权限模板</div>
                                <div class="template-desc">专注于设计相关的权限配置</div>
                            </div>
                            <button class="btn btn-primary">应用</button>
                        </div>
                        <div class="template-item" onclick="applyTemplate('constructor_template')">
                            <div class="template-info">
                                <div class="template-name">施工方权限模板</div>
                                <div class="template-desc">施工管理和进度更新权限</div>
                            </div>
                            <button class="btn btn-primary">应用</button>
                        </div>
                    </div>
                </div>
                <div class="template-section">
                    <h4>自定义模板</h4>
                    <div class="template-actions">
                        <button class="btn btn-success" onclick="saveAsTemplate()">
                            <i class="fas fa-plus"></i> 保存当前配置为模板
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div id="batchModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量权限操作</h3>
                <button class="modal-close" onclick="closeModal('batchModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="batch-section">
                    <h4>批量启用/禁用模块</h4>
                    <div class="batch-controls">
                        <button class="btn btn-success" onclick="batchEnableModules()">
                            <i class="fas fa-check-circle"></i> 全部启用
                        </button>
                        <button class="btn btn-secondary" onclick="batchDisableModules()">
                            <i class="fas fa-times-circle"></i> 全部禁用
                        </button>
                    </div>
                </div>
                <div class="batch-section">
                    <h4>权限继承</h4>
                    <div class="inherit-controls">
                        <select id="inheritFromRole" class="form-select">
                            <option value="">选择继承源角色</option>
                            <option value="owner">业主/空间所有者</option>
                            <option value="family_member">家庭成员</option>
                            <option value="home_designer">家装设计师</option>
                            <option value="smart_home_designer">智能家居设计师</option>
                            <option value="constructor">施工方</option>
                        </select>
                        <button class="btn btn-primary" onclick="inheritPermissions()">
                            <i class="fas fa-copy"></i> 继承权限
                        </button>
                    </div>
                </div>
                <div class="batch-section">
                    <h4>权限冲突检测</h4>
                    <div class="conflict-controls">
                        <button class="btn btn-warning" onclick="detectConflicts()">
                            <i class="fas fa-exclamation-triangle"></i> 检测冲突
                        </button>
                        <div id="conflictResults" class="conflict-results"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../js/admin-common.js"></script>
    <script src="../../js/internal-permissions.js"></script>
    <script>
        // 当前选中的角色
        let currentRole = 'owner';

        // 智能家居系统角色权限配置
        const rolePermissions = {
            owner: {
                project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE'],
                design: ['VIEW', 'COMMENT', 'APPROVE'],
                construction: ['VIEW', 'COMMENT', 'APPROVE'],
                cost: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE'],
                files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
                comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
                system: ['VIEW', 'CONFIGURE']
            },
            family_member: {
                project: ['VIEW', 'COMMENT'],
                design: ['VIEW', 'COMMENT'],
                construction: ['VIEW', 'COMMENT'],
                cost: ['VIEW'],
                files: ['VIEW', 'DOWNLOAD'],
                comments: ['VIEW', 'CREATE', 'EDIT'],
                marketing: ['VIEW', 'SHARE'],
                system: []
            },
            home_designer: {
                project: ['VIEW', 'COMMENT'],
                design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
                construction: ['VIEW', 'COMMENT'],
                cost: ['VIEW', 'EDIT'],
                files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                comments: ['VIEW', 'CREATE', 'EDIT'],
                marketing: ['VIEW'],
                system: []
            },
            smart_home_designer: {
                project: ['VIEW', 'COMMENT'],
                design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
                construction: ['VIEW', 'COMMENT'],
                cost: ['VIEW', 'EDIT'],
                files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                comments: ['VIEW', 'CREATE', 'EDIT'],
                marketing: ['VIEW'],
                system: []
            },
            constructor: {
                project: ['VIEW', 'COMMENT'],
                design: ['VIEW'],
                construction: ['VIEW', 'UPDATE', 'COMMENT', 'INSPECT'],
                cost: ['VIEW'],
                files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                comments: ['VIEW', 'CREATE', 'EDIT'],
                marketing: ['VIEW'],
                system: []
            }
        };
        
        // 选择角色
        function selectRole(role) {
            // 更新选中状态
            document.querySelectorAll('.role-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-role="${role}"]`).classList.add('active');
            
            // 更新当前角色
            currentRole = role;
            
            // 更新权限标题
            const roleNames = {
                owner: '业主/空间所有者',
                family_member: '家庭成员',
                home_designer: '家装设计师',
                smart_home_designer: '智能家居设计师',
                constructor: '施工方'
            };
            document.getElementById('permissionTitle').textContent = `${roleNames[role]} - 权限配置`;

            // 加载角色权限
            loadRolePermissions(role);
        }

        // 加载角色权限
        function loadRolePermissions(role) {
            const permissions = rolePermissions[role] || {};

            // 更新权限复选框状态
            document.querySelectorAll('.permission-module').forEach(module => {
                const moduleId = module.querySelector('.module-toggle input').id.replace('-module', '');
                const modulePermissions = permissions[moduleId] || [];
                const hasPermissions = modulePermissions.length > 0;

                // 更新模块开关
                const moduleCheckbox = module.querySelector('.module-toggle input');
                moduleCheckbox.checked = hasPermissions;

                // 更新模块显示状态
                const permissionsDiv = module.querySelector('.module-permissions');
                if (hasPermissions) {
                    permissionsDiv.style.display = 'block';
                } else {
                    permissionsDiv.style.display = 'none';
                }

                // 更新具体权限复选框
                const checkboxes = module.querySelectorAll('.permission-item input');
                checkboxes.forEach((checkbox, index) => {
                    // 根据角色的实际权限设置复选框状态
                    checkbox.checked = hasPermissions && index < modulePermissions.length;
                });
            });

            console.log(`已加载 ${roleNames[role]} 的权限配置:`, permissions);
        }
        
        // 切换模块权限
        function toggleModule(moduleId) {
            const moduleCheckbox = document.getElementById(`${moduleId}-module`);
            const permissionsDiv = document.getElementById(`${moduleId}-permissions`);
            
            if (moduleCheckbox.checked) {
                permissionsDiv.style.display = 'block';
            } else {
                permissionsDiv.style.display = 'none';
                // 取消所有子权限
                permissionsDiv.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    cb.checked = false;
                });
            }
        }
        
        // 保存权限
        function savePermissions() {
            // 收集当前权限配置
            const permissions = {};

            document.querySelectorAll('.permission-module').forEach(module => {
                const moduleId = module.querySelector('.module-toggle input').id.replace('-module', '');
                const isEnabled = module.querySelector('.module-toggle input').checked;

                if (isEnabled) {
                    permissions[moduleId] = [];
                    // 根据选中的权限项收集具体权限
                    module.querySelectorAll('.permission-item input:checked').forEach(checkbox => {
                        const permissionText = checkbox.parentElement.textContent.trim();
                        permissions[moduleId].push(permissionText);
                    });
                } else {
                    permissions[moduleId] = [];
                }
            });

            // 更新角色权限配置
            rolePermissions[currentRole] = permissions;

            // 显示保存成功消息
            showSuccessMessage('权限配置已保存成功！');
            console.log(`已保存 ${currentRole} 的权限配置:`, permissions);

            // 模拟API调用保存到后端
            setTimeout(() => {
                console.log('权限配置已同步到服务器');
            }, 500);
        }

        // 重置权限
        function resetPermissions() {
            if (confirm('确定要重置权限配置吗？此操作将恢复该角色的默认权限设置。')) {
                loadRolePermissions(currentRole);
                showInfoMessage('权限配置已重置为默认设置');
            }
        }

        // 添加角色
        function addRole() {
            // 创建模态框显示添加角色表单
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 500px;
                    max-height: 80vh;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">添加新客户角色</h3>
                    <form id="addCustomerRoleForm">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色名称</label>
                            <input type="text" name="roleName" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" placeholder="请输入角色名称" required>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色类型</label>
                            <select name="roleType" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" required>
                                <option value="customer">客户角色</option>
                                <option value="partner">合作伙伴</option>
                                <option value="vendor">供应商</option>
                                <option value="guest">访客</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色描述</label>
                            <textarea name="description" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; min-height: 80px;" placeholder="请输入角色描述"></textarea>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">权限等级</label>
                            <select name="permissionLevel" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" required>
                                <option value="basic">基础权限</option>
                                <option value="standard">标准权限</option>
                                <option value="advanced">高级权限</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeAddRoleModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                            <button type="submit" style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">确认添加</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 处理表单提交
            modal.querySelector('#addCustomerRoleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const roleData = {
                    name: formData.get('roleName'),
                    type: formData.get('roleType'),
                    description: formData.get('description'),
                    permissionLevel: formData.get('permissionLevel')
                };
                
                // 模拟创建新角色
                const roleId = `custom_${Date.now()}`;
                rolePermissions[roleId] = {
                    project: roleData.permissionLevel === 'advanced' ? ['VIEW', 'COMMENT'] : ['VIEW'],
                    design: roleData.permissionLevel === 'basic' ? [] : ['VIEW'],
                    construction: ['VIEW'],
                    cost: roleData.permissionLevel === 'advanced' ? ['VIEW'] : [],
                    files: ['VIEW'],
                    comments: ['VIEW', 'CREATE'],
                    marketing: roleData.permissionLevel === 'advanced' ? ['VIEW', 'SHARE'] : ['VIEW'],
                    system: []
                };
                
                console.log('新增客户角色数据:', roleData);
                showSuccessMessage(`客户角色 "${roleData.name}" 添加成功！`);
                document.body.removeChild(modal);
            });
            
            // 点击外部关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
            // 全局关闭函数
            window.closeAddRoleModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
        }

        // 编辑角色
        function editRole(role) {
            const roleNames = {
                owner: '业主/空间所有者',
                family_member: '家庭成员',
                home_designer: '家装设计师',
                smart_home_designer: '智能家居设计师',
                constructor: '施工方'
            };
            
            const roleName = roleNames[role] || role;
            const currentRoleConfig = rolePermissions[role] || {};
            
            // 创建编辑模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">编辑客户角色: ${roleName}</h3>
                    <form id="editCustomerRoleForm">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色名称</label>
                            <input type="text" value="${roleName}" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" readonly>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">活跃状态</label>
                            <select name="status" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="active" selected>活跃</option>
                                <option value="inactive">停用</option>
                                <option value="restricted">受限</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">访问限制</label>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" name="timeRestriction" checked>
                                    <span>启用时间限制 (9:00-18:00)</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" name="ipRestriction">
                                    <span>启用IP地址限制</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" name="deviceRestriction">
                                    <span>启用设备限制</span>
                                </label>
                            </div>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">会话超时时间</label>
                            <select name="sessionTimeout" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="30">半小时</option>
                                <option value="60" selected>一小时</option>
                                <option value="120">两小时</option>
                                <option value="240">四小时</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">数据访问等级</label>
                            <select name="dataLevel" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="public">公开数据</option>
                                <option value="private" selected>私有数据</option>
                                <option value="sensitive">敏感数据</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeEditRoleModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                            <button type="submit" style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">保存更改</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 处理表单提交
            modal.querySelector('#editCustomerRoleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                
                const updateData = {
                    status: formData.get('status'),
                    timeRestriction: formData.has('timeRestriction'),
                    ipRestriction: formData.has('ipRestriction'),
                    deviceRestriction: formData.has('deviceRestriction'),
                    sessionTimeout: formData.get('sessionTimeout'),
                    dataLevel: formData.get('dataLevel')
                };
                
                console.log('更新客户角色配置:', updateData);
                showSuccessMessage(`客户角色 "${roleName}" 更新成功！`);
                document.body.removeChild(modal);
            });
            
            // 点击外部关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
            // 全局关闭函数
            window.closeEditRoleModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const toast = createToast(message, 'success');
            document.body.appendChild(toast);
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // 显示信息消息
        function showInfoMessage(message) {
            const toast = createToast(message, 'info');
            document.body.appendChild(toast);
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // 创建消息提示
        function createToast(message, type) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
            `;

            if (type === 'success') {
                toast.style.background = '#1a1a1a';
            } else if (type === 'info') {
                toast.style.background = '#374151';
            }

            toast.textContent = message;
            return toast;
        }

        // 权限模板管理
        function showTemplateManager() {
            document.getElementById('templateModal').style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function applyTemplate(templateType) {
            const templates = {
                owner_template: {
                    project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE'],
                    design: ['VIEW', 'COMMENT', 'APPROVE'],
                    construction: ['VIEW', 'COMMENT', 'APPROVE'],
                    cost: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE'],
                    files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
                    comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
                    system: ['VIEW', 'CONFIGURE']
                },
                designer_template: {
                    project: ['VIEW', 'COMMENT'],
                    design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
                    construction: ['VIEW', 'COMMENT'],
                    cost: ['VIEW', 'EDIT'],
                    files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                    comments: ['VIEW', 'CREATE', 'EDIT'],
                    marketing: ['VIEW'],
                    system: []
                },
                constructor_template: {
                    project: ['VIEW', 'COMMENT'],
                    design: ['VIEW'],
                    construction: ['VIEW', 'UPDATE', 'COMMENT', 'INSPECT'],
                    cost: ['VIEW'],
                    files: ['VIEW', 'UPLOAD', 'DOWNLOAD'],
                    comments: ['VIEW', 'CREATE', 'EDIT'],
                    marketing: ['VIEW'],
                    system: []
                }
            };

            if (templates[templateType]) {
                rolePermissions[currentRole] = templates[templateType];
                loadRolePermissions(currentRole);
                showSuccessMessage(`已应用${templateType.replace('_template', '')}权限模板`);
                closeModal('templateModal');
            }
        }

        function saveAsTemplate() {
            const templateName = prompt('请输入模板名称:');
            if (templateName && templateName.trim()) {
                // 这里可以保存当前权限配置为自定义模板
                showSuccessMessage(`模板 "${templateName.trim()}" 保存成功`);
                closeModal('templateModal');
            }
        }

        // 批量操作
        function batchOperations() {
            document.getElementById('batchModal').style.display = 'flex';
        }

        function batchEnableModules() {
            document.querySelectorAll('.module-toggle input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = true;
                const moduleId = checkbox.id.replace('-module', '');
                const permissionsDiv = document.getElementById(moduleId + '-permissions');
                if (permissionsDiv) {
                    permissionsDiv.style.display = 'block';
                }
            });
            showSuccessMessage('已启用所有权限模块');
        }

        function batchDisableModules() {
            document.querySelectorAll('.module-toggle input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
                const moduleId = checkbox.id.replace('-module', '');
                const permissionsDiv = document.getElementById(moduleId + '-permissions');
                if (permissionsDiv) {
                    permissionsDiv.style.display = 'none';
                }
            });
            showSuccessMessage('已禁用所有权限模块');
        }

        function inheritPermissions() {
            const sourceRole = document.getElementById('inheritFromRole').value;
            if (!sourceRole) {
                showInfoMessage('请选择要继承权限的源角色');
                return;
            }

            if (sourceRole === currentRole) {
                showInfoMessage('不能从自己继承权限');
                return;
            }

            const sourcePermissions = rolePermissions[sourceRole];
            if (sourcePermissions) {
                rolePermissions[currentRole] = JSON.parse(JSON.stringify(sourcePermissions));
                loadRolePermissions(currentRole);
                showSuccessMessage(`已从 ${sourceRole} 继承权限配置`);
                closeModal('batchModal');
            }
        }

        function detectConflicts() {
            const conflictResults = document.getElementById('conflictResults');
            const conflicts = [];

            // 检查权限冲突逻辑
            const currentPermissions = rolePermissions[currentRole] || {};

            // 示例冲突检测：检查是否有矛盾的权限组合
            if (currentPermissions.system && currentPermissions.system.length > 0 && currentRole !== 'owner') {
                conflicts.push('非业主角色不应拥有系统管理权限');
            }

            if (currentPermissions.cost && currentPermissions.cost.includes('APPROVE') &&
                currentRole === 'constructor') {
                conflicts.push('施工方不应拥有成本审批权限');
            }

            if (conflicts.length > 0) {
                conflictResults.innerHTML = `
                    <div style="color: #dc2626; font-weight: 600;">发现 ${conflicts.length} 个权限冲突:</div>
                    ${conflicts.map(conflict => `<div style="margin-top: 8px;">• ${conflict}</div>`).join('')}
                `;
            } else {
                conflictResults.innerHTML = `
                    <div style="color: #059669; font-weight: 600;">✅ 未发现权限冲突</div>
                `;
            }
        }
        
        // 全选所有权限功能
        function toggleAllPermissions(checkbox) {
            const isChecked = checkbox.checked;

            // 选中/取消所有权限项
            document.querySelectorAll('.permission-item input[type="checkbox"][data-permission]').forEach(permissionCheckbox => {
                permissionCheckbox.checked = isChecked;
            });

            // 更新所有模块的全选状态
            document.querySelectorAll('.module-select-all input[type="checkbox"]').forEach(moduleCheckbox => {
                moduleCheckbox.checked = isChecked;
            });

            // 更新所有模块的启用状态
            document.querySelectorAll('.module-toggle input[type="checkbox"]').forEach(moduleToggle => {
                moduleToggle.checked = isChecked;
                const moduleId = moduleToggle.id.replace('-module', '');
                const permissionsDiv = document.getElementById(moduleId + '-permissions');
                if (permissionsDiv) {
                    permissionsDiv.style.display = isChecked ? 'block' : 'none';
                }
            });

            showSuccessMessage(isChecked ? '已选中所有权限' : '已取消所有权限选择');
        }

        // 模块级别全选功能
        function toggleModulePermissions(moduleId, checkbox) {
            const isChecked = checkbox.checked;

            // 选中/取消该模块下的所有权限项
            document.querySelectorAll(`input[data-module="${moduleId}"]`).forEach(permissionCheckbox => {
                permissionCheckbox.checked = isChecked;
            });

            // 更新模块启用状态
            const moduleToggle = document.getElementById(moduleId + '-module');
            if (moduleToggle) {
                moduleToggle.checked = isChecked;
                const permissionsDiv = document.getElementById(moduleId + '-permissions');
                if (permissionsDiv) {
                    permissionsDiv.style.display = isChecked ? 'block' : 'none';
                }
            }

            // 检查是否需要更新全局全选状态
            updateGlobalSelectState();

            showSuccessMessage(isChecked ? `已选中${getModuleName(moduleId)}模块所有权限` : `已取消${getModuleName(moduleId)}模块权限选择`);
        }

        // 更新全局全选状态
        function updateGlobalSelectState() {
            const allPermissions = document.querySelectorAll('.permission-item input[type="checkbox"][data-permission]');
            const checkedPermissions = document.querySelectorAll('.permission-item input[type="checkbox"][data-permission]:checked');
            const globalSelect = document.getElementById('selectAllPermissions');

            if (globalSelect) {
                globalSelect.checked = allPermissions.length === checkedPermissions.length;
                globalSelect.indeterminate = checkedPermissions.length > 0 && checkedPermissions.length < allPermissions.length;
            }
        }

        // 获取模块名称
        function getModuleName(moduleId) {
            const moduleNames = {
                'project': '项目空间管理',
                'design': '设计管理',
                'construction': '施工管理',
                'cost': '成本管理',
                'files': '文件管理',
                'comments': '评论管理',
                'marketing': '营销管理',
                'system': '系统管理'
            };
            return moduleNames[moduleId] || moduleId;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 智能家居权限管理系统已加载');

            // 默认选中业主角色
            selectRole('owner');

            // 初始化权限状态监听
            document.querySelectorAll('.permission-item input[type="checkbox"][data-permission]').forEach(checkbox => {
                checkbox.addEventListener('change', updateGlobalSelectState);
            });

            // 初始化全局全选状态
            updateGlobalSelectState();

            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl+S 保存权限
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    savePermissions();
                }
                // Ctrl+R 重置权限
                if (e.ctrlKey && e.key === 'r') {
                    e.preventDefault();
                    resetPermissions();
                }
            });

            // 添加权限变更监听
            document.querySelectorAll('.permission-item input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    console.log('权限变更:', this.parentElement.textContent.trim(), this.checked);
                });
            });

            // 显示加载完成提示
            setTimeout(() => {
                showInfoMessage('权限管理系统已就绪，可以开始配置角色权限');
            }, 500);

            console.log('📋 支持的角色:', Object.keys(rolePermissions));
            console.log('⌨️ 快捷键: Ctrl+S 保存, Ctrl+R 重置');

            // 模态框点击外部关闭
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });

            // 移动端响应式处理
            handleMobileResponsive();
        });

        // 移动端菜单控制
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 移动端响应式处理
        function handleMobileResponsive() {
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const sidebar = document.getElementById('sidebar');

            function checkMobile() {
                if (window.innerWidth <= 767) {
                    mobileMenuBtn.style.display = 'block';
                    sidebar.classList.remove('mobile-open');
                } else {
                    mobileMenuBtn.style.display = 'none';
                    sidebar.classList.remove('mobile-open');
                }
            }

            // 初始检查
            checkMobile();

            // 窗口大小变化时检查
            window.addEventListener('resize', checkMobile);

            // 点击页面其他区域关闭移动端菜单
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 767 &&
                    !sidebar.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target) &&
                    sidebar.classList.contains('mobile-open')) {
                    sidebar.classList.remove('mobile-open');
                }
            });
        }
    </script>
</body>
</html>

{"permissions": {"allow": ["Bash(find:*)", "Bash(for file in \"02-business/design-cases.html\" \"02-business/design-center.html\" \"02-business/requirements-management-api.html\" \"02-business/requirements-management.html\" \"05-tools/api-tools.html\" \"aqara-product-import.html\")", "Bash(do echo \"=== $file ===\")", "Bash(grep:*)", "Bash(done)", "Bash(for file in \"02-business/design-cases.html\" \"02-business/requirements-management-api.html\" \"02-business/requirements-management.html\" \"05-tools/api-tools.html\" \"aqara-product-import.html\")", "Bash(for file in \"02-business/design-cases.html\" \"02-business/requirements-management-api.html\" \"02-business/requirements-management.html\" \"05-tools/api-tools.html\")", "Bash(ls:*)", "Bash(git add:*)", "Bash(rm:*)"], "deny": []}}
/**
 * 实时重复注册检查模块
 * 提供前端实时验证功能，防止重复注册
 */
class RealTimeValidation {
    constructor() {
        this.apiBase = '/api/auth';
        this.debounceDelay = 500; // 防抖延迟
        this.validationCache = new Map(); // 验证结果缓存
        this.pendingRequests = new Map(); // 待处理请求
        
        this.init();
    }

    /**
     * 初始化实时验证
     */
    init() {
        this.setupEmailValidation();
        this.setupPhoneValidation();
        this.setupUsernameValidation();
        this.setupPasswordValidation();
    }

    /**
     * 设置邮箱实时验证
     */
    setupEmailValidation() {
        const emailInputs = document.querySelectorAll('input[type="email"], input[name*="email"]');
        
        emailInputs.forEach(input => {
            // 添加验证状态指示器
            this.addValidationIndicator(input);
            
            // 绑定实时验证事件
            input.addEventListener('input', this.debounce((event) => {
                this.validateEmail(event.target);
            }, this.debounceDelay));
            
            // 失去焦点时验证
            input.addEventListener('blur', (event) => {
                this.validateEmail(event.target);
            });
        });
    }

    /**
     * 设置手机号实时验证
     */
    setupPhoneValidation() {
        const phoneInputs = document.querySelectorAll('input[type="tel"], input[name*="phone"]');
        
        phoneInputs.forEach(input => {
            this.addValidationIndicator(input);
            
            input.addEventListener('input', this.debounce((event) => {
                this.validatePhone(event.target);
            }, this.debounceDelay));
            
            input.addEventListener('blur', (event) => {
                this.validatePhone(event.target);
            });
        });
    }

    /**
     * 设置用户名实时验证
     */
    setupUsernameValidation() {
        const usernameInputs = document.querySelectorAll('input[name*="username"], input[name*="userName"]');
        
        usernameInputs.forEach(input => {
            this.addValidationIndicator(input);
            
            input.addEventListener('input', this.debounce((event) => {
                this.validateUsername(event.target);
            }, this.debounceDelay));
            
            input.addEventListener('blur', (event) => {
                this.validateUsername(event.target);
            });
        });
    }

    /**
     * 设置密码强度实时验证
     */
    setupPasswordValidation() {
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        
        passwordInputs.forEach(input => {
            this.addPasswordStrengthIndicator(input);
            
            input.addEventListener('input', (event) => {
                this.validatePasswordStrength(event.target);
            });
        });
    }

    /**
     * 验证邮箱
     */
    async validateEmail(input) {
        const email = input.value.trim();
        
        if (!email) {
            this.clearValidation(input);
            return;
        }

        // 基础格式验证
        if (!this.isValidEmailFormat(email)) {
            this.showValidationError(input, '邮箱格式不正确');
            return;
        }

        // 检查缓存
        const cacheKey = `email:${email}`;
        if (this.validationCache.has(cacheKey)) {
            const result = this.validationCache.get(cacheKey);
            this.showValidationResult(input, result);
            return;
        }

        // 显示验证中状态
        this.showValidationLoading(input);

        try {
            // 取消之前的请求
            this.cancelPendingRequest(cacheKey);
            
            // 发起新的验证请求
            const controller = new AbortController();
            this.pendingRequests.set(cacheKey, controller);

            const response = await fetch(`${this.apiBase}/check-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
                signal: controller.signal
            });

            const result = await response.json();
            
            // 缓存结果
            this.validationCache.set(cacheKey, result);
            
            // 显示验证结果
            this.showValidationResult(input, result);
            
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('邮箱验证失败:', error);
                this.showValidationError(input, '验证失败，请稍后重试');
            }
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    /**
     * 验证手机号
     */
    async validatePhone(input) {
        const phone = input.value.trim();
        
        if (!phone) {
            this.clearValidation(input);
            return;
        }

        // 基础格式验证
        if (!this.isValidPhoneFormat(phone)) {
            this.showValidationError(input, '手机号格式不正确');
            return;
        }

        const cacheKey = `phone:${phone}`;
        if (this.validationCache.has(cacheKey)) {
            const result = this.validationCache.get(cacheKey);
            this.showValidationResult(input, result);
            return;
        }

        this.showValidationLoading(input);

        try {
            this.cancelPendingRequest(cacheKey);
            
            const controller = new AbortController();
            this.pendingRequests.set(cacheKey, controller);

            const response = await fetch(`${this.apiBase}/check-phone`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ phoneNumber: phone }),
                signal: controller.signal
            });

            const result = await response.json();
            this.validationCache.set(cacheKey, result);
            this.showValidationResult(input, result);
            
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('手机号验证失败:', error);
                this.showValidationError(input, '验证失败，请稍后重试');
            }
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    /**
     * 验证用户名
     */
    async validateUsername(input) {
        const username = input.value.trim();
        
        if (!username) {
            this.clearValidation(input);
            return;
        }

        // 基础格式验证
        if (!this.isValidUsernameFormat(username)) {
            this.showValidationError(input, '用户名格式不正确（3-20位字母数字下划线）');
            return;
        }

        const cacheKey = `username:${username}`;
        if (this.validationCache.has(cacheKey)) {
            const result = this.validationCache.get(cacheKey);
            this.showValidationResult(input, result);
            return;
        }

        this.showValidationLoading(input);

        try {
            this.cancelPendingRequest(cacheKey);
            
            const controller = new AbortController();
            this.pendingRequests.set(cacheKey, controller);

            const response = await fetch(`${this.apiBase}/check-username`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username }),
                signal: controller.signal
            });

            const result = await response.json();
            this.validationCache.set(cacheKey, result);
            this.showValidationResult(input, result);
            
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('用户名验证失败:', error);
                this.showValidationError(input, '验证失败，请稍后重试');
            }
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    /**
     * 验证密码强度
     */
    validatePasswordStrength(input) {
        const password = input.value;
        const strength = this.calculatePasswordStrength(password);
        
        this.showPasswordStrength(input, strength);
    }

    /**
     * 计算密码强度
     */
    calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) {
            score += 25;
        } else {
            feedback.push('至少8位字符');
        }

        if (/[a-z]/.test(password)) {
            score += 25;
        } else {
            feedback.push('包含小写字母');
        }

        if (/[A-Z]/.test(password)) {
            score += 25;
        } else {
            feedback.push('包含大写字母');
        }

        if (/\d/.test(password)) {
            score += 25;
        } else {
            feedback.push('包含数字');
        }

        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            score += 25;
        } else {
            feedback.push('包含特殊字符');
        }

        // 长度加分
        if (password.length >= 12) score += 10;
        if (password.length >= 16) score += 10;

        let level = 'weak';
        if (score >= 80) level = 'strong';
        else if (score >= 60) level = 'medium';

        return {
            score: Math.min(score, 100),
            level,
            feedback
        };
    }

    /**
     * 格式验证方法
     */
    isValidEmailFormat(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidPhoneFormat(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }

    isValidUsernameFormat(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
    }

    /**
     * UI操作方法
     */
    addValidationIndicator(input) {
        const container = input.parentElement;
        const indicator = document.createElement('div');
        indicator.className = 'validation-indicator';
        indicator.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
            pointer-events: none;
        `;
        
        if (container.style.position !== 'relative') {
            container.style.position = 'relative';
        }
        
        container.appendChild(indicator);
    }

    addPasswordStrengthIndicator(input) {
        const container = input.parentElement;
        const strengthBar = document.createElement('div');
        strengthBar.className = 'password-strength-bar';
        strengthBar.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <div class="strength-text"></div>
            <div class="strength-feedback"></div>
        `;
        strengthBar.style.cssText = `
            margin-top: 5px;
            font-size: 12px;
        `;
        
        container.appendChild(strengthBar);
    }

    showValidationLoading(input) {
        const indicator = input.parentElement.querySelector('.validation-indicator');
        if (indicator) {
            indicator.innerHTML = '<i class="fas fa-spinner fa-spin" style="color: #6b7280;"></i>';
        }
    }

    showValidationResult(input, result) {
        const indicator = input.parentElement.querySelector('.validation-indicator');
        if (indicator) {
            if (result.success && result.data.available) {
                indicator.innerHTML = '<i class="fas fa-check" style="color: #10b981;"></i>';
                this.clearValidationError(input);
            } else {
                indicator.innerHTML = '<i class="fas fa-times" style="color: #ef4444;"></i>';
                this.showValidationError(input, result.message || '该信息已被使用');
            }
        }
    }

    showValidationError(input, message) {
        const indicator = input.parentElement.querySelector('.validation-indicator');
        if (indicator) {
            indicator.innerHTML = '<i class="fas fa-times" style="color: #ef4444;"></i>';
        }
        
        // 显示错误消息
        let errorElement = input.parentElement.querySelector('.validation-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'validation-error';
            errorElement.style.cssText = `
                color: #ef4444;
                font-size: 12px;
                margin-top: 4px;
            `;
            input.parentElement.appendChild(errorElement);
        }
        errorElement.textContent = message;
        
        input.classList.add('validation-error-input');
    }

    clearValidation(input) {
        const indicator = input.parentElement.querySelector('.validation-indicator');
        if (indicator) {
            indicator.innerHTML = '';
        }
        this.clearValidationError(input);
    }

    clearValidationError(input) {
        const errorElement = input.parentElement.querySelector('.validation-error');
        if (errorElement) {
            errorElement.remove();
        }
        input.classList.remove('validation-error-input');
    }

    showPasswordStrength(input, strength) {
        const strengthBar = input.parentElement.querySelector('.password-strength-bar');
        if (!strengthBar) return;

        const fill = strengthBar.querySelector('.strength-fill');
        const text = strengthBar.querySelector('.strength-text');
        const feedback = strengthBar.querySelector('.strength-feedback');

        // 更新强度条
        fill.style.width = strength.score + '%';
        
        // 更新颜色
        let color = '#ef4444'; // 弱
        if (strength.level === 'medium') color = '#f59e0b'; // 中
        if (strength.level === 'strong') color = '#10b981'; // 强
        
        fill.style.backgroundColor = color;

        // 更新文本
        const levelText = {
            weak: '弱',
            medium: '中',
            strong: '强'
        };
        text.textContent = `密码强度: ${levelText[strength.level]} (${strength.score}%)`;
        text.style.color = color;

        // 更新建议
        if (strength.feedback.length > 0) {
            feedback.textContent = '建议: ' + strength.feedback.join('、');
            feedback.style.color = '#6b7280';
        } else {
            feedback.textContent = '密码强度良好';
            feedback.style.color = '#10b981';
        }
    }

    /**
     * 工具方法
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    cancelPendingRequest(key) {
        if (this.pendingRequests.has(key)) {
            this.pendingRequests.get(key).abort();
            this.pendingRequests.delete(key);
        }
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.validationCache.clear();
    }

    /**
     * 销毁实例
     */
    destroy() {
        // 取消所有待处理的请求
        this.pendingRequests.forEach(controller => controller.abort());
        this.pendingRequests.clear();
        this.clearCache();
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.realTimeValidation = new RealTimeValidation();
});

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTimeValidation;
}

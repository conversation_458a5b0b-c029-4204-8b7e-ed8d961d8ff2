<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 商品管理页面</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            padding: 6px 20px 4px;
            margin-top: 12px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
        }

        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
        }

        .page-content {
            flex: 1;
            padding: 24px;
        }

        .content-with-menu {
            display: flex;
            gap: 24px;
        }

        .menu-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .right-menu {
            width: 240px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 24px;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .menu-level-1 > .menu-item {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            padding: 8px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .menu-level-2 {
            margin-left: 12px;
            margin-top: 8px;
            border-left: 1px solid #e5e7eb;
            padding-left: 12px;
        }

        .menu-level-2 > .menu-item {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            padding: 6px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .menu-level-3 {
            margin-left: 12px;
            margin-top: 6px;
            border-left: 1px solid #f3f4f6;
            padding-left: 12px;
        }

        .menu-level-3 > .menu-item {
            font-size: 12px;
            color: #6b7280;
            padding: 4px 0;
            cursor: pointer;
        }

        .menu-level-4 {
            margin-left: 12px;
            margin-top: 4px;
            padding-left: 12px;
        }

        .menu-level-4 > .menu-item {
            font-size: 11px;
            color: #9ca3af;
            padding: 3px 0;
            cursor: pointer;
        }

        .menu-level-4 > .menu-item.active {
            color: #374151;
            font-weight: 500;
        }

        .expand-icon {
            font-size: 10px;
            transition: transform 0.2s;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .menu-children {
            display: none;
        }

        .menu-children.expanded {
            display: block;
        }

        @media (max-width: 1200px) {
            .content-with-menu {
                flex-direction: column;
            }
            .right-menu {
                width: 100%;
                position: static;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="#" class="nav-item">我的代办</a>
                    <a href="#" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="#" class="nav-item">设计商品</a>
                    <a href="#" class="nav-item">需求管理</a>
                    <a href="#" class="nav-item">设计中心</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="#" class="nav-item active">商品管理</a>
                    <a href="#" class="nav-item">订单管理</a>
                    <a href="#" class="nav-item">客户管理</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <header class="top-nav">
                <h1 class="breadcrumb-title">商品管理</h1>
                <p class="breadcrumb-description">管理您的商品信息、库存和价格</p>
            </header>

            <div class="page-content">
                <div class="content-with-menu">
                    <!-- 主要内容区域 -->
                    <div class="menu-content">
                        <h2>商品列表</h2>
                        <p>这里是商品管理的主要内容区域。</p>
                        <div style="height: 400px; background: #f8fafc; border-radius: 8px; margin-top: 16px; display: flex; align-items: center; justify-content: center; color: #6b7280;">
                            商品表格和筛选器将在这里显示
                        </div>
                    </div>

                    <!-- 右侧4层菜单 -->
                    <div class="right-menu">
                        <div class="menu-title">商品管理菜单</div>
                        
                        <!-- 一级菜单：商品管理 -->
                        <div class="menu-level-1">
                            <div class="menu-item" onclick="toggleMenu('product-management')">
                                <span>商品管理</span>
                                <i class="fas fa-chevron-right expand-icon" id="icon-product-management"></i>
                            </div>
                            <div class="menu-children expanded" id="menu-product-management">
                                <!-- 二级菜单：商品信息 -->
                                <div class="menu-level-2">
                                    <div class="menu-item" onclick="toggleMenu('product-info')">
                                        <span>商品信息</span>
                                        <i class="fas fa-chevron-right expand-icon expanded" id="icon-product-info"></i>
                                    </div>
                                    <div class="menu-children expanded" id="menu-product-info">
                                        <!-- 三级菜单：基础信息 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="toggleMenu('basic-info')">
                                                <span>基础信息</span>
                                                <i class="fas fa-chevron-right expand-icon expanded" style="font-size: 8px;"></i>
                                            </div>
                                            <div class="menu-children expanded" id="menu-basic-info">
                                                <!-- 四级菜单：具体字段 -->
                                                <div class="menu-level-4">
                                                    <div class="menu-item active">商品名称</div>
                                                    <div class="menu-item">商品SKU</div>
                                                    <div class="menu-item">商品描述</div>
                                                    <div class="menu-item">商品分类</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 三级菜单：价格信息 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item">价格信息</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 二级菜单：库存管理 -->
                                <div class="menu-level-2">
                                    <div class="menu-item">库存管理</div>
                                </div>
                            </div>
                        </div>

                        <!-- 一级菜单：系统集成 -->
                        <div class="menu-level-1">
                            <div class="menu-item">
                                <span>系统集成</span>
                                <i class="fas fa-chevron-right expand-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function toggleMenu(menuId) {
            const menu = document.getElementById('menu-' + menuId);
            const icon = document.getElementById('icon-' + menuId);
            
            if (menu && icon) {
                menu.classList.toggle('expanded');
                icon.classList.toggle('expanded');
            }
        }
    </script>
</body>
</html>

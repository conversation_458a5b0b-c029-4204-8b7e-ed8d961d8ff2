/**
 * 国际环境API优化模块
 * 提供重试机制、超时处理、错误恢复等功能
 */
class APIOptimization {
    constructor(options = {}) {
        this.config = {
            // 重试配置
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            retryMultiplier: options.retryMultiplier || 2,
            
            // 超时配置
            timeout: options.timeout || 10000,
            longTimeout: options.longTimeout || 30000,
            
            // 国际环境配置
            internationalTimeout: options.internationalTimeout || 15000,
            internationalMaxRetries: options.internationalMaxRetries || 5,
            
            // 错误恢复配置
            circuitBreakerThreshold: options.circuitBreakerThreshold || 5,
            circuitBreakerTimeout: options.circuitBreakerTimeout || 60000,
            
            // 缓存配置
            cacheEnabled: options.cacheEnabled !== false,
            cacheTimeout: options.cacheTimeout || 300000, // 5分钟
        };
        
        this.circuitBreakers = new Map();
        this.requestCache = new Map();
        this.performanceMetrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            responseTimeHistory: []
        };
        
        this.init();
    }

    /**
     * 初始化API优化
     */
    init() {
        this.detectEnvironment();
        this.setupPerformanceMonitoring();
        this.setupNetworkStatusMonitoring();
    }

    /**
     * 检测网络环境
     */
    detectEnvironment() {
        // 检测是否为国际环境
        this.isInternational = this.detectInternationalEnvironment();
        
        // 根据环境调整配置
        if (this.isInternational) {
            this.config.timeout = this.config.internationalTimeout;
            this.config.maxRetries = this.config.internationalMaxRetries;
        }
        
        console.log(`API优化已启用 - 环境: ${this.isInternational ? '国际' : '国内'}`);
    }

    /**
     * 检测是否为国际环境
     */
    detectInternationalEnvironment() {
        // 通过多种方式检测国际环境
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const language = navigator.language;
        const userAgent = navigator.userAgent;
        
        // 检查时区
        const chineseTimezones = ['Asia/Shanghai', 'Asia/Chongqing', 'Asia/Harbin', 'Asia/Urumqi'];
        const isChineseTimezone = chineseTimezones.includes(timezone);
        
        // 检查语言
        const isChineseLanguage = language.startsWith('zh');
        
        // 检查域名
        const hostname = window.location.hostname;
        const isChineseDomain = hostname.includes('.cn') || hostname.includes('china');
        
        // 综合判断
        return !(isChineseTimezone && isChineseLanguage) || hostname.includes('international');
    }

    /**
     * 优化的fetch请求
     */
    async optimizedFetch(url, options = {}) {
        const requestId = this.generateRequestId();
        const startTime = Date.now();
        
        try {
            // 检查缓存
            if (this.config.cacheEnabled && options.method !== 'POST') {
                const cachedResponse = this.getFromCache(url, options);
                if (cachedResponse) {
                    this.recordMetrics(startTime, true, true);
                    return cachedResponse;
                }
            }
            
            // 检查熔断器
            if (this.isCircuitBreakerOpen(url)) {
                throw new Error('Circuit breaker is open for this endpoint');
            }
            
            // 执行请求
            const response = await this.executeRequestWithRetry(url, options, requestId);
            
            // 缓存成功响应
            if (this.config.cacheEnabled && response.ok && options.method !== 'POST') {
                this.saveToCache(url, options, response.clone());
            }
            
            this.recordMetrics(startTime, true, false);
            this.recordCircuitBreakerSuccess(url);
            
            return response;
            
        } catch (error) {
            this.recordMetrics(startTime, false, false);
            this.recordCircuitBreakerFailure(url);
            
            // 尝试错误恢复
            const recoveredResponse = await this.attemptErrorRecovery(url, options, error);
            if (recoveredResponse) {
                return recoveredResponse;
            }
            
            throw error;
        }
    }

    /**
     * 带重试的请求执行
     */
    async executeRequestWithRetry(url, options, requestId, attempt = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
        
        try {
            const requestOptions = {
                ...options,
                signal: controller.signal,
                headers: {
                    ...options.headers,
                    'X-Request-ID': requestId,
                    'X-Attempt': attempt.toString(),
                    'X-Environment': this.isInternational ? 'international' : 'domestic'
                }
            };
            
            const response = await fetch(url, requestOptions);
            clearTimeout(timeoutId);
            
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            // 判断是否需要重试
            if (this.shouldRetry(error, attempt)) {
                const delay = this.calculateRetryDelay(attempt);
                console.warn(`请求失败，${delay}ms后重试 (${attempt}/${this.config.maxRetries}):`, error.message);
                
                await this.sleep(delay);
                return this.executeRequestWithRetry(url, options, requestId, attempt + 1);
            }
            
            throw error;
        }
    }

    /**
     * 判断是否应该重试
     */
    shouldRetry(error, attempt) {
        if (attempt >= this.config.maxRetries) {
            return false;
        }
        
        // 网络错误或超时错误可以重试
        if (error.name === 'AbortError' || 
            error.name === 'TypeError' || 
            error.message.includes('fetch')) {
            return true;
        }
        
        // 5xx服务器错误可以重试
        if (error.message.includes('HTTP 5')) {
            return true;
        }
        
        // 429 Too Many Requests 可以重试
        if (error.message.includes('HTTP 429')) {
            return true;
        }
        
        return false;
    }

    /**
     * 计算重试延迟
     */
    calculateRetryDelay(attempt) {
        // 指数退避 + 随机抖动
        const baseDelay = this.config.retryDelay * Math.pow(this.config.retryMultiplier, attempt - 1);
        const jitter = Math.random() * 1000; // 0-1秒随机抖动
        return Math.min(baseDelay + jitter, 30000); // 最大30秒
    }

    /**
     * 错误恢复尝试
     */
    async attemptErrorRecovery(url, options, originalError) {
        console.log('尝试错误恢复...');
        
        // 1. 尝试降级到备用端点
        const fallbackUrl = this.getFallbackUrl(url);
        if (fallbackUrl && fallbackUrl !== url) {
            try {
                console.log('尝试备用端点:', fallbackUrl);
                return await this.executeRequestWithRetry(fallbackUrl, options, this.generateRequestId());
            } catch (fallbackError) {
                console.warn('备用端点也失败:', fallbackError.message);
            }
        }
        
        // 2. 尝试从缓存获取过期数据
        if (this.config.cacheEnabled) {
            const staleData = this.getStaleFromCache(url, options);
            if (staleData) {
                console.log('使用过期缓存数据');
                return staleData;
            }
        }
        
        // 3. 返回默认响应（如果适用）
        const defaultResponse = this.getDefaultResponse(url, options);
        if (defaultResponse) {
            console.log('使用默认响应');
            return defaultResponse;
        }
        
        return null;
    }

    /**
     * 获取备用URL
     */
    getFallbackUrl(url) {
        // 定义备用端点映射
        const fallbackMap = {
            '/api/auth/login': '/api/v2/auth/login',
            '/api/auth/register': '/api/v2/auth/register',
            '/api/user/profile': '/api/v2/user/profile'
        };
        
        const path = new URL(url, window.location.origin).pathname;
        return fallbackMap[path] ? new URL(fallbackMap[path], window.location.origin).href : null;
    }

    /**
     * 熔断器相关方法
     */
    isCircuitBreakerOpen(url) {
        const key = this.getCircuitBreakerKey(url);
        const breaker = this.circuitBreakers.get(key);
        
        if (!breaker) return false;
        
        if (breaker.state === 'open') {
            if (Date.now() - breaker.lastFailureTime > this.config.circuitBreakerTimeout) {
                breaker.state = 'half-open';
                console.log(`熔断器半开: ${key}`);
            } else {
                return true;
            }
        }
        
        return false;
    }

    recordCircuitBreakerFailure(url) {
        const key = this.getCircuitBreakerKey(url);
        let breaker = this.circuitBreakers.get(key);
        
        if (!breaker) {
            breaker = { failures: 0, state: 'closed', lastFailureTime: 0 };
            this.circuitBreakers.set(key, breaker);
        }
        
        breaker.failures++;
        breaker.lastFailureTime = Date.now();
        
        if (breaker.failures >= this.config.circuitBreakerThreshold) {
            breaker.state = 'open';
            console.warn(`熔断器打开: ${key} (失败次数: ${breaker.failures})`);
        }
    }

    recordCircuitBreakerSuccess(url) {
        const key = this.getCircuitBreakerKey(url);
        const breaker = this.circuitBreakers.get(key);
        
        if (breaker) {
            if (breaker.state === 'half-open') {
                breaker.state = 'closed';
                breaker.failures = 0;
                console.log(`熔断器关闭: ${key}`);
            } else if (breaker.state === 'closed') {
                breaker.failures = Math.max(0, breaker.failures - 1);
            }
        }
    }

    getCircuitBreakerKey(url) {
        return new URL(url, window.location.origin).pathname;
    }

    /**
     * 缓存相关方法
     */
    getFromCache(url, options) {
        const key = this.getCacheKey(url, options);
        const cached = this.requestCache.get(key);
        
        if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
            return cached.response.clone();
        }
        
        return null;
    }

    getStaleFromCache(url, options) {
        const key = this.getCacheKey(url, options);
        const cached = this.requestCache.get(key);
        
        if (cached) {
            console.log('使用过期缓存数据');
            return cached.response.clone();
        }
        
        return null;
    }

    saveToCache(url, options, response) {
        const key = this.getCacheKey(url, options);
        this.requestCache.set(key, {
            response: response.clone(),
            timestamp: Date.now()
        });
        
        // 清理过期缓存
        this.cleanupCache();
    }

    getCacheKey(url, options) {
        const method = options.method || 'GET';
        const headers = JSON.stringify(options.headers || {});
        return `${method}:${url}:${headers}`;
    }

    cleanupCache() {
        const now = Date.now();
        for (const [key, cached] of this.requestCache.entries()) {
            if (now - cached.timestamp > this.config.cacheTimeout * 2) {
                this.requestCache.delete(key);
            }
        }
    }

    /**
     * 性能监控
     */
    setupPerformanceMonitoring() {
        // 监控网络状态变化
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                console.log('网络状态变化:', navigator.connection.effectiveType);
                this.adjustConfigForConnection();
            });
        }
    }

    setupNetworkStatusMonitoring() {
        window.addEventListener('online', () => {
            console.log('网络已连接');
            this.resetCircuitBreakers();
        });
        
        window.addEventListener('offline', () => {
            console.log('网络已断开');
        });
    }

    adjustConfigForConnection() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            // 根据网络类型调整超时时间
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                this.config.timeout = 30000;
                this.config.maxRetries = 2;
            } else if (connection.effectiveType === '3g') {
                this.config.timeout = 20000;
                this.config.maxRetries = 3;
            } else {
                this.config.timeout = this.isInternational ? 15000 : 10000;
                this.config.maxRetries = this.isInternational ? 5 : 3;
            }
        }
    }

    recordMetrics(startTime, success, fromCache) {
        const responseTime = Date.now() - startTime;
        
        this.performanceMetrics.totalRequests++;
        if (success) {
            this.performanceMetrics.successfulRequests++;
        } else {
            this.performanceMetrics.failedRequests++;
        }
        
        if (!fromCache) {
            this.performanceMetrics.responseTimeHistory.push(responseTime);
            if (this.performanceMetrics.responseTimeHistory.length > 100) {
                this.performanceMetrics.responseTimeHistory.shift();
            }
            
            this.performanceMetrics.averageResponseTime = 
                this.performanceMetrics.responseTimeHistory.reduce((a, b) => a + b, 0) / 
                this.performanceMetrics.responseTimeHistory.length;
        }
    }

    /**
     * 工具方法
     */
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    resetCircuitBreakers() {
        this.circuitBreakers.clear();
        console.log('熔断器已重置');
    }

    getDefaultResponse(url, options) {
        // 为某些端点提供默认响应
        const defaults = {
            '/api/user/profile': new Response(JSON.stringify({
                success: false,
                message: '网络连接失败，请稍后重试',
                data: null
            }), { status: 503 })
        };
        
        const path = new URL(url, window.location.origin).pathname;
        return defaults[path] || null;
    }

    /**
     * 获取性能指标
     */
    getMetrics() {
        return {
            ...this.performanceMetrics,
            successRate: this.performanceMetrics.totalRequests > 0 ? 
                (this.performanceMetrics.successfulRequests / this.performanceMetrics.totalRequests * 100).toFixed(2) + '%' : '0%',
            environment: this.isInternational ? 'international' : 'domestic',
            circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([key, breaker]) => ({
                endpoint: key,
                state: breaker.state,
                failures: breaker.failures
            }))
        };
    }

    /**
     * 清理资源
     */
    destroy() {
        this.requestCache.clear();
        this.circuitBreakers.clear();
    }
}

// 创建全局实例
window.apiOptimization = new APIOptimization();

// 重写全局fetch
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    return window.apiOptimization.optimizedFetch(url, options);
};

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIOptimization;
}

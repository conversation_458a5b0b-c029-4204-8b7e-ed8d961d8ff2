/**
 * 智能家居管理系统 - 设计令牌系统
 * 基于现代设计系统最佳实践，提供统一的视觉语言
 * 
 * 设计原则：
 * - 简洁现代：黑白灰主色调，突出内容
 * - 层次清晰：通过间距、字体、颜色建立信息层级
 * - 一致性：统一的组件样式和交互模式
 * - 可访问性：符合WCAG 2.1 AA标准
 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 - 黑白灰系统 */
  --color-primary: #000000;
  --color-primary-50: #f9fafb;
  --color-primary-100: #f3f4f6;
  --color-primary-200: #e5e7eb;
  --color-primary-300: #d1d5db;
  --color-primary-400: #9ca3af;
  --color-primary-500: #6b7280;
  --color-primary-600: #4b5563;
  --color-primary-700: #374151;
  --color-primary-800: #1f2937;
  --color-primary-900: #111827;
  --color-primary-950: #030712;

  /* 语义化颜色 */
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-success-dark: #047857;
  
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #d97706;
  
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-error-dark: #dc2626;
  
  --color-info: #3b82f6;
  --color-info-light: #dbeafe;
  --color-info-dark: #1d4ed8;

  /* 背景色系统 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-glass: rgba(255, 255, 255, 0.95);

  /* 文字颜色系统 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  --text-link: #3b82f6;
  --text-link-hover: #1d4ed8;

  /* 边框颜色系统 */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #3b82f6;
  --border-error: #ef4444;

  /* ========== 字体系统 ========== */
  
  /* 字体族 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ========== 间距系统 ========== */
  
  /* 基础间距单位 */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* 组件间距 */
  --spacing-component-xs: var(--space-2);
  --spacing-component-sm: var(--space-4);
  --spacing-component-md: var(--space-6);
  --spacing-component-lg: var(--space-8);
  --spacing-component-xl: var(--space-12);

  /* ========== 圆角系统 ========== */
  
  --radius-none: 0;
  --radius-sm: 0.25rem;    /* 4px */
  --radius-base: 0.375rem; /* 6px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* ========== 阴影系统 ========== */
  
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* ========== 动画系统 ========== */
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  /* 动画缓动 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ========== 布局系统 ========== */
  
  /* 容器宽度 */
  --container-xs: 480px;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* 侧边栏 */
  --sidebar-width: 240px;
  --sidebar-width-collapsed: 64px;

  /* 头部高度 */
  --header-height: 64px;

  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ========== 响应式断点 ========== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* ========== 组件特定令牌 ========== */
  
  /* 按钮 */
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  --button-padding-x-sm: var(--space-3);
  --button-padding-x-md: var(--space-4);
  --button-padding-x-lg: var(--space-6);

  /* 输入框 */
  --input-height-sm: 32px;
  --input-height-md: 40px;
  --input-height-lg: 48px;
  --input-padding-x: var(--space-3);

  /* 卡片 */
  --card-padding: var(--space-6);
  --card-radius: var(--radius-lg);
  --card-shadow: var(--shadow-sm);

  /* 表格 */
  --table-row-height: 48px;
  --table-cell-padding-x: var(--space-4);
  --table-cell-padding-y: var(--space-3);

  /* ========== 状态颜色 ========== */
  
  /* 悬停状态 */
  --hover-bg: var(--color-primary-50);
  --hover-border: var(--color-primary-300);

  /* 激活状态 */
  --active-bg: var(--color-primary-100);
  --active-border: var(--color-primary-400);

  /* 禁用状态 */
  --disabled-bg: var(--color-primary-100);
  --disabled-text: var(--color-primary-400);
  --disabled-border: var(--color-primary-200);

  /* 选中状态 */
  --selected-bg: var(--color-primary-900);
  --selected-text: var(--color-primary-50);
  --selected-border: var(--color-primary-900);
}

/* ========== 深色模式支持 ========== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-glass: rgba(17, 24, 39, 0.95);

    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --text-inverse: #111827;

    --border-primary: #374151;
    --border-secondary: #4b5563;

    --hover-bg: #374151;
    --active-bg: #4b5563;
    --disabled-bg: #374151;
    --selected-bg: #f9fafb;
    --selected-text: #111827;
  }
}

/* ========== 高对比度模式支持 ========== */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-secondary: #000000;
  }
}

/* ========== 减少动画模式支持 ========== */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-fast: 0ms;
    --duration-normal: 0ms;
    --duration-slow: 0ms;
  }
}

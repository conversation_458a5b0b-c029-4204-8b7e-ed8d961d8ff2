/* 智能家居管理系统 - 移动端响应式优化 */

/* ==================== 基础响应式断点 ==================== */
/* 
  xs: 0-575px (手机竖屏)
  sm: 576-767px (手机横屏)
  md: 768-991px (平板竖屏)
  lg: 992-1199px (平板横屏/小屏笔记本)
  xl: 1200px+ (桌面)
*/

/* ==================== 移动端基础优化 ==================== */
@media (max-width: 767px) {
    /* 基础布局调整 */
    .admin-layout {
        flex-direction: column;
    }
    
    /* 侧边栏移动端优化 */
    .sidebar {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    
    .sidebar.mobile-open {
        left: 0;
    }
    
    /* 主内容区域调整 */
    .main-content {
        margin-left: 0;
        width: 100%;
        min-height: 100vh;
    }
    
    /* 顶部导航移动端优化 */
    .top-nav {
        padding: 12px 16px;
        position: sticky;
        top: 0;
        z-index: 999;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        background: rgba(255, 255, 255, 0.9);
    }
    
    /* 添加移动端菜单按钮 */
    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        font-size: 20px;
        color: #374151;
        padding: 8px;
        border-radius: 6px;
        cursor: pointer;
        margin-right: 12px;
    }
    
    .mobile-menu-btn:hover {
        background: rgba(107, 114, 128, 0.1);
    }
    
    /* 面包屑导航移动端优化 */
    .nav-breadcrumb {
        flex: 1;
    }
    
    .breadcrumb-title {
        font-size: 18px !important;
        margin-bottom: 4px;
    }
    
    .breadcrumb-description {
        font-size: 12px !important;
        line-height: 1.4;
    }
}

/* ==================== 统计卡片响应式 ==================== */
@media (max-width: 767px) {
    .user-stats, .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        padding: 16px 12px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .stat-value {
        font-size: 20px !important;
    }
    
    .stat-label {
        font-size: 11px !important;
    }
}

@media (max-width: 480px) {
    .user-stats, .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-card {
        flex-direction: row;
        align-items: center;
        gap: 12px;
        padding: 12px;
    }
}

/* ==================== 工具栏响应式 ==================== */
@media (max-width: 767px) {
    .user-toolbar, .toolbar {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .toolbar-left, .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .toolbar-left {
        order: 2;
    }
    
    .toolbar-right {
        order: 1;
    }
    
    /* 搜索框移动端优化 */
    .search-box {
        width: 100%;
        margin-bottom: 12px;
    }
    
    .search-box input {
        width: 100%;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    /* 筛选器移动端优化 */
    .toolbar-right select {
        flex: 1;
        min-width: 0;
        font-size: 14px;
    }
}

/* ==================== 表格响应式 ==================== */
@media (max-width: 767px) {
    .user-table-container, .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 8px;
    }
    
    .user-table, .data-table {
        min-width: 600px;
        font-size: 13px;
    }
    
    .user-table th, .data-table th {
        padding: 8px 6px;
        font-size: 12px;
        white-space: nowrap;
    }
    
    .user-table td, .data-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
    
    /* 表格操作按钮优化 */
    .table-actions {
        display: flex;
        gap: 4px;
    }
    
    .table-actions .btn {
        padding: 4px 8px;
        font-size: 12px;
        min-width: auto;
    }
}

/* ==================== 表单响应式 ==================== */
@media (max-width: 767px) {
    .form-container, .glass-form {
        margin: 0 16px;
        padding: 20px 16px;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .form-label {
        font-size: 14px;
        margin-bottom: 6px;
    }
    
    .form-input, .glass-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 16px;
    }
    
    .form-col {
        width: 100%;
    }
}

/* ==================== 模态框响应式 ==================== */
@media (max-width: 767px) {
    .modal {
        padding: 16px;
        align-items: flex-end;
    }
    
    .modal-content, .glass-modal-content {
        width: 100%;
        max-width: none;
        max-height: 90vh;
        margin: 0;
        border-radius: 12px 12px 0 0;
    }
    
    .modal-header {
        padding: 16px 20px;
    }
    
    .modal-header h3 {
        font-size: 16px;
    }
    
    .modal-body {
        padding: 20px;
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }
    
    .modal-footer {
        padding: 16px 20px;
        flex-direction: column-reverse;
        gap: 12px;
    }
    
    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ==================== 按钮响应式 ==================== */
@media (max-width: 767px) {
    .btn {
        padding: 10px 16px;
        font-size: 14px;
        min-height: 44px; /* 触摸友好 */
    }
    
    .btn-group {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn-group .btn {
        width: 100%;
    }
    
    /* 浮动操作按钮 */
    .fab {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: var(--primary-black);
        color: white;
        border: none;
        font-size: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

/* ==================== 分页响应式 ==================== */
@media (max-width: 767px) {
    .pagination-container {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .pagination-info {
        text-align: center;
        font-size: 13px;
    }
    
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .pagination .btn {
        min-width: 40px;
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* ==================== 卡片网格响应式 ==================== */
@media (max-width: 767px) {
    .cards-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 0 16px;
    }
    
    .card, .glass-card {
        margin-bottom: 16px;
    }
    
    .card-header {
        padding: 16px;
    }
    
    .card-body {
        padding: 16px;
    }
    
    .card-title {
        font-size: 16px;
    }
}

/* ==================== 导航标签响应式 ==================== */
@media (max-width: 767px) {
    .nav-tabs {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .nav-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .nav-tabs .nav-item {
        flex-shrink: 0;
        white-space: nowrap;
        min-width: 80px;
    }
}

/* ==================== 毛玻璃效果移动端优化 ==================== */
@media (max-width: 767px) {
    /* 减少模糊强度以提升性能 */
    .glass-container {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
    
    .glass-panel {
        backdrop-filter: blur(6px);
        -webkit-backdrop-filter: blur(6px);
    }
    
    .glass-card {
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
    
    /* 移动端背景优化 */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
}

/* ==================== 触摸优化 ==================== */
@media (hover: none) and (pointer: coarse) {
    /* 移除悬停效果，使用点击效果 */
    .btn:hover,
    .nav-item:hover,
    .table-row:hover {
        transform: none;
        box-shadow: none;
    }
    
    .btn:active,
    .nav-item:active {
        transform: scale(0.98);
        opacity: 0.8;
    }
    
    /* 增加触摸目标大小 */
    .btn,
    .nav-item,
    .form-input,
    .table-actions .btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* ==================== 横屏优化 ==================== */
@media (max-width: 767px) and (orientation: landscape) {
    .modal-content {
        max-height: 80vh;
    }
    
    .sidebar {
        width: 240px;
        left: -240px;
    }
    
    .stat-card {
        padding: 12px;
    }
}

/* ==================== 超小屏幕优化 ==================== */
@media (max-width: 360px) {
    .page-content {
        padding: 12px 8px;
    }
    
    .user-toolbar, .toolbar {
        padding: 12px 8px;
    }
    
    .modal {
        padding: 8px;
    }
    
    .btn {
        font-size: 13px;
        padding: 8px 12px;
    }
}

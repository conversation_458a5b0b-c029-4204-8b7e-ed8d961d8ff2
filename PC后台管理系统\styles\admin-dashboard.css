/* 自定义CSS样式 - 补充Tailwind CSS */

/* 导航链接样式 */
.nav-link {
    @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-600 rounded-lg transition-all duration-200 hover:bg-gray-100 hover:text-gray-900 group;
}

.nav-link i {
    @apply w-5 text-center flex-shrink-0 transition-colors duration-200;
}

.nav-link.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-600;
    position: relative;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    border-radius: 0 3px 3px 0;
}

.nav-link:hover:not(.active) {
    transform: translateX(2px);
}

/* 搜索框增强 */
.search-input:focus {
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 侧边栏阴影效果 */
.sidebar-shadow {
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 顶部栏阴影 */
.top-bar-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 按钮悬停效果 */
.hover-scale:hover {
    transform: scale(1.05);
}

/* 渐变背景 */
.gradient-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

/* 玻璃态效果 */
.glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.8);
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 响应式字体大小 */
@media (max-width: 640px) {
    .responsive-text {
        font-size: 0.875rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .dark-mode-support {
        background-color: #1f2937;
        color: #f9fafb;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .nav-link {
        border: 1px solid transparent;
    }
    
    .nav-link:hover,
    .nav-link.active {
        border-color: #0ea5e9;
    }
}

/* 减少动画（可访问性） */
@media (prefers-reduced-motion: reduce) {
    .nav-link,
    .hover-scale,
    .fade-in,
    .slide-in-left {
        transition: none;
        animation: none;
    }
}

/* 加载状态 */
.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* 通知徽章 */
.notification-badge {
    position: relative;
}

.notification-badge::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    border: 2px solid white;
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    margin-bottom: 4px;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 自定义焦点样式 */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* 表格样式增强 */
.table-hover tbody tr:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 卡片悬停效果 */
.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
} 
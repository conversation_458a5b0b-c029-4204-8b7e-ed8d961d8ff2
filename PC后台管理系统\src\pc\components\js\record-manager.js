/**
 * 现场记录管理模块
 * 修复照片上传和视频预览播放功能
 * 支持照片和视频上传，按时间排序展示，支持预览和删除
 * 版本: v2.0
 */

class RecordManager {
    constructor() {
        this.records = this.loadRecords();
        this.maxFileSize = 100 * 1024 * 1024; // 100MB
        this.supportedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
        this.supportedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
        this.init();
    }

    /**
     * 初始化现场记录管理器
     */
    init() {
        console.log('📸 现场记录管理器已初始化');
        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('record-manager-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'record-manager-styles';
        styles.textContent = `
            .record-upload-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .record-upload-content {
                background: white;
                border-radius: 12px;
                padding: 24px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
            }

            .record-drop-zone {
                border: 2px dashed #d1d5db;
                border-radius: 8px;
                padding: 40px 20px;
                text-align: center;
                background: #f9fafb;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .record-drop-zone:hover,
            .record-drop-zone.dragover {
                border-color: #3b82f6;
                background: #eff6ff;
            }

            .record-preview-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
            }

            .record-preview-content {
                position: relative;
                max-width: 90%;
                max-height: 90%;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .record-preview-media {
                max-width: 100%;
                max-height: 80vh;
                object-fit: contain;
                border-radius: 8px;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            }

            .record-preview-controls {
                margin-top: 20px;
                display: flex;
                gap: 12px;
                align-items: center;
            }

            .record-preview-info {
                background: rgba(255, 255, 255, 0.9);
                padding: 12px 16px;
                border-radius: 6px;
                color: #374151;
                font-size: 14px;
                margin-top: 12px;
            }

            .record-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 16px;
                margin-top: 16px;
            }

            .record-item {
                position: relative;
                border-radius: 8px;
                overflow: hidden;
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .record-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
                border-color: #3b82f6;
            }

            .record-preview {
                position: relative;
                width: 100%;
                height: 150px;
                overflow: hidden;
            }

            .record-preview img,
            .record-preview video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .record-type-badge {
                position: absolute;
                top: 8px;
                left: 8px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .record-info {
                padding: 12px;
            }

            .record-name {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .record-time {
                font-size: 12px;
                color: #6b7280;
                margin-bottom: 8px;
            }

            .record-actions {
                display: flex;
                gap: 4px;
            }

            .upload-preview-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 12px;
                margin-top: 16px;
            }

            .upload-preview-item {
                position: relative;
                border-radius: 6px;
                overflow: hidden;
                background: #f9fafb;
                border: 1px solid #e5e7eb;
            }

            .upload-preview-media {
                width: 100%;
                height: 80px;
                object-fit: cover;
            }

            .upload-preview-info {
                padding: 8px;
                font-size: 12px;
                color: #6b7280;
                text-align: center;
            }

            .upload-preview-remove {
                position: absolute;
                top: 4px;
                right: 4px;
                background: rgba(239, 68, 68, 0.9);
                color: white;
                border: none;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 10px;
            }

            .video-play-overlay {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                pointer-events: none;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }

            .record-preview-modal {
                animation: fadeIn 0.3s ease;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * 上传现场记录
     */
    uploadRecord(phase) {
        try {
            const modal = this.createUploadModal(phase);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        } catch (error) {
            console.error('创建上传模态框失败:', error);
            constructionManager.showErrorMessage('上传功能初始化失败');
        }
    }

    /**
     * 创建上传模态框
     */
    createUploadModal(phase) {
        const modal = document.createElement('div');
        modal.className = 'record-upload-modal';
        
        modal.innerHTML = `
            <div class="record-upload-content">
                <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                    <h3><i class="fas fa-camera"></i> 上传现场记录</h3>
                    <button class="btn-close" onclick="this.closest('.record-upload-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="record-drop-zone" id="recordDropZone">
                    <div class="drop-zone-content">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;"></i>
                        <p style="margin: 0 0 8px 0; font-size: 16px; color: #374151;">拖拽照片或视频到此处或点击选择文件</p>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">支持 JPG, PNG, GIF, MP4, WebM 等格式</p>
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #9ca3af;">最大文件大小: 100MB</p>
                    </div>
                    <input type="file" id="recordInput" style="display: none;" multiple 
                           accept="image/*,video/*">
                </div>
                
                <div id="uploadPreview" style="display: none;">
                    <h4 style="margin: 20px 0 12px 0; color: #374151;">预览</h4>
                    <div id="previewGrid" class="upload-preview-grid"></div>
                </div>
                
                <div id="uploadProgress" style="display: none; margin-top: 20px;">
                    <div class="upload-progress" style="width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden;">
                        <div class="upload-progress-bar" id="recordProgressBar" style="height: 100%; background: #3b82f6; transition: width 0.3s ease; width: 0%;"></div>
                    </div>
                    <div id="uploadStatus" style="text-align: center; color: #6b7280; font-size: 14px; margin-top: 8px;"></div>
                </div>
                
                <div class="modal-footer" style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.record-upload-modal').remove()">取消</button>
                    <button class="btn btn-primary" id="uploadRecordBtn" onclick="recordManager.processRecordUpload('${phase}', this)" disabled>
                        <i class="fas fa-upload"></i> 开始上传
                    </button>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindRecordUploadEvents(modal, phase);
        
        return modal;
    }

    /**
     * 绑定上传事件
     */
    bindRecordUploadEvents(modal, phase) {
        const dropZone = modal.querySelector('#recordDropZone');
        const fileInput = modal.querySelector('#recordInput');
        const previewContainer = modal.querySelector('#uploadPreview');
        const previewGrid = modal.querySelector('#previewGrid');
        const uploadBtn = modal.querySelector('#uploadRecordBtn');
        
        let selectedFiles = [];

        // 点击选择文件
        dropZone.addEventListener('click', () => fileInput.click());

        // 拖拽事件
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleRecordSelection(files, previewContainer, previewGrid, uploadBtn, selectedFiles);
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleRecordSelection(files, previewContainer, previewGrid, uploadBtn, selectedFiles);
        });

        // 存储选中的文件到模态框
        modal.selectedFiles = selectedFiles;
    }

    /**
     * 处理记录文件选择
     */
    handleRecordSelection(files, previewContainer, previewGrid, uploadBtn, selectedFiles) {
        files.forEach(file => {
            // 验证文件
            const validation = this.validateRecordFile(file);
            if (validation.valid) {
                selectedFiles.push(file);
                this.generatePreview(file, previewGrid, selectedFiles);
            } else {
                constructionManager.showErrorMessage(`文件 ${file.name}: ${validation.error}`);
            }
        });

        // 显示预览区域
        if (selectedFiles.length > 0) {
            previewContainer.style.display = 'block';
            uploadBtn.disabled = false;
        }
    }

    /**
     * 验证记录文件
     */
    validateRecordFile(file) {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
            return { valid: false, error: '文件大小超过100MB限制' };
        }

        // 检查文件类型
        const isImage = this.supportedImageTypes.includes(file.type);
        const isVideo = this.supportedVideoTypes.includes(file.type);

        if (!isImage && !isVideo) {
            return { valid: false, error: '不支持的文件格式，请选择图片或视频文件' };
        }

        return { valid: true, type: isImage ? 'image' : 'video' };
    }

    /**
     * 生成预览
     */
    generatePreview(file, container, selectedFiles) {
        const reader = new FileReader();
        const fileIndex = selectedFiles.length - 1;
        
        reader.onload = (e) => {
            const isVideo = this.supportedVideoTypes.includes(file.type);
            const previewItem = document.createElement('div');
            previewItem.className = 'upload-preview-item';
            previewItem.dataset.index = fileIndex;
            
            previewItem.innerHTML = `
                ${isVideo ? 
                    `<video class="upload-preview-media" src="${e.target.result}" muted></video>
                     <div class="video-play-overlay"><i class="fas fa-play"></i></div>` :
                    `<img class="upload-preview-media" src="${e.target.result}" alt="${file.name}">`
                }
                <button class="upload-preview-remove" onclick="recordManager.removeRecordFile(${fileIndex}, this)" title="移除">
                    <i class="fas fa-times"></i>
                </button>
                <div class="upload-preview-info">
                    <div style="font-weight: 500; margin-bottom: 2px;">${file.name}</div>
                    <div>${constructionManager.formatFileSize(file.size)}</div>
                </div>
            `;
            
            container.appendChild(previewItem);
        };

        reader.readAsDataURL(file);
    }

    /**
     * 移除记录文件
     */
    removeRecordFile(index, button) {
        const modal = button.closest('.record-upload-modal');
        const selectedFiles = modal.selectedFiles;
        const previewItem = button.closest('.upload-preview-item');
        const uploadBtn = modal.querySelector('#uploadRecordBtn');
        const previewContainer = modal.querySelector('#uploadPreview');

        selectedFiles.splice(index, 1);
        previewItem.remove();

        // 更新索引
        const remainingItems = modal.querySelectorAll('.upload-preview-item');
        remainingItems.forEach((item, newIndex) => {
            item.dataset.index = newIndex;
            const removeBtn = item.querySelector('.upload-preview-remove');
            removeBtn.setAttribute('onclick', `recordManager.removeRecordFile(${newIndex}, this)`);
        });

        // 如果没有文件了，隐藏预览区域
        if (selectedFiles.length === 0) {
            previewContainer.style.display = 'none';
            uploadBtn.disabled = true;
        }
    }

    /**
     * 处理记录上传
     */
    async processRecordUpload(phase, button) {
        const modal = button.closest('.record-upload-modal');
        const selectedFiles = modal.selectedFiles;
        const progressContainer = modal.querySelector('#uploadProgress');
        const progressBar = modal.querySelector('#recordProgressBar');
        const statusText = modal.querySelector('#uploadStatus');

        if (selectedFiles.length === 0) {
            constructionManager.showWarningMessage('请先选择文件');
            return;
        }

        try {
            // 显示进度
            progressContainer.style.display = 'block';
            button.disabled = true;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = ((i + 1) / selectedFiles.length) * 100;
                
                progressBar.style.width = `${progress}%`;
                statusText.textContent = `正在上传: ${file.name} (${i + 1}/${selectedFiles.length})`;

                // 上传单个文件
                await this.uploadSingleRecord(file, phase);
                
                // 短暂延迟以显示进度
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            // 上传完成
            constructionManager.showSuccessMessage(`成功上传 ${selectedFiles.length} 个现场记录`);
            modal.remove();
            
            // 刷新记录列表
            constructionManager.renderRecords(phase);

        } catch (error) {
            console.error('现场记录上传失败:', error);
            constructionManager.showErrorMessage('上传失败: ' + error.message);
        } finally {
            button.disabled = false;
        }
    }

    /**
     * 上传单个记录
     */
    async uploadSingleRecord(file, phase) {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();
                
                reader.onload = async (e) => {
                    const isVideo = this.supportedVideoTypes.includes(file.type);
                    
                    const record = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        type: isVideo ? 'video' : 'image',
                        mimeType: file.type,
                        size: file.size,
                        url: e.target.result,
                        thumbnail: isVideo ? await this.generateVideoThumbnail(e.target.result) : e.target.result,
                        timestamp: Date.now(),
                        phase: phase
                    };

                    // 保存到阶段数据
                    constructionManager.phaseData[phase].records.push(record);
                    
                    // 保存到全局记录
                    this.records.push(record);
                    this.saveRecords();
                    
                    // 保存阶段数据
                    constructionManager.savePhaseData();
                    
                    resolve(record);
                };

                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 生成视频缩略图
     */
    generateVideoThumbnail(videoUrl) {
        return new Promise((resolve) => {
            try {
                const video = document.createElement('video');
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                video.onloadedmetadata = () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    
                    video.currentTime = 1; // 获取第1秒的帧
                };
                
                video.onseeked = () => {
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                    const thumbnail = canvas.toDataURL('image/jpeg', 0.7);
                    resolve(thumbnail);
                };
                
                video.onerror = () => {
                    console.warn('视频缩略图生成失败，使用默认图标');
                    resolve(null);
                };
                
                video.src = videoUrl;
                video.load();
                
            } catch (error) {
                console.warn('视频缩略图生成失败:', error);
                resolve(null);
            }
        });
    }

    /**
     * 预览媒体
     */
    previewMedia(recordId) {
        try {
            const record = this.findRecord(recordId);
            if (!record) {
                constructionManager.showErrorMessage('记录不存在');
                return;
            }

            const modal = this.createPreviewModal(record);
            document.body.appendChild(modal);
            modal.style.display = 'flex';

        } catch (error) {
            console.error('预览媒体失败:', error);
            constructionManager.showErrorMessage('媒体预览失败');
        }
    }

    /**
     * 创建预览模态框
     */
    createPreviewModal(record) {
        const modal = document.createElement('div');
        modal.className = 'record-preview-modal';
        
        const isVideo = record.type === 'video';
        
        modal.innerHTML = `
            <div class="record-preview-content">
                ${isVideo ? 
                    `<video class="record-preview-media" src="${record.url}" controls autoplay>
                        您的浏览器不支持视频播放。
                    </video>` :
                    `<img class="record-preview-media" src="${record.url}" alt="${record.name}">`
                }
                
                <div class="record-preview-info">
                    <strong>${record.name}</strong> • 
                    ${constructionManager.formatFileSize(record.size)} • 
                    ${constructionManager.formatDate(record.timestamp)}
                </div>
                
                <div class="record-preview-controls">
                    <button class="btn btn-secondary" onclick="recordManager.downloadRecord(${record.id})" title="下载">
                        <i class="fas fa-download"></i> 下载
                    </button>
                    <button class="btn btn-danger" onclick="recordManager.deleteRecordFromPreview(${record.id}, this)" title="删除">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                    <button class="btn btn-secondary" onclick="this.closest('.record-preview-modal').remove()" title="关闭">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // ESC键关闭
        const handleKeyPress = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleKeyPress);
            }
        };
        document.addEventListener('keydown', handleKeyPress);

        return modal;
    }

    /**
     * 下载记录
     */
    downloadRecord(recordId) {
        try {
            const record = this.findRecord(recordId);
            if (!record) {
                constructionManager.showErrorMessage('记录不存在');
                return;
            }

            const link = document.createElement('a');
            link.href = record.url;
            link.download = record.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            constructionManager.showSuccessMessage('文件下载已开始');

        } catch (error) {
            console.error('下载记录失败:', error);
            constructionManager.showErrorMessage('下载失败');
        }
    }

    /**
     * 从预览中删除记录
     */
    deleteRecordFromPreview(recordId, button) {
        if (confirm('确认删除此现场记录吗？此操作不可恢复。')) {
            this.deleteRecord(recordId);
            button.closest('.record-preview-modal').remove();
            
            // 刷新当前阶段的记录列表
            constructionManager.renderRecords(constructionManager.currentPhase);
        }
    }

    /**
     * 删除记录
     */
    deleteRecord(recordId) {
        try {
            // 从全局记录中删除
            const globalIndex = this.records.findIndex(r => r.id == recordId);
            if (globalIndex !== -1) {
                this.records.splice(globalIndex, 1);
            }

            // 从所有阶段数据中删除
            Object.keys(constructionManager.phaseData).forEach(phase => {
                const phaseRecords = constructionManager.phaseData[phase].records;
                const phaseIndex = phaseRecords.findIndex(r => r.id == recordId);
                if (phaseIndex !== -1) {
                    phaseRecords.splice(phaseIndex, 1);
                }
            });

            this.saveRecords();
            constructionManager.savePhaseData();
            constructionManager.showSuccessMessage('现场记录已删除');

        } catch (error) {
            console.error('删除记录失败:', error);
            constructionManager.showErrorMessage('删除失败');
        }
    }

    /**
     * 工具方法
     */
    findRecord(recordId) {
        return this.records.find(r => r.id == recordId);
    }

    /**
     * 数据持久化
     */
    loadRecords() {
        try {
            const data = localStorage.getItem('construction_records');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('加载记录数据失败:', error);
            return [];
        }
    }

    saveRecords() {
        try {
            localStorage.setItem('construction_records', JSON.stringify(this.records));
        } catch (error) {
            console.error('保存记录数据失败:', error);
        }
    }
}

// 全局实例
let recordManager;

// 延迟初始化，等待其他模块加载
if (typeof window !== 'undefined') {
    window.RecordManager = RecordManager;
}

/**
 * 检查所有页面侧边栏一致性的脚本
 * 对比每个页面的侧边栏与标准模板的差异
 */

const fs = require('fs');
const path = require('path');

// 标准侧边栏菜单结构（来自project-center.html）
const STANDARD_MENU = {
    sections: [
        {
            title: "个人中心",
            items: [
                { href: "my-todos.html", text: "我的代办" },
                { href: "my-orders.html", text: "我的订单" }
            ]
        },
        {
            title: "业务管理", 
            items: [
                { href: "design-products.html", text: "设计商品" },
                { href: "requirements-management.html", text: "需求管理" },
                { href: "design-center.html", text: "设计中心" },
                { href: "design-cases.html", text: "设计案例" },
                { href: "project-center.html", text: "项目中心" },
                { href: "construction-management.html", text: "施工管理" },
                { href: "construction-guide.html", text: "施工指导" }
            ]
        },
        {
            title: "商务中心",
            items: [
                { href: "products.html", text: "商品管理" },
                { href: "orders.html", text: "订单管理" },
                { href: "customer-management.html", text: "客户管理" },
                { href: "marketing-management.html", text: "营销管理" }
            ]
        },
        {
            title: "知识库",
            items: [
                { href: "design-knowledge.html", text: "设计知识库" },
                { href: "delivery-knowledge.html", text: "交底知识库" },
                { href: "electrical-delivery-knowledge.html", text: "水电交底知识库" },
                { href: "market-knowledge.html", text: "市转知识库" },
                { href: "installation-knowledge.html", text: "布线知识库" },
                { href: "product-knowledge.html", text: "产品知识库" }
            ]
        },
        {
            title: "系统工具",
            items: [
                { href: "api-tools.html", text: "API 工具" },
                { href: "erp-documentation.html", text: "ERP文档" },
                { href: "system-settings.html", text: "系统配置" },
                { href: "user-management.html", text: "用户管理" },
                { href: "internal-permissions.html", text: "内部权限" },
                { href: "customer-permissions.html", text: "客户权限" },
                { href: "data-management.html", text: "数据管理" }
            ]
        },
        {
            title: "数据分析",
            items: [
                { href: "requirements-analytics.html", text: "需求分析" },
                { href: "project-analytics.html", text: "项目分析" },
                { href: "order-analytics.html", text: "订单分析" },
                { href: "customer-analytics.html", text: "客户分析" }
            ]
        },
        {
            title: "个人中心",
            items: [
                { href: "demo.html", text: "演示展示" },
                { href: "user-profile.html", text: "个人资料" },
                { href: "logout.html", text: "退出登录" }
            ]
        }
    ]
};

// 需要检查的页面列表（排除一些特殊页面）
const PAGES_TO_CHECK = [
    'design-center.html',
    'design-requirements-table.html', 
    'user-management.html',
    'products.html',
    'orders.html',
    'customer-management.html',
    'construction-management.html',
    'design-cases.html',
    'design-products.html',
    'requirements-management.html',
    'marketing-management.html',
    'analytics.html',
    'demo.html',
    'user-profile.html'
];

// 检查单个页面的侧边栏
function checkPageSidebar(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        console.log(`\n📄 检查页面: ${fileName}`);
        console.log('=' .repeat(50));
        
        // 检查是否有侧边栏
        if (!content.includes('nav-menu') && !content.includes('sidebar')) {
            console.log('❌ 没有找到侧边栏结构');
            return { fileName, status: 'no-sidebar', issues: ['缺少侧边栏'] };
        }
        
        const issues = [];
        
        // 检查知识库部分的常见问题
        if (content.includes('交付知识库')) {
            issues.push('❌ 使用了错误的"交付知识库"，应为"交底知识库"');
        }
        
        if (content.includes('安装知识库')) {
            issues.push('❌ 使用了错误的"安装知识库"，应为"布线知识库"');
        }
        
        if (content.includes('设计指导库')) {
            issues.push('❌ 包含了不需要的"设计指导库"');
        }
        
        if (!content.includes('水电交底知识库')) {
            issues.push('❌ 缺少"水电交底知识库"');
        }
        
        // 检查重命名的文件链接
        if (content.includes('api-tester.html')) {
            issues.push('❌ 使用了旧的"api-tester.html"，应为"api-tools.html"');
        }
        
        if (content.includes('user-permissions.html')) {
            issues.push('❌ 使用了旧的"user-permissions.html"，应为"internal-permissions.html"');
        }
        
        if (content.includes('permissions.html')) {
            issues.push('❌ 使用了旧的"permissions.html"，应为"customer-permissions.html"');
        }
        
        if (content.includes('settings.html')) {
            issues.push('❌ 使用了旧的"settings.html"，应为"system-settings.html"');
        }
        
        if (content.includes('requirements-analysis.html')) {
            issues.push('❌ 使用了旧的"requirements-analysis.html"，应为"requirements-analytics.html"');
        }
        
        // 检查是否有完整的33个菜单项
        const menuItemCount = (content.match(/class="nav-item"/g) || []).length;
        if (menuItemCount < 33) {
            issues.push(`❌ 菜单项数量不足：${menuItemCount}/33`);
        }
        
        // 检查是否有7个模块
        const sectionCount = (content.match(/nav-section-title/g) || []).length;
        if (sectionCount < 7) {
            issues.push(`❌ 模块数量不足：${sectionCount}/7`);
        }
        
        if (issues.length === 0) {
            console.log('✅ 侧边栏结构正确');
            return { fileName, status: 'ok', issues: [] };
        } else {
            console.log('❌ 发现问题：');
            issues.forEach(issue => console.log(`   ${issue}`));
            return { fileName, status: 'needs-update', issues };
        }
        
    } catch (error) {
        console.log(`❌ 读取文件失败: ${error.message}`);
        return { fileName: path.basename(filePath), status: 'error', issues: [error.message] };
    }
}

// 主检查函数
function checkAllPages() {
    console.log('🔍 开始检查所有页面的侧边栏一致性...\n');
    
    const pagesDir = path.join(__dirname, '../pages');
    const results = [];
    
    for (const pageFile of PAGES_TO_CHECK) {
        const filePath = path.join(pagesDir, pageFile);
        if (fs.existsSync(filePath)) {
            const result = checkPageSidebar(filePath);
            results.push(result);
        } else {
            console.log(`⚠️  文件不存在: ${pageFile}`);
            results.push({ fileName: pageFile, status: 'not-found', issues: ['文件不存在'] });
        }
    }
    
    // 生成总结报告
    console.log('\n📊 检查总结');
    console.log('=' .repeat(50));
    
    const okPages = results.filter(r => r.status === 'ok');
    const needsUpdatePages = results.filter(r => r.status === 'needs-update');
    const errorPages = results.filter(r => r.status === 'error' || r.status === 'not-found' || r.status === 'no-sidebar');
    
    console.log(`✅ 正确的页面: ${okPages.length}`);
    console.log(`❌ 需要更新的页面: ${needsUpdatePages.length}`);
    console.log(`⚠️  有问题的页面: ${errorPages.length}`);
    
    if (needsUpdatePages.length > 0) {
        console.log('\n🔧 需要更新的页面列表:');
        needsUpdatePages.forEach(page => {
            console.log(`   📄 ${page.fileName}`);
        });
    }
    
    return results;
}

// 如果直接运行此脚本
if (require.main === module) {
    checkAllPages();
}

module.exports = {
    checkAllPages,
    checkPageSidebar,
    STANDARD_MENU,
    PAGES_TO_CHECK
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目中心 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #f3f4f6;
            color: #374151;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 第一层：面包屑导航样式 */
        .top-nav {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }

        .breadcrumb-section {
            flex: 1;
        }

        .page-title-section {
            margin-top: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
            max-width: 600px;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 第二层：主菜单栏样式 */
        .main-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .main-menu-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .main-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .main-menu-tab {
            flex-shrink: 0;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
        }

        .main-menu-tab:hover {
            color: #1f2937;
            background: #f8fafc;
        }

        .main-menu-tab.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
            background: #f8fafc;
        }

        /* 项目生命周期展示区域 */
        .lifecycle-section {
            padding: 24px;
            background: #f8fafc;
        }

        .lifecycle-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .lifecycle-cards {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            scrollbar-width: none;
            padding-bottom: 8px;
        }

        .lifecycle-cards::-webkit-scrollbar {
            display: none;
        }

        .lifecycle-card {
            flex-shrink: 0;
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lifecycle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #9ca3af;
        }

        .lifecycle-card-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 20px;
            color: #374151;
        }

        .lifecycle-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .lifecycle-card-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 第三层：子菜单栏样式 */
        .sub-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
        }

        .sub-menu-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .sub-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .sub-menu-tab {
            flex-shrink: 0;
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .sub-menu-tab:hover {
            color: #1f2937;
            border-color: #1f2937;
            background: #f9fafb;
        }

        .sub-menu-tab.active {
            color: #ffffff;
            background: #1f2937;
            border-color: #1f2937;
        }

        /* 第四层：内容区域样式 */
        .content-area {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .content-section {
            display: none;
            padding: 24px;
        }

        .content-section.active {
            display: block;
        }

        /* 项目卡片网格 */
        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .project-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .project-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .project-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .project-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .project-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #d1fae5;
            color: #059669;
        }

        /* 图纸卡片样式 */
        .drawing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .drawing-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .drawing-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .drawing-preview {
            width: 100%;
            height: 120px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #9ca3af;
        }

        .drawing-info {
            padding: 12px;
        }

        .drawing-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .drawing-meta {
            font-size: 12px;
            color: #6b7280;
        }

        /* 人员卡片样式 */
        .member-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .member-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }

        .member-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .member-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6b7280;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .member-role {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .member-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            background: #dcfce7;
            color: #166534;
        }

        /* 搜索和筛选样式 */
        .search-filter-bar {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px 10px 40px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        .filter-select {
            padding: 10px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            min-width: 120px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .top-nav {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .page-title {
                font-size: 20px;
            }

            .page-description {
                font-size: 13px;
            }

            .nav-actions {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }

            .nav-actions .btn {
                width: 100%;
                justify-content: center;
            }

            .main-menu-tab {
                padding: 12px 16px;
                font-size: 13px;
            }

            .lifecycle-cards {
                gap: 12px;
            }

            .lifecycle-card {
                min-width: 160px;
                padding: 16px;
            }

            .project-grid {
                grid-template-columns: 1fr;
            }

            .drawing-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .member-grid {
                grid-template-columns: 1fr;
            }

            .search-filter-bar {
                flex-direction: column;
                gap: 12px;
            }

            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item active">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 第一层：面包屑导航 -->
            <div class="top-nav">
                <div class="breadcrumb-section">
                    <!-- 页面标题和描述 -->
                    <div class="page-title-section">
                        <h1 class="page-title">项目中心</h1>
                        <p class="page-description">
                            统一管理项目全生命周期，包含项目列表、项目空间、合同管理等功能
                        </p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="nav-actions">
                    <button class="btn btn-secondary" onclick="exportReport()" title="导出项目报告">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="btn btn-primary" onclick="createNewProject()" title="创建新项目">
                        <i class="fas fa-plus"></i>
                        新建项目
                    </button>
                </div>
            </div>

            <!-- 第二层：主菜单栏 -->
            <div class="main-menu-nav">
                <div class="main-menu-tabs">
                    <button class="main-menu-tab active" onclick="showMainMenu('projects')" data-menu="projects">
                        <i class="fas fa-list-ul"></i>
                        项目列表
                    </button>
                    <button class="main-menu-tab" onclick="showMainMenu('workspace')" data-menu="workspace">
                        <i class="fas fa-folder-open"></i>
                        项目空间
                    </button>
                    <button class="main-menu-tab" onclick="showMainMenu('contracts')" data-menu="contracts">
                        <i class="fas fa-file-contract"></i>
                        合同管理
                    </button>
                </div>

                <!-- 项目生命周期展示区域 -->
                <div class="lifecycle-section">
                    <h3 class="lifecycle-title">项目生命周期</h3>
                    <div class="lifecycle-cards">
                        <div class="lifecycle-card" onclick="selectLifecycleStage('analysis')">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="lifecycle-card-title">需求分析</div>
                            <div class="lifecycle-card-desc">客户需求调研、现场勘察、方案初步设计</div>
                        </div>
                        <div class="lifecycle-card" onclick="selectLifecycleStage('design')">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-drafting-compass"></i>
                            </div>
                            <div class="lifecycle-card-title">设计阶段</div>
                            <div class="lifecycle-card-desc">详细设计、图纸绘制、方案确认</div>
                        </div>
                        <div class="lifecycle-card" onclick="selectLifecycleStage('construction')">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-hard-hat"></i>
                            </div>
                            <div class="lifecycle-card-title">施工阶段</div>
                            <div class="lifecycle-card-desc">现场施工、设备安装、系统调试</div>
                        </div>
                        <div class="lifecycle-card" onclick="selectLifecycleStage('acceptance')">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="lifecycle-card-title">验收阶段</div>
                            <div class="lifecycle-card-desc">功能测试、客户验收、项目交付</div>
                        </div>
                        <div class="lifecycle-card" onclick="selectLifecycleStage('service')">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="lifecycle-card-title">售后服务</div>
                            <div class="lifecycle-card-desc">维护保养、技术支持、升级服务</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三层：子菜单栏 -->
            <div class="sub-menu-nav">
                <!-- 项目列表子菜单 -->
                <div class="sub-menu-tabs" id="projects-submenu">
                    <button class="sub-menu-tab active" onclick="showSubMenu('projects', 'all')" data-submenu="all">
                        全部项目
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('projects', 'active')" data-submenu="active">
                        进行中
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('projects', 'pending')" data-submenu="pending">
                        待开始
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('projects', 'history')" data-submenu="history">
                        历史项目
                    </button>
                </div>

                <!-- 项目空间子菜单 -->
                <div class="sub-menu-tabs" id="workspace-submenu" style="display: none;">
                    <button class="sub-menu-tab active" onclick="showSubMenu('workspace', 'drawings')" data-submenu="drawings">
                        项目图纸
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('workspace', 'members')" data-submenu="members">
                        项目人员
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('workspace', 'progress')" data-submenu="progress">
                        项目进度
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('workspace', 'quality')" data-submenu="quality">
                        质量检查
                    </button>
                </div>

                <!-- 合同管理子菜单 -->
                <div class="sub-menu-tabs" id="contracts-submenu" style="display: none;">
                    <button class="sub-menu-tab active" onclick="showSubMenu('contracts', 'pending')" data-submenu="pending">
                        待签署
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('contracts', 'executing')" data-submenu="executing">
                        执行中
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('contracts', 'completed')" data-submenu="completed">
                        已完成
                    </button>
                    <button class="sub-menu-tab" onclick="showSubMenu('contracts', 'terminated')" data-submenu="terminated">
                        已终止
                    </button>
                </div>
            </div>

            <!-- 第四层：内容区域 -->
            <div class="content-area">
                <!-- 项目列表内容 -->
                <div class="content-section active" id="projects-content">
                    <!-- 搜索和筛选栏 -->
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索项目名称、客户或项目编号...">
                        </div>
                        <select class="filter-select">
                            <option value="">所有状态</option>
                            <option value="active">进行中</option>
                            <option value="pending">待开始</option>
                            <option value="completed">已完成</option>
                        </select>
                        <select class="filter-select">
                            <option value="">所有类型</option>
                            <option value="residential">住宅项目</option>
                            <option value="commercial">商业项目</option>
                            <option value="office">办公项目</option>
                        </select>
                    </div>

                    <!-- 项目卡片网格 -->
                    <div class="project-grid" id="project-grid">
                        <!-- 项目卡片将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 项目空间内容 -->
                <div class="content-section" id="workspace-content">
                    <!-- 项目图纸内容 -->
                    <div class="workspace-section active" id="drawings-section">
                        <h3>项目图纸</h3>
                        <div class="drawing-grid" id="drawing-grid">
                            <!-- 图纸卡片将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 项目人员内容 -->
                    <div class="workspace-section" id="members-section" style="display: none;">
                        <h3>项目人员</h3>
                        <div class="member-grid" id="member-grid">
                            <!-- 人员卡片将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 项目进度内容 -->
                    <div class="workspace-section" id="progress-section" style="display: none;">
                        <h3>项目进度</h3>
                        <div id="progress-content">
                            <!-- 进度内容将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 质量检查内容 -->
                    <div class="workspace-section" id="quality-section" style="display: none;">
                        <h3>质量检查</h3>
                        <div id="quality-content">
                            <!-- 质量检查内容将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 合同管理内容 -->
                <div class="content-section" id="contracts-content">
                    <h3>合同管理</h3>
                    <div id="contracts-list">
                        <!-- 合同列表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>


        </main>
    </div>

    <script>
        // 全局状态管理
        const appState = {
            currentMainMenu: 'projects',
            currentSubMenu: 'all',
            projects: [
                {
                    id: 1,
                    name: '张先生别墅智能化改造',
                    customer: '张先生',
                    type: 'residential',
                    status: 'active',
                    progress: 65,
                    startDate: '2024-01-15',
                    endDate: '2024-03-15',
                    manager: '李工程师'
                },
                {
                    id: 2,
                    name: '李女士公寓智能家居',
                    customer: '李女士',
                    type: 'residential',
                    status: 'pending',
                    progress: 0,
                    startDate: '2024-02-01',
                    endDate: '2024-04-01',
                    manager: '王设计师'
                },
                {
                    id: 3,
                    name: '王总办公室智能化',
                    customer: '王总',
                    type: 'office',
                    status: 'completed',
                    progress: 100,
                    startDate: '2023-11-01',
                    endDate: '2024-01-01',
                    manager: '陈工程师'
                }
            ],
            drawings: [
                {
                    id: 1,
                    name: '平面布局图',
                    version: 'v2.1',
                    uploadTime: '2024-01-20 14:30',
                    type: 'layout'
                },
                {
                    id: 2,
                    name: '电路设计图',
                    version: 'v1.3',
                    uploadTime: '2024-01-18 09:15',
                    type: 'electrical'
                }
            ],
            members: [
                {
                    id: 1,
                    name: '李工程师',
                    role: '项目经理',
                    phone: '138****1234',
                    status: 'online'
                },
                {
                    id: 2,
                    name: '王设计师',
                    role: '设计师',
                    phone: '139****5678',
                    status: 'offline'
                }
            ]
        };

        // 主菜单切换
        function showMainMenu(menu) {
            // 更新主菜单激活状态
            document.querySelectorAll('.main-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-menu="${menu}"]`).classList.add('active');

            // 显示对应的子菜单
            document.querySelectorAll('.sub-menu-tabs').forEach(submenu => {
                submenu.style.display = 'none';
            });
            document.getElementById(`${menu}-submenu`).style.display = 'flex';

            // 显示对应的内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${menu}-content`).classList.add('active');

            // 更新全局状态
            appState.currentMainMenu = menu;

            // 根据菜单类型渲染内容
            switch(menu) {
                case 'projects':
                    renderProjectList();
                    break;
                case 'workspace':
                    renderWorkspaceContent();
                    break;
                case 'contracts':
                    renderContractsList();
                    break;
            }
        }

        // 子菜单切换
        function showSubMenu(mainMenu, subMenu) {
            // 更新子菜单激活状态
            const submenuContainer = document.getElementById(`${mainMenu}-submenu`);
            submenuContainer.querySelectorAll('.sub-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            submenuContainer.querySelector(`[data-submenu="${subMenu}"]`).classList.add('active');

            // 更新全局状态
            appState.currentSubMenu = subMenu;

            // 根据子菜单类型渲染内容
            if (mainMenu === 'projects') {
                renderProjectList();
            } else if (mainMenu === 'workspace') {
                renderWorkspaceContent();
            } else if (mainMenu === 'contracts') {
                renderContractsList();
            }
        }

        // 渲染项目列表
        function renderProjectList() {
            const container = document.getElementById('project-grid');
            if (!container) return;

            let filteredProjects = appState.projects;

            // 根据子菜单筛选
            if (appState.currentSubMenu !== 'all') {
                filteredProjects = appState.projects.filter(project => {
                    switch(appState.currentSubMenu) {
                        case 'active':
                            return project.status === 'active';
                        case 'pending':
                            return project.status === 'pending';
                        case 'history':
                            return project.status === 'completed';
                        default:
                            return true;
                    }
                });
            }

            const html = filteredProjects.map(project => `
                <div class="project-card" onclick="viewProject(${project.id})">
                    <div class="project-card-header">
                        <div>
                            <div class="project-title">${project.name}</div>
                            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                客户：${project.customer} | 负责人：${project.manager}
                            </div>
                        </div>
                        <span class="project-status status-${project.status}">
                            ${getStatusText(project.status)}
                        </span>
                    </div>
                    <div style="margin: 12px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="font-size: 12px; color: #6b7280;">进度</span>
                            <span style="font-size: 12px; font-weight: 500;">${project.progress}%</span>
                        </div>
                        <div style="background: #f3f4f6; height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="background: #1f2937; height: 100%; width: ${project.progress}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 11px; color: #9ca3af;">
                        <span>开始：${project.startDate}</span>
                        <span>预计完成：${project.endDate}</span>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html || '<div style="text-align: center; padding: 40px; color: #9ca3af;">暂无项目数据</div>';
        }

        // 渲染项目空间内容
        function renderWorkspaceContent() {
            // 显示对应的工作空间子内容
            document.querySelectorAll('.workspace-section').forEach(section => {
                section.style.display = 'none';
            });

            const currentSection = `${appState.currentSubMenu}-section`;
            const sectionElement = document.getElementById(currentSection);
            if (sectionElement) {
                sectionElement.style.display = 'block';

                // 根据子菜单类型渲染具体内容
                switch(appState.currentSubMenu) {
                    case 'drawings':
                        renderDrawings();
                        break;
                    case 'members':
                        renderMembers();
                        break;
                    case 'progress':
                        renderProgress();
                        break;
                    case 'quality':
                        renderQuality();
                        break;
                }
            }
        }

        // 渲染图纸
        function renderDrawings() {
            const container = document.getElementById('drawing-grid');
            if (!container) return;

            const html = appState.drawings.map(drawing => `
                <div class="drawing-card" onclick="previewDrawing(${drawing.id})">
                    <div class="drawing-preview">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="drawing-info">
                        <div class="drawing-name">${drawing.name}</div>
                        <div class="drawing-meta">${drawing.version} | ${drawing.uploadTime}</div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 渲染人员
        function renderMembers() {
            const container = document.getElementById('member-grid');
            if (!container) return;

            const html = appState.members.map(member => `
                <div class="member-card">
                    <div class="member-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="member-info">
                        <div class="member-name">${member.name}</div>
                        <div class="member-role">${member.role}</div>
                        <span class="member-status">${member.status === 'online' ? '在线' : '离线'}</span>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 渲染进度
        function renderProgress() {
            const container = document.getElementById('progress-content');
            if (!container) return;

            container.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                    <h4 style="margin-bottom: 16px;">整体项目进度</h4>
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>总体进度</span>
                            <span style="font-weight: 600;">65%</span>
                        </div>
                        <div style="background: #f3f4f6; height: 12px; border-radius: 6px; overflow: hidden;">
                            <div style="background: #10b981; height: 100%; width: 65%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                    <div style="color: #6b7280; font-size: 14px;">
                        <p>• 需求分析：已完成</p>
                        <p>• 设计阶段：已完成</p>
                        <p>• 施工阶段：进行中 (65%)</p>
                        <p>• 验收阶段：待开始</p>
                    </div>
                </div>
            `;
        }

        // 渲染质量检查
        function renderQuality() {
            const container = document.getElementById('quality-content');
            if (!container) return;

            container.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                    <h4 style="margin-bottom: 16px;">质量检查记录</h4>
                    <div style="color: #6b7280; text-align: center; padding: 40px;">
                        <i class="fas fa-clipboard-check" style="font-size: 48px; margin-bottom: 16px; color: #d1d5db;"></i>
                        <p>暂无质量检查记录</p>
                    </div>
                </div>
            `;
        }

        // 渲染合同列表
        function renderContractsList() {
            const container = document.getElementById('contracts-list');
            if (!container) return;

            container.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                    <div style="color: #6b7280; text-align: center; padding: 40px;">
                        <i class="fas fa-file-contract" style="font-size: 48px; margin-bottom: 16px; color: #d1d5db;"></i>
                        <p>暂无合同数据</p>
                    </div>
                </div>
            `;
        }

        // 工具函数
        function getStatusText(status) {
            const statusMap = {
                'active': '进行中',
                'pending': '待开始',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }

        // 生命周期阶段选择
        function selectLifecycleStage(stage) {
            console.log('选择生命周期阶段:', stage);
            showToast(`查看${stage}阶段详情`, 'info');
        }

        // 操作函数
        function exportReport() {
            showToast('开始导出项目报告...', 'info');
        }

        function createNewProject() {
            showToast('打开新建项目表单', 'info');
        }

        function viewProject(id) {
            showToast(`查看项目详情 (ID: ${id})`, 'info');
        }

        function previewDrawing(id) {
            showToast(`预览图纸 (ID: ${id})`, 'info');
        }

        // Toast提示
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1f2937'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面的导航高亮
            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPage) {
                    item.classList.add('active');
                }
            });

            // 初始化页面内容
            renderProjectList();

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>

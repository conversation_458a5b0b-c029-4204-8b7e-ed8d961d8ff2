<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新建角色 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 新建角色模态框专用样式 */
        .create-role-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .create-role-container {
            background: var(--glass-bg-white);
            backdrop-filter: var(--glass-blur-medium);
            -webkit-backdrop-filter: var(--glass-blur-medium);
            border: 1px solid var(--glass-border-light);
            box-shadow: var(--glass-shadow-medium);
            border-radius: var(--radius-lg);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-muted);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-muted);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--transition-base);
        }

        .modal-close:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .form-section {
            margin-bottom: var(--spacing-lg);
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-xs);
            border-bottom: 1px solid var(--border-light);
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .form-label.required::after {
            content: " *";
            color: var(--error-color);
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-medium);
            border-radius: var(--radius-sm);
            font-size: 12px;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-black);
            box-shadow: 0 0 0 2px rgba(26, 26, 26, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .permission-module {
            background: var(--bg-muted);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
        }

        .module-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-xs);
        }

        .permission-item:last-child {
            margin-bottom: 0;
        }

        .permission-checkbox {
            margin: 0;
        }

        .permission-label {
            font-size: 11px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .modal-footer {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--border-light);
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
            background: var(--bg-muted);
            border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid transparent;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-secondary);
            border-color: var(--border-medium);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .btn-primary {
            background: var(--primary-black);
            color: var(--text-inverse);
        }

        .btn-primary:hover {
            background: var(--gray-800);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .create-role-container {
                width: 95%;
                margin: var(--spacing-sm);
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--spacing-md);
            }

            .permission-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="create-role-modal" id="createRoleModal">
        <div class="create-role-container">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    新建角色
                </h2>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="modal-body">
                <form id="createRoleForm">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="section-title">基本信息</div>
                        
                        <div class="form-group">
                            <label class="form-label required" for="roleName">角色名称</label>
                            <input type="text" id="roleName" name="roleName" class="form-input" 
                                   placeholder="请输入角色名称" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="roleDescription">角色描述</label>
                            <textarea id="roleDescription" name="roleDescription" class="form-textarea" 
                                      placeholder="请输入角色描述（可选）"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="roleIcon">角色图标</label>
                            <select id="roleIcon" name="roleIcon" class="form-select">
                                <option value="fas fa-user">默认用户</option>
                                <option value="fas fa-user-crown">业主</option>
                                <option value="fas fa-user-tie">项目经理</option>
                                <option value="fas fa-home">智能家居设计师</option>
                                <option value="fas fa-drafting-compass">家装设计师</option>
                                <option value="fas fa-lightbulb">灯光设计师</option>
                                <option value="fas fa-hard-hat">施工队长</option>
                                <option value="fas fa-user-tie">销售人员</option>
                                <option value="fas fa-user-shield">超级管理员</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="roleTemplate">角色模板</label>
                            <select id="roleTemplate" name="roleTemplate" class="form-select" onchange="applyRoleTemplate()">
                                <option value="">选择角色模板（可选）</option>
                                <option value="PROJECT_OWNER">业主模板</option>
                                <option value="PROJECT_MANAGER">项目经理模板</option>
                                <option value="SMART_HOME_DESIGNER">智能家居设计师模板</option>
                                <option value="HOME_DESIGNER">家装设计师模板</option>
                                <option value="LIGHTING_DESIGNER">灯光设计师模板</option>
                                <option value="CONSTRUCTION_LEADER">施工队长模板</option>
                                <option value="SALES_REPRESENTATIVE">销售人员模板</option>
                            </select>
                            <small style="color: var(--text-muted); font-size: 11px; margin-top: 4px; display: block;">
                                选择模板将自动填充对应的权限设置，您可以在此基础上进行调整
                            </small>
                        </div>
                    </div>

                    <!-- 权限设置 -->
                    <div class="form-section">
                        <div class="section-title">权限设置</div>
                        <div class="permission-grid" id="permissionGrid">
                            <!-- 权限模块将通过JavaScript动态生成 -->
                            <div style="text-align: center; color: var(--text-muted); padding: 20px;">
                                <i class="fas fa-shield-alt" style="font-size: 24px; margin-bottom: 8px;"></i>
                                <p style="margin: 0; font-size: 12px;">请选择角色模板或手动配置权限</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 模态框底部 -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveRole()">
                    <i class="fas fa-save"></i>
                    保存角色
                </button>
            </div>
        </div>
    </div>

    <script>
        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('createRoleModal');
            modal.style.display = 'none';
            
            // 如果是在父窗口中打开的，通知父窗口
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({ action: 'closeCreateRoleModal' }, '*');
            }
        }

        // 保存角色
        function saveRole() {
            const form = document.getElementById('createRoleForm');
            const formData = new FormData(form);

            // 验证必填字段
            const roleName = formData.get('roleName');
            if (!roleName || roleName.trim() === '') {
                alert('请输入角色名称！');
                return;
            }

            // 收集权限数据
            const permissionCheckboxes = document.querySelectorAll('#permissionGrid input[name="permissions[]"]:checked');
            const permissions = {};

            permissionCheckboxes.forEach(checkbox => {
                const [module, operation] = checkbox.value.split(':');
                if (!permissions[module]) {
                    permissions[module] = [];
                }
                permissions[module].push(operation);
            });

            // 验证至少选择了一个权限
            if (Object.keys(permissions).length === 0) {
                alert('请至少选择一个权限！');
                return;
            }

            // 构建角色数据
            const roleData = {
                name: roleName.trim(),
                description: formData.get('roleDescription') || '',
                icon: formData.get('roleIcon') || 'fas fa-user',
                color: '#374151', // 默认颜色
                permissions: permissions,
                createdAt: new Date().toISOString()
            };

            console.log('新建角色数据：', roleData);

            // 这里可以添加实际的保存逻辑
            // 例如发送到后端API

            // 显示成功消息
            const saveBtn = event.target;
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-check"></i> 创建成功';
            saveBtn.style.background = '#10b981';
            saveBtn.disabled = true;

            setTimeout(() => {
                alert('角色创建成功！');
                closeModal();
            }, 1000);
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('createRoleModal');
            const container = document.querySelector('.create-role-container');
            
            if (event.target === modal && !container.contains(event.target)) {
                closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // 权限模块定义（基于真实业务需求）
        const PERMISSION_MODULES = {
            project: {
                name: '项目空间',
                icon: 'fas fa-home',
                color: '#2563eb',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'INVITE', 'MANAGE', 'COMMENT']
            },
            design: {
                name: '设计方案',
                icon: 'fas fa-drafting-compass',
                color: '#7c3aed',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT']
            },
            construction: {
                name: '施工管理',
                icon: 'fas fa-hard-hat',
                color: '#dc2626',
                operations: ['VIEW', 'CREATE', 'EDIT', 'UPDATE', 'INSPECT', 'APPROVE', 'COMMENT']
            },
            cost: {
                name: '预算成本',
                icon: 'fas fa-calculator',
                color: '#059669',
                operations: ['VIEW', 'EDIT', 'ANALYZE', 'APPROVE']
            },
            files: {
                name: '文件管理',
                icon: 'fas fa-folder',
                color: '#f59e0b',
                operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE', 'MANAGE']
            },
            comments: {
                name: '评论互动',
                icon: 'fas fa-comments',
                color: '#10b981',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'MANAGE']
            },
            sharing: {
                name: '项目空间分享',
                icon: 'fas fa-share-alt',
                color: '#14b8a6',
                operations: ['VIEW', 'CREATE', 'EDIT', 'INVITE', 'MANAGE', 'CONFIGURE']
            },
            marketing: {
                name: '营销分享',
                icon: 'fas fa-bullhorn',
                color: '#ec4899',
                operations: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS']
            },
            system: {
                name: '系统管理',
                icon: 'fas fa-cogs',
                color: '#374151',
                operations: ['VIEW', 'EDIT', 'CONFIGURE', 'MANAGE']
            }
        };

        // 角色模板定义（基于真实业务需求）
        const ROLE_TEMPLATES = {
            OWNER: {
                name: '业主/空间所有者',
                description: '拥有项目空间的完全控制权，可邀请他人并设置权限',
                permissions: ['project:*', 'design:*', 'construction:*', 'cost:*', 'sharing:*', 'files:*', 'comments:*', 'marketing:*']
            },
            FAMILY_MEMBER: {
                name: '家庭成员',
                description: '由业主邀请的家庭成员，权限由业主设定和控制',
                permissions: ['project:VIEW,COMMENT', 'design:VIEW,COMMENT', 'construction:VIEW,UPDATE,COMMENT', 'files:VIEW,UPLOAD', 'comments:CREATE,EDIT', 'marketing:VIEW,SHARE,EARN_REWARDS']
            },
            HOME_DESIGNER: {
                name: '家装设计师',
                description: '被邀请进项目空间参与设计工作',
                permissions: ['project:VIEW,COMMENT', 'design:VIEW,CREATE,EDIT,COMMENT', 'construction:VIEW,UPDATE,COMMENT', 'files:VIEW,UPLOAD', 'comments:CREATE,EDIT', 'marketing:VIEW,SHARE,EARN_REWARDS']
            },
            SMART_HOME_DESIGNER: {
                name: '智能家居设计师+灯光设计师',
                description: '不需要被邀请，可以在设计模块中看到用户所有的项目文件（1.0阶段智能家居设计师默认是灯光设计师）',
                permissions: ['project:VIEW,EDIT,COMMENT', 'design:*', 'construction:VIEW,UPDATE,COMMENT', 'cost:VIEW', 'files:*', 'comments:*', 'system:VIEW,EDIT,CONFIGURE', 'marketing:*']
            },
            CONSTRUCTOR: {
                name: '施工人员',
                description: '被邀请参与施工阶段，由用户决定可以看到的内容',
                permissions: ['project:VIEW,COMMENT', 'design:VIEW', 'construction:VIEW,UPDATE,COMMENT', 'files:VIEW,UPLOAD', 'comments:CREATE,EDIT', 'marketing:VIEW,SHARE,EARN_REWARDS']
            }
        };

        // 生成权限网格
        function generatePermissionGrid() {
            const grid = document.getElementById('permissionGrid');
            let gridHTML = '';

            Object.keys(PERMISSION_MODULES).forEach(moduleKey => {
                const module = PERMISSION_MODULES[moduleKey];
                gridHTML += `
                    <div class="permission-module" style="border: 1px solid var(--border-light); border-radius: 8px; margin-bottom: 16px;">
                        <div class="module-title" style="background: ${module.color}15; padding: 12px 16px; border-bottom: 1px solid var(--border-light); display: flex; align-items: center; gap: 8px;">
                            <i class="${module.icon}" style="color: ${module.color}; font-size: 14px;"></i>
                            <span style="font-weight: 600; color: var(--text-primary); font-size: 13px;">${module.name}</span>
                            <label style="margin-left: auto; display: flex; align-items: center; gap: 4px; font-size: 11px; color: var(--text-secondary);">
                                <input type="checkbox" onchange="toggleModulePermissions('${moduleKey}', this.checked)" style="margin: 0;">
                                启用
                            </label>
                        </div>
                        <div class="module-permissions" id="module_${moduleKey}" style="padding: 12px; display: none;">
                `;

                module.operations.forEach(operation => {
                    const operationNames = {
                        'VIEW': '查看',
                        'CREATE': '创建',
                        'EDIT': '编辑',
                        'DELETE': '删除',
                        'APPROVE': '审批',
                        'MANAGE': '管理',
                        'INVITE': '邀请',
                        'CONFIGURE': '配置',
                        'INSPECT': '巡检',
                        'ANALYZE': '分析',
                        'UPDATE': '更新',
                        'COMMENT': '评论',
                        'UPLOAD': '上传',
                        'DOWNLOAD': '下载',
                        'ACCESS': '访问',
                        'SHARE': '分享',
                        'EARN_REWARDS': '获得奖励',
                        'VIEW_STATS': '查看统计'
                    };

                    gridHTML += `
                        <div class="permission-item" style="display: inline-block; margin: 4px 8px 4px 0;">
                            <input type="checkbox" id="${moduleKey}_${operation}" name="permissions[]" value="${moduleKey}:${operation}" class="permission-checkbox" style="margin-right: 4px;">
                            <label for="${moduleKey}_${operation}" class="permission-label" style="font-size: 11px; color: var(--text-secondary);">${operationNames[operation] || operation}</label>
                        </div>
                    `;
                });

                gridHTML += `
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = gridHTML;
        }

        // 切换模块权限显示
        function toggleModulePermissions(moduleKey, enabled) {
            const moduleContainer = document.getElementById(`module_${moduleKey}`);
            const checkboxes = moduleContainer.querySelectorAll('input[type="checkbox"]');

            if (enabled) {
                moduleContainer.style.display = 'block';
                // 默认选中VIEW权限
                const viewCheckbox = moduleContainer.querySelector(`input[value="${moduleKey}:VIEW"]`);
                if (viewCheckbox) viewCheckbox.checked = true;
            } else {
                moduleContainer.style.display = 'none';
                // 取消所有权限
                checkboxes.forEach(cb => cb.checked = false);
            }
        }

        // 应用角色模板
        function applyRoleTemplate() {
            const templateSelect = document.getElementById('roleTemplate');
            const templateKey = templateSelect.value;

            if (!templateKey || !ROLE_TEMPLATES[templateKey]) {
                return;
            }

            const template = ROLE_TEMPLATES[templateKey];

            // 清除所有权限选择
            document.querySelectorAll('#permissionGrid input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
            });

            // 隐藏所有模块
            Object.keys(PERMISSION_MODULES).forEach(moduleKey => {
                const moduleContainer = document.getElementById(`module_${moduleKey}`);
                if (moduleContainer) {
                    moduleContainer.style.display = 'none';
                }
            });

            // 应用模板权限
            template.permissions.forEach(permission => {
                const [module, operations] = permission.split(':');
                const operationList = operations.split(',');

                // 启用模块
                const moduleToggle = document.querySelector(`input[onchange*="toggleModulePermissions('${module}'"]`);
                if (moduleToggle) {
                    moduleToggle.checked = true;
                    toggleModulePermissions(module, true);
                }

                // 选中对应权限
                operationList.forEach(op => {
                    const checkbox = document.querySelector(`input[value="${module}:${op.trim()}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            });

            // 自动填充角色名称（如果为空）
            const roleNameInput = document.getElementById('roleName');
            if (!roleNameInput.value.trim()) {
                roleNameInput.value = template.name;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('新建角色模态框初始化完成');
            generatePermissionGrid();
        });
    </script>
</body>
</html>

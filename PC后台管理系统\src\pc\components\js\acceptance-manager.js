/**
 * 验收管理模块
 * 支持验收图片上传和视频验收结果确认
 * 实现验收流程管理和报告生成
 * 版本: v2.0
 */

class AcceptanceManager {
    constructor() {
        this.acceptances = this.loadAcceptances();
        this.acceptanceResults = ['通过', '不通过', '需整改'];
        this.init();
    }

    /**
     * 初始化验收管理器
     */
    init() {
        console.log('✅ 验收管理器已初始化');
        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('acceptance-manager-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'acceptance-manager-styles';
        styles.textContent = `
            .acceptance-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .acceptance-modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
            }

            .acceptance-modal-header {
                padding: 20px 24px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .acceptance-modal-body {
                padding: 24px;
                flex: 1;
            }

            .acceptance-section {
                margin-bottom: 32px;
                padding-bottom: 24px;
                border-bottom: 1px solid #e5e7eb;
            }

            .acceptance-section:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }

            .acceptance-section-title {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .acceptance-upload-area {
                border: 2px dashed #d1d5db;
                border-radius: 8px;
                padding: 24px;
                text-align: center;
                background: #f9fafb;
                transition: all 0.3s ease;
                cursor: pointer;
                margin-bottom: 16px;
            }

            .acceptance-upload-area:hover,
            .acceptance-upload-area.dragover {
                border-color: #3b82f6;
                background: #eff6ff;
            }

            .acceptance-media-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 16px;
                margin-top: 16px;
            }

            .acceptance-media-item {
                position: relative;
                border-radius: 8px;
                overflow: hidden;
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                transition: all 0.3s ease;
            }

            .acceptance-media-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
            }

            .acceptance-media-preview {
                position: relative;
                width: 100%;
                height: 150px;
                overflow: hidden;
            }

            .acceptance-media-preview img,
            .acceptance-media-preview video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .acceptance-media-type {
                position: absolute;
                top: 8px;
                left: 8px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .acceptance-media-actions {
                position: absolute;
                top: 8px;
                right: 8px;
                display: flex;
                gap: 4px;
            }

            .acceptance-media-info {
                padding: 12px;
            }

            .acceptance-media-name {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .acceptance-media-time {
                font-size: 12px;
                color: #6b7280;
            }

            .acceptance-form-group {
                margin-bottom: 20px;
            }

            .acceptance-form-group label {
                display: block;
                font-weight: 600;
                color: #374151;
                font-size: 14px;
                margin-bottom: 8px;
            }

            .acceptance-form-group select,
            .acceptance-form-group textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                transition: border-color 0.2s ease;
            }

            .acceptance-form-group select:focus,
            .acceptance-form-group textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .acceptance-result-badge {
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                display: inline-block;
            }

            .acceptance-result-passed {
                background: #d1fae5;
                color: #065f46;
            }

            .acceptance-result-failed {
                background: #fecaca;
                color: #991b1b;
            }

            .acceptance-result-rework {
                background: #fef3c7;
                color: #92400e;
            }

            .acceptance-result-pending {
                background: #f3f4f6;
                color: #374151;
            }

            .acceptance-comparison {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-top: 16px;
            }

            .acceptance-comparison-section {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                overflow: hidden;
            }

            .acceptance-comparison-header {
                background: #f9fafb;
                padding: 12px 16px;
                font-weight: 600;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
            }

            .acceptance-comparison-content {
                padding: 16px;
            }

            .acceptance-report-section {
                background: #f9fafb;
                border-radius: 8px;
                padding: 20px;
                margin-top: 24px;
            }

            .acceptance-report-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .acceptance-report-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #e5e7eb;
            }

            .acceptance-report-item:last-child {
                border-bottom: none;
            }

            .acceptance-report-label {
                font-weight: 500;
                color: #6b7280;
            }

            .acceptance-report-value {
                color: #374151;
            }

            @keyframes fadeInScale {
                from { opacity: 0; transform: scale(0.95); }
                to { opacity: 1; transform: scale(1); }
            }

            .acceptance-modal {
                animation: fadeInScale 0.3s ease;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * 开始验收
     */
    startAcceptance(phase) {
        try {
            const acceptance = constructionManager.phaseData[phase].acceptance;
            const modal = this.createAcceptanceModal(phase, acceptance);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        } catch (error) {
            console.error('创建验收模态框失败:', error);
            constructionManager.showErrorMessage('验收功能初始化失败');
        }
    }

    /**
     * 创建验收模态框
     */
    createAcceptanceModal(phase, acceptance) {
        const modal = document.createElement('div');
        modal.className = 'acceptance-modal';
        
        const phaseNames = {
            briefing: '交底',
            wiring: '水电',
            installation: '安装',
            debugging: '调试',
            afterservice: '售后'
        };
        
        const phaseName = phaseNames[phase] || phase;
        
        modal.innerHTML = `
            <div class="acceptance-modal-content">
                <div class="acceptance-modal-header">
                    <h3><i class="fas fa-clipboard-check"></i> ${phaseName}阶段验收</h3>
                    <button class="btn-close" onclick="this.closest('.acceptance-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="acceptance-modal-body">
                    <!-- 验收前照片 -->
                    <div class="acceptance-section">
                        <div class="acceptance-section-title">
                            <i class="fas fa-camera"></i>
                            验收前照片
                        </div>
                        <div class="acceptance-upload-area" onclick="acceptanceManager.uploadAcceptanceMedia('${phase}', 'before', this)">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 32px; color: #9ca3af; margin-bottom: 12px;"></i>
                            <p style="margin: 0 0 8px 0; color: #374151;">点击上传验收前照片</p>
                            <p style="margin: 0; font-size: 14px; color: #6b7280;">支持 JPG, PNG, GIF 格式</p>
                        </div>
                        <div id="beforeImages" class="acceptance-media-grid">
                            ${this.renderAcceptanceMedia(acceptance.beforeImages || [])}
                        </div>
                    </div>
                    
                    <!-- 验收后照片 -->
                    <div class="acceptance-section">
                        <div class="acceptance-section-title">
                            <i class="fas fa-camera"></i>
                            验收后照片
                        </div>
                        <div class="acceptance-upload-area" onclick="acceptanceManager.uploadAcceptanceMedia('${phase}', 'after', this)">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 32px; color: #9ca3af; margin-bottom: 12px;"></i>
                            <p style="margin: 0 0 8px 0; color: #374151;">点击上传验收后照片</p>
                            <p style="margin: 0; font-size: 14px; color: #6b7280;">支持 JPG, PNG, GIF 格式</p>
                        </div>
                        <div id="afterImages" class="acceptance-media-grid">
                            ${this.renderAcceptanceMedia(acceptance.afterImages || [])}
                        </div>
                    </div>
                    
                    <!-- 验收视频 -->
                    <div class="acceptance-section">
                        <div class="acceptance-section-title">
                            <i class="fas fa-video"></i>
                            验收视频
                        </div>
                        <div class="acceptance-upload-area" onclick="acceptanceManager.uploadAcceptanceMedia('${phase}', 'video', this)">
                            <i class="fas fa-video" style="font-size: 32px; color: #9ca3af; margin-bottom: 12px;"></i>
                            <p style="margin: 0 0 8px 0; color: #374151;">点击上传验收视频</p>
                            <p style="margin: 0; font-size: 14px; color: #6b7280;">支持 MP4, WebM, MOV 格式</p>
                        </div>
                        <div id="acceptanceVideos" class="acceptance-media-grid">
                            ${this.renderAcceptanceMedia(acceptance.videos || [])}
                        </div>
                    </div>
                    
                    <!-- 验收结果 -->
                    <div class="acceptance-section">
                        <div class="acceptance-section-title">
                            <i class="fas fa-clipboard-check"></i>
                            验收结果
                        </div>
                        
                        <div class="acceptance-form-group">
                            <label for="acceptanceResult">验收结论</label>
                            <select id="acceptanceResult">
                                <option value="">请选择验收结果</option>
                                ${this.acceptanceResults.map(result => 
                                    `<option value="${result}" ${acceptance.result === result ? 'selected' : ''}>${result}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="acceptance-form-group">
                            <label for="acceptanceComments">验收意见</label>
                            <textarea id="acceptanceComments" rows="4" placeholder="请输入验收意见和建议">${acceptance.comments || ''}</textarea>
                        </div>
                        
                        <div class="acceptance-form-group">
                            <label for="acceptanceInspector">验收人员</label>
                            <input type="text" id="acceptanceInspector" placeholder="请输入验收人员姓名" 
                                   value="${acceptance.inspector || ''}" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                        </div>
                    </div>
                    
                    <!-- 对比展示 -->
                    ${(acceptance.beforeImages && acceptance.beforeImages.length > 0) || (acceptance.afterImages && acceptance.afterImages.length > 0) ? `
                        <div class="acceptance-section">
                            <div class="acceptance-section-title">
                                <i class="fas fa-exchange-alt"></i>
                                前后对比
                            </div>
                            <div class="acceptance-comparison">
                                <div class="acceptance-comparison-section">
                                    <div class="acceptance-comparison-header">验收前</div>
                                    <div class="acceptance-comparison-content">
                                        ${this.renderComparisonImages(acceptance.beforeImages || [])}
                                    </div>
                                </div>
                                <div class="acceptance-comparison-section">
                                    <div class="acceptance-comparison-header">验收后</div>
                                    <div class="acceptance-comparison-content">
                                        ${this.renderComparisonImages(acceptance.afterImages || [])}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                    
                    <!-- 验收报告预览 -->
                    <div class="acceptance-report-section">
                        <div class="acceptance-report-title">
                            <i class="fas fa-file-alt"></i>
                            验收报告预览
                        </div>
                        <div class="acceptance-report-item">
                            <span class="acceptance-report-label">验收阶段:</span>
                            <span class="acceptance-report-value">${phaseName}</span>
                        </div>
                        <div class="acceptance-report-item">
                            <span class="acceptance-report-label">验收时间:</span>
                            <span class="acceptance-report-value">${acceptance.timestamp ? constructionManager.formatDate(acceptance.timestamp) : '待确认'}</span>
                        </div>
                        <div class="acceptance-report-item">
                            <span class="acceptance-report-label">验收状态:</span>
                            <span class="acceptance-report-value">
                                <span class="acceptance-result-badge acceptance-result-${this.getResultClass(acceptance.result || 'pending')}">
                                    ${acceptance.result || '待验收'}
                                </span>
                            </span>
                        </div>
                        <div class="acceptance-report-item">
                            <span class="acceptance-report-label">验收图片:</span>
                            <span class="acceptance-report-value">${(acceptance.beforeImages?.length || 0) + (acceptance.afterImages?.length || 0)} 张</span>
                        </div>
                        <div class="acceptance-report-item">
                            <span class="acceptance-report-label">验收视频:</span>
                            <span class="acceptance-report-value">${acceptance.videos?.length || 0} 个</span>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer" style="padding: 20px 24px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="acceptanceManager.generateAcceptanceReport('${phase}', this)">
                        <i class="fas fa-file-export"></i> 生成报告
                    </button>
                    <button class="btn btn-secondary" onclick="this.closest('.acceptance-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="acceptanceManager.saveAcceptance('${phase}', this)">
                        <i class="fas fa-save"></i> 保存验收
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * 渲染验收媒体
     */
    renderAcceptanceMedia(mediaList = []) {
        if (mediaList.length === 0) {
            return '';
        }

        return mediaList.map(media => `
            <div class="acceptance-media-item" data-id="${media.id}">
                <div class="acceptance-media-preview">
                    ${media.type === 'video' ? 
                        `<video src="${media.url}" muted></video>
                         <div class="video-play-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); color: white; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-play"></i>
                         </div>` :
                        `<img src="${media.url}" alt="${media.name}">`
                    }
                    <div class="acceptance-media-type">${media.type === 'video' ? '视频' : '图片'}</div>
                    <div class="acceptance-media-actions">
                        <button class="btn btn-sm btn-primary" onclick="acceptanceManager.previewAcceptanceMedia(${media.id})" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="acceptanceManager.removeAcceptanceMedia(${media.id}, this)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="acceptance-media-info">
                    <div class="acceptance-media-name">${media.name}</div>
                    <div class="acceptance-media-time">${constructionManager.formatDate(media.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染对比图片
     */
    renderComparisonImages(images = []) {
        if (images.length === 0) {
            return '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无图片</div>';
        }

        return images.slice(0, 3).map(image => `
            <img src="${image.url}" alt="${image.name}" 
                 style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px; margin-bottom: 8px; cursor: pointer;"
                 onclick="acceptanceManager.previewAcceptanceMedia(${image.id})">
        `).join('');
    }

    /**
     * 上传验收媒体
     */
    uploadAcceptanceMedia(phase, type, button) {
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        
        if (type === 'video') {
            input.accept = 'video/*';
        } else {
            input.accept = 'image/*';
        }
        
        input.onchange = (e) => {
            const files = Array.from(e.target.files);
            this.handleAcceptanceMediaUpload(files, phase, type);
        };
        
        input.click();
    }

    /**
     * 处理验收媒体上传
     */
    async handleAcceptanceMediaUpload(files, phase, type) {
        try {
            const acceptance = constructionManager.phaseData[phase].acceptance;
            
            for (const file of files) {
                // 验证文件
                if (file.size > 100 * 1024 * 1024) {
                    constructionManager.showWarningMessage(`文件 ${file.name} 超过100MB限制`);
                    continue;
                }

                // 读取文件
                const fileData = await this.readFileAsDataURL(file);
                
                const media = {
                    id: Date.now() + Math.random(),
                    name: file.name,
                    type: file.type.startsWith('video/') ? 'video' : 'image',
                    url: fileData,
                    timestamp: Date.now()
                };

                // 根据类型添加到对应数组
                if (type === 'before') {
                    if (!acceptance.beforeImages) acceptance.beforeImages = [];
                    acceptance.beforeImages.push(media);
                } else if (type === 'after') {
                    if (!acceptance.afterImages) acceptance.afterImages = [];
                    acceptance.afterImages.push(media);
                } else if (type === 'video') {
                    if (!acceptance.videos) acceptance.videos = [];
                    acceptance.videos.push(media);
                }
            }

            // 更新显示
            this.updateAcceptanceMediaDisplay(phase, type);
            constructionManager.showSuccessMessage(`已上传 ${files.length} 个文件`);

        } catch (error) {
            console.error('上传验收媒体失败:', error);
            constructionManager.showErrorMessage('上传失败: ' + error.message);
        }
    }

    /**
     * 更新验收媒体显示
     */
    updateAcceptanceMediaDisplay(phase, type) {
        const acceptance = constructionManager.phaseData[phase].acceptance;
        
        if (type === 'before') {
            const container = document.getElementById('beforeImages');
            if (container) {
                container.innerHTML = this.renderAcceptanceMedia(acceptance.beforeImages || []);
            }
        } else if (type === 'after') {
            const container = document.getElementById('afterImages');
            if (container) {
                container.innerHTML = this.renderAcceptanceMedia(acceptance.afterImages || []);
            }
        } else if (type === 'video') {
            const container = document.getElementById('acceptanceVideos');
            if (container) {
                container.innerHTML = this.renderAcceptanceMedia(acceptance.videos || []);
            }
        }
    }

    /**
     * 预览验收媒体
     */
    previewAcceptanceMedia(mediaId) {
        // 查找媒体
        let media = null;
        Object.values(constructionManager.phaseData).forEach(phaseData => {
            const acceptance = phaseData.acceptance;
            if (acceptance.beforeImages) {
                const found = acceptance.beforeImages.find(m => m.id === mediaId);
                if (found) media = found;
            }
            if (acceptance.afterImages) {
                const found = acceptance.afterImages.find(m => m.id === mediaId);
                if (found) media = found;
            }
            if (acceptance.videos) {
                const found = acceptance.videos.find(m => m.id === mediaId);
                if (found) media = found;
            }
        });

        if (media) {
            recordManager.previewMedia({ ...media, type: media.type });
        }
    }

    /**
     * 移除验收媒体
     */
    removeAcceptanceMedia(mediaId, button) {
        if (confirm('确认删除此媒体文件吗？')) {
            const item = button.closest('.acceptance-media-item');
            item.remove();
            
            // 从数据中移除
            Object.values(constructionManager.phaseData).forEach(phaseData => {
                const acceptance = phaseData.acceptance;
                ['beforeImages', 'afterImages', 'videos'].forEach(key => {
                    if (acceptance[key]) {
                        const index = acceptance[key].findIndex(m => m.id === mediaId);
                        if (index !== -1) {
                            acceptance[key].splice(index, 1);
                        }
                    }
                });
            });
            
            constructionManager.savePhaseData();
            constructionManager.showSuccessMessage('媒体文件已删除');
        }
    }

    /**
     * 保存验收
     */
    saveAcceptance(phase, button) {
        try {
            const modal = button.closest('.acceptance-modal');
            const result = modal.querySelector('#acceptanceResult').value;
            const comments = modal.querySelector('#acceptanceComments').value.trim();
            const inspector = modal.querySelector('#acceptanceInspector').value.trim();

            if (!result) {
                constructionManager.showWarningMessage('请选择验收结果');
                return;
            }

            const acceptance = constructionManager.phaseData[phase].acceptance;
            acceptance.result = result;
            acceptance.comments = comments;
            acceptance.inspector = inspector;
            acceptance.timestamp = Date.now();

            constructionManager.savePhaseData();
            modal.remove();
            constructionManager.renderAcceptance(phase);
            constructionManager.showSuccessMessage('验收结果已保存');

        } catch (error) {
            console.error('保存验收失败:', error);
            constructionManager.showErrorMessage('保存失败: ' + error.message);
        }
    }

    /**
     * 生成验收报告
     */
    generateAcceptanceReport(phase, button) {
        try {
            const acceptance = constructionManager.phaseData[phase].acceptance;
            const phaseNames = {
                briefing: '交底',
                wiring: '水电',
                installation: '安装',
                debugging: '调试',
                afterservice: '售后'
            };
            
            const phaseName = phaseNames[phase] || phase;
            
            // 创建报告窗口
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${phaseName}阶段验收报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                        .header { text-align: center; margin-bottom: 40px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                        .section { margin-bottom: 30px; }
                        .section-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 15px; border-left: 4px solid #3b82f6; padding-left: 10px; }
                        .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .info-table td { padding: 8px 12px; border: 1px solid #ddd; }
                        .info-table td:first-child { background: #f9fafb; font-weight: bold; width: 150px; }
                        .media-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px; }
                        .media-item { text-align: center; }
                        .media-item img { max-width: 100%; height: 150px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; }
                        .media-caption { font-size: 12px; color: #666; margin-top: 5px; }
                        .result-badge { padding: 6px 12px; border-radius: 4px; font-weight: bold; }
                        .result-passed { background: #d1fae5; color: #065f46; }
                        .result-failed { background: #fecaca; color: #991b1b; }
                        .result-rework { background: #fef3c7; color: #92400e; }
                        @media print { body { margin: 20px; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${phaseName}阶段验收报告</h1>
                        <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
                    </div>
                    
                    <div class="section">
                        <div class="section-title">验收基本信息</div>
                        <table class="info-table">
                            <tr><td>验收阶段</td><td>${phaseName}</td></tr>
                            <tr><td>验收时间</td><td>${acceptance.timestamp ? constructionManager.formatDate(acceptance.timestamp) : '待确认'}</td></tr>
                            <tr><td>验收人员</td><td>${acceptance.inspector || '待填写'}</td></tr>
                            <tr><td>验收结果</td><td><span class="result-badge result-${this.getResultClass(acceptance.result || 'pending')}">${acceptance.result || '待验收'}</span></td></tr>
                        </table>
                    </div>
                    
                    <div class="section">
                        <div class="section-title">验收意见</div>
                        <p>${acceptance.comments || '暂无验收意见'}</p>
                    </div>
                    
                    ${acceptance.beforeImages && acceptance.beforeImages.length > 0 ? `
                        <div class="section">
                            <div class="section-title">验收前照片 (${acceptance.beforeImages.length}张)</div>
                            <div class="media-grid">
                                ${acceptance.beforeImages.map((img, index) => `
                                    <div class="media-item">
                                        <img src="${img.url}" alt="验收前照片${index + 1}">
                                        <div class="media-caption">${img.name}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                    
                    ${acceptance.afterImages && acceptance.afterImages.length > 0 ? `
                        <div class="section">
                            <div class="section-title">验收后照片 (${acceptance.afterImages.length}张)</div>
                            <div class="media-grid">
                                ${acceptance.afterImages.map((img, index) => `
                                    <div class="media-item">
                                        <img src="${img.url}" alt="验收后照片${index + 1}">
                                        <div class="media-caption">${img.name}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="section">
                        <div class="section-title">验收统计</div>
                        <table class="info-table">
                            <tr><td>验收前照片</td><td>${acceptance.beforeImages?.length || 0} 张</td></tr>
                            <tr><td>验收后照片</td><td>${acceptance.afterImages?.length || 0} 张</td></tr>
                            <tr><td>验收视频</td><td>${acceptance.videos?.length || 0} 个</td></tr>
                            <tr><td>总计媒体文件</td><td>${(acceptance.beforeImages?.length || 0) + (acceptance.afterImages?.length || 0) + (acceptance.videos?.length || 0)} 个</td></tr>
                        </table>
                    </div>
                    
                    <div style="margin-top: 50px; text-align: center; color: #666; font-size: 14px;">
                        <p>此报告由智能家居施工管理系统自动生成</p>
                    </div>
                </body>
                </html>
            `);
            
            reportWindow.document.close();
            
            // 等待内容加载后打印
            setTimeout(() => {
                reportWindow.print();
            }, 1000);

            constructionManager.showSuccessMessage('验收报告已生成');

        } catch (error) {
            console.error('生成验收报告失败:', error);
            constructionManager.showErrorMessage('报告生成失败');
        }
    }

    /**
     * 工具方法
     */
    getResultClass(result) {
        const classes = {
            '通过': 'passed',
            '不通过': 'failed',
            '需整改': 'rework',
            'pending': 'pending'
        };
        return classes[result] || 'pending';
    }

    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    /**
     * 数据持久化
     */
    loadAcceptances() {
        try {
            const data = localStorage.getItem('construction_acceptances');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('加载验收数据失败:', error);
            return [];
        }
    }

    saveAcceptances() {
        try {
            localStorage.setItem('construction_acceptances', JSON.stringify(this.acceptances));
        } catch (error) {
            console.error('保存验收数据失败:', error);
        }
    }
}

// 全局实例
let acceptanceManager;

// 延迟初始化，等待其他模块加载
if (typeof window !== 'undefined') {
    window.AcceptanceManager = AcceptanceManager;
}

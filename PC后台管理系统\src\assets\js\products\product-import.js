/**
 * 商品批量导入模块
 * 支持Excel/CSV文件解析、数据验证、批量保存
 * 兼容MedusaJS电商框架
 */

class ProductImporter {
    constructor(productManager) {
        this.productManager = productManager;
        this.apiBaseUrl = '/api/products/import';
        this.supportedFormats = ['.xlsx', '.xls', '.csv'];
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.batchSize = 100; // 每批处理100条记录
        
        // 字段映射配置
        this.fieldMapping = {
            'title': ['商品名称', '产品名称', 'name', 'title', '名称'],
            'sku': ['SKU', 'sku', '商品编码', '产品编码', '编码'],
            'description': ['描述', '商品描述', 'description', 'desc', '说明'],
            'category': ['分类', '商品分类', 'category', '类别'],
            'price': ['价格', '销售价格', 'price', '售价'],
            'cost_price': ['成本价', '成本价格', 'cost_price', 'cost'],
            'inventory_quantity': ['库存', '库存数量', 'inventory', 'stock', '数量'],
            'status': ['状态', '商品状态', 'status', '上架状态']
        };
    }

    /**
     * 显示导入模态框
     */
    showImportModal() {
        const modal = this.createImportModal();
        document.body.appendChild(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'translateY(0)';
        }, 10);
    }

    /**
     * 创建导入模态框
     */
    createImportModal() {
        const modal = document.createElement('div');
        modal.id = 'importModal';
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 700px;
                max-height: 90vh;
                overflow-y: auto;
                transform: translateY(-20px);
                transition: transform 0.3s ease;
            ">
                <div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0;">批量导入商品</h2>
                        <button class="close-modal" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div style="padding: 24px;">
                    <!-- 步骤指示器 -->
                    <div style="display: flex; justify-content: center; margin-bottom: 32px;">
                        <div style="display: flex; align-items: center;">
                            <div class="step-indicator active" data-step="1" style="
                                width: 32px; height: 32px; border-radius: 50%; background: #1f2937; color: white;
                                display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 600;
                            ">1</div>
                            <div style="width: 60px; height: 2px; background: #e5e7eb; margin: 0 8px;"></div>
                            <div class="step-indicator" data-step="2" style="
                                width: 32px; height: 32px; border-radius: 50%; background: #e5e7eb; color: #6b7280;
                                display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 600;
                            ">2</div>
                            <div style="width: 60px; height: 2px; background: #e5e7eb; margin: 0 8px;"></div>
                            <div class="step-indicator" data-step="3" style="
                                width: 32px; height: 32px; border-radius: 50%; background: #e5e7eb; color: #6b7280;
                                display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 600;
                            ">3</div>
                        </div>
                    </div>

                    <!-- 步骤1: 文件上传 -->
                    <div id="step1" class="import-step">
                        <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 16px;">第一步：选择导入文件</h3>
                        
                        <div style="margin-bottom: 24px;">
                            <div style="border: 2px dashed #d1d5db; border-radius: 8px; padding: 32px; text-align: center; background: #f9fafb;">
                                <input type="file" id="importFile" accept=".xlsx,.xls,.csv" style="display: none;">
                                <div id="fileDropZone" style="cursor: pointer;">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #6b7280; margin-bottom: 16px; display: block;"></i>
                                    <p style="font-size: 16px; color: #1f2937; margin-bottom: 8px;">点击选择文件或拖拽文件到此处</p>
                                    <p style="font-size: 14px; color: #6b7280; margin-bottom: 16px;">支持 Excel (.xlsx, .xls) 和 CSV 文件，最大 10MB</p>
                                    <button type="button" onclick="document.getElementById('importFile').click()" 
                                            style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                        选择文件
                                    </button>
                                </div>
                                <div id="fileInfo" style="display: none; margin-top: 16px;">
                                    <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                                        <i class="fas fa-file-excel" style="font-size: 24px; color: #10b981;"></i>
                                        <div>
                                            <div id="fileName" style="font-weight: 500; color: #1f2937;"></div>
                                            <div id="fileSize" style="font-size: 12px; color: #6b7280;"></div>
                                        </div>
                                        <button id="removeFile" style="background: none; border: none; color: #ef4444; cursor: pointer;">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                            <h4 style="font-size: 14px; font-weight: 600; color: #0369a1; margin-bottom: 8px;">
                                <i class="fas fa-info-circle"></i> 导入格式说明
                            </h4>
                            <ul style="font-size: 14px; color: #0369a1; margin: 0; padding-left: 20px;">
                                <li>第一行必须是表头，包含字段名称</li>
                                <li>支持的字段：商品名称、SKU、描述、分类、价格、成本价、库存、状态</li>
                                <li>价格字段请使用数字格式（如：99.99）</li>
                                <li>状态字段：草稿、已上架、已下架</li>
                            </ul>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <a href="/templates/product-import-template.xlsx" download 
                               style="color: #1f2937; text-decoration: none; font-size: 14px;">
                                <i class="fas fa-download"></i> 下载导入模板
                            </a>
                            <button id="nextStep1" disabled 
                                    style="padding: 8px 16px; background: #d1d5db; color: #9ca3af; border: none; border-radius: 6px; cursor: not-allowed;">
                                下一步 <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 步骤2: 数据预览和字段映射 -->
                    <div id="step2" class="import-step" style="display: none;">
                        <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 16px;">第二步：数据预览和字段映射</h3>
                        
                        <div id="dataPreview" style="margin-bottom: 24px;">
                            <!-- 数据预览表格将在这里动态生成 -->
                        </div>

                        <div style="display: flex; justify-content: space-between;">
                            <button id="prevStep2" 
                                    style="padding: 8px 16px; background: #f8fafc; color: #374151; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                <i class="fas fa-arrow-left"></i> 上一步
                            </button>
                            <button id="nextStep2" 
                                    style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                开始导入 <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 步骤3: 导入进度和结果 -->
                    <div id="step3" class="import-step" style="display: none;">
                        <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 16px;">第三步：导入进度</h3>
                        
                        <div id="importProgress" style="margin-bottom: 24px;">
                            <div style="background: #f3f4f6; border-radius: 8px; height: 8px; margin-bottom: 16px;">
                                <div id="progressBar" style="background: #10b981; height: 100%; border-radius: 8px; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                            <div style="display: flex; justify-content: space-between; font-size: 14px; color: #6b7280;">
                                <span id="progressText">准备导入...</span>
                                <span id="progressPercent">0%</span>
                            </div>
                        </div>

                        <div id="importResults" style="display: none;">
                            <!-- 导入结果将在这里显示 -->
                        </div>

                        <div style="display: flex; justify-content: flex-end;">
                            <button id="finishImport" style="display: none; padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                完成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindImportModalEvents(modal);

        return modal;
    }

    /**
     * 绑定导入模态框事件
     */
    bindImportModalEvents(modal) {
        // 关闭模态框
        const closeBtn = modal.querySelector('.close-modal');
        const closeModal = () => {
            modal.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };

        closeBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 文件选择
        const fileInput = modal.querySelector('#importFile');
        const fileDropZone = modal.querySelector('#fileDropZone');
        const fileInfo = modal.querySelector('#fileInfo');
        const nextStep1Btn = modal.querySelector('#nextStep1');

        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0], fileInfo, nextStep1Btn);
        });

        // 拖拽上传
        fileDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropZone.style.borderColor = '#1f2937';
            fileDropZone.style.background = '#f1f5f9';
        });

        fileDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            fileDropZone.style.borderColor = '#d1d5db';
            fileDropZone.style.background = '#f9fafb';
        });

        fileDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropZone.style.borderColor = '#d1d5db';
            fileDropZone.style.background = '#f9fafb';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0], fileInfo, nextStep1Btn);
            }
        });

        // 移除文件
        modal.querySelector('#removeFile').addEventListener('click', () => {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            fileDropZone.style.display = 'block';
            nextStep1Btn.disabled = true;
            nextStep1Btn.style.background = '#d1d5db';
            nextStep1Btn.style.color = '#9ca3af';
            nextStep1Btn.style.cursor = 'not-allowed';
        });

        // 步骤导航
        nextStep1Btn.addEventListener('click', () => {
            if (!nextStep1Btn.disabled) {
                this.goToStep(2, modal);
            }
        });

        modal.querySelector('#prevStep2').addEventListener('click', () => {
            this.goToStep(1, modal);
        });

        modal.querySelector('#nextStep2').addEventListener('click', () => {
            this.startImport(modal);
        });

        modal.querySelector('#finishImport').addEventListener('click', () => {
            closeModal();
            this.productManager.loadProducts(); // 刷新商品列表
        });
    }

    /**
     * 处理文件选择
     */
    handleFileSelect(file, fileInfo, nextBtn) {
        if (!file) return;

        // 验证文件类型
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.supportedFormats.includes(fileExtension)) {
            alert('不支持的文件格式，请选择 Excel 或 CSV 文件');
            return;
        }

        // 验证文件大小
        if (file.size > this.maxFileSize) {
            alert('文件大小超过限制，请选择小于 10MB 的文件');
            return;
        }

        // 显示文件信息
        const fileName = fileInfo.querySelector('#fileName');
        const fileSize = fileInfo.querySelector('#fileSize');
        
        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        fileInfo.style.display = 'block';
        fileInfo.previousElementSibling.style.display = 'none';

        // 启用下一步按钮
        nextBtn.disabled = false;
        nextBtn.style.background = '#1f2937';
        nextBtn.style.color = 'white';
        nextBtn.style.cursor = 'pointer';

        // 保存文件引用
        this.selectedFile = file;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 切换步骤
     */
    goToStep(step, modal) {
        // 隐藏所有步骤
        modal.querySelectorAll('.import-step').forEach(stepEl => {
            stepEl.style.display = 'none';
        });

        // 显示目标步骤
        modal.querySelector(`#step${step}`).style.display = 'block';

        // 更新步骤指示器
        modal.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNum = index + 1;
            if (stepNum <= step) {
                indicator.style.background = '#1f2937';
                indicator.style.color = 'white';
                indicator.classList.add('active');
            } else {
                indicator.style.background = '#e5e7eb';
                indicator.style.color = '#6b7280';
                indicator.classList.remove('active');
            }
        });

        // 如果是步骤2，解析文件并显示预览
        if (step === 2) {
            this.parseFileAndPreview(modal);
        }
    }

    /**
     * 解析文件并显示预览
     */
    async parseFileAndPreview(modal) {
        try {
            const previewContainer = modal.querySelector('#dataPreview');
            previewContainer.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 正在解析文件...</div>';

            const data = await this.parseFile(this.selectedFile);
            this.parsedData = data;

            if (data.length === 0) {
                previewContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #ef4444;">文件为空或格式不正确</div>';
                return;
            }

            // 显示数据预览和字段映射
            this.renderDataPreview(data, previewContainer);

        } catch (error) {
            console.error('文件解析失败:', error);
            const previewContainer = modal.querySelector('#dataPreview');
            previewContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #ef4444;">文件解析失败，请检查文件格式</div>';
        }
    }

    /**
     * 解析文件
     */
    async parseFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = e.target.result;
                    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                    if (fileExtension === '.csv') {
                        resolve(this.parseCSV(data));
                    } else if (['.xlsx', '.xls'].includes(fileExtension)) {
                        resolve(this.parseExcel(data));
                    } else {
                        reject(new Error('不支持的文件格式'));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('文件读取失败'));

            if (file.name.endsWith('.csv')) {
                reader.readAsText(file, 'UTF-8');
            } else {
                reader.readAsArrayBuffer(file);
            }
        });
    }

    /**
     * 解析CSV文件
     */
    parseCSV(csvText) {
        const lines = csvText.split('\n').filter(line => line.trim());
        if (lines.length < 2) return [];

        const headers = this.parseCSVLine(lines[0]);
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length === headers.length) {
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index];
                });
                data.push(row);
            }
        }

        return data;
    }

    /**
     * 解析CSV行
     */
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }

        result.push(current.trim());
        return result;
    }

    /**
     * 解析Excel文件 (简化版本，实际项目中建议使用 SheetJS)
     */
    parseExcel(arrayBuffer) {
        // 这里应该使用 SheetJS 库来解析 Excel 文件
        // 为了简化，这里返回模拟数据
        console.warn('Excel解析需要引入SheetJS库，当前返回模拟数据');

        return [
            {
                '商品名称': '智能开关',
                'SKU': 'SW001',
                '描述': '单火线智能开关',
                '分类': '智能开关',
                '价格': '99.99',
                '库存': '100',
                '状态': '已上架'
            },
            {
                '商品名称': '智能插座',
                'SKU': 'SO001',
                '描述': '16A智能插座',
                '分类': '智能插座',
                '价格': '79.99',
                '库存': '50',
                '状态': '草稿'
            }
        ];
    }

    /**
     * 渲染数据预览
     */
    renderDataPreview(data, container) {
        const headers = Object.keys(data[0]);
        const previewData = data.slice(0, 5); // 只显示前5行

        let html = `
            <div style="margin-bottom: 16px;">
                <h4 style="font-size: 14px; font-weight: 600; color: #1f2937; margin-bottom: 8px;">
                    数据预览 (共 ${data.length} 条记录，显示前 ${previewData.length} 条)
                </h4>
            </div>

            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden; margin-bottom: 24px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f9fafb;">
                            ${headers.map(header => `
                                <th style="padding: 8px 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                                    ${header}
                                </th>
                            `).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${previewData.map(row => `
                            <tr>
                                ${headers.map(header => `
                                    <td style="padding: 8px 12px; font-size: 12px; color: #1f2937; border-bottom: 1px solid #f3f4f6;">
                                        ${row[header] || ''}
                                    </td>
                                `).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div style="margin-bottom: 24px;">
                <h4 style="font-size: 14px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">字段映射</h4>
                <div style="display: grid; gap: 12px;">
                    ${headers.map(header => this.renderFieldMapping(header)).join('')}
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * 渲染字段映射
     */
    renderFieldMapping(originalField) {
        const mappedField = this.detectFieldMapping(originalField);
        const options = Object.keys(this.fieldMapping);

        return `
            <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                <div style="flex: 1;">
                    <label style="font-size: 12px; color: #6b7280;">原字段</label>
                    <div style="font-size: 14px; font-weight: 500; color: #1f2937;">${originalField}</div>
                </div>
                <div style="color: #6b7280;">→</div>
                <div style="flex: 1;">
                    <label style="font-size: 12px; color: #6b7280;">映射到</label>
                    <select class="field-mapping" data-original="${originalField}"
                            style="width: 100%; padding: 6px 8px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 14px;">
                        <option value="">不导入</option>
                        ${options.map(option => `
                            <option value="${option}" ${option === mappedField ? 'selected' : ''}>
                                ${this.getFieldDisplayName(option)}
                            </option>
                        `).join('')}
                    </select>
                </div>
            </div>
        `;
    }

    /**
     * 检测字段映射
     */
    detectFieldMapping(originalField) {
        const lowerField = originalField.toLowerCase();

        for (const [targetField, aliases] of Object.entries(this.fieldMapping)) {
            if (aliases.some(alias => lowerField.includes(alias.toLowerCase()))) {
                return targetField;
            }
        }

        return '';
    }

    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(field) {
        const displayNames = {
            'title': '商品名称',
            'sku': 'SKU',
            'description': '商品描述',
            'category': '商品分类',
            'price': '销售价格',
            'cost_price': '成本价格',
            'inventory_quantity': '库存数量',
            'status': '商品状态'
        };

        return displayNames[field] || field;
    }

    /**
     * 开始导入
     */
    async startImport(modal) {
        this.goToStep(3, modal);

        try {
            // 获取字段映射
            const fieldMappings = this.getFieldMappings(modal);

            // 转换数据
            const convertedData = this.convertData(this.parsedData, fieldMappings);

            // 验证数据
            const validationResult = this.validateData(convertedData);

            if (validationResult.errors.length > 0) {
                this.showValidationErrors(modal, validationResult.errors);
                return;
            }

            // 批量导入
            await this.batchImport(convertedData, modal);

        } catch (error) {
            console.error('导入失败:', error);
            this.showImportError(modal, error.message);
        }
    }

    /**
     * 获取字段映射
     */
    getFieldMappings(modal) {
        const mappings = {};
        const selects = modal.querySelectorAll('.field-mapping');

        selects.forEach(select => {
            const originalField = select.dataset.original;
            const mappedField = select.value;

            if (mappedField) {
                mappings[originalField] = mappedField;
            }
        });

        return mappings;
    }

    /**
     * 转换数据
     */
    convertData(rawData, fieldMappings) {
        return rawData.map(row => {
            const convertedRow = {};

            for (const [originalField, mappedField] of Object.entries(fieldMappings)) {
                const value = row[originalField];
                convertedRow[mappedField] = this.convertFieldValue(mappedField, value);
            }

            return convertedRow;
        });
    }

    /**
     * 转换字段值
     */
    convertFieldValue(field, value) {
        if (!value || value.toString().trim() === '') {
            return null;
        }

        switch (field) {
            case 'price':
            case 'cost_price':
                // 转换为分
                const price = parseFloat(value);
                return isNaN(price) ? null : Math.round(price * 100);

            case 'inventory_quantity':
                const quantity = parseInt(value);
                return isNaN(quantity) ? 0 : quantity;

            case 'status':
                // 状态映射
                const statusMap = {
                    '草稿': 'draft',
                    '已上架': 'published',
                    '上架': 'published',
                    '已下架': 'archived',
                    '下架': 'archived'
                };
                return statusMap[value] || 'draft';

            default:
                return value.toString().trim();
        }
    }

    /**
     * 验证数据
     */
    validateData(data) {
        const errors = [];
        const requiredFields = ['title', 'sku', 'price'];

        data.forEach((row, index) => {
            const rowNumber = index + 1;

            // 检查必填字段
            requiredFields.forEach(field => {
                if (!row[field]) {
                    errors.push(`第${rowNumber}行：${this.getFieldDisplayName(field)}不能为空`);
                }
            });

            // 检查价格
            if (row.price && row.price <= 0) {
                errors.push(`第${rowNumber}行：价格必须大于0`);
            }

            // 检查SKU唯一性
            const duplicateIndex = data.findIndex((otherRow, otherIndex) =>
                otherIndex !== index && otherRow.sku === row.sku
            );
            if (duplicateIndex !== -1) {
                errors.push(`第${rowNumber}行：SKU "${row.sku}" 重复`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 批量导入
     */
    async batchImport(data, modal) {
        const progressBar = modal.querySelector('#progressBar');
        const progressText = modal.querySelector('#progressText');
        const progressPercent = modal.querySelector('#progressPercent');
        const resultsContainer = modal.querySelector('#importResults');
        const finishBtn = modal.querySelector('#finishImport');

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        const totalBatches = Math.ceil(data.length / this.batchSize);

        for (let i = 0; i < totalBatches; i++) {
            const start = i * this.batchSize;
            const end = Math.min(start + this.batchSize, data.length);
            const batch = data.slice(start, end);

            try {
                progressText.textContent = `正在导入第 ${start + 1}-${end} 条记录...`;

                const response = await fetch(this.apiBaseUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': this.getCSRFToken()
                    },
                    body: JSON.stringify({ products: batch })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                successCount += result.success_count || 0;
                errorCount += result.error_count || 0;

                if (result.errors) {
                    errors.push(...result.errors);
                }

            } catch (error) {
                console.error(`批次 ${i + 1} 导入失败:`, error);
                errorCount += batch.length;
                errors.push(`批次 ${i + 1} 导入失败: ${error.message}`);
            }

            // 更新进度
            const progress = ((i + 1) / totalBatches) * 100;
            progressBar.style.width = `${progress}%`;
            progressPercent.textContent = `${Math.round(progress)}%`;
        }

        // 显示结果
        progressText.textContent = '导入完成';
        this.showImportResults(resultsContainer, successCount, errorCount, errors);
        finishBtn.style.display = 'block';
    }

    /**
     * 显示导入结果
     */
    showImportResults(container, successCount, errorCount, errors) {
        let html = `
            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 6px; padding: 20px;">
                <h4 style="font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 16px;">导入结果</h4>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                    <div style="text-align: center; padding: 16px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 6px;">
                        <div style="font-size: 24px; font-weight: 600; color: #16a34a;">${successCount}</div>
                        <div style="font-size: 14px; color: #15803d;">成功导入</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px;">
                        <div style="font-size: 24px; font-weight: 600; color: #dc2626;">${errorCount}</div>
                        <div style="font-size: 14px; color: #b91c1c;">导入失败</div>
                    </div>
                </div>
        `;

        if (errors.length > 0) {
            html += `
                <div style="margin-top: 16px;">
                    <h5 style="font-size: 14px; font-weight: 600; color: #dc2626; margin-bottom: 8px;">错误详情：</h5>
                    <div style="max-height: 200px; overflow-y: auto; background: #fef2f2; border: 1px solid #fecaca; border-radius: 4px; padding: 12px;">
                        ${errors.map(error => `<div style="font-size: 12px; color: #b91c1c; margin-bottom: 4px;">${error}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        html += '</div>';
        container.innerHTML = html;
        container.style.display = 'block';
    }

    /**
     * 获取CSRF Token
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductImporter;
}

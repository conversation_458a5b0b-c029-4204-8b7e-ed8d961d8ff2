/**
 * 商品表格组件
 * 支持虚拟滚动、排序、筛选等功能
 */

class ProductTable {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            itemHeight: 64,
            bufferSize: 5,
            enableVirtualScroll: true,
            enableSort: true,
            enableFilter: true,
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.sortConfig = { field: null, direction: 'asc' };
        this.filterConfig = {};
        
        // 事件回调
        this.onEdit = options.onEdit || (() => {});
        this.onView = options.onView || (() => {});
        this.onDelete = options.onDelete || (() => {});
        this.onSelect = options.onSelect || (() => {});
        
        // 虚拟滚动相关
        this.virtualScroll = null;
        this.selectedItems = new Set();
        
        this.init();
    }

    init() {
        this.createTableStructure();
        this.setupEventListeners();
        
        if (this.options.enableVirtualScroll) {
            this.initVirtualScroll();
        }
    }

    createTableStructure() {
        this.container.innerHTML = `
            <div class="product-table-container">
                <div class="table-header">
                    <div class="table-controls">
                        <div class="bulk-actions" style="display: none;">
                            <span class="selected-count">已选择 0 项</span>
                            <button class="btn btn-sm btn-secondary" data-action="bulk-edit">批量编辑</button>
                            <button class="btn btn-sm btn-danger" data-action="bulk-delete">批量删除</button>
                        </div>
                        <div class="table-actions">
                            <button class="btn btn-sm btn-secondary" data-action="refresh">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button class="btn btn-sm btn-secondary" data-action="export">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="table-wrapper">
                    <div class="table-header-row">
                        <div class="table-cell checkbox-cell">
                            <input type="checkbox" class="select-all-checkbox">
                        </div>
                        <div class="table-cell image-cell">图片</div>
                        <div class="table-cell info-cell sortable" data-field="name">
                            商品信息
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell price-cell sortable" data-field="price">
                            价格
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell stock-cell sortable" data-field="stock">
                            库存
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell sales-cell sortable" data-field="sales">
                            销量
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell status-cell sortable" data-field="status">
                            状态
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell sync-cell">MedusaJS</div>
                        <div class="table-cell date-cell sortable" data-field="created_at">
                            创建时间
                            <i class="fas fa-sort sort-icon"></i>
                        </div>
                        <div class="table-cell actions-cell">操作</div>
                    </div>
                    
                    <div class="table-body" id="tableBody">
                        <!-- 表格内容将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="table-footer">
                    <div class="pagination-info">
                        <span class="total-count">共 0 条记录</span>
                    </div>
                    <div class="pagination-controls">
                        <!-- 分页控件将在这里生成 -->
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const container = this.container;
        
        // 全选/取消全选
        const selectAllCheckbox = container.querySelector('.select-all-checkbox');
        selectAllCheckbox.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // 排序
        container.addEventListener('click', (e) => {
            const sortableCell = e.target.closest('.sortable');
            if (sortableCell) {
                const field = sortableCell.dataset.field;
                this.toggleSort(field);
            }
        });
        
        // 表格行事件委托
        const tableBody = container.querySelector('.table-body');
        tableBody.addEventListener('click', (e) => {
            this.handleTableClick(e);
        });
        
        // 批量操作
        container.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            if (action) {
                this.handleAction(action, e);
            }
        });
    }

    initVirtualScroll() {
        const tableBody = this.container.querySelector('.table-body');
        
        this.virtualScroll = new VirtualScroll(tableBody, {
            itemHeight: this.options.itemHeight,
            bufferSize: this.options.bufferSize,
            renderItem: (item, index) => this.renderTableRow(item, index)
        });
    }

    setData(data) {
        this.data = data;
        this.applyFiltersAndSort();
        this.updateDisplay();
    }

    applyFiltersAndSort() {
        let filtered = [...this.data];
        
        // 应用筛选
        Object.keys(this.filterConfig).forEach(field => {
            const value = this.filterConfig[field];
            if (value) {
                filtered = filtered.filter(item => {
                    if (field === 'search') {
                        return item.name.toLowerCase().includes(value.toLowerCase()) ||
                               item.sku.toLowerCase().includes(value.toLowerCase()) ||
                               (item.brand && item.brand.toLowerCase().includes(value.toLowerCase()));
                    }
                    return item[field] === value;
                });
            }
        });
        
        // 应用排序
        if (this.sortConfig.field) {
            filtered.sort((a, b) => {
                const field = this.sortConfig.field;
                const direction = this.sortConfig.direction === 'asc' ? 1 : -1;
                
                let aVal = a[field];
                let bVal = b[field];
                
                // 处理不同数据类型
                if (typeof aVal === 'string') {
                    return aVal.localeCompare(bVal) * direction;
                } else if (typeof aVal === 'number') {
                    return (aVal - bVal) * direction;
                } else if (aVal instanceof Date) {
                    return (aVal.getTime() - bVal.getTime()) * direction;
                }
                
                return 0;
            });
        }
        
        this.filteredData = filtered;
    }

    updateDisplay() {
        if (this.options.enableVirtualScroll && this.filteredData.length > 50) {
            this.virtualScroll.setData(this.filteredData);
        } else {
            this.renderTable();
        }
        
        this.updateFooter();
        this.updateSortIcons();
    }

    renderTable() {
        const tableBody = this.container.querySelector('.table-body');
        const fragment = document.createDocumentFragment();
        
        this.filteredData.forEach((item, index) => {
            const row = this.renderTableRow(item, index);
            fragment.appendChild(row);
        });
        
        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
    }

    renderTableRow(item, index) {
        const row = document.createElement('div');
        row.className = 'table-row';
        row.dataset.id = item.id;
        
        const isSelected = this.selectedItems.has(item.id);
        const statusClass = this.getStatusClass(item.status);
        const syncStatus = this.getSyncStatus(item);
        
        row.innerHTML = `
            <div class="table-cell checkbox-cell">
                <input type="checkbox" class="row-checkbox" ${isSelected ? 'checked' : ''}>
            </div>
            <div class="table-cell image-cell">
                ${item.image ? 
                    `<img src="${item.image}" alt="${item.name}" class="product-image">` :
                    `<i class="${this.getProductIcon(item.category)}" style="font-size: 24px; color: #6b7280;"></i>`
                }
            </div>
            <div class="table-cell info-cell">
                <div class="product-name">${item.name}</div>
                <div class="product-meta">
                    SKU: ${item.sku} ${item.brand ? `| 品牌: ${item.brand}` : ''}
                </div>
            </div>
            <div class="table-cell price-cell">
                <span class="price">¥${item.price}</span>
            </div>
            <div class="table-cell stock-cell">
                <span class="stock ${item.stock <= 10 ? 'low-stock' : ''}">${item.stock}</span>
            </div>
            <div class="table-cell sales-cell">
                <span class="sales">${item.sales || 0}</span>
            </div>
            <div class="table-cell status-cell">
                <span class="status ${statusClass}">${this.getStatusText(item.status)}</span>
            </div>
            <div class="table-cell sync-cell">
                <span class="sync-status ${syncStatus.class}">${syncStatus.text}</span>
            </div>
            <div class="table-cell date-cell">
                <span class="date">${this.formatDate(item.created_at)}</span>
            </div>
            <div class="table-cell actions-cell">
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${item.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" data-action="view" data-id="${item.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" data-action="delete" data-id="${item.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        return row;
    }

    handleTableClick(e) {
        const checkbox = e.target.closest('.row-checkbox');
        if (checkbox) {
            const row = checkbox.closest('.table-row');
            const id = parseInt(row.dataset.id);
            this.toggleRowSelection(id, checkbox.checked);
            return;
        }
        
        const actionBtn = e.target.closest('[data-action]');
        if (actionBtn) {
            const action = actionBtn.dataset.action;
            const id = parseInt(actionBtn.dataset.id);
            
            switch (action) {
                case 'edit':
                    this.onEdit(id);
                    break;
                case 'view':
                    this.onView(id);
                    break;
                case 'delete':
                    if (confirm('确定要删除这个商品吗？')) {
                        this.onDelete(id);
                    }
                    break;
            }
        }
    }

    toggleRowSelection(id, selected) {
        if (selected) {
            this.selectedItems.add(id);
        } else {
            this.selectedItems.delete(id);
        }
        
        this.updateBulkActions();
        this.onSelect(Array.from(this.selectedItems));
    }

    toggleSelectAll(selected) {
        this.selectedItems.clear();
        
        if (selected) {
            this.filteredData.forEach(item => {
                this.selectedItems.add(item.id);
            });
        }
        
        // 更新所有复选框状态
        const checkboxes = this.container.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selected;
        });
        
        this.updateBulkActions();
        this.onSelect(Array.from(this.selectedItems));
    }

    updateBulkActions() {
        const bulkActions = this.container.querySelector('.bulk-actions');
        const selectedCount = this.container.querySelector('.selected-count');
        
        if (this.selectedItems.size > 0) {
            bulkActions.style.display = 'flex';
            selectedCount.textContent = `已选择 ${this.selectedItems.size} 项`;
        } else {
            bulkActions.style.display = 'none';
        }
    }

    toggleSort(field) {
        if (this.sortConfig.field === field) {
            this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortConfig.field = field;
            this.sortConfig.direction = 'asc';
        }
        
        this.applyFiltersAndSort();
        this.updateDisplay();
    }

    updateSortIcons() {
        const sortIcons = this.container.querySelectorAll('.sort-icon');
        sortIcons.forEach(icon => {
            const cell = icon.closest('.sortable');
            const field = cell.dataset.field;
            
            icon.className = 'fas fa-sort sort-icon';
            
            if (this.sortConfig.field === field) {
                icon.className = this.sortConfig.direction === 'asc' 
                    ? 'fas fa-sort-up sort-icon active'
                    : 'fas fa-sort-down sort-icon active';
            }
        });
    }

    setFilter(field, value) {
        if (value) {
            this.filterConfig[field] = value;
        } else {
            delete this.filterConfig[field];
        }
        
        this.applyFiltersAndSort();
        this.updateDisplay();
    }

    clearFilters() {
        this.filterConfig = {};
        this.applyFiltersAndSort();
        this.updateDisplay();
    }

    updateFooter() {
        const totalCount = this.container.querySelector('.total-count');
        totalCount.textContent = `共 ${this.filteredData.length} 条记录`;
    }

    handleAction(action, e) {
        switch (action) {
            case 'refresh':
                this.refresh();
                break;
            case 'export':
                this.export();
                break;
            case 'bulk-edit':
                this.bulkEdit();
                break;
            case 'bulk-delete':
                this.bulkDelete();
                break;
        }
    }

    refresh() {
        // 触发数据刷新
        if (this.options.onRefresh) {
            this.options.onRefresh();
        }
    }

    export() {
        // 导出当前筛选的数据
        if (this.options.onExport) {
            this.options.onExport(this.filteredData);
        }
    }

    bulkEdit() {
        if (this.selectedItems.size === 0) return;
        
        if (this.options.onBulkEdit) {
            this.options.onBulkEdit(Array.from(this.selectedItems));
        }
    }

    bulkDelete() {
        if (this.selectedItems.size === 0) return;
        
        if (confirm(`确定要删除选中的 ${this.selectedItems.size} 个商品吗？`)) {
            if (this.options.onBulkDelete) {
                this.options.onBulkDelete(Array.from(this.selectedItems));
            }
        }
    }

    // 工具方法
    getStatusClass(status) {
        const statusMap = {
            'active': 'status-active',
            'inactive': 'status-inactive',
            'draft': 'status-draft',
            'out_of_stock': 'status-out-of-stock'
        };
        return statusMap[status] || 'status-unknown';
    }

    getStatusText(status) {
        const statusMap = {
            'active': '上架中',
            'inactive': '已下架',
            'draft': '草稿',
            'out_of_stock': '缺货'
        };
        return statusMap[status] || status;
    }

    getSyncStatus(item) {
        if (item.medusa_synced) {
            return { class: 'synced', text: '已同步' };
        } else {
            return { class: 'not-synced', text: '未同步' };
        }
    }

    getProductIcon(category) {
        const iconMap = {
            'switch': 'fas fa-toggle-on',
            'lighting': 'fas fa-lightbulb',
            'security': 'fas fa-shield-alt',
            'sensor': 'fas fa-satellite-dish',
            'environment': 'fas fa-thermometer-half'
        };
        return iconMap[category] || 'fas fa-cube';
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN') + '<br>' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    destroy() {
        if (this.virtualScroll) {
            this.virtualScroll.destroy();
        }
        this.container.innerHTML = '';
    }
}

// 导出组件
window.ProductTable = ProductTable;

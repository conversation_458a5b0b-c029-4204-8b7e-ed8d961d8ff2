<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 订单头部控制 */
        .orders-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        /* 筛选和搜索区域 */
        .filter-search-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 6px;
            background: #f8fafc;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #e5e7eb;
        }

        .tab.active {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .tab:hover:not(.active) {
            background: #f1f5f9;
            color: #1f2937;
        }

        /* 搜索框 */
        .search-box {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            width: 280px;
            outline: none;
            transition: border-color 0.2s;
        }

        .search-box input:focus {
            border-color: #1f2937;
        }

        .search-btn {
            padding: 8px 16px;
            background: #1f2937;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .search-btn:hover {
            background: #374151;
        }

        /* 视图切换按钮 */
        .view-toggle {
            display: flex;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: white;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn.active {
            background: #1f2937;
            color: white;
        }

        .view-btn:hover:not(.active) {
            background: #f9fafb;
        }

        /* 表格样式 */
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .orders-table th,
        .orders-table td {
            padding: 16px 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.6;
        }

        .orders-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .orders-table td {
            font-size: 14px;
            color: #1f2937;
        }

        .orders-table tbody tr:hover {
            background: #f9fafb;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-badge.status-processing {
            background: #dbeafe;
            color: #2563eb;
        }

        .status-badge.status-completed {
            background: #d1fae5;
            color: #059669;
        }

        /* 小按钮 */
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-sm.btn-primary {
            background: #1f2937;
            color: white;
        }

        .btn-sm.btn-primary:hover {
            background: #374151;
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item active">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">我的订单</h1>
                    <p class="breadcrumb-description">查看和管理您的订单信息</p>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">

                <div class="orders-container">
                <div class="orders-header">
                    <h2 class="orders-title">订单列表</h2>
                    <div class="view-toggle">
                        <button class="view-btn active" onclick="switchView('list')">列表视图</button>
                        <button class="view-btn" onclick="switchView('table')">表格视图</button>
                    </div>
                </div>

                <!-- 筛选和搜索区域 -->
                <div class="filter-search-bar">
                    <div class="filter-tabs">
                        <div class="tab active" onclick="filterOrders('all')">全部</div>
                        <div class="tab" onclick="filterOrders('pending')">待处理</div>
                        <div class="tab" onclick="filterOrders('processing')">进行中</div>
                        <div class="tab" onclick="filterOrders('completed')">已完成</div>
                    </div>
                    <div class="search-box">
                        <input type="text" placeholder="搜索订单号、客户姓名..." id="orderSearch" onkeyup="searchOrders()">
                        <button class="search-btn" onclick="searchOrders()">搜索</button>
                    </div>
                </div>

                <!-- 列表视图 -->
                <ul class="order-list" id="list-view">
                    <li class="order-item">
                        <div class="order-header">
                            <span class="order-id">订单 #ORD-2025-001</span>
                            <span class="order-status status-processing">进行中</span>
                        </div>
                        <div class="order-details">全屋智能设计方案 - 王先生家（120㎡三居室）</div>
                        <div class="order-meta">
                            <span>下单时间: 2025-01-20</span>
                            <span>金额: ¥999</span>
                        </div>
                    </li>
                    <li class="order-item">
                        <div class="order-header">
                            <span class="order-id">订单 #ORD-2025-002</span>
                            <span class="order-status status-pending">待处理</span>
                        </div>
                        <div class="order-details">户型优化设计 - 李女士家（90㎡两居室）</div>
                        <div class="order-meta">
                            <span>下单时间: 2025-01-25</span>
                            <span>金额: ¥299</span>
                        </div>
                    </li>
                    <li class="order-item">
                        <div class="order-header">
                            <span class="order-id">订单 #ORD-2025-003</span>
                            <span class="order-status status-completed">已完成</span>
                        </div>
                        <div class="order-details">智能照明设计 - 张先生家（150㎡复式）</div>
                        <div class="order-meta">
                            <span>下单时间: 2025-01-15</span>
                            <span>金额: ¥1,299</span>
                        </div>
                    </li>
                </ul>

                <!-- 表格视图 -->
                <div class="table-view" id="table-view" style="display: none;">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>订单详情</th>
                                <th>状态</th>
                                <th>下单时间</th>
                                <th>金额</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#ORD-2025-001</td>
                                <td>全屋智能设计方案 - 王先生家（120㎡三居室）</td>
                                <td><span class="status-badge status-processing">进行中</span></td>
                                <td>2025-01-20</td>
                                <td>¥999</td>
                                <td>
                                    <button class="btn-sm btn-primary">查看详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-2025-002</td>
                                <td>户型优化设计 - 李女士家（90㎡两居室）</td>
                                <td><span class="status-badge status-pending">待处理</span></td>
                                <td>2025-01-25</td>
                                <td>¥299</td>
                                <td>
                                    <button class="btn-sm btn-primary">查看详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-2025-003</td>
                                <td>智能照明设计 - 张先生家（150㎡复式）</td>
                                <td><span class="status-badge status-completed">已完成</span></td>
                                <td>2025-01-15</td>
                                <td>¥1,299</td>
                                <td>
                                    <button class="btn-sm btn-primary">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            </div>
        </main>
    </div>

    <script>
        // 视图切换功能
        function switchView(viewType) {
            const listView = document.getElementById('list-view');
            const tableView = document.getElementById('table-view');
            const viewBtns = document.querySelectorAll('.view-btn');

            // 移除所有按钮的active状态
            viewBtns.forEach(btn => btn.classList.remove('active'));

            if (viewType === 'list') {
                listView.style.display = 'block';
                tableView.style.display = 'none';
                document.querySelector('.view-btn[onclick="switchView(\'list\')"]').classList.add('active');
            } else if (viewType === 'table') {
                listView.style.display = 'none';
                tableView.style.display = 'block';
                document.querySelector('.view-btn[onclick="switchView(\'table\')"]').classList.add('active');
            }
        }

        // 筛选订单功能
        function filterOrders(status) {
            // 更新筛选标签状态
            const tabs = document.querySelectorAll('.filter-tabs .tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('筛选状态:', status);

            // 示例：根据状态筛选订单
            const listItems = document.querySelectorAll('.order-item');
            const tableRows = document.querySelectorAll('.orders-table tbody tr');

            listItems.forEach(item => {
                if (status === 'all') {
                    item.style.display = 'block';
                } else {
                    const statusElement = item.querySelector('.order-status');
                    if (statusElement) {
                        const itemStatus = statusElement.classList.contains('status-pending') ? 'pending' :
                                         statusElement.classList.contains('status-processing') ? 'processing' :
                                         statusElement.classList.contains('status-completed') ? 'completed' : '';
                        item.style.display = itemStatus === status ? 'block' : 'none';
                    }
                }
            });

            tableRows.forEach(row => {
                if (status === 'all') {
                    row.style.display = 'table-row';
                } else {
                    const statusElement = row.querySelector('.status-badge');
                    if (statusElement) {
                        const itemStatus = statusElement.classList.contains('status-pending') ? 'pending' :
                                         statusElement.classList.contains('status-processing') ? 'processing' :
                                         statusElement.classList.contains('status-completed') ? 'completed' : '';
                        row.style.display = itemStatus === status ? 'table-row' : 'none';
                    }
                }
            });
        }

        // 搜索订单功能
        function searchOrders() {
            const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
            const listItems = document.querySelectorAll('.order-item');
            const tableRows = document.querySelectorAll('.orders-table tbody tr');

            // 搜索列表视图
            listItems.forEach(item => {
                const orderText = item.textContent.toLowerCase();
                item.style.display = orderText.includes(searchTerm) ? 'block' : 'none';
            });

            // 搜索表格视图
            tableRows.forEach(row => {
                const rowText = row.textContent.toLowerCase();
                row.style.display = rowText.includes(searchTerm) ? 'table-row' : 'none';
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示列表视图
            switchView('list');
        });
    </script>
</body>
</html>

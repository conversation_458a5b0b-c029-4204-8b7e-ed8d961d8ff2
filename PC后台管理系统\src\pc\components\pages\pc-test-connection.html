<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC页面连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>PC页面API连接测试</h1>
    
    <div id="connectionStatus" class="status info">正在检测API连接...</div>
    
    <div id="testResults"></div>
    
    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8003';
        
        // 测试API连接
        async function testAPIConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            const resultsDiv = document.getElementById('testResults');
            
            try {
                console.log('开始测试API连接...');
                
                // 测试健康检查
                const healthResponse = await fetch(`${API_BASE_URL}/health`);
                const healthData = await healthResponse.json();
                
                if (healthResponse.ok && healthData.success) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✅ API连接成功！<br>服务器端口: ${healthData.data.port}<br>Redis状态: ${healthData.data.redis}`;
                    
                    // 测试需求列表接口
                    try {
                        const requirementsResponse = await fetch(`${API_BASE_URL}/api/v1/requirements`);
                        if (requirementsResponse.ok) {
                            const requirementsData = await requirementsResponse.json();
                            resultsDiv.innerHTML += `
                                <div class="status success">
                                    ✅ 需求列表接口正常<br>
                                    数据条数: ${requirementsData.data ? requirementsData.data.length : 0}
                                </div>
                            `;
                        } else {
                            throw new Error('需求列表接口响应异常');
                        }
                    } catch (error) {
                        resultsDiv.innerHTML += `
                            <div class="status error">
                                ❌ 需求列表接口测试失败: ${error.message}
                            </div>
                        `;
                    }
                    
                    // 测试统计接口
                    try {
                        const statsResponse = await fetch(`${API_BASE_URL}/api/v1/requirements/stats/summary`);
                        if (statsResponse.ok) {
                            resultsDiv.innerHTML += `
                                <div class="status success">
                                    ✅ 统计接口正常
                                </div>
                            `;
                        } else {
                            throw new Error('统计接口响应异常');
                        }
                    } catch (error) {
                        resultsDiv.innerHTML += `
                            <div class="status error">
                                ❌ 统计接口测试失败: ${error.message}
                            </div>
                        `;
                    }
                    
                } else {
                    throw new Error('健康检查失败');
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ API连接失败: ${error.message}<br>请检查服务器是否启动在端口8003`;
                console.error('API连接测试失败:', error);
            }
        }
        
        // 页面加载后自动测试
        document.addEventListener('DOMContentLoaded', testAPIConnection);
    </script>
</body>
</html>
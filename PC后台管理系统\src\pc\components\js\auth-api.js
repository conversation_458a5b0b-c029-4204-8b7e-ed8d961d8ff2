/**
 * 认证API客户端
 * 处理用户登录、登出、令牌管理等认证相关功能
 * 版本: v1.0
 * 创建时间: 2025-07-01
 */

class AuthAPI {
    constructor() {
        this.baseURL = 'http://localhost:8001/api/v1'  // 修正API路径，匹配后端
        this.timeout = 10000
        this.retryCount = 3
        this.retryDelay = 1000
        
        // 令牌存储键名
        this.ACCESS_TOKEN_KEY = 'smart_home_access_token'
        this.REFRESH_TOKEN_KEY = 'smart_home_refresh_token'
        this.USER_INFO_KEY = 'smart_home_user_info'
    }

    /**
     * 通用HTTP请求方法
     * @param {string} endpoint - API端点
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} API响应
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            timeout: this.timeout,
            ...options
        }

        // 添加认证头
        const token = this.getAccessToken()
        if (token && !endpoint.startsWith('/auth/login')) {
            config.headers['Authorization'] = `Bearer ${token}`
        }

        // 如果有body数据且不是FormData，转换为JSON
        if (config.body && !(config.body instanceof FormData)) {
            config.body = JSON.stringify(config.body)
        }

        let lastError = null
        
        // 重试机制
        for (let attempt = 1; attempt <= this.retryCount; attempt++) {
            try {
                console.log(`🌐 认证API请求 [尝试${attempt}/${this.retryCount}]:`, config.method, url)
                
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), this.timeout)
                
                const response = await fetch(url, {
                    ...config,
                    signal: controller.signal
                })
                
                clearTimeout(timeoutId)
                
                // 解析响应
                const data = await response.json()
                
                if (response.ok) {
                    console.log(`✅ 认证API响应成功:`, data)
                    return data
                } else {
                    console.error(`❌ 认证API响应错误 [${response.status}]:`, data)
                    
                    // 如果是401错误且不是登录请求，尝试刷新令牌
                    if (response.status === 401 && !endpoint.startsWith('/auth/login')) {
                        const refreshed = await this.refreshToken()
                        if (refreshed && attempt < this.retryCount) {
                            continue // 重试请求
                        }
                    }
                    
                    throw new Error(data.error?.message || `HTTP ${response.status}: ${response.statusText}`)
                }
                
            } catch (error) {
                lastError = error
                console.error(`❌ 认证API请求失败 [尝试${attempt}/${this.retryCount}]:`, error.message)
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < this.retryCount) {
                    await this.delay(this.retryDelay * attempt)
                }
            }
        }
        
        throw lastError
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms))
    }

    /**
     * 用户登录
     * @param {string} username - 用户名/邮箱/手机号
     * @param {string} password - 密码
     * @param {boolean} remember - 是否记住登录
     * @returns {Promise<Object>} 登录响应
     */
    async login(username, password, remember = false) {
        try {
            const response = await this.request('/auth/login/email', {  // 修正登录端点
                method: 'POST',
                body: {
                    email: username.trim(),  // 后端期望email字段
                    password: password
                }
            })

            if (response.success && response.data) {
                // 存储令牌和用户信息
                this.setAccessToken(response.data.access_token)
                this.setRefreshToken(response.data.refresh_token)
                this.setUserInfo(response.data.user)
                
                console.log('✅ 用户登录成功:', response.data.user.username)
                return response
            } else {
                throw new Error(response.error?.message || '登录失败')
            }
        } catch (error) {
            console.error('❌ 登录失败:', error)
            throw error
        }
    }

    /**
     * 用户登出
     * @returns {Promise<Object>} 登出响应
     */
    async logout() {
        try {
            // 注意：后端可能没有logout端点，这里先注释
            // const response = await this.request('/auth/logout', {
            //     method: 'POST'
            // })

            // 直接清除本地存储
            const response = { success: true, message: '登出成功' }

            // 清除本地存储的认证信息
            this.clearAuthData()
            
            console.log('✅ 用户登出成功')
            return response
        } catch (error) {
            console.error('❌ 登出失败:', error)
            // 即使API调用失败，也要清除本地数据
            this.clearAuthData()
            throw error
        }
    }

    /**
     * 刷新访问令牌
     * @returns {Promise<boolean>} 是否刷新成功
     */
    async refreshToken() {
        try {
            const refreshToken = this.getRefreshToken()
            if (!refreshToken) {
                console.warn('⚠️ 没有刷新令牌，无法刷新')
                return false
            }

            // 注意：后端可能没有refresh端点，这里先返回false
            // const response = await this.request('/auth/refresh', {
            //     method: 'POST',
            //     body: {
            //         refresh_token: refreshToken
            //     }
            // })

            // 暂时返回false，需要重新登录
            return false

            if (response.success && response.data) {
                this.setAccessToken(response.data.access_token)
                console.log('✅ 令牌刷新成功')
                return true
            } else {
                console.error('❌ 令牌刷新失败:', response.error?.message)
                this.clearAuthData()
                return false
            }
        } catch (error) {
            console.error('❌ 令牌刷新异常:', error)
            this.clearAuthData()
            return false
        }
    }

    /**
     * 获取当前用户信息
     * @returns {Promise<Object>} 用户信息
     */
    async getCurrentUser() {
        try {
            const response = await this.request('/auth/users/me')  // 修正获取用户信息端点
            
            if (response.success && response.data) {
                this.setUserInfo(response.data)
                return response.data
            } else {
                throw new Error(response.error?.message || '获取用户信息失败')
            }
        } catch (error) {
            console.error('❌ 获取用户信息失败:', error)
            throw error
        }
    }

    /**
     * 检查用户是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        const token = this.getAccessToken()
        const userInfo = this.getUserInfo()
        return !!(token && userInfo)
    }

    /**
     * 获取访问令牌
     * @returns {string|null} 访问令牌
     */
    getAccessToken() {
        return localStorage.getItem(this.ACCESS_TOKEN_KEY)
    }

    /**
     * 设置访问令牌
     * @param {string} token - 访问令牌
     */
    setAccessToken(token) {
        localStorage.setItem(this.ACCESS_TOKEN_KEY, token)
    }

    /**
     * 获取刷新令牌
     * @returns {string|null} 刷新令牌
     */
    getRefreshToken() {
        return localStorage.getItem(this.REFRESH_TOKEN_KEY)
    }

    /**
     * 设置刷新令牌
     * @param {string} token - 刷新令牌
     */
    setRefreshToken(token) {
        localStorage.setItem(this.REFRESH_TOKEN_KEY, token)
    }

    /**
     * 获取用户信息
     * @returns {Object|null} 用户信息
     */
    getUserInfo() {
        const userInfo = localStorage.getItem(this.USER_INFO_KEY)
        return userInfo ? JSON.parse(userInfo) : null
    }

    /**
     * 设置用户信息
     * @param {Object} userInfo - 用户信息
     */
    setUserInfo(userInfo) {
        localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo))
    }

    /**
     * 清除所有认证数据
     */
    clearAuthData() {
        localStorage.removeItem(this.ACCESS_TOKEN_KEY)
        localStorage.removeItem(this.REFRESH_TOKEN_KEY)
        localStorage.removeItem(this.USER_INFO_KEY)
        console.log('🗑️ 认证数据已清除')
    }

    /**
     * 格式化错误信息
     * @param {Error} error - 错误对象
     * @returns {string} 格式化的错误信息
     */
    formatError(error) {
        if (error.name === 'AbortError') {
            return '请求超时，请检查网络连接'
        }
        if (error.message.includes('Failed to fetch')) {
            return 'API服务器连接失败，请确保服务器正在运行'
        }
        return error.message || '未知错误'
    }

    /**
     * 检查API服务器健康状态
     * @returns {Promise<Object>} 健康状态响应
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.baseURL.replace('/api', '')}/health`)
            return await response.json()
        } catch (error) {
            console.error('❌ 健康检查失败:', error)
            throw error
        }
    }
}

// 创建全局认证API实例
window.authAPI = new AuthAPI()

// 导出认证API类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthAPI
}

console.log('✅ 认证API客户端已初始化')

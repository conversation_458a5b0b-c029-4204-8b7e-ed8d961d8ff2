/**
 * 智能家居管理系统页面全面分析脚本
 * 分析页面命名一致性、功能重复性、分类整理等
 */

const fs = require('fs');
const path = require('path');

// 页面分析结果结构
const analysisResults = {
    allPages: [],
    namingIssues: [],
    functionalDuplicates: [],
    categoryAnalysis: {},
    recommendations: {
        rename: [],
        merge: [],
        keep: [],
        remove: []
    }
};

// 功能模块分类定义
const FUNCTIONAL_CATEGORIES = {
    'design': {
        name: '设计模块',
        keywords: ['design'],
        expectedPages: ['design-center', 'design-cases', 'design-products', 'design-knowledge']
    },
    'requirements': {
        name: '需求管理',
        keywords: ['requirements', 'requirement'],
        expectedPages: ['requirements-management', 'requirements-analytics']
    },
    'construction': {
        name: '施工管理',
        keywords: ['construction'],
        expectedPages: ['construction-management', 'construction-guide']
    },
    'user': {
        name: '用户管理',
        keywords: ['user', 'customer'],
        expectedPages: ['user-management', 'user-profile', 'customer-management']
    },
    'product': {
        name: '商品管理',
        keywords: ['product'],
        expectedPages: ['products', 'product-knowledge', 'product-materials']
    },
    'order': {
        name: '订单管理',
        keywords: ['order'],
        expectedPages: ['orders', 'my-orders']
    },
    'analytics': {
        name: '数据分析',
        keywords: ['analytics', 'analysis'],
        expectedPages: ['requirements-analytics', 'project-analytics', 'order-analytics', 'customer-analytics']
    },
    'knowledge': {
        name: '知识库',
        keywords: ['knowledge'],
        expectedPages: ['design-knowledge', 'delivery-knowledge', 'electrical-delivery-knowledge', 'market-knowledge', 'installation-knowledge', 'product-knowledge']
    },
    'permissions': {
        name: '权限管理',
        keywords: ['permissions', 'permission'],
        expectedPages: ['internal-permissions', 'customer-permissions']
    },
    'system': {
        name: '系统管理',
        keywords: ['system', 'admin', 'api', 'settings'],
        expectedPages: ['system-settings', 'admin-dashboard', 'api-tools', 'data-management']
    },
    'auth': {
        name: '认证相关',
        keywords: ['login', 'logout', 'register'],
        expectedPages: ['logout', 'register']
    },
    'personal': {
        name: '个人中心',
        keywords: ['my-', 'profile'],
        expectedPages: ['my-todos', 'my-orders', 'user-profile']
    }
};

// 分析单个页面
function analyzePage(filePath) {
    const fileName = path.basename(filePath, '.html');
    const fullFileName = path.basename(filePath);
    
    const pageInfo = {
        fileName: fullFileName,
        baseName: fileName,
        filePath,
        exists: false,
        title: '',
        description: '',
        hasContent: false,
        sidebarType: 'none',
        functionalCategory: 'unknown',
        namingIssues: [],
        potentialDuplicates: []
    };
    
    try {
        if (!fs.existsSync(filePath)) {
            pageInfo.namingIssues.push('文件不存在');
            return pageInfo;
        }
        
        pageInfo.exists = true;
        const content = fs.readFileSync(filePath, 'utf8');
        pageInfo.hasContent = content.length > 1000; // 简单判断是否有实际内容
        
        // 提取页面标题
        const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
            pageInfo.title = titleMatch[1].trim();
        }
        
        // 提取页面描述或主标题
        const h1Match = content.match(/<h1[^>]*class="page-title"[^>]*>([^<]+)<\/h1>/i);
        if (h1Match) {
            pageInfo.description = h1Match[1].trim();
        }
        
        // 分析侧边栏类型
        if (content.includes('nav-menu')) {
            const navItems = content.match(/class="nav-item"/g) || [];
            const sections = content.match(/nav-section-title/g) || [];
            
            if (sections.length >= 7 && navItems.length >= 30) {
                pageInfo.sidebarType = 'standard';
            } else if (sections.length >= 3 && navItems.length >= 8) {
                pageInfo.sidebarType = 'simplified';
            } else {
                pageInfo.sidebarType = 'minimal';
            }
        }
        
        // 分析功能分类
        pageInfo.functionalCategory = categorizePage(fileName);
        
        // 分析命名问题
        pageInfo.namingIssues = analyzeNamingIssues(fileName, pageInfo.title);
        
    } catch (error) {
        pageInfo.namingIssues.push(`读取错误: ${error.message}`);
    }
    
    return pageInfo;
}

// 页面功能分类
function categorizePage(fileName) {
    for (const [category, config] of Object.entries(FUNCTIONAL_CATEGORIES)) {
        for (const keyword of config.keywords) {
            if (fileName.includes(keyword)) {
                return category;
            }
        }
    }
    return 'unknown';
}

// 分析命名问题
function analyzeNamingIssues(fileName, title) {
    const issues = [];
    
    // 检查命名一致性
    if (fileName.includes('_')) {
        issues.push('使用下划线而非短横线');
    }
    
    if (fileName.includes('camelCase') || /[A-Z]/.test(fileName)) {
        issues.push('使用大写字母，应使用kebab-case');
    }
    
    // 检查版本后缀
    if (fileName.includes('-new') || fileName.includes('-old') || fileName.includes('-test')) {
        issues.push('包含版本后缀，可能是临时文件');
    }
    
    // 检查功能描述的准确性
    if (fileName.includes('table') && !title.toLowerCase().includes('table')) {
        issues.push('文件名包含table但标题中无相关描述');
    }
    
    if (fileName.includes('fixed') || fileName.includes('enhanced')) {
        issues.push('包含修复/增强标识，可能是临时版本');
    }
    
    return issues;
}

// 识别功能重复的页面
function identifyDuplicates(pages) {
    const duplicateGroups = [];
    const processed = new Set();
    
    for (const page of pages) {
        if (processed.has(page.fileName)) continue;
        
        const similarPages = pages.filter(p => 
            p !== page && 
            !processed.has(p.fileName) &&
            areFunctionallySimilar(page, p)
        );
        
        if (similarPages.length > 0) {
            const group = [page, ...similarPages];
            duplicateGroups.push({
                category: page.functionalCategory,
                pages: group,
                reason: getDuplicationReason(group)
            });
            
            group.forEach(p => processed.add(p.fileName));
        }
    }
    
    return duplicateGroups;
}

// 判断页面功能是否相似
function areFunctionallySimilar(page1, page2) {
    // 同一功能分类
    if (page1.functionalCategory === page2.functionalCategory) {
        // 检查基础名称相似性
        const base1 = page1.baseName.replace(/-?(new|old|test|fixed|enhanced|table)$/, '');
        const base2 = page2.baseName.replace(/-?(new|old|test|fixed|enhanced|table)$/, '');
        
        if (base1 === base2) return true;
        
        // 检查核心关键词
        const keywords1 = base1.split('-');
        const keywords2 = base2.split('-');
        const commonKeywords = keywords1.filter(k => keywords2.includes(k));
        
        return commonKeywords.length >= 2;
    }
    
    return false;
}

// 获取重复原因
function getDuplicationReason(pages) {
    const reasons = [];
    
    const hasVersionSuffix = pages.some(p => 
        p.baseName.includes('-new') || 
        p.baseName.includes('-old') || 
        p.baseName.includes('-test')
    );
    
    if (hasVersionSuffix) {
        reasons.push('包含版本后缀的多个版本');
    }
    
    const hasTableVariant = pages.some(p => p.baseName.includes('-table'));
    if (hasTableVariant) {
        reasons.push('包含表格视图变体');
    }
    
    const hasEnhanced = pages.some(p => 
        p.baseName.includes('-enhanced') || 
        p.baseName.includes('-fixed')
    );
    if (hasEnhanced) {
        reasons.push('包含增强/修复版本');
    }
    
    return reasons.join(', ') || '功能相似';
}

// 生成重命名建议
function generateRenameSuggestions(pages) {
    const suggestions = [];
    
    for (const page of pages) {
        if (page.namingIssues.length > 0) {
            const newName = suggestNewName(page);
            if (newName !== page.fileName) {
                suggestions.push({
                    current: page.fileName,
                    suggested: newName,
                    reason: page.namingIssues.join(', '),
                    priority: calculateRenamePriority(page)
                });
            }
        }
    }
    
    return suggestions.sort((a, b) => b.priority - a.priority);
}

// 建议新的文件名
function suggestNewName(page) {
    let newName = page.baseName;
    
    // 移除版本后缀
    newName = newName.replace(/-?(new|old|test|fixed|enhanced)$/, '');
    
    // 标准化命名
    newName = newName.toLowerCase();
    newName = newName.replace(/_/g, '-');
    
    // 根据功能分类优化命名
    const category = FUNCTIONAL_CATEGORIES[page.functionalCategory];
    if (category && category.expectedPages) {
        const expectedPage = category.expectedPages.find(expected => 
            newName.includes(expected.split('-')[0])
        );
        if (expectedPage) {
            newName = expectedPage;
        }
    }
    
    return newName + '.html';
}

// 计算重命名优先级
function calculateRenamePriority(page) {
    let priority = 0;
    
    // 版本后缀问题优先级高
    if (page.baseName.includes('-new') || page.baseName.includes('-test')) {
        priority += 10;
    }
    
    // 命名不规范问题
    if (page.namingIssues.includes('使用下划线而非短横线')) {
        priority += 8;
    }
    
    // 有实际内容的页面优先级更高
    if (page.hasContent) {
        priority += 5;
    }
    
    // 标准侧边栏的页面优先级更高
    if (page.sidebarType === 'standard') {
        priority += 3;
    }
    
    return priority;
}

// 主分析函数
function performComprehensiveAnalysis() {
    console.log('🔍 开始智能家居管理系统页面全面分析...\n');
    
    const pagesDir = path.join(__dirname, '../pages');
    const files = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.html'))
        .sort();
    
    console.log(`📊 发现 ${files.length} 个HTML页面文件\n`);
    
    // 分析所有页面
    const pages = files.map(file => {
        const filePath = path.join(pagesDir, file);
        return analyzePage(filePath);
    });
    
    analysisResults.allPages = pages;
    
    // 识别功能重复
    analysisResults.functionalDuplicates = identifyDuplicates(pages);
    
    // 生成重命名建议
    analysisResults.recommendations.rename = generateRenameSuggestions(pages);
    
    // 按功能分类
    for (const [category, config] of Object.entries(FUNCTIONAL_CATEGORIES)) {
        const categoryPages = pages.filter(p => p.functionalCategory === category);
        analysisResults.categoryAnalysis[category] = {
            name: config.name,
            pages: categoryPages,
            expectedCount: config.expectedPages.length,
            actualCount: categoryPages.length,
            issues: categoryPages.filter(p => p.namingIssues.length > 0)
        };
    }
    
    return analysisResults;
}

// 生成分析报告
function generateAnalysisReport(results) {
    console.log('📊 智能家居管理系统页面全面分析报告');
    console.log('='.repeat(60));
    
    // 基本统计
    console.log(`\n📈 基本统计:`);
    console.log(`   总页面数: ${results.allPages.length}`);
    console.log(`   有内容页面: ${results.allPages.filter(p => p.hasContent).length}`);
    console.log(`   标准侧边栏: ${results.allPages.filter(p => p.sidebarType === 'standard').length}`);
    console.log(`   命名问题页面: ${results.allPages.filter(p => p.namingIssues.length > 0).length}`);
    console.log(`   功能重复组: ${results.functionalDuplicates.length}`);
    
    // 功能分类分析
    console.log(`\n📋 功能分类分析:`);
    for (const [category, analysis] of Object.entries(results.categoryAnalysis)) {
        if (analysis.pages.length > 0) {
            console.log(`   ${analysis.name}: ${analysis.pages.length}个页面`);
            if (analysis.issues.length > 0) {
                console.log(`     - 问题页面: ${analysis.issues.length}个`);
            }
        }
    }
    
    // 功能重复分析
    if (results.functionalDuplicates.length > 0) {
        console.log(`\n🔄 功能重复分析:`);
        results.functionalDuplicates.forEach((group, index) => {
            console.log(`   组${index + 1} (${group.category}): ${group.reason}`);
            group.pages.forEach(page => {
                console.log(`     - ${page.fileName}`);
            });
        });
    }
    
    // 重命名建议
    if (results.recommendations.rename.length > 0) {
        console.log(`\n📝 重命名建议 (前10个):`);
        results.recommendations.rename.slice(0, 10).forEach(suggestion => {
            console.log(`   ${suggestion.current} → ${suggestion.suggested}`);
            console.log(`     理由: ${suggestion.reason} (优先级: ${suggestion.priority})`);
        });
    }
    
    console.log(`\n✅ 分析完成！详细结果已保存到JSON文件。`);
}

// 主函数
function main() {
    const results = performComprehensiveAnalysis();
    generateAnalysisReport(results);
    
    // 保存详细结果
    const outputPath = path.join(__dirname, '../../../docs/comprehensive-page-analysis.json');
    try {
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
        console.log(`\n💾 详细分析结果已保存到: ${outputPath}`);
    } catch (error) {
        console.log(`\n⚠️  保存结果失败: ${error.message}`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    performComprehensiveAnalysis,
    generateAnalysisReport,
    analyzePage,
    identifyDuplicates,
    FUNCTIONAL_CATEGORIES
};

/**
 * 用户资料管理API客户端扩展
 * 专门处理用户资料相关的API调用
 * 版本: v1.0
 * 创建时间: 2025-07-01
 */

class UserProfileAPI {
    constructor(authAPI) {
        this.authAPI = authAPI || window.authAPI
        this.baseURL = 'http://localhost:8001/api'
        this.timeout = 10000
        this.retryCount = 3
        this.retryDelay = 1000
    }

    /**
     * 通用HTTP请求方法
     * @param {string} endpoint - API端点
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} API响应
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`
        const config = {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                ...options.headers
            },
            timeout: this.timeout,
            ...options
        }

        // 添加认证头
        const token = this.authAPI.getAccessToken()
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
        }

        // 如果有body数据且不是FormData，转换为JSON
        if (config.body && !(config.body instanceof FormData)) {
            config.headers['Content-Type'] = 'application/json'
            config.body = JSON.stringify(config.body)
        }

        let lastError = null
        
        // 重试机制
        for (let attempt = 1; attempt <= this.retryCount; attempt++) {
            try {
                console.log(`🌐 用户资料API请求 [尝试${attempt}/${this.retryCount}]:`, config.method, url)
                
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), this.timeout)
                
                const response = await fetch(url, {
                    ...config,
                    signal: controller.signal
                })
                
                clearTimeout(timeoutId)
                
                // 解析响应
                const data = await response.json()
                
                if (response.ok) {
                    console.log(`✅ 用户资料API响应成功:`, data)
                    return data
                } else {
                    console.error(`❌ 用户资料API响应错误 [${response.status}]:`, data)
                    
                    // 如果是401错误，尝试刷新令牌
                    if (response.status === 401 && attempt < this.retryCount) {
                        const refreshed = await this.authAPI.refreshToken()
                        if (refreshed) {
                            continue // 重试请求
                        }
                    }
                    
                    throw new Error(data.error?.message || `HTTP ${response.status}: ${response.statusText}`)
                }
                
            } catch (error) {
                lastError = error
                console.error(`❌ 用户资料API请求失败 [尝试${attempt}/${this.retryCount}]:`, error.message)
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < this.retryCount) {
                    await this.delay(this.retryDelay * attempt)
                }
            }
        }
        
        throw lastError
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms))
    }

    /**
     * 更新用户资料
     * @param {Object} profileData - 用户资料数据
     * @returns {Promise<Object>} 更新响应
     */
    async updateProfile(profileData) {
        try {
            const response = await this.request('/users/profile', {
                method: 'PUT',
                body: profileData
            })

            if (response.success) {
                console.log('✅ 用户资料更新成功:', response.data)
                
                // 更新本地存储的用户信息
                this.authAPI.setUserInfo(response.data)
                
                return response
            } else {
                throw new Error(response.error?.message || '用户资料更新失败')
            }
        } catch (error) {
            console.error('❌ 用户资料更新失败:', error)
            throw error
        }
    }

    /**
     * 上传用户头像
     * @param {File} file - 头像文件
     * @returns {Promise<Object>} 上传响应
     */
    async uploadAvatar(file) {
        try {
            // 验证文件
            if (!file) {
                throw new Error('请选择要上传的头像文件')
            }

            if (!file.type.startsWith('image/')) {
                throw new Error('只允许上传图片文件')
            }

            if (file.size > 5 * 1024 * 1024) {
                throw new Error('图片文件不能超过5MB')
            }

            // 创建FormData
            const formData = new FormData()
            formData.append('avatar', file)

            const response = await this.request('/users/avatar', {
                method: 'POST',
                body: formData
            })

            if (response.success) {
                console.log('✅ 头像上传成功:', response.data)
                
                // 更新本地存储的用户信息
                const userInfo = this.authAPI.getUserInfo()
                if (userInfo) {
                    userInfo.avatar = response.data.avatar
                    this.authAPI.setUserInfo(userInfo)
                }
                
                return response
            } else {
                throw new Error(response.error?.message || '头像上传失败')
            }
        } catch (error) {
            console.error('❌ 头像上传失败:', error)
            throw error
        }
    }

    /**
     * 删除用户头像
     * @returns {Promise<Object>} 删除响应
     */
    async deleteAvatar() {
        try {
            const response = await this.request('/users/avatar', {
                method: 'DELETE'
            })

            if (response.success) {
                console.log('✅ 头像删除成功:', response.data)
                
                // 更新本地存储的用户信息
                const userInfo = this.authAPI.getUserInfo()
                if (userInfo) {
                    userInfo.avatar = response.data.avatar
                    this.authAPI.setUserInfo(userInfo)
                }
                
                return response
            } else {
                throw new Error(response.error?.message || '头像删除失败')
            }
        } catch (error) {
            console.error('❌ 头像删除失败:', error)
            throw error
        }
    }

    /**
     * 验证用户资料数据
     * @param {Object} profileData - 用户资料数据
     * @returns {Object} 验证结果
     */
    validateProfileData(profileData) {
        const errors = []

        // 验证邮箱格式
        if (profileData.email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!emailRegex.test(profileData.email)) {
                errors.push('邮箱格式不正确')
            }
        }

        // 验证手机号格式
        if (profileData.phone) {
            const phoneRegex = /^1[3-9]\d{9}$/
            if (!phoneRegex.test(profileData.phone)) {
                errors.push('手机号格式不正确')
            }
        }

        // 验证生日格式
        if (profileData.birthday) {
            const birthday = new Date(profileData.birthday)
            const now = new Date()
            if (birthday > now) {
                errors.push('生日不能是未来日期')
            }
            if (now.getFullYear() - birthday.getFullYear() > 150) {
                errors.push('生日不能超过150年前')
            }
        }

        // 验证姓名长度
        if (profileData.real_name && profileData.real_name.length > 50) {
            errors.push('真实姓名不能超过50个字符')
        }

        if (profileData.nickname && profileData.nickname.length > 30) {
            errors.push('昵称不能超过30个字符')
        }

        return {
            valid: errors.length === 0,
            errors: errors
        }
    }

    /**
     * 格式化用户资料数据
     * @param {Object} rawData - 原始数据
     * @returns {Object} 格式化后的数据
     */
    formatProfileData(rawData) {
        const formatted = {}

        // 清理和格式化字符串字段
        const stringFields = ['real_name', 'nickname', 'first_name', 'last_name', 'email', 'phone']
        stringFields.forEach(field => {
            if (rawData[field] !== undefined && rawData[field] !== null) {
                const value = String(rawData[field]).trim()
                if (value) {
                    formatted[field] = value
                }
            }
        })

        // 处理性别字段
        if (rawData.gender && ['male', 'female', 'other'].includes(rawData.gender)) {
            formatted.gender = rawData.gender
        }

        // 处理生日字段
        if (rawData.birthday) {
            const birthday = new Date(rawData.birthday)
            if (!isNaN(birthday.getTime())) {
                formatted.birthday = birthday.toISOString().split('T')[0] // YYYY-MM-DD格式
            }
        }

        return formatted
    }

    /**
     * 获取用户资料字段的显示名称
     * @param {string} field - 字段名
     * @returns {string} 显示名称
     */
    getFieldDisplayName(field) {
        const fieldNames = {
            'real_name': '真实姓名',
            'nickname': '昵称',
            'first_name': '名',
            'last_name': '姓',
            'email': '邮箱地址',
            'phone': '手机号码',
            'gender': '性别',
            'birthday': '生日',
            'avatar': '头像'
        }
        
        return fieldNames[field] || field
    }

    /**
     * 格式化性别显示
     * @param {string} gender - 性别值
     * @returns {string} 性别显示名称
     */
    formatGender(gender) {
        const genderMap = {
            'male': '男',
            'female': '女',
            'other': '其他'
        }
        
        return genderMap[gender] || '未设置'
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    /**
     * 检查API服务器健康状态
     * @returns {Promise<Object>} 健康状态响应
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.baseURL.replace('/api', '')}/health`)
            return await response.json()
        } catch (error) {
            console.error('❌ 健康检查失败:', error)
            throw error
        }
    }
}

// 创建全局用户资料API实例
if (typeof window !== 'undefined') {
    window.userProfileAPI = new UserProfileAPI()
}

// 导出用户资料API类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserProfileAPI
}

console.log('✅ 用户资料API客户端已初始化')

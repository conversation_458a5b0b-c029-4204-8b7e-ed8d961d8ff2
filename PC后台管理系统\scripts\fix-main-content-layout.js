/**
 * 修复主内容区域布局脚本
 * 确保主内容区域有正确的左边距，避免被侧边栏遮挡
 */

const fs = require('fs');
const path = require('path');

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 修复单个文件的主内容区域布局
function fixMainContentLayout(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        let hasChanges = false;
        
        // 查找 .main-content 样式
        const mainContentPattern = /\.main-content\s*\{([^}]*)\}/g;
        const matches = content.match(mainContentPattern);
        
        if (matches) {
            for (const match of matches) {
                // 检查是否已经有 margin-left
                if (!match.includes('margin-left')) {
                    // 添加 margin-left: 200px;
                    const newMatch = match.replace(/(\{[^}]*)(flex:\s*1;?)([^}]*)\}/, 
                        '$1$2\n            margin-left: 200px;$3}');
                    content = content.replace(match, newMatch);
                    hasChanges = true;
                } else if (match.includes('margin-left') && !match.includes('margin-left: 200px')) {
                    // 修正错误的 margin-left 值
                    const newMatch = match.replace(/margin-left:\s*[^;]+;?/, 'margin-left: 200px;');
                    content = content.replace(match, newMatch);
                    hasChanges = true;
                }
            }
        }
        
        // 确保侧边栏是固定定位
        const sidebarPattern = /\.sidebar\s*\{([^}]*)\}/g;
        const sidebarMatches = content.match(sidebarPattern);
        
        if (sidebarMatches) {
            for (const match of sidebarMatches) {
                if (!match.includes('position: fixed')) {
                    const newMatch = match.replace(/(\{[^}]*)(width:\s*200px;?)([^}]*)\}/, 
                        '$1$2\n            position: fixed;$3}');
                    content = content.replace(match, newMatch);
                    hasChanges = true;
                }
            }
        }
        
        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已修复: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (布局正常)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🔧 开始修复主内容区域布局...\n');
    console.log('📋 修复内容:');
    console.log('   - 确保主内容区域有 margin-left: 200px');
    console.log('   - 确保侧边栏是 position: fixed');
    console.log('   - 避免内容被侧边栏遮挡\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const file of htmlFiles) {
        if (fixMainContentLayout(file)) {
            successCount++;
        } else {
            skipCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 已修复: ${successCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 布局修复完成！');
        console.log('📏 主内容区域现在有正确的左边距');
        console.log('🚫 内容不再被侧边栏遮挡');
        console.log('✨ 页面布局恢复正常');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixMainContentLayout,
    main
};

/**
 * 增强版施工管理系统 - 自动化功能测试脚本
 * 测试所有6大核心模块的功能
 * 版本: v2.0
 */

class ConstructionSystemTester {
    constructor() {
        this.testResults = [];
        this.currentTest = '';
        this.testStartTime = Date.now();
    }

    /**
     * 开始全面测试
     */
    async runAllTests() {
        console.log('🚀 开始增强版施工管理系统全面功能测试...');
        
        try {
            // 1. 系统初始化测试
            await this.testSystemInitialization();
            
            // 2. 阶段切换测试
            await this.testPhaseSwitch();
            
            // 3. 人员管理测试
            await this.testPersonnelManagement();
            
            // 4. 文件管理测试
            await this.testFileManagement();
            
            // 5. 知识库管理测试
            await this.testKnowledgeManagement();
            
            // 6. 现场记录测试
            await this.testRecordManagement();
            
            // 7. 异常处理测试
            await this.testIssueManagement();
            
            // 8. 验收管理测试
            await this.testAcceptanceManagement();
            
            // 9. 数据持久化测试
            await this.testDataPersistence();
            
            // 10. 用户界面测试
            await this.testUserInterface();
            
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
            this.addTestResult('系统测试', false, error.message);
        }
    }

    /**
     * 1. 系统初始化测试
     */
    async testSystemInitialization() {
        this.currentTest = '系统初始化测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            // 检查全局对象是否存在
            const globalObjects = [
                'constructionManager',
                'fileManager', 
                'knowledgeManager',
                'recordManager',
                'issueManager',
                'acceptanceManager'
            ];
            
            for (const obj of globalObjects) {
                if (window[obj]) {
                    console.log(`✅ ${obj} 已正确初始化`);
                    this.addTestResult(`${obj}初始化`, true);
                } else {
                    console.log(`❌ ${obj} 初始化失败`);
                    this.addTestResult(`${obj}初始化`, false, '对象未定义');
                }
            }
            
            // 检查阶段数据结构
            if (window.constructionManager && window.constructionManager.phaseData) {
                const phases = ['briefing', 'wiring', 'installation', 'debugging', 'afterservice'];
                for (const phase of phases) {
                    if (window.constructionManager.phaseData[phase]) {
                        console.log(`✅ ${phase} 阶段数据结构正确`);
                        this.addTestResult(`${phase}阶段数据`, true);
                    } else {
                        console.log(`❌ ${phase} 阶段数据结构缺失`);
                        this.addTestResult(`${phase}阶段数据`, false, '数据结构缺失');
                    }
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 2. 阶段切换测试
     */
    async testPhaseSwitch() {
        this.currentTest = '阶段切换测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            const phases = ['briefing', 'wiring', 'installation', 'debugging', 'afterservice'];
            
            for (const phase of phases) {
                if (window.constructionManager && typeof window.constructionManager.switchPhase === 'function') {
                    window.constructionManager.switchPhase(phase);
                    
                    // 检查当前阶段是否正确设置
                    if (window.constructionManager.currentPhase === phase) {
                        console.log(`✅ 成功切换到 ${phase} 阶段`);
                        this.addTestResult(`切换到${phase}`, true);
                    } else {
                        console.log(`❌ 切换到 ${phase} 阶段失败`);
                        this.addTestResult(`切换到${phase}`, false, '阶段切换失败');
                    }
                    
                    // 等待UI更新
                    await this.sleep(100);
                } else {
                    console.log(`❌ switchPhase 方法不存在`);
                    this.addTestResult('switchPhase方法', false, '方法不存在');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 3. 人员管理测试
     */
    async testPersonnelManagement() {
        this.currentTest = '人员管理测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.constructionManager) {
                // 测试添加人员功能
                const testPersonnel = {
                    name: '测试工程师',
                    role: '项目经理',
                    phone: '13800138000',
                    email: '<EMAIL>',
                    notes: '自动化测试人员'
                };
                
                // 模拟添加人员
                if (typeof window.constructionManager.addPersonnel === 'function') {
                    console.log('✅ addPersonnel 方法存在');
                    this.addTestResult('addPersonnel方法', true);
                } else {
                    console.log('❌ addPersonnel 方法不存在');
                    this.addTestResult('addPersonnel方法', false, '方法不存在');
                }
                
                // 测试人员数据结构
                const currentPhase = window.constructionManager.currentPhase || 'briefing';
                const personnelData = window.constructionManager.phaseData[currentPhase].personnel;
                
                if (Array.isArray(personnelData)) {
                    console.log('✅ 人员数据结构正确');
                    this.addTestResult('人员数据结构', true);
                } else {
                    console.log('❌ 人员数据结构错误');
                    this.addTestResult('人员数据结构', false, '不是数组类型');
                }
                
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 4. 文件管理测试
     */
    async testFileManagement() {
        this.currentTest = '文件管理测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.fileManager) {
                // 测试文件管理器方法
                const methods = ['uploadDocument', 'previewDocument', 'printDocument'];
                
                for (const method of methods) {
                    if (typeof window.fileManager[method] === 'function') {
                        console.log(`✅ ${method} 方法存在`);
                        this.addTestResult(`${method}方法`, true);
                    } else {
                        console.log(`❌ ${method} 方法不存在`);
                        this.addTestResult(`${method}方法`, false, '方法不存在');
                    }
                }
                
                // 测试支持的文件格式
                if (window.fileManager.supportedFormats) {
                    console.log('✅ 文件格式配置存在');
                    this.addTestResult('文件格式配置', true);
                } else {
                    console.log('❌ 文件格式配置缺失');
                    this.addTestResult('文件格式配置', false, '配置缺失');
                }
                
                // 测试文件大小限制
                if (window.fileManager.maxFileSize) {
                    console.log(`✅ 文件大小限制: ${window.fileManager.maxFileSize / 1024 / 1024}MB`);
                    this.addTestResult('文件大小限制', true);
                } else {
                    console.log('❌ 文件大小限制未设置');
                    this.addTestResult('文件大小限制', false, '未设置');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 5. 知识库管理测试
     */
    async testKnowledgeManagement() {
        this.currentTest = '知识库管理测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.knowledgeManager) {
                // 测试知识库方法
                const methods = ['createKnowledge', 'viewKnowledge', 'editKnowledge'];
                
                for (const method of methods) {
                    if (typeof window.knowledgeManager[method] === 'function') {
                        console.log(`✅ ${method} 方法存在`);
                        this.addTestResult(`${method}方法`, true);
                    } else {
                        console.log(`❌ ${method} 方法不存在`);
                        this.addTestResult(`${method}方法`, false, '方法不存在');
                    }
                }
                
                // 测试编辑器初始化
                if (window.knowledgeManager.editor !== undefined) {
                    console.log('✅ 编辑器属性存在');
                    this.addTestResult('编辑器属性', true);
                } else {
                    console.log('❌ 编辑器属性不存在');
                    this.addTestResult('编辑器属性', false, '属性不存在');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 6. 现场记录测试
     */
    async testRecordManagement() {
        this.currentTest = '现场记录测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.recordManager) {
                // 测试记录管理方法
                const methods = ['uploadRecord', 'previewMedia', 'deleteRecord'];
                
                for (const method of methods) {
                    if (typeof window.recordManager[method] === 'function') {
                        console.log(`✅ ${method} 方法存在`);
                        this.addTestResult(`${method}方法`, true);
                    } else {
                        console.log(`❌ ${method} 方法不存在`);
                        this.addTestResult(`${method}方法`, false, '方法不存在');
                    }
                }
                
                // 测试支持的媒体类型
                if (window.recordManager.supportedImageTypes && window.recordManager.supportedVideoTypes) {
                    console.log('✅ 媒体类型配置存在');
                    this.addTestResult('媒体类型配置', true);
                } else {
                    console.log('❌ 媒体类型配置缺失');
                    this.addTestResult('媒体类型配置', false, '配置缺失');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 7. 异常处理测试
     */
    async testIssueManagement() {
        this.currentTest = '异常处理测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.issueManager) {
                // 测试异常管理方法
                const methods = ['createIssue', 'editIssue', 'deleteIssue'];
                
                for (const method of methods) {
                    if (typeof window.issueManager[method] === 'function') {
                        console.log(`✅ ${method} 方法存在`);
                        this.addTestResult(`${method}方法`, true);
                    } else {
                        console.log(`❌ ${method} 方法不存在`);
                        this.addTestResult(`${method}方法`, false, '方法不存在');
                    }
                }
                
                // 测试异常类型和严重程度配置
                if (window.issueManager.issueTypes && window.issueManager.severityLevels) {
                    console.log('✅ 异常分类配置存在');
                    this.addTestResult('异常分类配置', true);
                } else {
                    console.log('❌ 异常分类配置缺失');
                    this.addTestResult('异常分类配置', false, '配置缺失');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 8. 验收管理测试
     */
    async testAcceptanceManagement() {
        this.currentTest = '验收管理测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            if (window.acceptanceManager) {
                // 测试验收管理方法
                const methods = ['startAcceptance', 'generateAcceptanceReport'];
                
                for (const method of methods) {
                    if (typeof window.acceptanceManager[method] === 'function') {
                        console.log(`✅ ${method} 方法存在`);
                        this.addTestResult(`${method}方法`, true);
                    } else {
                        console.log(`❌ ${method} 方法不存在`);
                        this.addTestResult(`${method}方法`, false, '方法不存在');
                    }
                }
                
                // 测试验收结果配置
                if (window.acceptanceManager.acceptanceResults) {
                    console.log('✅ 验收结果配置存在');
                    this.addTestResult('验收结果配置', true);
                } else {
                    console.log('❌ 验收结果配置缺失');
                    this.addTestResult('验收结果配置', false, '配置缺失');
                }
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 9. 数据持久化测试
     */
    async testDataPersistence() {
        this.currentTest = '数据持久化测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            // 测试LocalStorage功能
            const testKey = 'construction_test_data';
            const testData = { test: true, timestamp: Date.now() };
            
            // 写入测试
            localStorage.setItem(testKey, JSON.stringify(testData));
            console.log('✅ LocalStorage 写入测试通过');
            this.addTestResult('LocalStorage写入', true);
            
            // 读取测试
            const retrievedData = JSON.parse(localStorage.getItem(testKey));
            if (retrievedData && retrievedData.test === true) {
                console.log('✅ LocalStorage 读取测试通过');
                this.addTestResult('LocalStorage读取', true);
            } else {
                console.log('❌ LocalStorage 读取测试失败');
                this.addTestResult('LocalStorage读取', false, '数据不匹配');
            }
            
            // 清理测试数据
            localStorage.removeItem(testKey);
            
            // 测试系统数据持久化方法
            if (window.constructionManager && typeof window.constructionManager.savePhaseData === 'function') {
                console.log('✅ savePhaseData 方法存在');
                this.addTestResult('savePhaseData方法', true);
            } else {
                console.log('❌ savePhaseData 方法不存在');
                this.addTestResult('savePhaseData方法', false, '方法不存在');
            }
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 10. 用户界面测试
     */
    async testUserInterface() {
        this.currentTest = '用户界面测试';
        console.log(`\n📋 ${this.currentTest}`);
        
        try {
            // 测试关键UI元素是否存在
            const uiElements = [
                { selector: '.phase-tabs', name: '阶段切换标签' },
                { selector: '#currentPersonnelList', name: '人员列表容器' },
                { selector: '#currentDocumentList', name: '文档列表容器' },
                { selector: '#currentKnowledgeList', name: '知识库列表容器' },
                { selector: '#currentRecordList', name: '记录列表容器' }
            ];
            
            for (const element of uiElements) {
                const el = document.querySelector(element.selector);
                if (el) {
                    console.log(`✅ ${element.name} 存在`);
                    this.addTestResult(element.name, true);
                } else {
                    console.log(`❌ ${element.name} 不存在`);
                    this.addTestResult(element.name, false, '元素不存在');
                }
            }
            
            // 测试按钮功能绑定
            const buttons = document.querySelectorAll('button[onclick]');
            console.log(`✅ 找到 ${buttons.length} 个绑定了事件的按钮`);
            this.addTestResult('按钮事件绑定', true, `${buttons.length}个按钮`);
            
        } catch (error) {
            console.error(`❌ ${this.currentTest}失败:`, error);
            this.addTestResult(this.currentTest, false, error.message);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed, details = '') {
        this.testResults.push({
            name: testName,
            passed: passed,
            details: details,
            timestamp: Date.now()
        });
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const testDuration = Date.now() - this.testStartTime;
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 增强版施工管理系统测试报告');
        console.log('='.repeat(60));
        console.log(`测试时间: ${new Date().toLocaleString()}`);
        console.log(`测试耗时: ${testDuration}ms`);
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests} ✅`);
        console.log(`失败测试: ${failedTests} ❌`);
        console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
        console.log('='.repeat(60));
        
        // 详细结果
        console.log('\n📋 详细测试结果:');
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            const details = result.details ? ` (${result.details})` : '';
            console.log(`${index + 1}. ${status} ${result.name}${details}`);
        });
        
        // 失败测试汇总
        const failedTestsList = this.testResults.filter(r => !r.passed);
        if (failedTestsList.length > 0) {
            console.log('\n⚠️ 失败测试汇总:');
            failedTestsList.forEach((result, index) => {
                console.log(`${index + 1}. ${result.name}: ${result.details}`);
            });
        }
        
        console.log('\n🎉 测试完成！');
        
        // 返回测试结果供进一步处理
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests / totalTests) * 100,
            duration: testDuration,
            results: this.testResults
        };
    }

    /**
     * 工具方法：延时
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出测试类
window.ConstructionSystemTester = ConstructionSystemTester;

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && window.document) {
    // 等待页面完全加载后运行测试
    window.addEventListener('load', async () => {
        // 等待所有模块初始化完成
        setTimeout(async () => {
            const tester = new ConstructionSystemTester();
            const results = await tester.runAllTests();
            
            // 将测试结果保存到全局变量供查看
            window.testResults = results;
            
        }, 2000); // 等待2秒确保所有模块都已加载
    });
}

console.log('🧪 施工管理系统测试脚本已加载');

/**
 * 横向切换表格管理器
 * 实现表格内容的横向切换显示，避免滚动条
 */

class HorizontalTableManager {
    constructor() {
        this.config = {
            itemsPerPage: 5, // 每页显示的行数
            columnsPerPage: 6, // 每页显示的列数
            animationDuration: 300 // 切换动画时长
        };

        this.state = {
            currentRowPage: 0,
            currentColPage: 0,
            totalRows: 0,
            totalCols: 0,
            originalData: [],
            filteredData: []
        };

        this.elements = {
            container: null,
            table: null,
            pagination: null
        };

        this.init();
    }

    init() {
        this.createHorizontalTableStructure();
        this.bindEvents();
        console.log('📊 横向切换表格管理器初始化完成');
    }

    /**
     * 创建横向切换表格结构
     */
    createHorizontalTableStructure() {
        const tableContainer = document.querySelector('.table-container');
        if (!tableContainer) {
            console.warn('⚠️ 未找到表格容器');
            return;
        }

        // 保存原始表格引用
        this.elements.table = tableContainer.querySelector('.requirements-table');
        
        // 创建新的横向切换结构
        const horizontalTableHTML = `
            <div class="horizontal-table-wrapper">
                <!-- 表格头部控制区 -->
                <div class="table-header-controls">
                    <div class="table-info">
                        <span class="table-stats" id="table-stats">显示 1-5 条，共 0 条记录</span>
                        <span class="column-info" id="column-info">列 1-6 / 10</span>
                    </div>
                    <div class="table-controls">
                        <div class="view-mode-switcher">
                            <button type="button" class="view-btn active" data-view="card" title="卡片视图">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="view-btn" data-view="table" title="表格视图">
                                <i class="fas fa-table"></i>
                            </button>
                            <button type="button" class="view-btn" data-view="compact" title="紧凑视图">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        <div class="column-navigator">
                            <button type="button" class="nav-btn" id="prev-cols" title="上一组列">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <span class="nav-indicator" id="col-indicator">1/2</span>
                            <button type="button" class="nav-btn" id="next-cols" title="下一组列">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 表格主体 -->
                <div class="horizontal-table-body" id="horizontal-table-body">
                    <!-- 卡片视图 -->
                    <div class="card-view" id="card-view">
                        <div class="cards-container" id="cards-container">
                            <!-- 卡片内容动态生成 -->
                        </div>
                    </div>

                    <!-- 表格视图 -->
                    <div class="table-view" id="table-view" style="display: none;">
                        <div class="table-scroll-container">
                            <table class="horizontal-table" id="horizontal-table">
                                <thead id="table-head">
                                    <!-- 表头动态生成 -->
                                </thead>
                                <tbody id="table-body">
                                    <!-- 表体动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 紧凑视图 -->
                    <div class="compact-view" id="compact-view" style="display: none;">
                        <div class="compact-list" id="compact-list">
                            <!-- 紧凑列表动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 分页控制 -->
                <div class="table-pagination" id="table-pagination">
                    <div class="pagination-info">
                        <select class="page-size-selector" id="page-size">
                            <option value="5">5条/页</option>
                            <option value="10">10条/页</option>
                            <option value="20" selected>20条/页</option>
                            <option value="50">50条/页</option>
                        </select>
                    </div>
                    <div class="pagination-controls">
                        <button type="button" class="page-btn" id="first-page" title="首页">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button type="button" class="page-btn" id="prev-page" title="上一页">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="page-numbers" id="page-numbers">
                            <!-- 页码动态生成 -->
                        </div>
                        <button type="button" class="page-btn" id="next-page" title="下一页">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button type="button" class="page-btn" id="last-page" title="末页">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                    <div class="page-jump">
                        <span>跳转到</span>
                        <input type="number" class="page-input" id="page-input" min="1">
                        <span>页</span>
                        <button type="button" class="jump-btn" id="jump-btn">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 替换原始表格
        tableContainer.innerHTML = horizontalTableHTML;
        
        // 缓存新元素
        this.elements.container = tableContainer.querySelector('.horizontal-table-wrapper');
        this.addHorizontalTableStyles();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
        });

        // 列导航
        document.getElementById('prev-cols')?.addEventListener('click', () => this.navigateColumns(-1));
        document.getElementById('next-cols')?.addEventListener('click', () => this.navigateColumns(1));

        // 分页控制
        document.getElementById('first-page')?.addEventListener('click', () => this.goToPage(0));
        document.getElementById('prev-page')?.addEventListener('click', () => this.navigateRows(-1));
        document.getElementById('next-page')?.addEventListener('click', () => this.navigateRows(1));
        document.getElementById('last-page')?.addEventListener('click', () => this.goToLastPage());

        // 页面大小变更
        document.getElementById('page-size')?.addEventListener('change', (e) => {
            this.config.itemsPerPage = parseInt(e.target.value);
            this.render();
        });

        // 页面跳转
        document.getElementById('jump-btn')?.addEventListener('click', () => this.jumpToPage());
        document.getElementById('page-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.jumpToPage();
        });

        // 键盘导航
        document.addEventListener('keydown', (e) => this.handleKeyboardNavigation(e));
    }

    /**
     * 设置数据并渲染
     */
    setData(data) {
        this.state.originalData = data || [];
        this.state.filteredData = [...this.state.originalData];
        this.state.totalRows = this.state.filteredData.length;
        this.state.currentRowPage = 0;
        this.state.currentColPage = 0;
        
        this.render();
    }

    /**
     * 渲染表格
     */
    render() {
        const currentView = document.querySelector('.view-btn.active').dataset.view;
        
        switch (currentView) {
            case 'card':
                this.renderCardView();
                break;
            case 'table':
                this.renderTableView();
                break;
            case 'compact':
                this.renderCompactView();
                break;
        }

        this.updatePagination();
        this.updateStats();
    }

    /**
     * 渲染卡片视图
     */
    renderCardView() {
        const container = document.getElementById('cards-container');
        if (!container) return;

        const startIdx = this.state.currentRowPage * this.config.itemsPerPage;
        const endIdx = Math.min(startIdx + this.config.itemsPerPage, this.state.totalRows);
        const pageData = this.state.filteredData.slice(startIdx, endIdx);

        const cardsHTML = pageData.map(row => `
            <div class="requirement-card" data-id="${row.id}">
                <div class="card-header">
                    <div class="card-id">
                        <span class="id-label">需求ID</span>
                        <span class="id-value">${row.id}</span>
                    </div>
                    <div class="card-status">
                        <span class="status-badge status-${row.status.toLowerCase()}">${row.status}</span>
                    </div>
                </div>
                <div class="card-content">
                    <h4 class="card-title">${row.customer}</h4>
                    <div class="card-details">
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>${row.phone}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-tag"></i>
                            <span>${row.type}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${row.address}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span>${row.budget}</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="card-time">
                        <i class="fas fa-clock"></i>
                        <span>${row.createdAt}</span>
                    </div>
                    <div class="card-priority">
                        <span class="priority-badge priority-${row.priority.toLowerCase()}">${row.priority}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button type="button" class="action-btn view-btn" onclick="viewRequirement('${row.id}')">
                        <i class="fas fa-eye"></i>
                        查看
                    </button>
                    <button type="button" class="action-btn edit-btn" onclick="editRequirement('${row.id}')">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button type="button" class="action-btn delete-btn" onclick="deleteRequirement('${row.id}')">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        `).join('');

        container.innerHTML = cardsHTML || '<div class="no-data">暂无数据</div>';
    }

    /**
     * 渲染表格视图
     */
    renderTableView() {
        const thead = document.getElementById('table-head');
        const tbody = document.getElementById('table-body');
        if (!thead || !tbody) return;

        // 定义所有列
        const allColumns = [
            { key: 'id', title: '需求ID', width: '120px' },
            { key: 'customer', title: '客户姓名', width: '100px' },
            { key: 'phone', title: '联系电话', width: '120px' },
            { key: 'type', title: '需求类型', width: '100px' },
            { key: 'address', title: '地址信息', width: '200px' },
            { key: 'budget', title: '预算范围', width: '100px' },
            { key: 'priority', title: '优先级', width: '80px' },
            { key: 'status', title: '状态', width: '80px' },
            { key: 'createdAt', title: '创建时间', width: '140px' },
            { key: 'actions', title: '操作', width: '150px' }
        ];

        this.state.totalCols = allColumns.length;

        // 计算当前显示的列
        const startColIdx = this.state.currentColPage * this.config.columnsPerPage;
        const endColIdx = Math.min(startColIdx + this.config.columnsPerPage, this.state.totalCols);
        const visibleColumns = allColumns.slice(startColIdx, endColIdx);

        // 渲染表头
        const theadHTML = `
            <tr>
                ${visibleColumns.map(col => `<th style="width: ${col.width}">${col.title}</th>`).join('')}
            </tr>
        `;
        thead.innerHTML = theadHTML;

        // 渲染表体
        const startRowIdx = this.state.currentRowPage * this.config.itemsPerPage;
        const endRowIdx = Math.min(startRowIdx + this.config.itemsPerPage, this.state.totalRows);
        const pageData = this.state.filteredData.slice(startRowIdx, endRowIdx);

        const tbodyHTML = pageData.map(row => `
            <tr data-id="${row.id}">
                ${visibleColumns.map(col => {
                    if (col.key === 'actions') {
                        return `<td>
                            <div class="table-actions">
                                <button type="button" class="action-btn mini" onclick="viewRequirement('${row.id}')" title="查看">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="action-btn mini" onclick="editRequirement('${row.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="action-btn mini delete" onclick="deleteRequirement('${row.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>`;
                    } else if (col.key === 'status') {
                        return `<td><span class="status-badge status-${row[col.key].toLowerCase()}">${row[col.key]}</span></td>`;
                    } else if (col.key === 'priority') {
                        return `<td><span class="priority-badge priority-${row[col.key].toLowerCase()}">${row[col.key]}</span></td>`;
                    } else {
                        return `<td>${row[col.key] || '-'}</td>`;
                    }
                }).join('')}
            </tr>
        `).join('');

        tbody.innerHTML = tbodyHTML || '<tr><td colspan="100%" class="no-data">暂无数据</td></tr>';

        this.updateColumnNavigation();
    }

    /**
     * 渲染紧凑视图
     */
    renderCompactView() {
        const container = document.getElementById('compact-list');
        if (!container) return;

        const startIdx = this.state.currentRowPage * this.config.itemsPerPage;
        const endIdx = Math.min(startIdx + this.config.itemsPerPage, this.state.totalRows);
        const pageData = this.state.filteredData.slice(startIdx, endIdx);

        const compactHTML = pageData.map(row => `
            <div class="compact-item" data-id="${row.id}">
                <div class="compact-main">
                    <div class="compact-id">${row.id}</div>
                    <div class="compact-info">
                        <span class="compact-customer">${row.customer}</span>
                        <span class="compact-phone">${row.phone}</span>
                        <span class="compact-type">${row.type}</span>
                        <span class="compact-budget">${row.budget}</span>
                    </div>
                    <div class="compact-status">
                        <span class="status-badge status-${row.status.toLowerCase()}">${row.status}</span>
                        <span class="priority-badge priority-${row.priority.toLowerCase()}">${row.priority}</span>
                    </div>
                </div>
                <div class="compact-actions">
                    <button type="button" class="action-btn mini" onclick="viewRequirement('${row.id}')">查看</button>
                    <button type="button" class="action-btn mini" onclick="editRequirement('${row.id}')">编辑</button>
                    <button type="button" class="action-btn mini delete" onclick="deleteRequirement('${row.id}')">删除</button>
                </div>
            </div>
        `).join('');

        container.innerHTML = compactHTML || '<div class="no-data">暂无数据</div>';
    }

    /**
     * 切换视图
     */
    switchView(viewType) {
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');

        // 显示对应视图
        document.querySelectorAll('#card-view, #table-view, #compact-view').forEach(view => {
            view.style.display = 'none';
        });
        document.getElementById(`${viewType}-view`).style.display = 'block';

        // 更新列导航显示
        const columnNav = document.querySelector('.column-navigator');
        columnNav.style.display = viewType === 'table' ? 'flex' : 'none';

        this.render();
    }

    /**
     * 列导航
     */
    navigateColumns(direction) {
        const maxColPage = Math.ceil(this.state.totalCols / this.config.columnsPerPage) - 1;
        const newColPage = Math.max(0, Math.min(this.state.currentColPage + direction, maxColPage));
        
        if (newColPage !== this.state.currentColPage) {
            this.state.currentColPage = newColPage;
            this.renderTableView();
        }
    }

    /**
     * 行导航
     */
    navigateRows(direction) {
        const maxRowPage = Math.ceil(this.state.totalRows / this.config.itemsPerPage) - 1;
        const newRowPage = Math.max(0, Math.min(this.state.currentRowPage + direction, maxRowPage));
        
        if (newRowPage !== this.state.currentRowPage) {
            this.state.currentRowPage = newRowPage;
            this.render();
        }
    }

    /**
     * 跳转到指定页面
     */
    goToPage(pageIndex) {
        const maxPage = Math.ceil(this.state.totalRows / this.config.itemsPerPage) - 1;
        this.state.currentRowPage = Math.max(0, Math.min(pageIndex, maxPage));
        this.render();
    }

    goToLastPage() {
        const lastPage = Math.ceil(this.state.totalRows / this.config.itemsPerPage) - 1;
        this.goToPage(Math.max(0, lastPage));
    }

    jumpToPage() {
        const input = document.getElementById('page-input');
        const pageNum = parseInt(input.value) - 1; // 转换为0基索引
        if (!isNaN(pageNum)) {
            this.goToPage(pageNum);
            input.value = '';
        }
    }

    /**
     * 更新分页信息
     */
    updatePagination() {
        const totalPages = Math.ceil(this.state.totalRows / this.config.itemsPerPage);
        const currentPage = this.state.currentRowPage + 1;
        
        // 更新页码
        const pageNumbers = document.getElementById('page-numbers');
        if (pageNumbers) {
            let pagesHTML = '';
            const maxVisible = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            if (endPage - startPage < maxVisible - 1) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                pagesHTML += `
                    <button type="button" class="page-number ${i === currentPage ? 'active' : ''}" 
                            onclick="window.horizontalTableManager.goToPage(${i - 1})">
                        ${i}
                    </button>
                `;
            }
            pageNumbers.innerHTML = pagesHTML;
        }

        // 更新按钮状态
        document.getElementById('first-page').disabled = currentPage === 1;
        document.getElementById('prev-page').disabled = currentPage === 1;
        document.getElementById('next-page').disabled = currentPage === totalPages;
        document.getElementById('last-page').disabled = currentPage === totalPages;

        // 更新页面输入框提示
        const pageInput = document.getElementById('page-input');
        if (pageInput) {
            pageInput.max = totalPages;
            pageInput.placeholder = `1-${totalPages}`;
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const startIdx = this.state.currentRowPage * this.config.itemsPerPage + 1;
        const endIdx = Math.min((this.state.currentRowPage + 1) * this.config.itemsPerPage, this.state.totalRows);

        document.getElementById('table-stats').textContent = 
            `显示 ${startIdx}-${endIdx} 条，共 ${this.state.totalRows} 条记录`;
    }

    /**
     * 更新列导航
     */
    updateColumnNavigation() {
        const totalColPages = Math.ceil(this.state.totalCols / this.config.columnsPerPage);
        const currentColPage = this.state.currentColPage + 1;
        
        document.getElementById('col-indicator').textContent = `${currentColPage}/${totalColPages}`;
        document.getElementById('prev-cols').disabled = currentColPage === 1;
        document.getElementById('next-cols').disabled = currentColPage === totalColPages;

        document.getElementById('column-info').textContent = 
            `列 ${this.state.currentColPage * this.config.columnsPerPage + 1}-${Math.min((this.state.currentColPage + 1) * this.config.columnsPerPage, this.state.totalCols)} / ${this.state.totalCols}`;
    }

    /**
     * 键盘导航
     */
    handleKeyboardNavigation(e) {
        if (e.target.matches('input, textarea, select')) return;

        switch (e.key) {
            case 'ArrowLeft':
                if (e.ctrlKey) this.navigateColumns(-1);
                else this.navigateRows(-1);
                e.preventDefault();
                break;
            case 'ArrowRight':
                if (e.ctrlKey) this.navigateColumns(1);
                else this.navigateRows(1);
                e.preventDefault();
                break;
            case 'Home':
                this.goToPage(0);
                e.preventDefault();
                break;
            case 'End':
                this.goToLastPage();
                e.preventDefault();
                break;
        }
    }

    /**
     * 搜索过滤
     */
    filter(searchTerm) {
        if (!searchTerm) {
            this.state.filteredData = [...this.state.originalData];
        } else {
            this.state.filteredData = this.state.originalData.filter(row => 
                Object.values(row).some(value => 
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                )
            );
        }
        
        this.state.totalRows = this.state.filteredData.length;
        this.state.currentRowPage = 0;
        this.render();
    }

    /**
     * 添加样式
     */
    addHorizontalTableStyles() {
        const styles = `
            <style id="horizontal-table-styles">
                .horizontal-table-wrapper {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                
                .table-header-controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 16px 20px;
                    background: #f8fafc;
                    border-bottom: 1px solid #e5e7eb;
                }
                
                .table-info {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }
                
                .table-stats, .column-info {
                    font-size: 14px;
                    color: #6b7280;
                }
                
                .table-controls {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                }
                
                .view-mode-switcher {
                    display: flex;
                    background: #f3f4f6;
                    border-radius: 6px;
                    padding: 2px;
                }
                
                .view-btn {
                    padding: 6px 10px;
                    border: none;
                    background: none;
                    border-radius: 4px;
                    cursor: pointer;
                    color: #6b7280;
                    transition: all 0.2s ease;
                }
                
                .view-btn.active, .view-btn:hover {
                    background: white;
                    color: #3b82f6;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                
                .column-navigator {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    background: #f3f4f6;
                    border-radius: 6px;
                    padding: 4px;
                }
                
                .nav-btn {
                    padding: 4px 8px;
                    border: none;
                    background: none;
                    border-radius: 4px;
                    cursor: pointer;
                    color: #6b7280;
                    font-size: 12px;
                }
                
                .nav-btn:hover:not(:disabled) {
                    background: white;
                    color: #3b82f6;
                }
                
                .nav-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .nav-indicator {
                    font-size: 12px;
                    color: #374151;
                    font-weight: 500;
                    min-width: 30px;
                    text-align: center;
                }
                
                .horizontal-table-body {
                    min-height: 400px;
                    position: relative;
                }
                
                /* 卡片视图样式 */
                .cards-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                    gap: 16px;
                    padding: 20px;
                }
                
                .requirement-card {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 16px;
                    transition: all 0.3s ease;
                    position: relative;
                }
                
                .requirement-card:hover {
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    transform: translateY(-2px);
                }
                
                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 12px;
                }
                
                .card-id {
                    display: flex;
                    flex-direction: column;
                }
                
                .id-label {
                    font-size: 12px;
                    color: #6b7280;
                    margin-bottom: 2px;
                }
                
                .id-value {
                    font-family: monospace;
                    font-weight: 600;
                    color: #374151;
                }
                
                .card-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0 0 12px 0;
                }
                
                .card-details {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    margin-bottom: 12px;
                }
                
                .detail-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                    color: #6b7280;
                }
                
                .detail-item i {
                    width: 14px;
                    color: #9ca3af;
                }
                
                .card-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;
                    padding-top: 12px;
                    border-top: 1px solid #f3f4f6;
                }
                
                .card-time {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    color: #9ca3af;
                }
                
                .card-actions {
                    display: flex;
                    gap: 8px;
                }
                
                .action-btn {
                    padding: 6px 12px;
                    border: 1px solid #e5e7eb;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                }
                
                .action-btn:hover {
                    background: #f9fafb;
                }
                
                .action-btn.mini {
                    padding: 4px 8px;
                    font-size: 11px;
                }
                
                .action-btn.delete:hover {
                    background: #fee2e2;
                    border-color: #fecaca;
                    color: #dc2626;
                }
                
                /* 表格视图样式 */
                .table-scroll-container {
                    overflow: hidden;
                }
                
                .horizontal-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                
                .horizontal-table th {
                    background: #f9fafb;
                    padding: 12px 16px;
                    text-align: left;
                    font-weight: 600;
                    color: #374151;
                    font-size: 13px;
                    border-bottom: 1px solid #e5e7eb;
                    white-space: nowrap;
                }
                
                .horizontal-table td {
                    padding: 12px 16px;
                    border-bottom: 1px solid #f3f4f6;
                    vertical-align: middle;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .horizontal-table tbody tr:hover {
                    background: #f9fafb;
                }
                
                .table-actions {
                    display: flex;
                    gap: 4px;
                }
                
                /* 紧凑视图样式 */
                .compact-list {
                    padding: 16px 20px;
                }
                
                .compact-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px 16px;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    margin-bottom: 8px;
                    background: white;
                    transition: all 0.2s ease;
                }
                
                .compact-item:hover {
                    background: #f9fafb;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                }
                
                .compact-main {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    flex: 1;
                }
                
                .compact-id {
                    font-family: monospace;
                    font-weight: 600;
                    color: #374151;
                    min-width: 100px;
                }
                
                .compact-info {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 1;
                }
                
                .compact-info > span {
                    font-size: 14px;
                    color: #6b7280;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .compact-customer {
                    font-weight: 500;
                    color: #374151 !important;
                }
                
                .compact-status {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                
                .compact-actions {
                    display: flex;
                    gap: 6px;
                }
                
                /* 状态和优先级标签 */
                .status-badge, .priority-badge {
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 500;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                
                .status-pending { background: #fef3c7; color: #92400e; }
                .status-processing { background: #dbeafe; color: #1e40af; }
                .status-completed { background: #d1fae5; color: #065f46; }
                .status-cancelled { background: #fee2e2; color: #991b1b; }
                
                .priority-high { background: #fee2e2; color: #991b1b; }
                .priority-medium { background: #fef3c7; color: #92400e; }
                .priority-low { background: #e0f2fe; color: #0277bd; }
                
                /* 分页样式 */
                .table-pagination {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 16px 20px;
                    background: #f8fafc;
                    border-top: 1px solid #e5e7eb;
                }
                
                .pagination-controls {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }
                
                .page-btn, .page-number {
                    padding: 6px 10px;
                    border: 1px solid #e5e7eb;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                }
                
                .page-btn:hover:not(:disabled), .page-number:hover {
                    background: #f3f4f6;
                }
                
                .page-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .page-number.active {
                    background: #3b82f6;
                    color: white;
                    border-color: #3b82f6;
                }
                
                .page-jump {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                    color: #6b7280;
                }
                
                .page-input {
                    width: 50px;
                    padding: 4px 8px;
                    border: 1px solid #e5e7eb;
                    border-radius: 4px;
                    font-size: 12px;
                    text-align: center;
                }
                
                .jump-btn {
                    padding: 4px 12px;
                    border: 1px solid #e5e7eb;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                }
                
                .jump-btn:hover {
                    background: #f3f4f6;
                }
                
                .page-size-selector {
                    padding: 4px 8px;
                    border: 1px solid #e5e7eb;
                    border-radius: 4px;
                    background: white;
                    font-size: 12px;
                }
                
                .no-data {
                    text-align: center;
                    padding: 40px;
                    color: #9ca3af;
                    font-size: 14px;
                }
                
                /* 响应式设计 */
                @media (max-width: 768px) {
                    .table-header-controls {
                        flex-direction: column;
                        gap: 12px;
                        align-items: stretch;
                    }
                    
                    .table-info {
                        justify-content: center;
                    }
                    
                    .table-controls {
                        justify-content: center;
                    }
                    
                    .cards-container {
                        grid-template-columns: 1fr;
                    }
                    
                    .table-pagination {
                        flex-direction: column;
                        gap: 12px;
                    }
                    
                    .compact-main {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 8px;
                    }
                    
                    .compact-info {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 4px;
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 获取示例数据
     */
    getSampleData() {
        return [
            {
                id: 'REQ-2025-001',
                customer: '张先生',
                phone: '13800138001',
                type: '全屋智能',
                address: '北京市朝阳区国贸大厦',
                budget: '15-20万',
                priority: 'High',
                status: 'Processing',
                createdAt: '2025-01-20'
            },
            {
                id: 'REQ-2025-002',
                customer: '李女士',
                phone: '13800138002',
                type: '智能安防',
                address: '上海市浦东新区陆家嘴',
                budget: '5-8万',
                priority: 'Medium',
                status: 'Pending',
                createdAt: '2025-01-21'
            },
            {
                id: 'REQ-2025-003',
                customer: '王总',
                phone: '13800138003',
                type: '智能照明',
                address: '深圳市南山区科技园',
                budget: '3-5万',
                priority: 'Low',
                status: 'Completed',
                createdAt: '2025-01-22'
            }
            // 可以添加更多示例数据
        ];
    }
}

// 导出全局实例
window.HorizontalTableManager = HorizontalTableManager;
window.horizontalTableManager = new HorizontalTableManager();

// 与现有系统集成
window.renderTable = function() {
    // 这里可以从API获取真实数据
    const sampleData = window.horizontalTableManager.getSampleData();
    window.horizontalTableManager.setData(sampleData);
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他组件加载完成
    setTimeout(() => {
        window.renderTable();
    }, 500);
});

console.log('📊 横向切换表格管理器已加载');
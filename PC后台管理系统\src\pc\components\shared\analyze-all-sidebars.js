/**
 * 全面分析所有页面侧边栏情况的脚本
 * 不进行修改，只进行分析和报告
 */

const fs = require('fs');
const path = require('path');

// 标准侧边栏结构（来自project-center.html）
const STANDARD_STRUCTURE = {
    totalSections: 7,
    totalItems: 33,
    sections: [
        { title: "个人中心", items: 2, expectedItems: ["my-todos.html", "my-orders.html"] },
        { title: "业务管理", items: 7, expectedItems: ["design-products.html", "requirements-management.html", "design-center.html", "design-cases.html", "project-center.html", "construction-management.html", "construction-guide.html"] },
        { title: "商务中心", items: 4, expectedItems: ["products.html", "orders.html", "customer-management.html", "marketing-management.html"] },
        { title: "知识库", items: 6, expectedItems: ["design-knowledge.html", "delivery-knowledge.html", "electrical-delivery-knowledge.html", "market-knowledge.html", "installation-knowledge.html", "product-knowledge.html"] },
        { title: "系统工具", items: 7, expectedItems: ["api-tools.html", "erp-documentation.html", "system-settings.html", "user-management.html", "internal-permissions.html", "customer-permissions.html", "data-management.html"] },
        { title: "数据分析", items: 4, expectedItems: ["requirements-analytics.html", "project-analytics.html", "order-analytics.html", "customer-analytics.html"] },
        { title: "个人中心", items: 3, expectedItems: ["demo.html", "user-profile.html", "logout.html"] }
    ]
};

// 已知的文件重命名映射
const FILE_RENAMES = {
    "api-tester.html": "api-tools.html",
    "user-permissions.html": "internal-permissions.html", 
    "permissions.html": "customer-permissions.html",
    "settings.html": "system-settings.html",
    "requirements-analysis.html": "requirements-analytics.html",
    "一装ERP-API文档.html": "erp-documentation.html",
    "login.html": "logout.html"
};

// 知识库命名修正
const KNOWLEDGE_FIXES = {
    "交付知识库": "交底知识库",
    "安装知识库": "布线知识库"
};

// 分析单个页面
function analyzePage(filePath) {
    const fileName = path.basename(filePath);
    const analysis = {
        fileName,
        exists: false,
        hasSidebar: false,
        sidebarType: 'none',
        sections: 0,
        menuItems: 0,
        issues: [],
        details: {}
    };

    try {
        if (!fs.existsSync(filePath)) {
            analysis.issues.push('文件不存在');
            return analysis;
        }

        analysis.exists = true;
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否有侧边栏
        if (content.includes('nav-menu') || content.includes('sidebar')) {
            analysis.hasSidebar = true;
            
            // 统计菜单项数量
            const navItems = content.match(/class="nav-item"/g) || [];
            analysis.menuItems = navItems.length;
            
            // 统计模块数量
            const sections = content.match(/nav-section-title/g) || [];
            analysis.sections = sections.length;
            
            // 判断侧边栏类型
            if (analysis.sections >= 7 && analysis.menuItems >= 30) {
                analysis.sidebarType = 'standard';
            } else if (analysis.sections >= 4 && analysis.menuItems >= 15) {
                analysis.sidebarType = 'old-full';
            } else if (analysis.sections >= 1 && analysis.menuItems >= 3) {
                analysis.sidebarType = 'simplified';
            } else {
                analysis.sidebarType = 'minimal';
            }
            
            // 检查知识库命名问题
            if (content.includes('交付知识库')) {
                analysis.issues.push('使用错误的"交付知识库"命名');
            }
            if (content.includes('安装知识库')) {
                analysis.issues.push('使用错误的"安装知识库"命名');
            }
            if (content.includes('设计指导库')) {
                analysis.issues.push('包含不需要的"设计指导库"');
            }
            if (!content.includes('水电交底知识库')) {
                analysis.issues.push('缺少"水电交底知识库"');
            }
            
            // 检查旧文件链接
            Object.keys(FILE_RENAMES).forEach(oldFile => {
                if (content.includes(oldFile)) {
                    analysis.issues.push(`使用旧链接: ${oldFile}`);
                }
            });
            
            // 检查是否符合标准结构
            if (analysis.sections < STANDARD_STRUCTURE.totalSections) {
                analysis.issues.push(`模块数量不足: ${analysis.sections}/${STANDARD_STRUCTURE.totalSections}`);
            }
            if (analysis.menuItems < STANDARD_STRUCTURE.totalItems) {
                analysis.issues.push(`菜单项不足: ${analysis.menuItems}/${STANDARD_STRUCTURE.totalItems}`);
            }
            
            // 检查active状态
            const activeItems = content.match(/class="nav-item[^"]*active/g) || [];
            if (activeItems.length === 0) {
                analysis.issues.push('没有设置active状态');
            } else if (activeItems.length > 1) {
                analysis.issues.push('多个active状态');
            }
            
        } else {
            analysis.issues.push('没有侧边栏');
        }
        
    } catch (error) {
        analysis.issues.push(`读取错误: ${error.message}`);
    }
    
    return analysis;
}

// 分析所有页面
function analyzeAllPages() {
    console.log('🔍 开始全面分析所有页面的侧边栏情况...\n');
    
    const pagesDir = path.join(__dirname, '../pages');
    const results = {
        total: 0,
        analyzed: 0,
        byType: {
            standard: [],
            'old-full': [],
            simplified: [],
            minimal: [],
            none: []
        },
        issues: {
            knowledgeNaming: [],
            oldLinks: [],
            missingItems: [],
            noSidebar: [],
            other: []
        },
        summary: {}
    };
    
    // 获取所有HTML文件
    const files = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.html'))
        .sort();
    
    results.total = files.length;
    
    console.log(`📊 发现 ${files.length} 个HTML页面文件\n`);
    
    // 分析每个页面
    files.forEach(file => {
        const filePath = path.join(pagesDir, file);
        const analysis = analyzePage(filePath);
        
        if (analysis.exists) {
            results.analyzed++;
            results.byType[analysis.sidebarType].push(analysis);
            
            // 分类问题
            analysis.issues.forEach(issue => {
                if (issue.includes('知识库') || issue.includes('指导库')) {
                    results.issues.knowledgeNaming.push({ file, issue });
                } else if (issue.includes('旧链接')) {
                    results.issues.oldLinks.push({ file, issue });
                } else if (issue.includes('不足') || issue.includes('缺少')) {
                    results.issues.missingItems.push({ file, issue });
                } else if (issue.includes('没有侧边栏')) {
                    results.issues.noSidebar.push({ file, issue });
                } else {
                    results.issues.other.push({ file, issue });
                }
            });
        }
        
        // 输出单个页面分析结果
        console.log(`📄 ${file}`);
        console.log(`   类型: ${analysis.sidebarType} | 模块: ${analysis.sections} | 菜单项: ${analysis.menuItems}`);
        if (analysis.issues.length > 0) {
            console.log(`   问题: ${analysis.issues.slice(0, 2).join(', ')}${analysis.issues.length > 2 ? '...' : ''}`);
        }
        console.log('');
    });
    
    return results;
}

// 生成分析报告
function generateReport(results) {
    console.log('\n📊 全面分析报告');
    console.log('='.repeat(60));
    
    // 基本统计
    console.log(`\n📈 基本统计:`);
    console.log(`   总页面数: ${results.total}`);
    console.log(`   已分析: ${results.analyzed}`);
    console.log(`   分析率: ${(results.analyzed/results.total*100).toFixed(1)}%`);
    
    // 按类型分布
    console.log(`\n📋 侧边栏类型分布:`);
    Object.keys(results.byType).forEach(type => {
        const count = results.byType[type].length;
        const percentage = (count/results.analyzed*100).toFixed(1);
        console.log(`   ${type}: ${count}个 (${percentage}%)`);
    });
    
    // 问题统计
    console.log(`\n❌ 问题统计:`);
    Object.keys(results.issues).forEach(category => {
        const count = results.issues[category].length;
        if (count > 0) {
            console.log(`   ${category}: ${count}个问题`);
        }
    });
    
    // 详细问题列表
    console.log(`\n🔍 详细问题分析:`);
    
    if (results.issues.knowledgeNaming.length > 0) {
        console.log(`\n   知识库命名问题 (${results.issues.knowledgeNaming.length}个):`);
        results.issues.knowledgeNaming.slice(0, 5).forEach(item => {
            console.log(`     - ${item.file}: ${item.issue}`);
        });
    }
    
    if (results.issues.oldLinks.length > 0) {
        console.log(`\n   旧链接问题 (${results.issues.oldLinks.length}个):`);
        results.issues.oldLinks.slice(0, 5).forEach(item => {
            console.log(`     - ${item.file}: ${item.issue}`);
        });
    }
    
    if (results.issues.missingItems.length > 0) {
        console.log(`\n   结构不完整问题 (${results.issues.missingItems.length}个):`);
        results.issues.missingItems.slice(0, 5).forEach(item => {
            console.log(`     - ${item.file}: ${item.issue}`);
        });
    }
    
    // 推荐行动
    console.log(`\n🚀 推荐行动:`);
    
    const standardCount = results.byType.standard.length;
    const needsUpdateCount = results.analyzed - standardCount;
    
    if (standardCount === 0) {
        console.log(`   ❌ 没有页面符合标准结构，需要全面更新`);
    } else if (standardCount < results.analyzed * 0.2) {
        console.log(`   ⚠️  只有${standardCount}个页面符合标准，建议批量更新`);
    } else if (standardCount < results.analyzed * 0.8) {
        console.log(`   🔧 ${standardCount}个页面符合标准，继续更新剩余${needsUpdateCount}个页面`);
    } else {
        console.log(`   ✅ 大部分页面(${standardCount}个)已符合标准，完善剩余${needsUpdateCount}个页面`);
    }
    
    return results;
}

// 主函数
function main() {
    const results = analyzeAllPages();
    generateReport(results);
    
    // 保存详细结果到文件
    const outputPath = path.join(__dirname, '../../docs/sidebar-analysis-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 详细分析结果已保存到: ${outputPath}`);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    analyzePage,
    analyzeAllPages,
    generateReport,
    STANDARD_STRUCTURE,
    FILE_RENAMES,
    KNOWLEDGE_FIXES
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../design-system/design-tokens.css">
    <link rel="stylesheet" href="../../../design-system/components.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item active">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">订单管理</h1>
                            <p class="breadcrumb-description">管理客户订单和交易记录</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-body">
                <!-- 统计卡片 -->
                <div class="flex gap-6 mb-8">
                    <div class="card flex-1">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-orders">0</div>
                                    <div class="text-sm text-secondary">总订单数</div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-shopping-cart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card flex-1">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-amount">¥0</div>
                                    <div class="text-sm text-secondary">总金额</div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card flex-1">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="pending-orders">0</div>
                                    <div class="text-sm text-secondary">待处理</div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card flex-1">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="completed-orders">0</div>
                                    <div class="text-sm text-secondary">已完成</div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和搜索 -->
                <div class="card mb-6">
                    <div class="card-body">
                        <div class="flex gap-4 items-end">
                            <div class="form-group mb-0">
                                <label class="form-label">搜索订单</label>
                                <input type="text" class="form-control" id="search-input" placeholder="输入订单号、客户姓名或手机号">
                            </div>

                            <div class="form-group mb-0">
                                <label class="form-label">订单状态</label>
                                <select class="form-control" id="status-filter">
                                    <option value="">全部状态</option>
                                    <option value="pending">待处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="completed">已完成</option>
                                    <option value="cancelled">已取消</option>
                                </select>
                            </div>

                            <div class="form-group mb-0">
                                <label class="form-label">日期范围</label>
                                <input type="date" class="form-control" id="date-from" style="width: 150px;">
                            </div>

                            <div class="form-group mb-0">
                                <label class="form-label">&nbsp;</label>
                                <input type="date" class="form-control" id="date-to" style="width: 150px;">
                            </div>

                            <button class="btn btn-primary" onclick="searchOrders()">
                                <i class="fas fa-search"></i> 搜索
                            </button>

                            <button class="btn btn-secondary" onclick="resetFilters()">
                                <i class="fas fa-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 订单表格 -->
                <div class="card">
                    <div class="card-header">
                        <div class="flex justify-between items-center">
                            <h3 class="card-title">订单列表</h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm" onclick="exportOrders()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="refreshOrders()">
                                    <i class="fas fa-refresh"></i> 刷新
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="ordersPage.openAddModal()">
                                    <i class="fas fa-plus"></i> 新增订单
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table" id="orders-table">
                            <thead>
                                <tr>
                                    <th data-sort="order_number">订单编号 <i class="fas fa-sort"></i></th>
                                    <th data-sort="customer_name">客户信息</th>
                                    <th data-sort="total_amount">订单金额 <i class="fas fa-sort"></i></th>
                                    <th data-sort="status">状态</th>
                                    <th data-sort="created_at">创建时间 <i class="fas fa-sort"></i></th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="orders-tbody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="card-footer">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-secondary">
                                显示第 <span id="page-start">0</span> - <span id="page-end">0</span> 条，共 <span id="total-count">0</span> 条记录
                            </div>
                            <div class="flex gap-2" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增订单模态框 -->
    <div id="add-order-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增订单</h3>
                <span class="close" onclick="ds.closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-order-form">
                    <div class="form-group">
                        <label for="new-customer-name">客户姓名 *</label>
                        <input type="text" id="new-customer-name" required placeholder="请输入客户姓名">
                    </div>
                    <div class="form-group">
                        <label for="new-customer-phone">联系电话 *</label>
                        <input type="tel" id="new-customer-phone" required placeholder="请输入联系电话">
                    </div>
                    <div class="form-group">
                        <label for="new-order-type">订单类型 *</label>
                        <select id="new-order-type" required>
                            <option value="">请选择订单类型</option>
                            <option value="智能家居订单">智能家居订单</option>
                            <option value="安防系统订单">安防系统订单</option>
                            <option value="智能照明订单">智能照明订单</option>
                            <option value="智能门锁订单">智能门锁订单</option>
                            <option value="监控设备订单">监控设备订单</option>
                            <option value="其他订单">其他订单</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="new-subtotal">商品小计 *</label>
                        <input type="number" id="new-subtotal" required min="0" step="0.01" placeholder="请输入商品小计金额">
                    </div>
                    <div class="form-group">
                        <label for="new-shipping-fee">运费</label>
                        <input type="number" id="new-shipping-fee" min="0" step="0.01" value="0" placeholder="请输入运费">
                    </div>
                    <div class="form-group">
                        <label for="new-discount-amount">优惠金额</label>
                        <input type="number" id="new-discount-amount" min="0" step="0.01" value="0" placeholder="请输入优惠金额">
                    </div>
                    <div class="form-group">
                        <label for="new-order-notes">备注</label>
                        <textarea id="new-order-notes" placeholder="请输入订单备注（可选）"></textarea>
                    </div>
                    <div class="form-group">
                        <label>订单总金额</label>
                        <div id="total-amount-display" class="total-amount-display">¥0.00</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="ds.closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="ordersPage.createOrder()">创建订单</button>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal" id="order-detail-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">订单详情</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="order-detail-content">
                <!-- 订单详情内容将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="ds.closeModal()">关闭</button>
                <button class="btn btn-primary" onclick="updateOrderStatus()">更新状态</button>
            </div>
        </div>
    </div>

    <!-- 编辑订单模态框 -->
    <div class="modal" id="edit-order-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">编辑订单</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="edit-order-form" data-form-validate>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label required">客户姓名</label>
                        <input type="text" class="form-control" id="edit-customer-name" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">联系电话</label>
                        <input type="tel" class="form-control" id="edit-customer-phone" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">订单状态</label>
                        <select class="form-control" id="edit-order-status" required>
                            <option value="pending">待处理</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="edit-order-notes" rows="3" placeholder="订单备注信息"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="ds.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 设计系统模拟 (简化版)
        const ds = {
            showLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0.5';
                    element.style.pointerEvents = 'none';
                }
            },
            hideLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '1';
                    element.style.pointerEvents = 'auto';
                }
            },
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            openModal: (modalId) => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex';
                    setTimeout(() => modal.classList.add('modal-open'), 10);
                }
            },
            closeModal: () => {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.classList.remove('modal-open');
                    setTimeout(() => modal.style.display = 'none', 300);
                });
            },
            formatDate: (dateString, format = 'YYYY-MM-DD HH:mm') => {
                if (!dateString) return '';
                const date = new Date(dateString);
                if (format === 'YYYY-MM-DD') {
                    return date.toLocaleDateString('zh-CN');
                } else if (format === 'HH:mm:ss') {
                    return date.toLocaleTimeString('zh-CN');
                }
                return date.toLocaleString('zh-CN');
            },
            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // 订单管理页面逻辑
        class OrdersPage {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 10;
                this.totalCount = 0;
                this.currentFilters = {};
                this.currentOrderId = null;
                this.orders = [];

                this.init();
            }

            init() {
                this.loadData();
                this.loadOrders();
                this.bindEvents();
                this.updateStatistics();
            }

            // 数据管理
            loadData() {
                // 加载订单数据
                this.orders = JSON.parse(localStorage.getItem('orders') || '[]');

                // 初始化默认数据
                if (this.orders.length === 0) {
                    this.orders = [
                        {
                            id: 1,
                            order_number: 'ORD20250730001',
                            customer_name: '张三',
                            customer_phone: '13800138001',
                            total_amount: 25000,
                            subtotal: 23000,
                            shipping_fee: 200,
                            discount_amount: 200,
                            status: 'pending',
                            order_type: '智能家居订单',
                            items_count: 3,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                            notes: '客户要求尽快发货',
                            items: [
                                {
                                    product_name: '智能门锁',
                                    specification: '指纹+密码',
                                    unit_price: 1200,
                                    quantity: 2,
                                    subtotal: 2400
                                },
                                {
                                    product_name: '智能摄像头',
                                    specification: '1080P高清',
                                    unit_price: 800,
                                    quantity: 3,
                                    subtotal: 2400
                                },
                                {
                                    product_name: '智能开关',
                                    specification: '三位开关',
                                    unit_price: 150,
                                    quantity: 12,
                                    subtotal: 1800
                                }
                            ],
                            statusHistory: [
                                {
                                    from: null,
                                    to: 'pending',
                                    updateTime: new Date().toISOString(),
                                    operator: 'system'
                                }
                            ]
                        },
                        {
                            id: 2,
                            order_number: 'ORD20250730002',
                            customer_name: '李四',
                            customer_phone: '13800138002',
                            total_amount: 15800,
                            subtotal: 15000,
                            shipping_fee: 100,
                            discount_amount: 300,
                            status: 'processing',
                            order_type: '安防系统订单',
                            items_count: 2,
                            created_at: new Date(Date.now() - 86400000).toISOString(),
                            updated_at: new Date(Date.now() - 3600000).toISOString(),
                            notes: '安装地址：上海市浦东新区xxx路xxx号',
                            items: [
                                {
                                    product_name: '监控系统套装',
                                    specification: '8路NVR+4个摄像头',
                                    unit_price: 3500,
                                    quantity: 1,
                                    subtotal: 3500
                                },
                                {
                                    product_name: '智能报警器',
                                    specification: '无线红外',
                                    unit_price: 300,
                                    quantity: 4,
                                    subtotal: 1200
                                }
                            ],
                            statusHistory: [
                                {
                                    from: null,
                                    to: 'pending',
                                    updateTime: new Date(Date.now() - 86400000).toISOString(),
                                    operator: 'system'
                                },
                                {
                                    from: 'pending',
                                    to: 'processing',
                                    updateTime: new Date(Date.now() - 3600000).toISOString(),
                                    operator: 'admin'
                                }
                            ]
                        },
                        {
                            id: 3,
                            order_number: 'ORD20250729001',
                            customer_name: '王五',
                            customer_phone: '13800138003',
                            total_amount: 8500,
                            subtotal: 8000,
                            shipping_fee: 50,
                            discount_amount: 50,
                            status: 'completed',
                            order_type: '智能照明订单',
                            items_count: 5,
                            created_at: new Date(Date.now() - 172800000).toISOString(),
                            updated_at: new Date(Date.now() - 86400000).toISOString(),
                            notes: '客户满意，已完成安装',
                            items: [
                                {
                                    product_name: '智能灯泡',
                                    specification: 'RGB调色',
                                    unit_price: 80,
                                    quantity: 10,
                                    subtotal: 800
                                },
                                {
                                    product_name: '智能调光开关',
                                    specification: '触摸式',
                                    unit_price: 200,
                                    quantity: 6,
                                    subtotal: 1200
                                }
                            ],
                            statusHistory: [
                                {
                                    from: null,
                                    to: 'pending',
                                    updateTime: new Date(Date.now() - 172800000).toISOString(),
                                    operator: 'system'
                                },
                                {
                                    from: 'pending',
                                    to: 'processing',
                                    updateTime: new Date(Date.now() - 129600000).toISOString(),
                                    operator: 'admin'
                                },
                                {
                                    from: 'processing',
                                    to: 'completed',
                                    updateTime: new Date(Date.now() - 86400000).toISOString(),
                                    operator: 'admin'
                                }
                            ]
                        }
                    ];
                    this.saveOrders();
                }
            }

            saveOrders() {
                localStorage.setItem('orders', JSON.stringify(this.orders));
            }

            getNextOrderId() {
                return this.orders.length > 0 ? Math.max(...this.orders.map(o => o.id)) + 1 : 1;
            }

            generateOrderNumber() {
                const today = new Date();
                const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
                const orderCount = this.orders.filter(o =>
                    o.order_number.includes(dateStr)
                ).length + 1;
                return `ORD${dateStr}${String(orderCount).padStart(3, '0')}`;
            }

            bindEvents() {
                // 搜索输入防抖
                const searchInput = document.getElementById('search-input');
                searchInput.addEventListener('input', ds.debounce(() => {
                    this.searchOrders();
                }, 500));

                // 状态筛选
                document.getElementById('status-filter').addEventListener('change', () => {
                    this.searchOrders();
                });

                // 编辑订单表单提交
                document.getElementById('edit-order-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveOrderChanges();
                });
            }

            loadOrders(page = 1) {
                ds.showLoading('#orders-table');

                // 应用筛选条件
                let filteredOrders = [...this.orders];

                // 搜索筛选
                if (this.currentFilters.search) {
                    const searchTerm = this.currentFilters.search.toLowerCase();
                    filteredOrders = filteredOrders.filter(order =>
                        order.order_number.toLowerCase().includes(searchTerm) ||
                        order.customer_name.toLowerCase().includes(searchTerm) ||
                        (order.customer_phone && order.customer_phone.includes(searchTerm))
                    );
                }

                // 状态筛选
                if (this.currentFilters.status) {
                    filteredOrders = filteredOrders.filter(order =>
                        order.status === this.currentFilters.status
                    );
                }

                // 日期筛选
                if (this.currentFilters.date_from) {
                    const fromDate = new Date(this.currentFilters.date_from);
                    filteredOrders = filteredOrders.filter(order =>
                        new Date(order.created_at) >= fromDate
                    );
                }

                if (this.currentFilters.date_to) {
                    const toDate = new Date(this.currentFilters.date_to);
                    toDate.setHours(23, 59, 59, 999); // 包含当天
                    filteredOrders = filteredOrders.filter(order =>
                        new Date(order.created_at) <= toDate
                    );
                }

                // 按创建时间倒序排列
                filteredOrders.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

                // 分页处理
                this.totalCount = filteredOrders.length;
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const startIndex = (page - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageOrders = filteredOrders.slice(startIndex, endIndex);

                this.renderOrders(pageOrders);
                this.renderPagination({
                    current_page: page,
                    total_pages: totalPages,
                    total: this.totalCount
                });

                setTimeout(() => {
                    ds.hideLoading('#orders-table');
                }, 300);
            }

            renderOrders(orders) {
                const tbody = document.getElementById('orders-tbody');

                if (orders.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="text-center text-secondary" style="padding: 40px;">
                                <i class="fas fa-inbox fa-3x mb-4" style="display: block; opacity: 0.3;"></i>
                                暂无订单数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = orders.map(order => `
                    <tr>
                        <td>
                            <div class="font-medium">${order.order_number}</div>
                            <div class="text-xs text-secondary">${order.order_type || '普通订单'}</div>
                        </td>
                        <td>
                            <div class="font-medium">${order.customer_name}</div>
                            <div class="text-xs text-secondary">${order.customer_phone || ''}</div>
                        </td>
                        <td>
                            <div class="font-medium">¥${this.formatAmount(order.total_amount)}</div>
                            <div class="text-xs text-secondary">${order.items_count || 0} 件商品</div>
                        </td>
                        <td>
                            <span class="badge badge-${this.getStatusColor(order.status)}">
                                ${this.getStatusText(order.status)}
                            </span>
                        </td>
                        <td>
                            <div>${ds.formatDate(order.created_at, 'YYYY-MM-DD')}</div>
                            <div class="text-xs text-secondary">${ds.formatDate(order.created_at, 'HH:mm:ss')}</div>
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm" onclick="ordersPage.viewOrder('${order.id}')">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="ordersPage.editOrder('${order.id}')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="ordersPage.deleteOrder('${order.id}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }

            renderPagination(pagination) {
                const paginationEl = document.getElementById('pagination');
                const { current_page = 1, total_pages = 1, total = 0 } = pagination;

                // 更新统计信息
                const start = (current_page - 1) * this.pageSize + 1;
                const end = Math.min(current_page * this.pageSize, total);

                document.getElementById('page-start').textContent = total > 0 ? start : 0;
                document.getElementById('page-end').textContent = end;
                document.getElementById('total-count').textContent = total;

                // 生成分页按钮
                let paginationHTML = '';

                // 上一页
                if (current_page > 1) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="ordersPage.loadOrders(${current_page - 1})">上一页</button>`;
                }

                // 页码按钮
                const startPage = Math.max(1, current_page - 2);
                const endPage = Math.min(total_pages, current_page + 2);

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === current_page ? 'btn-primary' : 'btn-outline';
                    paginationHTML += `<button class="btn ${isActive} btn-sm" onclick="ordersPage.loadOrders(${i})">${i}</button>`;
                }

                // 下一页
                if (current_page < total_pages) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="ordersPage.loadOrders(${current_page + 1})">下一页</button>`;
                }

                paginationEl.innerHTML = paginationHTML;
                this.currentPage = current_page;
            }

            updateStatistics() {
                const totalOrders = this.orders.length;
                const totalAmount = this.orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
                const pendingOrders = this.orders.filter(order => order.status === 'pending').length;
                const completedOrders = this.orders.filter(order => order.status === 'completed').length;

                const totalOrdersEl = document.getElementById('total-orders');
                const totalAmountEl = document.getElementById('total-amount');
                const pendingOrdersEl = document.getElementById('pending-orders');
                const completedOrdersEl = document.getElementById('completed-orders');

                if (totalOrdersEl) totalOrdersEl.textContent = totalOrders;
                if (totalAmountEl) totalAmountEl.textContent = `¥${this.formatAmount(totalAmount)}`;
                if (pendingOrdersEl) pendingOrdersEl.textContent = pendingOrders;
                if (completedOrdersEl) completedOrdersEl.textContent = completedOrders;
            }

            viewOrder(orderId) {
                const order = this.orders.find(o => o.id === parseInt(orderId));
                if (!order) {
                    ds.showToast('订单不存在', 'error');
                    return;
                }

                this.renderOrderDetail(order);
                this.currentOrderId = orderId;
                ds.openModal('order-detail-modal');
            }

            renderOrderDetail(order) {
                const content = document.getElementById('order-detail-content');
                content.innerHTML = `
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold mb-4">基本信息</h4>
                            <div class="space-y-3">
                                <div><span class="text-secondary">订单编号：</span>${order.order_number}</div>
                                <div><span class="text-secondary">客户姓名：</span>${order.customer_name}</div>
                                <div><span class="text-secondary">联系电话：</span>${order.customer_phone || '未提供'}</div>
                                <div><span class="text-secondary">订单状态：</span>
                                    <span class="badge badge-${this.getStatusColor(order.status)}">
                                        ${this.getStatusText(order.status)}
                                    </span>
                                </div>
                                <div><span class="text-secondary">创建时间：</span>${ds.formatDate(order.created_at)}</div>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-semibold mb-4">订单金额</h4>
                            <div class="space-y-3">
                                <div><span class="text-secondary">商品总额：</span>¥${this.formatAmount(order.subtotal || 0)}</div>
                                <div><span class="text-secondary">运费：</span>¥${this.formatAmount(order.shipping_fee || 0)}</div>
                                <div><span class="text-secondary">优惠金额：</span>-¥${this.formatAmount(order.discount_amount || 0)}</div>
                                <div class="font-semibold"><span class="text-secondary">订单总额：</span>¥${this.formatAmount(order.total_amount)}</div>
                            </div>
                        </div>
                    </div>

                    ${order.items && order.items.length > 0 ? `
                        <div class="mt-6">
                            <h4 class="font-semibold mb-4">订单商品</h4>
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>商品名称</th>
                                            <th>规格</th>
                                            <th>单价</th>
                                            <th>数量</th>
                                            <th>小计</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${order.items.map(item => `
                                            <tr>
                                                <td>${item.product_name}</td>
                                                <td>${item.specification || '-'}</td>
                                                <td>¥${this.formatAmount(item.unit_price)}</td>
                                                <td>${item.quantity}</td>
                                                <td>¥${this.formatAmount(item.subtotal)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    ` : ''}

                    ${order.notes ? `
                        <div class="mt-6">
                            <h4 class="font-semibold mb-2">订单备注</h4>
                            <div class="bg-secondary p-4 rounded">${order.notes}</div>
                        </div>
                    ` : ''}
                `;
            }

            editOrder(orderId) {
                const order = this.orders.find(o => o.id === parseInt(orderId));
                if (!order) {
                    ds.showToast('订单不存在', 'error');
                    return;
                }

                // 填充表单
                document.getElementById('edit-customer-name').value = order.customer_name || '';
                document.getElementById('edit-customer-phone').value = order.customer_phone || '';
                document.getElementById('edit-order-status').value = order.status || '';
                document.getElementById('edit-order-notes').value = order.notes || '';

                this.currentOrderId = orderId;
                ds.openModal('edit-order-modal');
            }

            saveOrderChanges() {
                const form = document.getElementById('edit-order-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const orderIndex = this.orders.findIndex(o => o.id === parseInt(this.currentOrderId));
                if (orderIndex === -1) {
                    ds.showToast('订单不存在', 'error');
                    return;
                }

                const oldStatus = this.orders[orderIndex].status;
                const newStatus = document.getElementById('edit-order-status').value;

                // 更新订单信息
                this.orders[orderIndex] = {
                    ...this.orders[orderIndex],
                    customer_name: document.getElementById('edit-customer-name').value,
                    customer_phone: document.getElementById('edit-customer-phone').value,
                    status: newStatus,
                    notes: document.getElementById('edit-order-notes').value,
                    updated_at: new Date().toISOString()
                };

                // 如果状态发生变化，记录状态变更历史
                if (oldStatus !== newStatus) {
                    if (!this.orders[orderIndex].statusHistory) {
                        this.orders[orderIndex].statusHistory = [];
                    }
                    this.orders[orderIndex].statusHistory.push({
                        from: oldStatus,
                        to: newStatus,
                        updateTime: new Date().toISOString(),
                        operator: 'admin'
                    });
                }

                this.saveOrders();

                ds.showToast('订单更新成功', 'success');
                ds.closeModal();
                this.loadOrders(this.currentPage);
                this.updateStatistics();

                // 重置表单
                form.reset();
            }

            searchOrders() {
                const searchTerm = document.getElementById('search-input').value.trim();
                const status = document.getElementById('status-filter').value;
                const dateFrom = document.getElementById('date-from').value;
                const dateTo = document.getElementById('date-to').value;

                this.currentFilters = {};

                if (searchTerm) {
                    this.currentFilters.search = searchTerm;
                }

                if (status) {
                    this.currentFilters.status = status;
                }

                if (dateFrom) {
                    this.currentFilters.date_from = dateFrom;
                }

                if (dateTo) {
                    this.currentFilters.date_to = dateTo;
                }

                this.loadOrders(1);
            }

            resetFilters() {
                document.getElementById('search-input').value = '';
                document.getElementById('status-filter').value = '';
                document.getElementById('date-from').value = '';
                document.getElementById('date-to').value = '';

                this.currentFilters = {};
                this.loadOrders(1);
            }

            exportOrders() {
                ds.showToast('正在导出订单数据...', 'info');

                // 应用当前筛选条件获取要导出的订单
                let exportOrders = [...this.orders];

                if (this.currentFilters.search) {
                    const searchTerm = this.currentFilters.search.toLowerCase();
                    exportOrders = exportOrders.filter(order =>
                        order.order_number.toLowerCase().includes(searchTerm) ||
                        order.customer_name.toLowerCase().includes(searchTerm) ||
                        (order.customer_phone && order.customer_phone.includes(searchTerm))
                    );
                }

                if (this.currentFilters.status) {
                    exportOrders = exportOrders.filter(order =>
                        order.status === this.currentFilters.status
                    );
                }

                if (this.currentFilters.date_from) {
                    const fromDate = new Date(this.currentFilters.date_from);
                    exportOrders = exportOrders.filter(order =>
                        new Date(order.created_at) >= fromDate
                    );
                }

                if (this.currentFilters.date_to) {
                    const toDate = new Date(this.currentFilters.date_to);
                    toDate.setHours(23, 59, 59, 999);
                    exportOrders = exportOrders.filter(order =>
                        new Date(order.created_at) <= toDate
                    );
                }

                // 生成CSV内容
                const headers = ['订单编号', '客户姓名', '联系电话', '订单金额', '订单状态', '创建时间', '备注'];
                const csvContent = [
                    headers.join(','),
                    ...exportOrders.map(order => [
                        order.order_number,
                        order.customer_name,
                        order.customer_phone || '',
                        order.total_amount,
                        this.getStatusText(order.status),
                        ds.formatDate(order.created_at),
                        (order.notes || '').replace(/,/g, '，') // 替换逗号避免CSV格式问题
                    ].join(','))
                ].join('\n');

                // 创建下载链接
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `orders_${new Date().toISOString().slice(0, 10)}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                ds.showToast(`订单数据导出成功 (${exportOrders.length}条记录)`, 'success');
            }

            refreshOrders() {
                this.loadOrders(this.currentPage);
                this.updateStatistics();
                ds.showToast('数据已刷新', 'success');
            }

            openAddModal() {
                // 重置表单
                document.getElementById('add-order-form').reset();
                document.getElementById('total-amount-display').textContent = '¥0.00';

                // 绑定金额计算事件
                this.bindAmountCalculation();

                ds.openModal('add-order-modal');
            }

            bindAmountCalculation() {
                const subtotalInput = document.getElementById('new-subtotal');
                const shippingInput = document.getElementById('new-shipping-fee');
                const discountInput = document.getElementById('new-discount-amount');
                const totalDisplay = document.getElementById('total-amount-display');

                const calculateTotal = () => {
                    const subtotal = parseFloat(subtotalInput.value) || 0;
                    const shipping = parseFloat(shippingInput.value) || 0;
                    const discount = parseFloat(discountInput.value) || 0;
                    const total = subtotal + shipping - discount;
                    totalDisplay.textContent = `¥${total.toFixed(2)}`;
                };

                subtotalInput.addEventListener('input', calculateTotal);
                shippingInput.addEventListener('input', calculateTotal);
                discountInput.addEventListener('input', calculateTotal);
            }

            createOrder() {
                const form = document.getElementById('add-order-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const subtotal = parseFloat(document.getElementById('new-subtotal').value) || 0;
                const shippingFee = parseFloat(document.getElementById('new-shipping-fee').value) || 0;
                const discountAmount = parseFloat(document.getElementById('new-discount-amount').value) || 0;
                const totalAmount = subtotal + shippingFee - discountAmount;

                if (totalAmount < 0) {
                    ds.showToast('订单总金额不能为负数', 'error');
                    return;
                }

                const newOrder = {
                    id: this.getNextOrderId(),
                    order_number: this.generateOrderNumber(),
                    customer_name: document.getElementById('new-customer-name').value,
                    customer_phone: document.getElementById('new-customer-phone').value,
                    order_type: document.getElementById('new-order-type').value,
                    subtotal: subtotal,
                    shipping_fee: shippingFee,
                    discount_amount: discountAmount,
                    total_amount: totalAmount,
                    status: 'pending',
                    items_count: 1, // 默认1个商品，实际应该根据商品明细计算
                    notes: document.getElementById('new-order-notes').value,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    items: [], // 简化版本，实际应该有商品明细
                    statusHistory: [
                        {
                            from: null,
                            to: 'pending',
                            updateTime: new Date().toISOString(),
                            operator: 'admin'
                        }
                    ]
                };

                this.orders.unshift(newOrder); // 添加到开头
                this.saveOrders();

                ds.showToast('订单创建成功', 'success');
                ds.closeModal();
                this.loadOrders(1);
                this.updateStatistics();

                // 重置表单
                form.reset();
            }

            deleteOrder(orderId) {
                const order = this.orders.find(o => o.id === parseInt(orderId));
                if (!order) {
                    ds.showToast('订单不存在', 'error');
                    return;
                }

                if (confirm(`确定要删除订单"${order.order_number}"吗？此操作不可恢复。`)) {
                    this.orders = this.orders.filter(o => o.id !== parseInt(orderId));
                    this.saveOrders();

                    ds.showToast('订单删除成功', 'success');
                    this.loadOrders(this.currentPage);
                    this.updateStatistics();
                }
            }

            searchOrders() {
                const searchTerm = document.getElementById('search-orders').value.trim();
                const status = document.getElementById('status-filter').value;
                const dateFrom = document.getElementById('date-from').value;
                const dateTo = document.getElementById('date-to').value;

                this.currentFilters = {};

                if (searchTerm) this.currentFilters.search = searchTerm;
                if (status) this.currentFilters.status = status;
                if (dateFrom) this.currentFilters.date_from = dateFrom;
                if (dateTo) this.currentFilters.date_to = dateTo;

                this.loadOrders(1);
            }

            // 工具方法
            formatAmount(amount) {
                return Number(amount || 0).toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }

            getStatusColor(status) {
                const colors = {
                    pending: 'warning',
                    processing: 'info',
                    completed: 'success',
                    cancelled: 'error'
                };
                return colors[status] || 'info';
            }

            getStatusText(status) {
                const texts = {
                    pending: '待处理',
                    processing: '处理中',
                    completed: '已完成',
                    cancelled: '已取消'
                };
                return texts[status] || status;
            }
        }

        // 全局函数
        function searchOrders() {
            ordersPage.searchOrders();
        }

        function resetFilters() {
            ordersPage.resetFilters();
        }

        function exportOrders() {
            ordersPage.exportOrders();
        }

        function refreshOrders() {
            ordersPage.refreshOrders();
        }

        function updateOrderStatus() {
            // 在订单详情模态框中更新状态的逻辑
            const orderStatusSelect = document.getElementById('orderStatus');
            if (!orderStatusSelect) {
                ds.showToast('无法找到状态选择框', 'error');
                return;
            }

            const newStatus = orderStatusSelect.value;
            const currentOrderId = ordersPage.currentOrderId;

            if (!currentOrderId) {
                ds.showToast('未选择订单', 'error');
                return;
            }

            if (!newStatus) {
                ds.showToast('请选择新状态', 'error');
                return;
            }

            // 更新订单状态
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            const orderIndex = orders.findIndex(order => order.id === currentOrderId);

            if (orderIndex === -1) {
                ds.showToast('订单不存在', 'error');
                return;
            }

            // 记录状态变更历史
            const oldStatus = orders[orderIndex].status;
            orders[orderIndex].status = newStatus;
            orders[orderIndex].statusUpdateTime = new Date().toISOString();

            // 添加状态变更记录
            if (!orders[orderIndex].statusHistory) {
                orders[orderIndex].statusHistory = [];
            }
            orders[orderIndex].statusHistory.push({
                from: oldStatus,
                to: newStatus,
                updateTime: new Date().toISOString(),
                operator: 'admin'
            });

            // 保存更新
            localStorage.setItem('orders', JSON.stringify(orders));

            // 刷新订单列表
            ordersPage.refreshOrders();

            // 更新模态框显示
            ordersPage.updateOrderDetails(currentOrderId);

            ds.showToast(`订单状态已更新为: ${newStatus}`, 'success');
        }

        // 初始化页面
        const ordersPage = new OrdersPage();
    </script>

    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity var(--duration-normal) var(--ease-in-out);
        }

        .modal.modal-open {
            opacity: 1;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-overlay);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            z-index: 1;
            transform: scale(0.95);
            transition: transform var(--duration-normal) var(--ease-in-out);
        }

        .modal.modal-open .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
        }

        .modal-body {
            padding: var(--space-6);
        }

        .modal-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--border-primary);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
        }

        /* 网格布局 */
        .grid {
            display: grid;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .space-y-3 > * + * {
            margin-top: var(--space-3);
        }

        /* 表格排序图标 */
        th[data-sort] {
            cursor: pointer;
            user-select: none;
        }

        th[data-sort]:hover {
            background: var(--hover-bg);
        }

        th[data-sort] i {
            margin-left: var(--space-2);
            opacity: 0.5;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-content {
                margin: var(--space-4);
                max-width: calc(100vw - 2rem);
            }

            .grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-danger {
            background: #ef4444;
            color: #ffffff;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        .btn-outline {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #ffffff;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close {
            color: #6b7280;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: color 0.2s;
        }

        .close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .total-amount-display {
            font-size: 18px;
            font-weight: bold;
            color: #059669;
            padding: 12px;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            text-align: center;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>

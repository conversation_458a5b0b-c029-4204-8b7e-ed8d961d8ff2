<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示展示 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item active">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">演示展示</h1>
                            <p class="breadcrumb-description">系统功能演示和介绍</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->

    <div class="demo-layout">
        <!-- 演示导航 -->
        <header class="demo-header">
            <div class="demo-nav">
                <div class="demo-logo">
                    <div class="logo-icon"><i class="fas fa-home"></i></div>
                    <div>
                        <div class="logo-text">智能家居管理系统</div>
                        <div class="logo-subtitle">演示展示</div>
                    </div>
                </div>
                <nav class="demo-menu">
                    <a href="#overview" class="demo-menu-item active" onclick="showSection('overview')">系统概览</a>
                    <a href="#features" class="demo-menu-item" onclick="showSection('features')">功能特性</a>
                    <a href="#screenshots" class="demo-menu-item" onclick="showSection('screenshots')">界面截图</a>
                    <a href="#architecture" class="demo-menu-item" onclick="showSection('architecture')">技术架构</a>
                    <a href="#contact" class="demo-menu-item" onclick="showSection('contact')">联系我们</a>
                </nav>
                <div class="demo-actions">
                    <a href="logout.html" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> 进入系统
                    </a>
                </div>
            </div>
        </header>

        <!-- 演示内容 -->
        <main class="demo-content">
            <!-- 系统概览 -->
            <section class="demo-section active" id="overviewSection">
                <div class="hero-section">
                    <div class="hero-content">
                        <h1>智能家居管理系统</h1>
                        <p class="hero-subtitle">专业的智能家居项目管理平台，提供从设计到施工的全流程管理解决方案</p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <div class="stat-number">500+</div>
                                <div class="stat-label">项目案例</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1000+</div>
                                <div class="stat-label">用户数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">系统稳定性</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">技术支持</div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <a href="logout.html" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket"></i> 立即体验
                            </a>
                            <button class="btn btn-secondary btn-lg" onclick="showSection('features')">
                                <i class="fas fa-info-circle"></i> 了解更多
                            </button>
                        </div>
                    </div>
                    <div class="hero-image">
                        <div class="demo-dashboard">
                            <div class="dashboard-header">
                                <div class="dashboard-title">管理仪表板</div>
                                <div class="dashboard-stats">
                                    <div class="mini-stat">
                                        <div class="mini-stat-value">156</div>
                                        <div class="mini-stat-label">项目</div>
                                    </div>
                                    <div class="mini-stat">
                                        <div class="mini-stat-value">89</div>
                                        <div class="mini-stat-label">用户</div>
                                    </div>
                                    <div class="mini-stat">
                                        <div class="mini-stat-value">¥2.5M</div>
                                        <div class="mini-stat-label">收入</div>
                                    </div>
                                </div>
                            </div>
                            <div class="dashboard-chart">
                                <div class="chart-placeholder">
                                    <i class="fas fa-chart-line"></i>
                                    <span>实时数据图表</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 功能特性 -->
            <section class="demo-section" id="featuresSection">
                <div class="section-header">
                    <h2>核心功能特性</h2>
                    <p>全面覆盖智能家居项目管理的各个环节</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-drafting-compass"></i>
                        </div>
                        <h3>设计管理</h3>
                        <p>专业的设计方案管理，支持效果图、平面图、设备清单等全套设计文档管理</p>
                        <ul>
                            <li>户型优化设计</li>
                            <li>3D效果图制作</li>
                            <li>设备选型配置</li>
                            <li>设计方案版本控制</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <h3>施工管理</h3>
                        <p>完整的施工流程管理，从交底到售后的6个阶段全程跟踪</p>
                        <ul>
                            <li>施工进度跟踪</li>
                            <li>质量检查记录</li>
                            <li>人员调度管理</li>
                            <li>材料使用监控</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>商务管理</h3>
                        <p>集成的商务管理系统，涵盖商品、订单、客户和营销全流程</p>
                        <ul>
                            <li>智能设备商城</li>
                            <li>订单全程跟踪</li>
                            <li>客户关系管理</li>
                            <li>营销活动策划</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>权限管理</h3>
                        <p>灵活的权限管理体系，支持多角色、多层级的权限控制</p>
                        <ul>
                            <li>角色权限配置</li>
                            <li>项目空间隔离</li>
                            <li>操作日志审计</li>
                            <li>安全访问控制</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3>数据分析</h3>
                        <p>强大的数据分析功能，提供业务洞察和决策支持</p>
                        <ul>
                            <li>实时数据监控</li>
                            <li>业务趋势分析</li>
                            <li>财务报表生成</li>
                            <li>性能指标跟踪</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>移动端支持</h3>
                        <p>完整的移动端解决方案，随时随地管理项目</p>
                        <ul>
                            <li>微信小程序</li>
                            <li>H5响应式设计</li>
                            <li>离线数据同步</li>
                            <li>实时消息推送</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 界面截图 -->
            <section class="demo-section" id="screenshotsSection">
                <div class="section-header">
                    <h2>系统界面展示</h2>
                    <p>直观的用户界面，简洁高效的操作体验</p>
                </div>
                <div class="screenshots-gallery">
                    <div class="screenshot-item">
                        <div class="screenshot-image">
                            <div class="screenshot-placeholder">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>管理仪表板</span>
                            </div>
                        </div>
                        <div class="screenshot-info">
                            <h4>管理仪表板</h4>
                            <p>实时数据展示，一目了然的业务概况</p>
                        </div>
                    </div>
                    <div class="screenshot-item">
                        <div class="screenshot-image">
                            <div class="screenshot-placeholder">
                                <i class="fas fa-project-diagram"></i>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="screenshot-info">
                            <h4>项目管理</h4>
                            <p>项目进度跟踪，团队协作管理</p>
                        </div>
                    </div>
                    <div class="screenshot-item">
                        <div class="screenshot-image">
                            <div class="screenshot-placeholder">
                                <i class="fas fa-shopping-bag"></i>
                                <span>商品管理</span>
                            </div>
                        </div>
                        <div class="screenshot-info">
                            <h4>商品管理</h4>
                            <p>智能设备库存，价格策略管理</p>
                        </div>
                    </div>
                    <div class="screenshot-item">
                        <div class="screenshot-image">
                            <div class="screenshot-placeholder">
                                <i class="fas fa-user-friends"></i>
                                <span>客户管理</span>
                            </div>
                        </div>
                        <div class="screenshot-info">
                            <h4>客户管理</h4>
                            <p>客户信息维护，服务历史跟踪</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 技术架构 -->
            <section class="demo-section" id="architectureSection">
                <div class="section-header">
                    <h2>技术架构</h2>
                    <p>现代化的技术栈，保证系统的稳定性和扩展性</p>
                </div>
                <div class="architecture-content">
                    <div class="tech-stack">
                        <div class="tech-category">
                            <h4>前端技术</h4>
                            <div class="tech-items">
                                <span class="tech-item">HTML5/CSS3</span>
                                <span class="tech-item">JavaScript ES6+</span>
                                <span class="tech-item">Chart.js</span>
                                <span class="tech-item">响应式设计</span>
                            </div>
                        </div>
                        <div class="tech-category">
                            <h4>后端技术</h4>
                            <div class="tech-items">
                                <span class="tech-item">Node.js</span>
                                <span class="tech-item">Express.js</span>
                                <span class="tech-item">RESTful API</span>
                                <span class="tech-item">JWT认证</span>
                            </div>
                        </div>
                        <div class="tech-category">
                            <h4>数据库</h4>
                            <div class="tech-items">
                                <span class="tech-item">MySQL</span>
                                <span class="tech-item">PostgreSQL</span>
                                <span class="tech-item">Redis缓存</span>
                                <span class="tech-item">数据备份</span>
                            </div>
                        </div>
                        <div class="tech-category">
                            <h4>部署运维</h4>
                            <div class="tech-items">
                                <span class="tech-item">Docker</span>
                                <span class="tech-item">CI/CD</span>
                                <span class="tech-item">负载均衡</span>
                                <span class="tech-item">监控告警</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 联系我们 -->
            <section class="demo-section" id="contactSection">
                <div class="section-header">
                    <h2>联系我们</h2>
                    <p>专业的技术团队，为您提供全方位的服务支持</p>
                </div>
                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>邮箱联系</h4>
                                <p><EMAIL></p>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h4>电话咨询</h4>
                                <p>************</p>
                                <p>工作时间：9:00-18:00</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h4>公司地址</h4>
                                <p>北京市朝阳区科技园区</p>
                                <p>智能家居大厦 8层</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-details">
                                <h4>服务时间</h4>
                                <p>7×24小时技术支持</p>
                                <p>快速响应，专业服务</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 演示页脚 -->
        <footer class="demo-footer">
            <div class="footer-content">
                <div class="footer-info">
                    <div class="footer-logo">
                        <div class="logo-icon"><i class="fas fa-home"></i></div>
                        <div class="logo-text">智能家居管理系统</div>
                    </div>
                    <p>专业的智能家居项目管理平台</p>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h5>产品功能</h5>
                        <a href="#features">设计管理</a>
                        <a href="#features">施工管理</a>
                        <a href="#features">商务管理</a>
                    </div>
                    <div class="link-group">
                        <h5>技术支持</h5>
                        <a href="#contact">技术文档</a>
                        <a href="#contact">API接口</a>
                        <a href="#contact">在线支持</a>
                    </div>
                    <div class="link-group">
                        <h5>关于我们</h5>
                        <a href="#contact">公司介绍</a>
                        <a href="#contact">联系方式</a>
                        <a href="#contact">合作伙伴</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 智能家居管理系统. 保留所有权利.</p>
            </div>
        </footer>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script>
        function showSection(sectionName) {
            // 隐藏所有section
            document.querySelectorAll('.demo-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 移除所有菜单项的active状态
            document.querySelectorAll('.demo-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示目标section
            document.getElementById(sectionName + 'Section').classList.add('active');
            
            // 添加对应菜单项的active状态
            event.target.classList.add('active');
            
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些动画效果
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有卡片元素
            document.querySelectorAll('.feature-card, .screenshot-item, .tech-category, .contact-item').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            console.log('智能家居管理系统演示页面已加载');
        });
    </script>
            </div>
        </main>
    </div>
</body>
</html>

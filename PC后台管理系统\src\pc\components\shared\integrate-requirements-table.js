/**
 * 整合design-requirements-table.html到requirements-management.html的脚本
 * 将表格版本的产品选择功能整合到需求管理主页面
 */

const fs = require('fs');
const path = require('path');

// 提取design-requirements-table.html中的关键内容
function extractTableContent() {
    const tablePath = path.join(__dirname, '../pages/design-requirements-table.html');
    const content = fs.readFileSync(tablePath, 'utf8');
    
    // 提取产品选择相关的样式
    const styleMatch = content.match(/<style>([\s\S]*?)<\/style>/);
    const styles = styleMatch ? styleMatch[1] : '';
    
    // 提取产品选择的HTML结构
    const productSelectionMatch = content.match(/<div id="product-selection"[\s\S]*?<\/div>\s*<\/div>/);
    const productSelectionHTML = productSelectionMatch ? productSelectionMatch[0] : '';
    
    // 提取JavaScript代码
    const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/);
    const scripts = scriptMatch ? scriptMatch[1] : '';
    
    return {
        styles,
        productSelectionHTML,
        scripts
    };
}

// 整合内容到requirements-management.html
function integrateContent() {
    console.log('🔧 开始整合design-requirements-table.html到requirements-management.html...\n');
    
    const requirementsPath = path.join(__dirname, '../pages/requirements-management.html');
    let content = fs.readFileSync(requirementsPath, 'utf8');
    
    // 提取table页面的内容
    const tableContent = extractTableContent();
    
    // 1. 添加产品选择相关样式
    const additionalStyles = `
        /* 产品选择模块样式 - 从design-requirements-table.html整合 */
        .product-selection-module {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 32px;
        }
        
        .product-tab-navigation {
            margin-bottom: 24px;
        }
        
        .product-tab-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .product-tab-btn {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .product-tab-btn:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .product-tab-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }
        
        .product-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .product-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .product-item.selected {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .product-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 12px;
        }
        
        .product-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1f2937;
        }
        
        .product-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .selected-products {
            margin-top: 24px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .selected-products h4 {
            margin-bottom: 12px;
            color: #1f2937;
        }
        
        .selected-product-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .selected-product-tag {
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .remove-product {
            cursor: pointer;
            font-weight: bold;
        }
        
        .remove-product:hover {
            color: #fca5a5;
        }`;
    
    // 在现有样式后添加新样式
    content = content.replace(
        /(\/\* 需求管理主页面样式 \*\/[\s\S]*?)<\/style>/,
        `$1${additionalStyles}\n        </style>`
    );
    
    // 2. 在模块网格后添加产品选择模块
    const productSelectionModule = `
                <!-- 产品选择模块 - 从design-requirements-table.html整合 -->
                <div class="product-selection-module">
                    <div class="module-header" style="margin-bottom: 24px;">
                        <h2 style="color: #1f2937; font-size: 20px; font-weight: 600;">智能家居产品选择</h2>
                        <p style="color: #6b7280; margin-top: 8px;">为客户需求选择合适的智能家居产品和场景</p>
                    </div>
                    
                    <!-- 产品分类Tab -->
                    <div class="product-tab-navigation">
                        <div class="product-tab-container">
                            <button class="product-tab-btn active" data-category="lighting" onclick="switchProductCategory('lighting')">
                                💡 智能照明
                            </button>
                            <button class="product-tab-btn" data-category="security" onclick="switchProductCategory('security')">
                                📷 监控类
                            </button>
                            <button class="product-tab-btn" data-category="custom" onclick="switchProductCategory('custom')">
                                🎨 定制场景
                            </button>
                            <button class="product-tab-btn" data-category="auto" onclick="switchProductCategory('auto')">
                                🤖 自动场景
                            </button>
                            <button class="product-tab-btn" data-category="safety" onclick="switchProductCategory('safety')">
                                🛡️ 安防类
                            </button>
                            <button class="product-tab-btn" data-category="audio" onclick="switchProductCategory('audio')">
                                🎵 智能影音
                            </button>
                            <button class="product-tab-btn" data-category="ai" onclick="switchProductCategory('ai')">
                                🗣️ AI语音
                            </button>
                            <button class="product-tab-btn" data-category="hotel" onclick="switchProductCategory('hotel')">
                                🏨 酒店民宿
                            </button>
                            <button class="product-tab-btn" data-category="business" onclick="switchProductCategory('business')">
                                🏢 商业场景
                            </button>
                        </div>
                    </div>
                    
                    <!-- 产品列表 -->
                    <div class="product-list" id="productList">
                        <!-- 产品将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 已选择的产品 -->
                    <div class="selected-products" id="selectedProducts" style="display: none;">
                        <h4>已选择的产品 (<span id="selectedCount">0</span>)</h4>
                        <div class="selected-product-list" id="selectedProductList">
                            <!-- 已选择的产品标签 -->
                        </div>
                    </div>
                </div>`;
    
    // 在模块网格后插入产品选择模块
    content = content.replace(
        /(<\/div>\s*<!-- 最近活动 -->)/,
        `${productSelectionModule}\n\n                $1`
    );
    
    // 3. 添加JavaScript功能
    const productSelectionScript = `
        // 产品选择功能 - 从design-requirements-table.html整合
        let selectedProducts = [];
        let currentCategory = 'lighting';
        
        // 产品数据
        const productData = {
            lighting: [
                {
                    id: 'lighting_1',
                    title: '客餐厅无主灯',
                    desc: '无主灯设计的筒灯、射灯、灯带、轨道灯组合，营造层次丰富的照明效果',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif'
                },
                {
                    id: 'lighting_2',
                    title: '客厅无主灯场景',
                    desc: '通过情景面板或语音控制场景切换，实现不同时段的照明需求',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif'
                },
                {
                    id: 'lighting_3',
                    title: '卧室场景',
                    desc: '温馨、助眠、起夜、阅读等不同场景变化，满足卧室多样化需求',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222987728772.gif'
                },
                {
                    id: 'lighting_4',
                    title: '夏冬场景照明',
                    desc: '四季如春的智能调光定制场景，根据季节自动调整色温和亮度',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207113660186018.gif'
                }
            ],
            security: [
                {
                    id: 'security_1',
                    title: '空气质量智能显示',
                    desc: '实时显示CO2、tVoc、PM2.5、温湿度等参数，守护家庭健康',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326103963896389.jpg'
                },
                {
                    id: 'security_2',
                    title: '中控屏监控视窗',
                    desc: '中控屏直接显示监控实时画面，随时掌握家中动态',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326110565366536.jpg'
                }
            ],
            // 其他分类数据可以根据需要添加
            custom: [],
            auto: [],
            safety: [],
            audio: [],
            ai: [],
            hotel: [],
            business: []
        };
        
        // 切换产品分类
        function switchProductCategory(category) {
            currentCategory = category;
            
            // 更新tab状态
            document.querySelectorAll('.product-tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(\`[data-category="\${category}"]\`).classList.add('active');
            
            // 渲染产品列表
            renderProductList(category);
        }
        
        // 渲染产品列表
        function renderProductList(category) {
            const productList = document.getElementById('productList');
            const products = productData[category] || [];
            
            if (products.length === 0) {
                productList.innerHTML = '<div style="text-align: center; padding: 40px; color: #6b7280;">该分类暂无产品</div>';
                return;
            }
            
            productList.innerHTML = products.map(product => \`
                <div class="product-item \${selectedProducts.includes(product.id) ? 'selected' : ''}" 
                     onclick="toggleProduct('\${product.id}', '\${product.title}')">
                    <img src="\${product.image}" alt="\${product.title}" class="product-image" 
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDMwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZCNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD4KPC9zdmc+'" />
                    <div class="product-title">\${product.title}</div>
                    <div class="product-desc">\${product.desc}</div>
                </div>
            \`).join('');
        }
        
        // 切换产品选择状态
        function toggleProduct(productId, productTitle) {
            const index = selectedProducts.indexOf(productId);
            if (index > -1) {
                selectedProducts.splice(index, 1);
            } else {
                selectedProducts.push(productId);
            }
            
            // 更新UI
            renderProductList(currentCategory);
            updateSelectedProducts();
        }
        
        // 更新已选择产品显示
        function updateSelectedProducts() {
            const selectedContainer = document.getElementById('selectedProducts');
            const selectedCount = document.getElementById('selectedCount');
            const selectedList = document.getElementById('selectedProductList');
            
            selectedCount.textContent = selectedProducts.length;
            
            if (selectedProducts.length === 0) {
                selectedContainer.style.display = 'none';
                return;
            }
            
            selectedContainer.style.display = 'block';
            
            // 获取选中产品的详细信息
            const selectedProductDetails = [];
            Object.values(productData).forEach(categoryProducts => {
                categoryProducts.forEach(product => {
                    if (selectedProducts.includes(product.id)) {
                        selectedProductDetails.push(product);
                    }
                });
            });
            
            selectedList.innerHTML = selectedProductDetails.map(product => \`
                <div class="selected-product-tag">
                    \${product.title}
                    <span class="remove-product" onclick="toggleProduct('\${product.id}', '\${product.title}')">×</span>
                </div>
            \`).join('');
        }
        
        // 页面加载时初始化产品选择
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('productList')) {
                renderProductList('lighting');
            }
        });`;
    
    // 在现有script标签前添加新的JavaScript
    content = content.replace(
        /(<script>[\s\S]*?<\/script>)/,
        `$1\n    <script>\n${productSelectionScript}\n    </script>`
    );
    
    return content;
}

// 执行整合
function performIntegration() {
    try {
        const integratedContent = integrateContent();
        
        // 保存整合后的文件
        const outputPath = path.join(__dirname, '../pages/requirements-management.html');
        fs.writeFileSync(outputPath, integratedContent, 'utf8');
        
        console.log('✅ 整合完成！');
        console.log('📄 已将design-requirements-table.html的产品选择功能整合到requirements-management.html');
        console.log('🎯 新增功能：');
        console.log('   - 智能家居产品选择模块');
        console.log('   - 9个产品分类标签页');
        console.log('   - 产品图片展示和选择');
        console.log('   - 已选择产品管理');
        console.log('   - 响应式产品网格布局');
        
        return true;
    } catch (error) {
        console.error('❌ 整合失败:', error.message);
        return false;
    }
}

// 主函数
function main() {
    console.log('🚀 开始整合design-requirements-table.html到requirements-management.html...\n');
    
    const success = performIntegration();
    
    if (success) {
        console.log('\n🎉 整合成功完成！');
        console.log('💡 建议：');
        console.log('   1. 测试产品选择功能是否正常工作');
        console.log('   2. 验证响应式布局在不同屏幕尺寸下的表现');
        console.log('   3. 检查图片加载和错误处理');
        console.log('   4. 确认已选择产品的管理功能');
    } else {
        console.log('\n❌ 整合失败，请检查错误信息');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    performIntegration,
    extractTableContent,
    integrateContent
};

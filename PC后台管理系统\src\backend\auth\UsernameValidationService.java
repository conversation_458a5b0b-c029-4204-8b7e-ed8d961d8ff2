package com.smarthome.auth.service;

import com.smarthome.auth.repository.UserRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 用户名验证服务
 * 提供用户名格式验证、唯一性检查和推荐生成功能
 */
@Service
@Slf4j
public class UsernameValidationService {

    @Autowired
    private UserRepository userRepository;

    // 用户名格式正则表达式
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,20}$");
    
    // 不能以数字开头
    private static final Pattern NO_DIGIT_START = Pattern.compile("^[a-zA-Z_].*");
    
    // 系统保留用户名
    private static final Set<String> RESERVED_USERNAMES = Set.of(
        "admin", "administrator", "root", "system", "user", "guest", "test", "demo",
        "api", "www", "mail", "ftp", "support", "help", "info", "contact",
        "service", "manager", "operator", "moderator", "null", "undefined",
        "smarthome", "smart_home", "home", "house", "device", "sensor"
    );

    /**
     * 验证用户名
     * 
     * @param username 用户名
     * @return 验证结果
     */
    public ValidationResult validateUsername(String username) {
        ValidationResult result = new ValidationResult();
        result.setUsername(username);
        
        try {
            // 1. 基础格式验证
            List<String> formatErrors = validateFormat(username);
            result.getErrors().addAll(formatErrors);
            
            // 2. 如果格式正确，检查唯一性
            if (formatErrors.isEmpty()) {
                result.setValid(true);
                
                // 检查是否已被使用
                boolean exists = userRepository.existsByUsername(username);
                result.setAvailable(!exists);
                
                if (exists) {
                    result.getErrors().add("用户名已被使用");
                }
            }
            
            log.debug("用户名验证结果，用户名: {}, 有效: {}, 可用: {}", 
                username, result.isValid(), result.isAvailable());
            
        } catch (Exception e) {
            log.error("验证用户名时发生错误", e);
            result.getErrors().add("验证过程中发生错误");
        }
        
        return result;
    }

    /**
     * 验证用户名格式
     * 
     * @param username 用户名
     * @return 错误列表
     */
    private List<String> validateFormat(String username) {
        List<String> errors = new ArrayList<>();
        
        if (username == null || username.trim().isEmpty()) {
            errors.add("用户名不能为空");
            return errors;
        }
        
        username = username.trim();
        
        // 长度检查
        if (username.length() < 3) {
            errors.add("用户名长度不能少于3位");
        }
        
        if (username.length() > 20) {
            errors.add("用户名长度不能超过20位");
        }
        
        // 字符格式检查
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            errors.add("用户名只能包含字母、数字和下划线");
        }
        
        // 不能以数字开头
        if (!NO_DIGIT_START.matcher(username).matches()) {
            errors.add("用户名不能以数字开头");
        }
        
        // 检查是否为保留用户名
        if (RESERVED_USERNAMES.contains(username.toLowerCase())) {
            errors.add("该用户名为系统保留，请选择其他用户名");
        }
        
        // 不能全是数字
        if (username.matches("^\\d+$")) {
            errors.add("用户名不能全是数字");
        }
        
        // 不能全是下划线
        if (username.matches("^_+$")) {
            errors.add("用户名不能全是下划线");
        }
        
        return errors;
    }

    /**
     * 生成用户名推荐
     * 
     * @param baseName 基础名称
     * @param email 邮箱
     * @param realName 真实姓名
     * @return 推荐的用户名列表
     */
    public List<String> generateUsernameSuggestions(String baseName, String email, String realName) {
        Set<String> suggestions = new LinkedHashSet<>();
        
        try {
            // 1. 基于基础名称生成
            if (baseName != null && !baseName.trim().isEmpty()) {
                generateFromBaseName(suggestions, baseName.trim());
            }
            
            // 2. 基于邮箱生成
            if (email != null && !email.trim().isEmpty()) {
                generateFromEmail(suggestions, email.trim());
            }
            
            // 3. 基于真实姓名生成
            if (realName != null && !realName.trim().isEmpty()) {
                generateFromRealName(suggestions, realName.trim());
            }
            
            // 4. 过滤掉已存在的用户名
            List<String> availableSuggestions = new ArrayList<>();
            for (String suggestion : suggestions) {
                if (availableSuggestions.size() >= 8) break; // 最多返回8个推荐
                
                ValidationResult result = validateUsername(suggestion);
                if (result.isValid() && result.isAvailable()) {
                    availableSuggestions.add(suggestion);
                }
            }
            
            log.info("生成用户名推荐，基础信息: {}, 推荐数量: {}", baseName, availableSuggestions.size());
            return availableSuggestions;
            
        } catch (Exception e) {
            log.error("生成用户名推荐时发生错误", e);
            return new ArrayList<>();
        }
    }

    /**
     * 基于基础名称生成推荐
     */
    private void generateFromBaseName(Set<String> suggestions, String baseName) {
        String cleanName = cleanString(baseName);
        if (cleanName.length() >= 3) {
            suggestions.add(cleanName);
            suggestions.add(cleanName + "123");
            suggestions.add(cleanName + "2024");
            suggestions.add("user_" + cleanName);
            suggestions.add(cleanName + "_home");
        }
    }

    /**
     * 基于邮箱生成推荐
     */
    private void generateFromEmail(Set<String> suggestions, String email) {
        if (email.contains("@")) {
            String localPart = email.substring(0, email.indexOf("@"));
            String cleanName = cleanString(localPart);
            
            if (cleanName.length() >= 3) {
                suggestions.add(cleanName);
                suggestions.add(cleanName + "123");
                suggestions.add("smart_" + cleanName);
                suggestions.add(cleanName + "_user");
            }
        }
    }

    /**
     * 基于真实姓名生成推荐
     */
    private void generateFromRealName(Set<String> suggestions, String realName) {
        // 处理中文姓名转拼音（简化处理）
        String cleanName = cleanString(realName);
        
        if (cleanName.length() >= 2) {
            suggestions.add(cleanName + "123");
            suggestions.add("user_" + cleanName);
            suggestions.add(cleanName + "_smart");
            suggestions.add("home_" + cleanName);
        }
        
        // 如果是英文名，直接使用
        if (realName.matches("^[a-zA-Z\\s]+$")) {
            String[] parts = realName.trim().split("\\s+");
            if (parts.length >= 2) {
                String firstName = cleanString(parts[0]);
                String lastName = cleanString(parts[parts.length - 1]);
                
                if (firstName.length() >= 1 && lastName.length() >= 1) {
                    suggestions.add(firstName + "_" + lastName);
                    suggestions.add(firstName + lastName);
                    suggestions.add(lastName + "_" + firstName);
                }
            }
        }
    }

    /**
     * 清理字符串，只保留字母数字下划线
     */
    private String cleanString(String input) {
        if (input == null) return "";
        
        // 移除特殊字符，只保留字母数字
        String cleaned = input.replaceAll("[^a-zA-Z0-9]", "");
        
        // 如果以数字开头，添加字母前缀
        if (cleaned.matches("^\\d.*")) {
            cleaned = "user" + cleaned;
        }
        
        // 限制长度
        if (cleaned.length() > 15) {
            cleaned = cleaned.substring(0, 15);
        }
        
        return cleaned.toLowerCase();
    }

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    public boolean isUsernameExists(String username) {
        try {
            return userRepository.existsByUsername(username);
        } catch (Exception e) {
            log.error("检查用户名是否存在时发生错误", e);
            return true; // 出错时返回true，避免重复用户名
        }
    }

    /**
     * 验证结果类
     */
    @Data
    public static class ValidationResult {
        private String username;
        private boolean valid = false;
        private boolean available = false;
        private List<String> errors = new ArrayList<>();
        
        public boolean isSuccess() {
            return valid && available && errors.isEmpty();
        }
    }
}

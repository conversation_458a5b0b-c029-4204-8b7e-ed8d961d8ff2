/**
 * 权限管理页面 - Ant Design Pro版本
 * 左侧角色管理列表 + 右侧权限配置面板（树形结构）
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  List,
  Avatar,
  Tree,
  Switch,
  Checkbox,
  Typography,
  Space,
  Tag,
  Divider,
  Button,
  message,
  Spin
} from 'antd';
import {
  UserOutlined,
  HomeOutlined,
  TeamOutlined,
  ToolOutlined,
  BulbOutlined,
  BuildOutlined,
  SettingOutlined,
  DesktopOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  CommentOutlined,
  ShareAltOutlined,
  DollarOutlined
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import { PageContainer } from '@ant-design/pro-components';

const { Title, Text, Paragraph } = Typography;

// 智能家居角色定义
const SMART_HOME_ROLES = {
  OWNER: {
    name: '业主/空间所有者',
    icon: <HomeOutlined style={{ color: '#2563eb' }} />,
    color: '#2563eb',
    description: '拥有项目空间的完全控制权，可以邀请其他成员，管理项目进度，审批设计方案和施工计划。',
    permissions: {
      project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE'],
      design: ['VIEW', 'COMMENT', 'APPROVE'],
      construction: ['VIEW', 'COMMENT', 'APPROVE'],
      cost: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE'],
      files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
      comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
      system: ['VIEW', 'CONFIGURE']
    }
  },
  FAMILY_MEMBER: {
    name: '家庭成员',
    icon: <TeamOutlined style={{ color: '#059669' }} />,
    color: '#059669',
    description: '由业主邀请，权限由业主设定和控制，可以参与项目讨论和进度查看。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  },
  HOME_DESIGNER: {
    name: '家装设计师',
    icon: <ToolOutlined style={{ color: '#dc2626' }} />,
    color: '#dc2626',
    description: '被邀请进项目空间参与设计工作，负责方案设计和图纸制作。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  },
  SMART_HOME_DESIGNER: {
    name: '智能家居设计师',
    icon: <BulbOutlined style={{ color: '#7c3aed' }} />,
    color: '#7c3aed',
    description: '不需要被邀请，可以在设计模块中看到用户所有的项目文件，提供专业的智能家居解决方案。',
    permissions: {
      project: ['VIEW', 'EDIT', 'COMMENT'],
      design: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
      comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
      system: ['VIEW', 'EDIT', 'CONFIGURE']
    }
  },
  CONSTRUCTOR: {
    name: '施工人员',
    icon: <BuildOutlined style={{ color: '#ea580c' }} />,
    color: '#ea580c',
    description: '被邀请参与施工阶段，由用户决定可以看到的内容，负责施工进度更新和现场反馈。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  }
};

// 权限模块定义
const PERMISSION_MODULES = {
  project: {
    name: '项目空间',
    icon: <HomeOutlined />,
    color: '#2563eb',
    subModules: {
      project_overview: { name: '项目概览', operations: ['VIEW', 'EDIT', 'COMMENT'] },
      member_management: { name: '成员管理', operations: ['VIEW', 'INVITE', 'MANAGE'] },
      project_progress: { name: '项目进度', operations: ['VIEW', 'EDIT', 'COMMENT'] },
      project_settings: { name: '项目设置', operations: ['VIEW', 'CONFIGURE'] }
    }
  },
  design: {
    name: '设计方案',
    icon: <DesktopOutlined />,
    color: '#dc2626',
    subModules: {
      requirement_collection: { name: '需求收集', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      design_schemes: { name: '方案列表', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      design_drawings: { name: '设计图纸', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      effect_renderings: { name: '3D效果图', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      smart_home_design: { name: '智能家居设计', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      lighting_design: { name: '灯光设计', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      material_list: { name: '材料清单', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      modification_records: { name: '修改记录', operations: ['VIEW', 'CREATE', 'COMMENT'] }
    }
  },
  construction: {
    name: '施工管理',
    icon: <BuildOutlined />,
    color: '#ea580c',
    subModules: {
      construction_plan: { name: '施工计划', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      progress_update: { name: '进度更新', operations: ['VIEW', 'UPDATE', 'COMMENT'] },
      site_photos: { name: '现场照片', operations: ['VIEW', 'UPLOAD', 'COMMENT'] },
      issue_tracking: { name: '问题记录', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      quality_inspection: { name: '质量检查', operations: ['VIEW', 'INSPECT', 'COMMENT'] },
      acceptance_management: { name: '验收管理', operations: ['VIEW', 'APPROVE', 'COMMENT'] }
    }
  },
  cost: {
    name: '预算成本',
    icon: <DollarOutlined />,
    color: '#059669',
    subModules: {
      budget_preparation: { name: '预算编制', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      quotation_management: { name: '报价管理', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      payment_plan: { name: '付款计划', operations: ['VIEW', 'EDIT', 'APPROVE'] },
      cost_analysis: { name: '成本分析', operations: ['VIEW', 'ANALYZE', 'COMMENT'] }
    }
  },
  files: {
    name: '文件管理',
    icon: <FileTextOutlined />,
    color: '#7c3aed',
    subModules: {
      document_management: { name: '文档管理', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'] },
      image_management: { name: '图片管理', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'] },
      contract_files: { name: '合同文件', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'MANAGE'] },
      template_files: { name: '模板文件', operations: ['VIEW', 'DOWNLOAD', 'MANAGE'] }
    }
  },
  comments: {
    name: '评论互动',
    icon: <CommentOutlined />,
    color: '#0891b2',
    subModules: {
      design_comments: { name: '设计评论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      construction_comments: { name: '施工评论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      team_discussion: { name: '团队讨论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      comment_notifications: { name: '评论通知', operations: ['VIEW', 'MANAGE'] }
    }
  },
  marketing: {
    name: '营销分享',
    icon: <ShareAltOutlined />,
    color: '#be185d',
    subModules: {
      share_management: { name: '分享管理', operations: ['VIEW', 'SHARE', 'MANAGE'] },
      reward_management: { name: '奖励管理', operations: ['VIEW', 'EARN_REWARDS', 'MANAGE'] },
      share_statistics: { name: '分享统计', operations: ['VIEW', 'VIEW_STATS', 'ANALYZE'] },
      share_templates: { name: '分享模板', operations: ['VIEW', 'CREATE', 'EDIT'] }
    }
  },
  system: {
    name: '系统管理',
    icon: <SettingOutlined />,
    color: '#374151',
    subModules: {
      system_settings: { name: '系统设置', operations: ['VIEW', 'EDIT', 'CONFIGURE'] },
      user_management: { name: '用户管理', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      role_management: { name: '角色管理', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      system_logs: { name: '系统日志', operations: ['VIEW', 'ANALYZE'] }
    }
  }
};

// 操作权限中文映射
const OPERATION_LABELS = {
  VIEW: '查看',
  CREATE: '创建',
  EDIT: '编辑',
  DELETE: '删除',
  COMMENT: '评论',
  APPROVE: '审批',
  INVITE: '邀请',
  MANAGE: '管理',
  CONFIGURE: '配置',
  UPDATE: '更新',
  UPLOAD: '上传',
  DOWNLOAD: '下载',
  INSPECT: '巡检',
  ANALYZE: '分析',
  SHARE: '分享',
  EARN_REWARDS: '获得奖励',
  VIEW_STATS: '查看统计'
};

const PermissionManagement: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<string>('OWNER');
  const [permissions, setPermissions] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // 初始化权限数据
  useEffect(() => {
    loadRolePermissions(selectedRole);
  }, [selectedRole]);

  // 加载角色权限
  const loadRolePermissions = async (role: string) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      const roleData = SMART_HOME_ROLES[role];
      setPermissions(roleData.permissions);
      
      // 默认展开所有模块
      const allKeys = Object.keys(PERMISSION_MODULES);
      setExpandedKeys(allKeys);
    } catch (error) {
      message.error('加载权限数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换模块权限
  const toggleModulePermission = (moduleKey: string, enabled: boolean) => {
    const newPermissions = { ...permissions };
    if (enabled) {
      // 启用模块时，给予基础查看权限
      newPermissions[moduleKey] = ['VIEW'];
    } else {
      // 禁用模块时，清空所有权限
      delete newPermissions[moduleKey];
    }
    setPermissions(newPermissions);
  };

  // 切换具体操作权限
  const toggleOperationPermission = (moduleKey: string, operation: string, checked: boolean) => {
    const newPermissions = { ...permissions };
    if (!newPermissions[moduleKey]) {
      newPermissions[moduleKey] = [];
    }
    
    if (checked) {
      if (!newPermissions[moduleKey].includes(operation)) {
        newPermissions[moduleKey].push(operation);
      }
    } else {
      newPermissions[moduleKey] = newPermissions[moduleKey].filter(op => op !== operation);
    }
    
    setPermissions(newPermissions);
  };

  // 保存权限配置
  const savePermissions = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('权限配置保存成功');
    } catch (error) {
      message.error('保存权限配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成权限树数据
  const generatePermissionTree = (): DataNode[] => {
    return Object.entries(PERMISSION_MODULES).map(([moduleKey, module]) => {
      const modulePermissions = permissions[moduleKey] || [];
      const hasPermissions = modulePermissions.length > 0;

      return {
        title: (
          <div className="flex items-center justify-between">
            <Space>
              <span style={{ color: module.color }}>{module.icon}</span>
              <span className="font-medium">{module.name}</span>
              <Switch
                size="small"
                checked={hasPermissions}
                onChange={(checked) => toggleModulePermission(moduleKey, checked)}
              />
            </Space>
          </div>
        ),
        key: moduleKey,
        children: Object.entries(module.subModules).map(([subKey, subModule]) => ({
          title: (
            <div className="py-2">
              <div className="font-medium text-gray-700 mb-2">{subModule.name}</div>
              <div className="flex flex-wrap gap-2">
                {subModule.operations.map(operation => (
                  <Checkbox
                    key={operation}
                    checked={modulePermissions.includes(operation)}
                    disabled={!hasPermissions}
                    onChange={(e) => toggleOperationPermission(moduleKey, operation, e.target.checked)}
                  >
                    <Tag color={modulePermissions.includes(operation) ? module.color : 'default'}>
                      {OPERATION_LABELS[operation]}
                    </Tag>
                  </Checkbox>
                ))}
              </div>
            </div>
          ),
          key: `${moduleKey}-${subKey}`,
          isLeaf: true
        }))
      };
    });
  };

  return (
    <PageContainer
      title="权限管理"
      subTitle="管理用户角色和权限配置"
      extra={[
        <Button key="save" type="primary" loading={loading} onClick={savePermissions}>
          保存配置
        </Button>
      ]}
    >
      <Row gutter={24}>
        {/* 左侧角色列表 */}
        <Col span={8}>
          <Card title="用户角色" className="h-full">
            <List
              dataSource={Object.entries(SMART_HOME_ROLES)}
              renderItem={([roleKey, role]) => (
                <List.Item
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedRole === roleKey 
                      ? 'bg-blue-50 border-l-4 border-blue-500' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedRole(roleKey)}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={role.icon} style={{ backgroundColor: role.color }} />}
                    title={
                      <Space>
                        <span>{role.name}</span>
                        {selectedRole === roleKey && <Tag color="blue">当前选择</Tag>}
                      </Space>
                    }
                    description={
                      <Text type="secondary" className="text-sm">
                        {role.description}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 右侧权限配置 */}
        <Col span={16}>
          <Card title="权限配置" className="h-full">
            <Spin spinning={loading}>
              {selectedRole && (
                <>
                  {/* 角色描述 */}
                  <div 
                    className="p-4 mb-6 rounded-lg border-l-4"
                    style={{ 
                      backgroundColor: `${SMART_HOME_ROLES[selectedRole].color}10`,
                      borderLeftColor: SMART_HOME_ROLES[selectedRole].color
                    }}
                  >
                    <Space align="start">
                      <Avatar 
                        icon={SMART_HOME_ROLES[selectedRole].icon} 
                        style={{ backgroundColor: SMART_HOME_ROLES[selectedRole].color }}
                      />
                      <div>
                        <Title level={4} className="mb-2">
                          {SMART_HOME_ROLES[selectedRole].name}
                        </Title>
                        <Paragraph className="text-gray-600 mb-0">
                          {SMART_HOME_ROLES[selectedRole].description}
                        </Paragraph>
                      </div>
                    </Space>
                  </div>

                  <Divider />

                  {/* 权限树 */}
                  <Tree
                    showLine
                    defaultExpandAll
                    expandedKeys={expandedKeys}
                    onExpand={setExpandedKeys}
                    treeData={generatePermissionTree()}
                    className="permission-tree"
                  />
                </>
              )}
            </Spin>
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default PermissionManagement;

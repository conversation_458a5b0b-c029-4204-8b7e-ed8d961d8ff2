{"timestamp": "2025-07-21T03:08:25.945Z", "summary": {"totalLinks": 36, "validLinks": 36, "brokenLinks": 0, "missingPages": 0}, "validLinks": [{"link": "../pages/admin-dashboard.html", "path": "src\\pc\\components\\pages\\admin-dashboard.html", "status": "valid"}, {"link": "../pages/index.html", "path": "src\\pc\\components\\pages\\index.html", "status": "valid"}, {"link": "../pages/design-management.html", "path": "src\\pc\\components\\pages\\design-management.html", "status": "valid"}, {"link": "../pages/design-requirements.html", "path": "src\\pc\\components\\pages\\design-requirements.html", "status": "valid"}, {"link": "../pages/design-requirements-table.html", "path": "src\\pc\\components\\pages\\design-requirements-table.html", "status": "valid"}, {"link": "../pages/design-effects.html", "path": "src\\pc\\components\\pages\\design-effects.html", "status": "valid"}, {"link": "../pages/design-progress.html", "path": "src\\pc\\components\\pages\\design-progress.html", "status": "valid"}, {"link": "../pages/design-tasks.html", "path": "src\\pc\\components\\pages\\design-tasks.html", "status": "valid"}, {"link": "../pages/projects.html", "path": "src\\pc\\components\\pages\\projects.html", "status": "valid"}, {"link": "../pages/construction-management.html", "path": "src\\pc\\components\\pages\\construction-management.html", "status": "valid"}, {"link": "../pages/construction-enhanced-demo.html", "path": "src\\pc\\components\\pages\\construction-enhanced-demo.html", "status": "valid"}, {"link": "../pages/products.html", "path": "src\\pc\\components\\pages\\products.html", "status": "valid"}, {"link": "../pages/product-materials.html", "path": "src\\pc\\components\\pages\\product-materials.html", "status": "valid"}, {"link": "../pages/aqara-product-import.html", "path": "src\\pc\\components\\pages\\aqara-product-import.html", "status": "valid"}, {"link": "../pages/orders.html", "path": "src\\pc\\components\\pages\\orders.html", "status": "valid"}, {"link": "../pages/customer-management.html", "path": "src\\pc\\components\\pages\\customer-management.html", "status": "valid"}, {"link": "../pages/marketing-management.html", "path": "src\\pc\\components\\pages\\marketing-management.html", "status": "valid"}, {"link": "../pages/knowledge-management.html", "path": "src\\pc\\components\\pages\\knowledge-management.html", "status": "valid"}, {"link": "../pages/contract-management.html", "path": "src\\pc\\components\\pages\\contract-management.html", "status": "valid"}, {"link": "../pages/requirements-analysis.html", "path": "src\\pc\\components\\pages\\requirements-analysis.html", "status": "valid"}, {"link": "../pages/requirements-management.html", "path": "src\\pc\\components\\pages\\requirements-management.html", "status": "valid"}, {"link": "../pages/analytics.html", "path": "src\\pc\\components\\pages\\analytics.html", "status": "valid"}, {"link": "../pages/api-tester.html", "path": "src\\pc\\components\\pages\\api-tester.html", "status": "valid"}, {"link": "../pages/一装ERP-API文档.html", "path": "src\\pc\\components\\pages\\一装ERP-API文档.html", "status": "valid"}, {"link": "../pages/settings.html", "path": "src\\pc\\components\\pages\\settings.html", "status": "valid"}, {"link": "../pages/user-management.html", "path": "src\\pc\\components\\pages\\user-management.html", "status": "valid"}, {"link": "../pages/user-permissions.html", "path": "src\\pc\\components\\pages\\user-permissions.html", "status": "valid"}, {"link": "../pages/permissions.html", "path": "src\\pc\\components\\pages\\permissions.html", "status": "valid"}, {"link": "../pages/user-profile.html", "path": "src\\pc\\components\\pages\\user-profile.html", "status": "valid"}, {"link": "../pages/demo.html", "path": "src\\pc\\components\\pages\\demo.html", "status": "valid"}, {"link": "../pages/register.html", "path": "src\\pc\\components\\pages\\register.html", "status": "valid"}, {"link": "../pages/login.html", "path": "src\\pc\\components\\pages\\login.html", "status": "valid"}, {"link": "../pages/design-management-new.html", "path": "src\\pc\\components\\pages\\design-management-new.html", "status": "valid"}, {"link": "../pages/design-requirements-new.html", "path": "src\\pc\\components\\pages\\design-requirements-new.html", "status": "valid"}, {"link": "../pages/design-requirements-fixed.html", "path": "src\\pc\\components\\pages\\design-requirements-fixed.html", "status": "valid"}, {"link": "../pages/design-requirements-test.html", "path": "src\\pc\\components\\pages\\design-requirements-test.html", "status": "valid"}], "errors": [], "warnings": []}
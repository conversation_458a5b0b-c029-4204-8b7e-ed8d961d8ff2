<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品素材上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .file-input { margin: 10px 0; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; margin: 10px 0; }
        .progress-fill { height: 100%; background: #28a745; border-radius: 10px; width: 0%; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>产品素材上传功能测试</h1>
        
        <div class="test-section">
            <h3>1. 单文件上传测试</h3>
            <div class="file-input">
                <input type="file" id="singleFile" accept="image/*,video/*,.pdf,.md,.txt">
                <button onclick="testSingleUpload()">测试单文件上传</button>
            </div>
            <div id="singleUploadResult"></div>
        </div>

        <div class="test-section">
            <h3>2. 批量上传测试</h3>
            <div class="file-input">
                <input type="file" id="batchFiles" multiple accept="image/*,video/*,.pdf,.md,.txt">
                <button onclick="testBatchUpload()">测试批量上传</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="batchProgress"></div>
            </div>
            <div id="batchUploadResult"></div>
        </div>

        <div class="test-section">
            <h3>3. 上传后同步显示测试</h3>
            <button onclick="testSyncDisplay()">测试同步显示</button>
            <div id="syncDisplayResult"></div>
        </div>

        <div class="test-section">
            <h3>4. 素材列表展示测试</h3>
            <button onclick="loadMaterialsList()">加载素材列表</button>
            <div id="materialsListResult"></div>
        </div>

        <div class="test-section">
            <h3>5. 模拟后端API测试</h3>
            <button onclick="testBackendAPI()">测试后端API连接</button>
            <div id="backendAPIResult"></div>
        </div>
    </div>

    <script>
        // 模拟后端API地址
        const API_BASE = '/api/v1';
        
        // 测试单文件上传
        async function testSingleUpload() {
            const fileInput = document.getElementById('singleFile');
            const resultDiv = document.getElementById('singleUploadResult');
            
            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请选择要上传的文件');
                return;
            }
            
            const file = fileInput.files[0];
            showResult(resultDiv, 'info', `开始上传文件: ${file.name} (${formatFileSize(file.size)})`);
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('material_type', detectMaterialType(file.name));
                formData.append('uploaded_by', 'test-user');
                
                // 模拟上传请求
                const response = await simulateUpload(formData, 'single');
                
                if (response.success) {
                    showResult(resultDiv, 'success', `上传成功! 文件ID: ${response.data.id}, 状态: ${response.data.status}`);
                } else {
                    showResult(resultDiv, 'error', `上传失败: ${response.message}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `上传异常: ${error.message}`);
            }
        }
        
        // 测试批量上传
        async function testBatchUpload() {
            const fileInput = document.getElementById('batchFiles');
            const resultDiv = document.getElementById('batchUploadResult');
            const progressBar = document.getElementById('batchProgress');
            
            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请选择要上传的文件');
                return;
            }
            
            const files = Array.from(fileInput.files);
            showResult(resultDiv, 'info', `开始批量上传 ${files.length} 个文件`);
            
            try {
                const formData = new FormData();
                files.forEach(file => formData.append('files', file));
                formData.append('batch_name', `测试批量上传-${new Date().toISOString()}`);
                formData.append('uploaded_by', 'test-user');
                
                // 模拟批量上传进度
                for (let i = 0; i <= files.length; i++) {
                    const progress = (i / files.length) * 100;
                    progressBar.style.width = `${progress}%`;
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                const response = await simulateUpload(formData, 'batch');
                
                if (response.success) {
                    showResult(resultDiv, 'success', `批量上传成功! 批次ID: ${response.data.batch_id}, 成功: ${response.data.success_files}, 失败: ${response.data.failed_files}`);
                } else {
                    showResult(resultDiv, 'error', `批量上传失败: ${response.message}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `批量上传异常: ${error.message}`);
            }
        }
        
        // 测试同步显示
        async function testSyncDisplay() {
            const resultDiv = document.getElementById('syncDisplayResult');
            showResult(resultDiv, 'info', '测试上传后同步显示功能...');
            
            try {
                // 模拟上传一个文件
                const mockFile = new File(['test content'], 'test-sync.txt', { type: 'text/plain' });
                const formData = new FormData();
                formData.append('file', mockFile);
                formData.append('material_type', 'overview');
                
                const uploadResponse = await simulateUpload(formData, 'single');
                
                if (uploadResponse.success) {
                    // 立即查询素材列表，验证同步
                    const listResponse = await simulateGetMaterials();
                    const isFound = listResponse.data.some(item => item.id === uploadResponse.data.id);
                    
                    if (isFound) {
                        showResult(resultDiv, 'success', '同步显示测试通过！上传的文件立即出现在素材列表中');
                    } else {
                        showResult(resultDiv, 'error', '同步显示测试失败：上传的文件未在素材列表中找到');
                    }
                } else {
                    showResult(resultDiv, 'error', `同步显示测试失败: ${uploadResponse.message}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `同步显示测试异常: ${error.message}`);
            }
        }
        
        // 加载素材列表
        async function loadMaterialsList() {
            const resultDiv = document.getElementById('materialsListResult');
            showResult(resultDiv, 'info', '正在加载素材列表...');
            
            try {
                const response = await simulateGetMaterials();
                
                if (response.success) {
                    const materials = response.data;
                    let html = `<h4>素材列表 (共${materials.length}项):</h4>`;
                    
                    materials.forEach(material => {
                        html += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                                <strong>${material.display_name}</strong> (${material.material_type})
                                <br>文件: ${material.file_name} | 大小: ${formatFileSize(material.file_size)}
                                <br>状态: ${material.status} | 上传时间: ${new Date(material.created_at).toLocaleString()}
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = `<div class="success">${html}</div>`;
                } else {
                    showResult(resultDiv, 'error', `加载素材列表失败: ${response.message}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `加载素材列表异常: ${error.message}`);
            }
        }
        
        // 测试后端API
        async function testBackendAPI() {
            const resultDiv = document.getElementById('backendAPIResult');
            showResult(resultDiv, 'info', '测试后端API连接...');
            
            const tests = [
                { name: '获取素材列表', endpoint: '/materials', method: 'GET' },
                { name: '上传单文件', endpoint: '/materials/upload', method: 'POST' },
                { name: '批量上传', endpoint: '/materials/batch-upload', method: 'POST' },
                { name: '获取批次状态', endpoint: '/materials/batch/test-batch', method: 'GET' }
            ];
            
            let results = '<h4>API连接测试结果:</h4>';
            
            for (const test of tests) {
                try {
                    const response = await fetch(`${API_BASE}${test.endpoint}`, {
                        method: test.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer test-token'
                        }
                    });
                    
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? '✓ 连接成功' : `✗ 连接失败 (${response.status})`;
                    
                    results += `<div style="color: ${status === 'success' ? 'green' : 'red'};">${test.name}: ${statusText}</div>`;
                } catch (error) {
                    results += `<div style="color: red;">${test.name}: ✗ 连接异常 (${error.message})</div>`;
                }
            }
            
            resultDiv.innerHTML = `<div class="info">${results}</div>`;
        }
        
        // 工具函数
        function showResult(element, type, message) {
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function formatFileSize(bytes) {
            const sizes = ['B', 'KB', 'MB', 'GB'];
            if (bytes === 0) return '0 B';
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        function detectMaterialType(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const typeMap = {
                'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image',
                'mp4': 'video', 'avi': 'video', 'mov': 'video',
                'pdf': 'manual', 'md': 'overview', 'txt': 'overview'
            };
            return typeMap[ext] || 'overview';
        }
        
        // 模拟上传函数
        async function simulateUpload(formData, type) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    if (type === 'single') {
                        resolve({
                            success: true,
                            data: {
                                id: 'test-' + Date.now(),
                                status: 'completed',
                                file_name: 'test-file.jpg',
                                material_type: 'image',
                                file_size: 1024000,
                                created_at: new Date().toISOString()
                            }
                        });
                    } else {
                        resolve({
                            success: true,
                            data: {
                                batch_id: 'batch-' + Date.now(),
                                total_files: 3,
                                success_files: 3,
                                failed_files: 0,
                                status: 'completed'
                            }
                        });
                    }
                }, 1000);
            });
        }
        
        // 模拟获取素材列表
        async function simulateGetMaterials() {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        success: true,
                        data: [
                            {
                                id: 'test-1',
                                display_name: 'Aqara 智能开关产品图',
                                file_name: 'aqara-switch.jpg',
                                material_type: 'image',
                                file_size: 2048000,
                                status: 'completed',
                                created_at: new Date().toISOString()
                            },
                            {
                                id: 'test-2',
                                display_name: 'Yeelight 安装视频',
                                file_name: 'yeelight-install.mp4',
                                material_type: 'video',
                                file_size: 15360000,
                                status: 'completed',
                                created_at: new Date().toISOString()
                            },
                            {
                                id: 'test-3',
                                display_name: 'Mijia 产品手册',
                                file_name: 'mijia-manual.pdf',
                                material_type: 'manual',
                                file_size: 5120000,
                                status: 'completed',
                                created_at: new Date().toISOString()
                            }
                        ]
                    });
                }, 500);
            });
        }
    </script>
</body>
</html>
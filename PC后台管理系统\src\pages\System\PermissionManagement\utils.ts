/**
 * 权限管理工具函数
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

import type {
  SmartHomeRole,
  PermissionModule,
  PermissionOperation,
  Role,
  Module
} from './types';

// 操作权限中文映射
export const OPERATION_LABELS: Record<PermissionOperation, string> = {
  VIEW: '查看',
  CREATE: '创建',
  EDIT: '编辑',
  DELETE: '删除',
  COMMENT: '评论',
  APPROVE: '审批',
  INVITE: '邀请',
  MANAGE: '管理',
  CONFIGURE: '配置',
  UPDATE: '更新',
  UPLOAD: '上传',
  DOWNLOAD: '下载',
  INSPECT: '巡检',
  ANALYZE: '分析',
  SHARE: '分享',
  EARN_REWARDS: '获得奖励',
  VIEW_STATS: '查看统计'
};

// 角色颜色映射
export const ROLE_COLORS: Record<SmartHomeRole, string> = {
  OWNER: '#2563eb',
  FAMILY_MEMBER: '#059669',
  HOME_DESIGNER: '#dc2626',
  SMART_HOME_DESIGNER: '#7c3aed',
  CONSTRUCTOR: '#ea580c'
};

// 模块颜色映射
export const MODULE_COLORS: Record<PermissionModule, string> = {
  project: '#2563eb',
  design: '#dc2626',
  construction: '#ea580c',
  cost: '#059669',
  files: '#7c3aed',
  comments: '#0891b2',
  marketing: '#be185d',
  system: '#374151'
};

/**
 * 检查用户是否有特定权限
 */
export const hasPermission = (
  userPermissions: Record<PermissionModule, PermissionOperation[]>,
  module: PermissionModule,
  operation: PermissionOperation
): boolean => {
  const modulePermissions = userPermissions[module] || [];
  return modulePermissions.includes(operation) || modulePermissions.includes('*' as PermissionOperation);
};

/**
 * 检查用户是否有模块的任何权限
 */
export const hasAnyPermissionInModule = (
  userPermissions: Record<PermissionModule, PermissionOperation[]>,
  module: PermissionModule
): boolean => {
  const modulePermissions = userPermissions[module] || [];
  return modulePermissions.length > 0;
};

/**
 * 获取用户在模块中的权限列表
 */
export const getModulePermissions = (
  userPermissions: Record<PermissionModule, PermissionOperation[]>,
  module: PermissionModule
): PermissionOperation[] => {
  return userPermissions[module] || [];
};

/**
 * 合并权限（用于权限继承）
 */
export const mergePermissions = (
  basePermissions: Record<PermissionModule, PermissionOperation[]>,
  additionalPermissions: Record<PermissionModule, PermissionOperation[]>
): Record<PermissionModule, PermissionOperation[]> => {
  const merged: Record<PermissionModule, PermissionOperation[]> = { ...basePermissions };
  
  Object.entries(additionalPermissions).forEach(([module, operations]) => {
    const moduleKey = module as PermissionModule;
    if (!merged[moduleKey]) {
      merged[moduleKey] = [];
    }
    
    operations.forEach(operation => {
      if (!merged[moduleKey].includes(operation)) {
        merged[moduleKey].push(operation);
      }
    });
  });
  
  return merged;
};

/**
 * 计算权限差异
 */
export const getPermissionDiff = (
  oldPermissions: Record<PermissionModule, PermissionOperation[]>,
  newPermissions: Record<PermissionModule, PermissionOperation[]>
): {
  added: Record<PermissionModule, PermissionOperation[]>;
  removed: Record<PermissionModule, PermissionOperation[]>;
  unchanged: Record<PermissionModule, PermissionOperation[]>;
} => {
  const added: Record<PermissionModule, PermissionOperation[]> = {};
  const removed: Record<PermissionModule, PermissionOperation[]> = {};
  const unchanged: Record<PermissionModule, PermissionOperation[]> = {};
  
  // 获取所有模块
  const allModules = new Set([
    ...Object.keys(oldPermissions),
    ...Object.keys(newPermissions)
  ]) as Set<PermissionModule>;
  
  allModules.forEach(module => {
    const oldOps = oldPermissions[module] || [];
    const newOps = newPermissions[module] || [];
    
    added[module] = newOps.filter(op => !oldOps.includes(op));
    removed[module] = oldOps.filter(op => !newOps.includes(op));
    unchanged[module] = oldOps.filter(op => newOps.includes(op));
  });
  
  return { added, removed, unchanged };
};

/**
 * 验证权限配置的有效性
 */
export const validatePermissions = (
  permissions: Record<PermissionModule, PermissionOperation[]>,
  availableModules: Record<PermissionModule, Module>
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  Object.entries(permissions).forEach(([module, operations]) => {
    const moduleKey = module as PermissionModule;
    
    // 检查模块是否存在
    if (!availableModules[moduleKey]) {
      errors.push(`模块 "${module}" 不存在`);
      return;
    }
    
    // 检查操作是否有效
    const validOperations = new Set<string>();
    Object.values(availableModules[moduleKey].subModules).forEach(subModule => {
      subModule.operations.forEach(op => validOperations.add(op));
    });
    
    operations.forEach(operation => {
      if (!validOperations.has(operation) && operation !== '*') {
        errors.push(`模块 "${module}" 中的操作 "${operation}" 无效`);
      }
    });
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 格式化权限显示文本
 */
export const formatPermissionText = (
  module: PermissionModule,
  operation: PermissionOperation
): string => {
  const moduleText = MODULE_COLORS[module] ? module : '未知模块';
  const operationText = OPERATION_LABELS[operation] || operation;
  return `${moduleText}:${operationText}`;
};

/**
 * 生成权限摘要
 */
export const generatePermissionSummary = (
  permissions: Record<PermissionModule, PermissionOperation[]>
): {
  totalModules: number;
  totalOperations: number;
  modulesSummary: Array<{
    module: PermissionModule;
    operationCount: number;
    operations: PermissionOperation[];
  }>;
} => {
  const modulesSummary = Object.entries(permissions).map(([module, operations]) => ({
    module: module as PermissionModule,
    operationCount: operations.length,
    operations
  }));
  
  const totalOperations = modulesSummary.reduce((sum, item) => sum + item.operationCount, 0);
  
  return {
    totalModules: modulesSummary.length,
    totalOperations,
    modulesSummary
  };
};

/**
 * 检查角色权限层级
 */
export const checkRoleHierarchy = (
  currentRole: SmartHomeRole,
  targetRole: SmartHomeRole
): {
  canManage: boolean;
  reason?: string;
} => {
  // 定义角色层级（数字越大权限越高）
  const roleHierarchy: Record<SmartHomeRole, number> = {
    CONSTRUCTOR: 1,
    FAMILY_MEMBER: 2,
    HOME_DESIGNER: 3,
    SMART_HOME_DESIGNER: 4,
    OWNER: 5
  };
  
  const currentLevel = roleHierarchy[currentRole];
  const targetLevel = roleHierarchy[targetRole];
  
  if (currentLevel >= targetLevel) {
    return { canManage: true };
  } else {
    return {
      canManage: false,
      reason: `${currentRole} 角色无法管理 ${targetRole} 角色的权限`
    };
  }
};

/**
 * 生成权限变更日志
 */
export const generatePermissionChangeLog = (
  userId: string,
  role: SmartHomeRole,
  oldPermissions: Record<PermissionModule, PermissionOperation[]>,
  newPermissions: Record<PermissionModule, PermissionOperation[]>,
  operator: string
): {
  timestamp: string;
  userId: string;
  role: SmartHomeRole;
  operator: string;
  changes: Array<{
    type: 'added' | 'removed';
    module: PermissionModule;
    operation: PermissionOperation;
  }>;
} => {
  const diff = getPermissionDiff(oldPermissions, newPermissions);
  const changes: Array<{
    type: 'added' | 'removed';
    module: PermissionModule;
    operation: PermissionOperation;
  }> = [];
  
  // 添加的权限
  Object.entries(diff.added).forEach(([module, operations]) => {
    operations.forEach(operation => {
      changes.push({
        type: 'added',
        module: module as PermissionModule,
        operation
      });
    });
  });
  
  // 移除的权限
  Object.entries(diff.removed).forEach(([module, operations]) => {
    operations.forEach(operation => {
      changes.push({
        type: 'removed',
        module: module as PermissionModule,
        operation
      });
    });
  });
  
  return {
    timestamp: new Date().toISOString(),
    userId,
    role,
    operator,
    changes
  };
};

/**
 * 权限搜索和过滤
 */
export const searchPermissions = (
  permissions: Record<PermissionModule, PermissionOperation[]>,
  searchTerm: string
): Record<PermissionModule, PermissionOperation[]> => {
  if (!searchTerm.trim()) {
    return permissions;
  }
  
  const filtered: Record<PermissionModule, PermissionOperation[]> = {};
  const term = searchTerm.toLowerCase();
  
  Object.entries(permissions).forEach(([module, operations]) => {
    const moduleKey = module as PermissionModule;
    
    // 检查模块名是否匹配
    if (module.toLowerCase().includes(term)) {
      filtered[moduleKey] = operations;
      return;
    }
    
    // 检查操作是否匹配
    const matchingOperations = operations.filter(operation => {
      const operationLabel = OPERATION_LABELS[operation] || operation;
      return operation.toLowerCase().includes(term) || 
             operationLabel.toLowerCase().includes(term);
    });
    
    if (matchingOperations.length > 0) {
      filtered[moduleKey] = matchingOperations;
    }
  });
  
  return filtered;
};

/**
 * 导出权限配置为JSON
 */
export const exportPermissionsToJSON = (
  permissions: Record<PermissionModule, PermissionOperation[]>,
  role: SmartHomeRole
): string => {
  const exportData = {
    role,
    permissions,
    exportedAt: new Date().toISOString(),
    version: '1.0'
  };
  
  return JSON.stringify(exportData, null, 2);
};

/**
 * 从JSON导入权限配置
 */
export const importPermissionsFromJSON = (
  jsonString: string
): {
  success: boolean;
  data?: {
    role: SmartHomeRole;
    permissions: Record<PermissionModule, PermissionOperation[]>;
  };
  error?: string;
} => {
  try {
    const data = JSON.parse(jsonString);
    
    if (!data.role || !data.permissions) {
      return {
        success: false,
        error: '无效的权限配置格式'
      };
    }
    
    return {
      success: true,
      data: {
        role: data.role,
        permissions: data.permissions
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'JSON格式错误'
    };
  }
};

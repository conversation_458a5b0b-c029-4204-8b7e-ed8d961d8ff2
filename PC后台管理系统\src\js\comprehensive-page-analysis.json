{"allPages": [{"fileName": "admin-dashboard.html", "baseName": "admin-dashboard", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\admin-dashboard.html", "exists": true, "title": "数据概览 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "analytics.html", "baseName": "analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\analytics.html", "exists": true, "title": "数据分析 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "analytics", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "api-tools.html", "baseName": "api-tools", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\api-tools.html", "exists": true, "title": "API 工具 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "aqara-product-import.html", "baseName": "aqara-product-import", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\aqara-product-import.html", "exists": true, "title": "Aqara产品导入 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "construction-enhanced-demo.html", "baseName": "construction-enhanced-demo", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-enhanced-demo.html", "exists": true, "title": "增强版施工管理系统演示", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "construction", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "construction-guide.html", "baseName": "construction-guide", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-guide.html", "exists": true, "title": "施工指导 - 智能家居管理系统", "description": "施工指导", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "construction", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "construction-management.html", "baseName": "construction-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-management.html", "exists": true, "title": "施工管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "construction", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "contract-management.html", "baseName": "contract-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\contract-management.html", "exists": true, "title": "合同管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "customer-analytics.html", "baseName": "customer-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "customer-management.html", "baseName": "customer-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-management.html", "exists": true, "title": "客户管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "customer-permissions.html", "baseName": "customer-permissions", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-permissions.html", "exists": true, "title": "权限管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "data-management.html", "baseName": "data-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\data-management.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "delivery-knowledge.html", "baseName": "delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\delivery-knowledge.html", "exists": true, "title": "交底知识库 - 智能家居管理系统", "description": "交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "demo.html", "baseName": "demo", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\demo.html", "exists": true, "title": "演示展示 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-cases.html", "baseName": "design-cases", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-cases.html", "exists": true, "title": "设计案例 - 智能家居管理系统", "description": "设计案例", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-center.html", "baseName": "design-center", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-center.html", "exists": true, "title": "设计中心 - 智能家居管理系统", "description": "设计中心", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-effects.html", "baseName": "design-effects", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-effects.html", "exists": true, "title": "设计效果图管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-knowledge.html", "baseName": "design-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-knowledge.html", "exists": true, "title": "设计知识库 - 智能家居管理系统", "description": "设计知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-management-new.html", "baseName": "design-management-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management-new.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-management.html", "baseName": "design-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-products.html", "baseName": "design-products", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-products.html", "exists": true, "title": "设计商品 - 智能家居管理系统", "description": "设计商品", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-progress.html", "baseName": "design-progress", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-progress.html", "exists": true, "title": "设计进度管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-requirements-fixed.html", "baseName": "design-requirements-fixed", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-fixed.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "design-requirements-new.html", "baseName": "design-requirements-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-new.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements-table.html", "baseName": "design-requirements-table", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-table.html", "exists": true, "title": "智能家居需求管理 - Table版本", "description": "", "hasContent": true, "sidebarType": "minimal", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-requirements-test.html", "baseName": "design-requirements-test", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-test.html", "exists": true, "title": "需求管理测试 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements.html", "baseName": "design-requirements", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-tasks.html", "baseName": "design-tasks", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-tasks.html", "exists": true, "title": "设计任务管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "electrical-delivery-knowledge.html", "baseName": "electrical-delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\electrical-delivery-knowledge.html", "exists": true, "title": "水电交底知识库 - 智能家居管理系统", "description": "水电交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "erp-documentation.html", "baseName": "erp-documentation", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\erp-documentation.html", "exists": true, "title": "һװERP����API-һװ�����ĵ�-װ�޹�������", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "index.html", "baseName": "index", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\index.html", "exists": true, "title": "数据概览 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "installation-knowledge.html", "baseName": "installation-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\installation-knowledge.html", "exists": true, "title": "布线知识库 - 智能家居管理系统", "description": "市转知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "internal-permissions.html", "baseName": "internal-permissions", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\internal-permissions.html", "exists": true, "title": "内部员工权限管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "permissions", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "knowledge-management.html", "baseName": "knowledge-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\knowledge-management.html", "exists": true, "title": "知识库管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "logout.html", "baseName": "logout", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\logout.html", "exists": true, "title": "后台管理系统登录", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "auth", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "market-knowledge.html", "baseName": "market-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\market-knowledge.html", "exists": true, "title": "市转知识库 - 智能家居管理系统", "description": "市转知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "marketing-management.html", "baseName": "marketing-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\marketing-management.html", "exists": true, "title": "营销管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "my-orders.html", "baseName": "my-orders", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\my-orders.html", "exists": true, "title": "我的订单 - 智能家居管理系统", "description": "我的订单", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "my-todos.html", "baseName": "my-todos", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\my-todos.html", "exists": true, "title": "我的代办 - 智能家居管理系统", "description": "我的代办", "hasContent": true, "sidebarType": "standard", "functionalCategory": "personal", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "order-analytics.html", "baseName": "order-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\order-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "orders.html", "baseName": "orders", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\orders.html", "exists": true, "title": "订单管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "product-knowledge.html", "baseName": "product-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\product-knowledge.html", "exists": true, "title": "产品知识库 - 智能家居管理系统", "description": "产品知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "product-materials.html", "baseName": "product-materials", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\product-materials.html", "exists": true, "title": "产品素材管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "minimal", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "products.html", "baseName": "products", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\products.html", "exists": true, "title": "商品管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "project-analytics.html", "baseName": "project-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\project-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "analytics", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "project-center.html", "baseName": "project-center", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\project-center.html", "exists": true, "title": "项目中心 - 智能家居管理系统", "description": "项目中心", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "projects.html", "baseName": "projects", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\projects.html", "exists": true, "title": "项目管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "unknown", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "register.html", "baseName": "register", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\register.html", "exists": true, "title": "智能家居系统 - 用户注册", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "auth", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "requirements-analytics.html", "baseName": "requirements-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\requirements-analytics.html", "exists": true, "title": "需求分析 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "requirements", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "requirements-management.html", "baseName": "requirements-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\requirements-management.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "requirements", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "system-settings.html", "baseName": "system-settings", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\system-settings.html", "exists": true, "title": "系统配置 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "user-management.html", "baseName": "user-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\user-management.html", "exists": true, "title": "用户管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "user-profile.html", "baseName": "user-profile", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\user-profile.html", "exists": true, "title": "个人资料 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}], "namingIssues": [], "functionalDuplicates": [{"category": "knowledge", "pages": [{"fileName": "delivery-knowledge.html", "baseName": "delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\delivery-knowledge.html", "exists": true, "title": "交底知识库 - 智能家居管理系统", "description": "交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "electrical-delivery-knowledge.html", "baseName": "electrical-delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\electrical-delivery-knowledge.html", "exists": true, "title": "水电交底知识库 - 智能家居管理系统", "description": "水电交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}], "reason": "功能相似"}, {"category": "design", "pages": [{"fileName": "design-management-new.html", "baseName": "design-management-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management-new.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-management.html", "baseName": "design-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}], "reason": "包含版本后缀的多个版本"}, {"category": "design", "pages": [{"fileName": "design-requirements-fixed.html", "baseName": "design-requirements-fixed", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-fixed.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "design-requirements-new.html", "baseName": "design-requirements-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-new.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements-table.html", "baseName": "design-requirements-table", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-table.html", "exists": true, "title": "智能家居需求管理 - Table版本", "description": "", "hasContent": true, "sidebarType": "minimal", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-requirements-test.html", "baseName": "design-requirements-test", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-test.html", "exists": true, "title": "需求管理测试 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements.html", "baseName": "design-requirements", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}], "reason": "包含版本后缀的多个版本, 包含表格视图变体, 包含增强/修复版本"}], "categoryAnalysis": {"design": {"name": "设计模块", "pages": [{"fileName": "design-cases.html", "baseName": "design-cases", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-cases.html", "exists": true, "title": "设计案例 - 智能家居管理系统", "description": "设计案例", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-center.html", "baseName": "design-center", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-center.html", "exists": true, "title": "设计中心 - 智能家居管理系统", "description": "设计中心", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-effects.html", "baseName": "design-effects", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-effects.html", "exists": true, "title": "设计效果图管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-knowledge.html", "baseName": "design-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-knowledge.html", "exists": true, "title": "设计知识库 - 智能家居管理系统", "description": "设计知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-management-new.html", "baseName": "design-management-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management-new.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-management.html", "baseName": "design-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-products.html", "baseName": "design-products", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-products.html", "exists": true, "title": "设计商品 - 智能家居管理系统", "description": "设计商品", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-progress.html", "baseName": "design-progress", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-progress.html", "exists": true, "title": "设计进度管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-requirements-fixed.html", "baseName": "design-requirements-fixed", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-fixed.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "design-requirements-new.html", "baseName": "design-requirements-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-new.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements-table.html", "baseName": "design-requirements-table", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-table.html", "exists": true, "title": "智能家居需求管理 - Table版本", "description": "", "hasContent": true, "sidebarType": "minimal", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-requirements-test.html", "baseName": "design-requirements-test", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-test.html", "exists": true, "title": "需求管理测试 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements.html", "baseName": "design-requirements", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "design-tasks.html", "baseName": "design-tasks", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-tasks.html", "exists": true, "title": "设计任务管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 4, "actualCount": 14, "issues": [{"fileName": "design-management-new.html", "baseName": "design-management-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-management-new.html", "exists": true, "title": "设计管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements-fixed.html", "baseName": "design-requirements-fixed", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-fixed.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "design-requirements-new.html", "baseName": "design-requirements-new", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-new.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}, {"fileName": "design-requirements-test.html", "baseName": "design-requirements-test", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\design-requirements-test.html", "exists": true, "title": "需求管理测试 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "design", "namingIssues": ["包含版本后缀，可能是临时文件"], "potentialDuplicates": []}]}, "requirements": {"name": "需求管理", "pages": [{"fileName": "requirements-analytics.html", "baseName": "requirements-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\requirements-analytics.html", "exists": true, "title": "需求分析 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "requirements", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "requirements-management.html", "baseName": "requirements-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\requirements-management.html", "exists": true, "title": "需求管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "requirements", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 2, "actualCount": 2, "issues": []}, "construction": {"name": "施工管理", "pages": [{"fileName": "construction-enhanced-demo.html", "baseName": "construction-enhanced-demo", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-enhanced-demo.html", "exists": true, "title": "增强版施工管理系统演示", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "construction", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}, {"fileName": "construction-guide.html", "baseName": "construction-guide", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-guide.html", "exists": true, "title": "施工指导 - 智能家居管理系统", "description": "施工指导", "hasContent": true, "sidebarType": "simplified", "functionalCategory": "construction", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "construction-management.html", "baseName": "construction-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-management.html", "exists": true, "title": "施工管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "construction", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 2, "actualCount": 3, "issues": [{"fileName": "construction-enhanced-demo.html", "baseName": "construction-enhanced-demo", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\construction-enhanced-demo.html", "exists": true, "title": "增强版施工管理系统演示", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "construction", "namingIssues": ["包含修复/增强标识，可能是临时版本"], "potentialDuplicates": []}]}, "user": {"name": "用户管理", "pages": [{"fileName": "customer-analytics.html", "baseName": "customer-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "customer-management.html", "baseName": "customer-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-management.html", "exists": true, "title": "客户管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "customer-permissions.html", "baseName": "customer-permissions", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\customer-permissions.html", "exists": true, "title": "权限管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "user-management.html", "baseName": "user-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\user-management.html", "exists": true, "title": "用户管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "user-profile.html", "baseName": "user-profile", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\user-profile.html", "exists": true, "title": "个人资料 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "user", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 3, "actualCount": 5, "issues": []}, "product": {"name": "商品管理", "pages": [{"fileName": "aqara-product-import.html", "baseName": "aqara-product-import", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\aqara-product-import.html", "exists": true, "title": "Aqara产品导入 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "product-knowledge.html", "baseName": "product-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\product-knowledge.html", "exists": true, "title": "产品知识库 - 智能家居管理系统", "description": "产品知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "product-materials.html", "baseName": "product-materials", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\product-materials.html", "exists": true, "title": "产品素材管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "minimal", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "products.html", "baseName": "products", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\products.html", "exists": true, "title": "商品管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "product", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 3, "actualCount": 4, "issues": []}, "order": {"name": "订单管理", "pages": [{"fileName": "my-orders.html", "baseName": "my-orders", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\my-orders.html", "exists": true, "title": "我的订单 - 智能家居管理系统", "description": "我的订单", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "order-analytics.html", "baseName": "order-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\order-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "orders.html", "baseName": "orders", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\orders.html", "exists": true, "title": "订单管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "order", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 2, "actualCount": 3, "issues": []}, "analytics": {"name": "数据分析", "pages": [{"fileName": "analytics.html", "baseName": "analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\analytics.html", "exists": true, "title": "数据分析 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "analytics", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "project-analytics.html", "baseName": "project-analytics", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\project-analytics.html", "exists": true, "title": "项目分析 - 智能家居管理系统", "description": "项目分析", "hasContent": true, "sidebarType": "standard", "functionalCategory": "analytics", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 4, "actualCount": 2, "issues": []}, "knowledge": {"name": "知识库", "pages": [{"fileName": "delivery-knowledge.html", "baseName": "delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\delivery-knowledge.html", "exists": true, "title": "交底知识库 - 智能家居管理系统", "description": "交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "electrical-delivery-knowledge.html", "baseName": "electrical-delivery-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\electrical-delivery-knowledge.html", "exists": true, "title": "水电交底知识库 - 智能家居管理系统", "description": "水电交底知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "installation-knowledge.html", "baseName": "installation-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\installation-knowledge.html", "exists": true, "title": "布线知识库 - 智能家居管理系统", "description": "市转知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "knowledge-management.html", "baseName": "knowledge-management", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\knowledge-management.html", "exists": true, "title": "知识库管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "market-knowledge.html", "baseName": "market-knowledge", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\market-knowledge.html", "exists": true, "title": "市转知识库 - 智能家居管理系统", "description": "市转知识库", "hasContent": true, "sidebarType": "standard", "functionalCategory": "knowledge", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 6, "actualCount": 5, "issues": []}, "permissions": {"name": "权限管理", "pages": [{"fileName": "internal-permissions.html", "baseName": "internal-permissions", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\internal-permissions.html", "exists": true, "title": "内部员工权限管理 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "permissions", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 2, "actualCount": 1, "issues": []}, "system": {"name": "系统管理", "pages": [{"fileName": "admin-dashboard.html", "baseName": "admin-dashboard", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\admin-dashboard.html", "exists": true, "title": "数据概览 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "api-tools.html", "baseName": "api-tools", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\api-tools.html", "exists": true, "title": "API 工具 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "system-settings.html", "baseName": "system-settings", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\system-settings.html", "exists": true, "title": "系统配置 - 智能家居管理系统", "description": "", "hasContent": true, "sidebarType": "standard", "functionalCategory": "system", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 4, "actualCount": 3, "issues": []}, "auth": {"name": "认证相关", "pages": [{"fileName": "logout.html", "baseName": "logout", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\logout.html", "exists": true, "title": "后台管理系统登录", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "auth", "namingIssues": [], "potentialDuplicates": []}, {"fileName": "register.html", "baseName": "register", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\register.html", "exists": true, "title": "智能家居系统 - 用户注册", "description": "", "hasContent": true, "sidebarType": "none", "functionalCategory": "auth", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 2, "actualCount": 2, "issues": []}, "personal": {"name": "个人中心", "pages": [{"fileName": "my-todos.html", "baseName": "my-todos", "filePath": "F:\\AI编程\\miniprogram\\PC后台管理系统\\src\\pc\\components\\pages\\my-todos.html", "exists": true, "title": "我的代办 - 智能家居管理系统", "description": "我的代办", "hasContent": true, "sidebarType": "standard", "functionalCategory": "personal", "namingIssues": [], "potentialDuplicates": []}], "expectedCount": 3, "actualCount": 1, "issues": []}}, "recommendations": {"rename": [{"current": "design-management-new.html", "suggested": "design-center.html", "reason": "包含版本后缀，可能是临时文件", "priority": 18}, {"current": "design-requirements-new.html", "suggested": "design-center.html", "reason": "包含版本后缀，可能是临时文件", "priority": 15}, {"current": "design-requirements-test.html", "suggested": "design-center.html", "reason": "包含版本后缀，可能是临时文件", "priority": 15}, {"current": "construction-enhanced-demo.html", "suggested": "construction-management.html", "reason": "包含修复/增强标识，可能是临时版本", "priority": 5}, {"current": "design-requirements-fixed.html", "suggested": "design-center.html", "reason": "包含修复/增强标识，可能是临时版本", "priority": 5}], "merge": [], "keep": [], "remove": []}}
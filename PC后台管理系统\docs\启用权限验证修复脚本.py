#!/usr/bin/env python3
"""
启用权限验证修复脚本
版本: v1.0
创建时间: 2025-07-01
说明: 批量启用user_auth.py中被注释的权限验证
"""

import re
import os

def fix_permission_middleware():
    """修复权限中间件"""
    file_path = r"PC后台管理系统\商城系统\smart-home-backend\user_auth.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已备份原文件到: {backup_path}")
        
        # 修复注释的权限验证
        # 模式1: # current_user: models.User = Depends(deps.get_current_active_superuser)
        pattern1 = r'#\s*current_user:\s*models\.User\s*=\s*Depends\(deps\.get_current_active_superuser\)'
        replacement1 = 'current_user: models.user_auth.User = Depends(deps.get_current_active_superuser)'
        
        # 模式2: # current_user: models.user_auth.User = Depends(deps.get_current_active_superuser)
        pattern2 = r'#\s*current_user:\s*models\.user_auth\.User\s*=\s*Depends\(deps\.get_current_active_superuser\)'
        replacement2 = 'current_user: models.user_auth.User = Depends(deps.get_current_active_superuser)'
        
        # 执行替换
        original_content = content
        content = re.sub(pattern1, replacement1, content)
        content = re.sub(pattern2, replacement2, content)
        
        # 统计修改数量
        changes1 = len(re.findall(pattern1, original_content))
        changes2 = len(re.findall(pattern2, original_content))
        total_changes = changes1 + changes2
        
        if total_changes > 0:
            # 写入修改后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 成功启用 {total_changes} 个权限验证")
            print(f"   - 模式1修复: {changes1} 个")
            print(f"   - 模式2修复: {changes2} 个")
            return True
        else:
            print("ℹ️ 没有找到需要修复的权限验证注释")
            return True
            
    except Exception as e:
        print(f"❌ 修复权限验证失败: {e}")
        return False

def verify_fixes():
    """验证修复结果"""
    file_path = r"PC后台管理系统\商城系统\smart-home-backend\user_auth.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有注释的权限验证
        remaining_comments = re.findall(r'#.*current_user.*Depends.*get_current', content)
        
        if remaining_comments:
            print(f"⚠️ 仍有 {len(remaining_comments)} 个权限验证被注释:")
            for i, comment in enumerate(remaining_comments, 1):
                print(f"   {i}. {comment.strip()}")
            return False
        else:
            print("✅ 所有权限验证已成功启用")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_permission_summary():
    """创建权限验证摘要"""
    file_path = r"PC后台管理系统\商城系统\smart-home-backend\user_auth.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有启用的权限验证
        enabled_permissions = re.findall(r'current_user:.*?Depends\(deps\.get_current_active_superuser\)', content)
        
        # 查找所有API端点
        api_endpoints = re.findall(r'@router\.(get|post|put|delete)\("([^"]+)"', content)
        
        print("\n📊 权限验证摘要:")
        print(f"   - 总API端点数: {len(api_endpoints)}")
        print(f"   - 已启用权限验证: {len(enabled_permissions)}")
        
        print("\n🔒 受保护的API端点:")
        for method, path in api_endpoints:
            if '/admin/' in path:
                print(f"   - {method.upper()} {path} ✅")
            else:
                print(f"   - {method.upper()} {path} ⚠️ (可能需要权限保护)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建摘要失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 启用权限验证修复脚本")
    print("=" * 50)
    
    # 步骤1: 修复权限中间件
    print("\n📝 步骤1: 修复权限中间件...")
    if not fix_permission_middleware():
        print("❌ 修复失败，退出")
        return False
    
    # 步骤2: 验证修复结果
    print("\n🔍 步骤2: 验证修复结果...")
    if not verify_fixes():
        print("⚠️ 验证发现问题，请手动检查")
    
    # 步骤3: 创建权限验证摘要
    print("\n📊 步骤3: 创建权限验证摘要...")
    create_permission_summary()
    
    print("\n✅ 权限验证修复完成!")
    print("\n📋 后续步骤:")
    print("   1. 检查deps.py中的权限验证函数是否正常工作")
    print("   2. 测试API端点的权限验证是否生效")
    print("   3. 确保数据库中有相应的用户角色数据")
    print("   4. 更新前端API调用以包含正确的认证头")
    
    return True

if __name__ == "__main__":
    main()

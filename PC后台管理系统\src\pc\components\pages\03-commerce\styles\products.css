/* 商品管理页面样式 */

/* 全局变量 */
:root {
    --primary-color: #1f2937;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --border-color: #e5e7eb;
    --border-radius: 8px;
    --box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --transition: all 0.2s ease;
}

/* 页面布局 */
.products-page {
    min-height: 100vh;
    background-color: var(--light-bg);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 筛选器样式 */
.product-filters {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.filters-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.filters-body {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--secondary-color);
    z-index: 2;
}

.search-input {
    padding-left: 2.5rem !important;
    padding-right: 2.5rem !important;
}

.clear-search {
    position: absolute;
    right: 0.5rem;
    border: none !important;
    background: none !important;
    color: var(--secondary-color);
    z-index: 2;
}

.status-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-active { background-color: var(--success-color); }
.status-inactive { background-color: var(--secondary-color); }
.status-draft { background-color: var(--warning-color); }
.status-out-of-stock { background-color: var(--danger-color); }

.date-range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-separator {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.date-presets {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.filters-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

.active-filters {
    display: none;
}

.active-filters-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-right: 0.5rem;
}

.active-filters-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: var(--info-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    gap: 0.25rem;
}

.remove-filter {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    margin-left: 0.25rem;
}

/* 表格样式 */
.product-table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.table-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-count {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.table-wrapper {
    overflow-x: auto;
}

.table-header-row {
    display: flex;
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--primary-color);
}

.table-row {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.table-row:hover {
    background-color: #f9fafb;
}

.table-cell {
    padding: 1rem;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.checkbox-cell { width: 60px; justify-content: center; }
.image-cell { width: 80px; justify-content: center; }
.info-cell { min-width: 250px; flex: 1; flex-direction: column; align-items: flex-start; }
.price-cell { width: 120px; justify-content: flex-end; }
.stock-cell { width: 100px; justify-content: center; }
.sales-cell { width: 100px; justify-content: center; }
.status-cell { width: 120px; justify-content: center; }
.sync-cell { width: 120px; justify-content: center; }
.date-cell { width: 140px; justify-content: center; }
.actions-cell { width: 160px; justify-content: center; }

.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sort-icon {
    margin-left: 0.5rem;
    color: var(--secondary-color);
    transition: var(--transition);
}

.sort-icon.active {
    color: var(--primary-color);
}

.product-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.product-name {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.product-meta {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

.price {
    font-weight: 600;
    color: var(--primary-color);
}

.stock.low-stock {
    color: var(--danger-color);
    font-weight: 500;
}

.status {
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.sync-status {
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.synced {
    background-color: #d1fae5;
    color: #065f46;
}

.not-synced {
    background-color: #fef3c7;
    color: #92400e;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--light-bg);
}

/* 模态框样式 */
.modal-dialog.modal-large {
    max-width: 900px;
}

.product-form {
    max-height: 70vh;
    overflow-y: auto;
}

.form-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.required {
    color: var(--danger-color);
}

.image-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
}

.image-upload-area:hover,
.image-upload-area.drag-over {
    border-color: var(--info-color);
    background-color: #f0f9ff;
}

.upload-zone {
    cursor: pointer;
    padding: 1rem;
}

.upload-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.upload-hint {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin: 0;
}

.image-preview-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.image-preview-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    aspect-ratio: 1;
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-actions {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
}

.remove-image {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 虚拟滚动样式 */
.virtual-table-container {
    position: relative;
    overflow: auto;
}

.virtual-table-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
}

.virtual-table-row {
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.virtual-table-row:hover {
    background-color: #f9fafb;
}

/* 加载状态样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.loading-message {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.element-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    z-index: 10;
}

/* Toast样式 */
.toast-container {
    z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filters-body {
        grid-template-columns: 1fr;
    }
    
    .table-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .bulk-actions,
    .table-actions {
        justify-content: center;
    }
    
    .table-wrapper {
        overflow-x: scroll;
    }
    
    .table-header-row,
    .table-row {
        min-width: 800px;
    }
    
    .modal-dialog.modal-large {
        max-width: 95%;
        margin: 1rem;
    }
    
    .product-form .row {
        flex-direction: column;
    }
    
    .date-range-inputs {
        flex-direction: column;
    }
    
    .date-presets {
        justify-content: center;
    }
}

/* 打印样式 */
@media print {
    .page-actions,
    .filters-header,
    .table-actions,
    .bulk-actions,
    .actions-cell {
        display: none !important;
    }
    
    .product-table-container {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

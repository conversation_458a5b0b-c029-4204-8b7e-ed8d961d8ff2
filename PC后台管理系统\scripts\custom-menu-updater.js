/**
 * 自定义菜单批量更新工具
 * 支持灵活配置菜单结构，一键应用到所有页面
 */

const fs = require('fs');
const path = require('path');

// 📋 菜单配置 - 在这里定义您的新菜单结构
const MENU_CONFIG = {
    logo: {
        icon: 'fas fa-home',
        title: '智能家居管理',
        subtitle: '管理系统'
    },
    sections: [
        {
            title: '个人中心',
            items: [
                { href: 'my-todos.html', text: '我的代办', icon: 'fas fa-tasks' },
                { href: 'my-orders.html', text: '我的订单', icon: 'fas fa-shopping-cart' }
            ]
        },
        {
            title: '业务管理',
            items: [
                { href: 'design-products.html', text: '设计商品', icon: 'fas fa-palette' },
                { href: 'requirements-management.html', text: '需求管理', icon: 'fas fa-clipboard-list' },
                { href: 'design-center.html', text: '设计中心', icon: 'fas fa-drafting-compass' },
                { href: 'project-center.html', text: '项目中心', icon: 'fas fa-project-diagram' },
                { href: 'construction-management.html', text: '施工管理', icon: 'fas fa-hard-hat' },
                { href: 'construction-guide.html', text: '施工指导', icon: 'fas fa-tools' }
            ]
        },
        {
            title: '商务中心',
            items: [
                { href: 'products.html', text: '商品管理', icon: 'fas fa-box' },
                { href: 'orders.html', text: '订单管理', icon: 'fas fa-file-invoice' },
                { href: 'customer-management.html', text: '客户管理', icon: 'fas fa-users' },
                { href: 'marketing-management.html', text: '营销管理', icon: 'fas fa-bullhorn' }
            ]
        },
        {
            title: '知识库',
            items: [
                { href: 'design-knowledge.html', text: '设计知识库', icon: 'fas fa-lightbulb' },
                { href: 'delivery-knowledge.html', text: '交底知识库', icon: 'fas fa-handshake' },
                { href: 'wiring-knowledge.html', text: '布线知识库', icon: 'fas fa-plug' },
                { href: 'installation-knowledge.html', text: '安装知识库', icon: 'fas fa-wrench' },
                { href: 'debugging-knowledge.html', text: '调试知识库', icon: 'fas fa-bug' },
                { href: 'product-knowledge.html', text: '产品知识库', icon: 'fas fa-cube' }
            ]
        },
        {
            title: '系统工具',
            items: [
                { href: 'api-tools.html', text: 'API 工具', icon: 'fas fa-code' },
                { href: 'erp-documentation.html', text: 'ERP文档', icon: 'fas fa-file-alt' },
                { href: 'system-settings.html', text: '系统配置', icon: 'fas fa-cog' },
                { href: 'user-management.html', text: '用户管理', icon: 'fas fa-user-cog' },
                { href: 'internal-permissions.html', text: '内部权限', icon: 'fas fa-shield-alt' },
                { href: 'customer-permissions.html', text: '客户权限', icon: 'fas fa-user-shield' },
                { href: 'data-management.html', text: '数据管理', icon: 'fas fa-database' }
            ]
        },
        {
            title: '数据分析',
            items: [
                { href: 'requirements-analytics.html', text: '需求分析', icon: 'fas fa-chart-line' },
                { href: 'project-analytics.html', text: '项目分析', icon: 'fas fa-chart-bar' },
                { href: 'order-analytics.html', text: '订单分析', icon: 'fas fa-chart-pie' },
                { href: 'customer-analytics.html', text: '客户分析', icon: 'fas fa-chart-area' }
            ]
        },
        {
            title: '个人资料',
            items: [
                { href: 'demo.html', text: '演示展示', icon: 'fas fa-play-circle' },
                { href: 'user-profile.html', text: '个人资料', icon: 'fas fa-user' },
                { href: 'logout.html', text: '退出登录', icon: 'fas fa-sign-out-alt' }
            ]
        }
    ]
};

// 🎯 生成菜单HTML
function generateMenuHTML(activePageFile = '') {
    const { logo, sections } = MENU_CONFIG;
    
    let menuHTML = `        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="${logo.icon}"></i>
                    </div>
                    <div>
                        <div class="logo-text">${logo.title}</div>
                        <div style="font-size: 10px; color: #6b7280;">${logo.subtitle}</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">`;

    // 生成菜单分组
    sections.forEach(section => {
        menuHTML += `
                <div class="nav-section">
                    <div class="nav-section-title">${section.title}</div>`;
        
        // 生成菜单项
        section.items.forEach(item => {
            const isActive = activePageFile === item.href ? ' active' : '';
            const iconHTML = item.icon ? `<i class="${item.icon}"></i> ` : '';
            
            menuHTML += `
                    <a href="${item.href}" class="nav-item${isActive}">${iconHTML}${item.text}</a>`;
        });
        
        menuHTML += `
                </div>`;
    });

    menuHTML += `
            </nav>
        </aside>`;

    return menuHTML;
}

// 📁 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            files.push(...getAllHtmlFiles(fullPath));
        } else if (item.endsWith('.html')) {
            files.push(fullPath);
        }
    }
    
    return files;
}

// 🔄 更新单个文件的菜单
function updateMenuInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找侧边栏的开始和结束位置
        const sidebarStartRegex = /<aside[^>]*class="sidebar"[^>]*>/;
        const sidebarEndRegex = /<\/aside>/;
        
        const startMatch = content.match(sidebarStartRegex);
        if (!startMatch) {
            console.log(`⏭️  跳过: ${fileName} (未找到侧边栏)`);
            return false;
        }
        
        const startIndex = startMatch.index;
        const afterStart = content.substring(startIndex);
        const endMatch = afterStart.match(sidebarEndRegex);
        
        if (!endMatch) {
            console.log(`⚠️  警告: ${fileName} (侧边栏结构不完整)`);
            return false;
        }
        
        const endIndex = startIndex + endMatch.index + endMatch[0].length;
        
        // 分割内容
        const beforeSidebar = content.substring(0, startIndex);
        const afterSidebar = content.substring(endIndex);
        
        // 生成新的菜单HTML（带有正确的激活状态）
        const newMenuHTML = generateMenuHTML(fileName);
        
        // 重新组合内容
        const newContent = beforeSidebar + newMenuHTML + afterSidebar;
        
        // 保存文件
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ 已更新: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 🚀 主函数
function main() {
    console.log('🚀 开始批量更新自定义菜单...\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    for (const file of htmlFiles) {
        if (updateMenuInFile(file)) {
            successCount++;
        } else {
            failCount++;
        }
    }
    
    console.log('\n📊 更新统计:');
    console.log(`✅ 成功更新: ${successCount} 个文件`);
    console.log(`❌ 更新失败: ${failCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 自定义菜单批量更新完成！');
        console.log('💡 所有页面现在都使用新的菜单结构');
        console.log('🎯 菜单激活状态已根据页面自动设置');
    }
}

// 🔧 导出功能（用于其他脚本调用）
module.exports = {
    MENU_CONFIG,
    generateMenuHTML,
    updateMenuInFile,
    main
};

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册模块测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 18px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
            color: #374151;
        }
        
        .status-indicator {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
        }
        
        .status-pass {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-fail {
            background: #fecaca;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .iframe-container {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #1f2937, #374151);
            color: white;
            padding: 24px;
            border-radius: 12px;
            margin-top: 20px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .summary-label {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-user-plus"></i> 用户注册模块测试</h1>
            <p>全面测试用户注册功能的完整性和可用性</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="runRegistrationTest()">
                <i class="fas fa-play"></i> 开始测试
            </button>
            <button class="btn btn-success" onclick="clearLog()">
                <i class="fas fa-trash"></i> 清空日志
            </button>
            <a href="../src/pc/components/pages/user-management.html" class="btn btn-warning" target="_blank">
                <i class="fas fa-external-link-alt"></i> 打开用户管理页面
            </a>
        </div>
        
        <div class="test-log" id="testLog">
            <div style="color: #10b981; font-weight: bold;">👤 用户注册模块测试控制台</div>
            <div style="color: #6b7280; margin-top: 8px;">点击"开始测试"验证用户注册功能...</div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <div class="section-header">
                    <i class="fas fa-file-alt"></i>
                    <span>页面结构检查</span>
                </div>
                <div id="pageStructureTests">
                    <div class="test-item">
                        <span class="test-name">登录页面存在</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">用户管理页面存在</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">注册表单元素</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">表单验证机制</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="section-header">
                    <i class="fas fa-cogs"></i>
                    <span>功能模块检查</span>
                </div>
                <div id="functionalTests">
                    <div class="test-item">
                        <span class="test-name">新增用户功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">用户信息验证</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">角色分配功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">邀请用户功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="section-header">
                    <i class="fas fa-shield-alt"></i>
                    <span>安全性检查</span>
                </div>
                <div id="securityTests">
                    <div class="test-item">
                        <span class="test-name">密码强度验证</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">邮箱格式验证</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">重复注册检查</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">权限控制</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="section-header">
                    <i class="fas fa-database"></i>
                    <span>数据处理检查</span>
                </div>
                <div id="dataTests">
                    <div class="test-item">
                        <span class="test-name">用户数据存储</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">数据格式验证</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">数据持久化</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">用户列表更新</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="iframe-container">
            <iframe id="userManagementFrame" src="../src/pc/components/pages/user-management.html"></iframe>
        </div>
        
        <div class="summary-card" id="testSummary" style="display: none;">
            <h3><i class="fas fa-chart-bar"></i> 测试结果汇总</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number" style="color: #3b82f6;" id="totalTests">0</div>
                    <div class="summary-label">总测试项</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #10b981;" id="passedTests">0</div>
                    <div class="summary-label">通过测试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #ef4444;" id="failedTests">0</div>
                    <div class="summary-label">失败测试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #f59e0b;" id="successRate">0%</div>
                    <div class="summary-label">成功率</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class UserRegistrationTester {
            constructor() {
                this.testResults = [];
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;
            }

            async runRegistrationTest() {
                this.log('👤 开始用户注册模块测试...');
                this.log('');

                // 重置计数器
                this.testResults = [];
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;

                // 等待iframe加载
                await this.waitForIframeLoad();

                // 1. 检查页面结构
                await this.checkPageStructure();
                
                // 2. 检查功能模块
                await this.checkFunctionalModules();
                
                // 3. 检查安全性
                await this.checkSecurity();
                
                // 4. 检查数据处理
                await this.checkDataHandling();
                
                // 5. 生成测试报告
                this.generateTestReport();
            }

            async waitForIframeLoad() {
                return new Promise((resolve) => {
                    const iframe = document.getElementById('userManagementFrame');
                    iframe.onload = () => {
                        this.log('📄 用户管理页面已加载');
                        setTimeout(resolve, 1000);
                    };
                });
            }

            async checkPageStructure() {
                this.log('📋 检查页面结构...');
                
                const iframe = document.getElementById('userManagementFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查登录页面
                const loginPageExists = await this.checkFileExists('../src/pc/components/pages/login.html');
                this.updateTestStatus('pageStructureTests', 0, loginPageExists, 
                    loginPageExists ? '✅ 存在' : '❌ 不存在');
                
                // 检查用户管理页面
                const userMgmtExists = iframeDoc.title.includes('用户管理') || iframeDoc.querySelector('.user-toolbar');
                this.updateTestStatus('pageStructureTests', 1, userMgmtExists,
                    userMgmtExists ? '✅ 存在' : '❌ 不存在');
                
                // 检查注册表单元素
                const addUserBtn = iframeDoc.querySelector('button[onclick*="showAddUserModal"]');
                const userModal = iframeDoc.getElementById('userModal');
                const hasRegistrationForm = addUserBtn && userModal;
                this.updateTestStatus('pageStructureTests', 2, hasRegistrationForm,
                    hasRegistrationForm ? '✅ 完整' : '❌ 缺失');
                
                // 检查表单验证
                const userForm = iframeDoc.getElementById('userForm');
                const hasValidation = userForm && userForm.querySelector('input[required]');
                this.updateTestStatus('pageStructureTests', 3, hasValidation,
                    hasValidation ? '✅ 存在' : '❌ 缺失');
                
                await this.sleep(500);
            }

            async checkFunctionalModules() {
                this.log('📋 检查功能模块...');
                
                const iframe = document.getElementById('userManagementFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeWindow = iframe.contentWindow;
                
                // 检查新增用户功能
                const addUserFunction = typeof iframeWindow.showAddUserModal === 'function';
                this.updateTestStatus('functionalTests', 0, addUserFunction,
                    addUserFunction ? '✅ 可用' : '❌ 不可用');
                
                // 检查用户信息验证
                const userForm = iframeDoc.getElementById('userForm');
                const hasValidationInputs = userForm && userForm.querySelectorAll('input[required]').length > 0;
                this.updateTestStatus('functionalTests', 1, hasValidationInputs,
                    hasValidationInputs ? '✅ 完整' : '❌ 不完整');
                
                // 检查角色分配功能
                const roleSelect = iframeDoc.querySelector('select[name*="role"], select[id*="role"]');
                this.updateTestStatus('functionalTests', 2, !!roleSelect,
                    roleSelect ? '✅ 存在' : '❌ 缺失');
                
                // 检查邀请用户功能
                const inviteFunction = typeof iframeWindow.showInviteModal === 'function';
                this.updateTestStatus('functionalTests', 3, inviteFunction,
                    inviteFunction ? '✅ 可用' : '❌ 不可用');
                
                await this.sleep(500);
            }

            async checkSecurity() {
                this.log('📋 检查安全性...');
                
                const iframe = document.getElementById('userManagementFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查密码强度验证
                const passwordInput = iframeDoc.querySelector('input[type="password"]');
                const hasPasswordValidation = passwordInput && (
                    passwordInput.hasAttribute('pattern') || 
                    passwordInput.hasAttribute('minlength')
                );
                this.updateTestStatus('securityTests', 0, hasPasswordValidation,
                    hasPasswordValidation ? '✅ 存在' : '❌ 缺失');
                
                // 检查邮箱格式验证
                const emailInput = iframeDoc.querySelector('input[type="email"]');
                this.updateTestStatus('securityTests', 1, !!emailInput,
                    emailInput ? '✅ 存在' : '❌ 缺失');
                
                // 检查重复注册检查（通过查看是否有相关验证逻辑）
                const hasUniqueValidation = iframeDoc.querySelector('script') && 
                    iframeDoc.querySelector('script').textContent.includes('已存在');
                this.updateTestStatus('securityTests', 2, hasUniqueValidation,
                    hasUniqueValidation ? '✅ 存在' : '⚠️ 需确认');
                
                // 检查权限控制
                const hasPermissionCheck = iframeDoc.querySelector('script') && 
                    iframeDoc.querySelector('script').textContent.includes('权限');
                this.updateTestStatus('securityTests', 3, hasPermissionCheck,
                    hasPermissionCheck ? '✅ 存在' : '⚠️ 需确认');
                
                await this.sleep(500);
            }

            async checkDataHandling() {
                this.log('📋 检查数据处理...');
                
                const iframe = document.getElementById('userManagementFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查用户数据存储
                const hasLocalStorage = typeof iframe.contentWindow.localStorage !== 'undefined';
                this.updateTestStatus('dataTests', 0, hasLocalStorage,
                    hasLocalStorage ? '✅ 支持' : '❌ 不支持');
                
                // 检查数据格式验证
                const hasFormValidation = iframeDoc.querySelector('form') && 
                    iframeDoc.querySelector('form').hasAttribute('novalidate') === false;
                this.updateTestStatus('dataTests', 1, hasFormValidation,
                    hasFormValidation ? '✅ 存在' : '⚠️ 需确认');
                
                // 检查数据持久化
                const hasDataPersistence = iframeDoc.querySelector('script') && 
                    iframeDoc.querySelector('script').textContent.includes('localStorage');
                this.updateTestStatus('dataTests', 2, hasDataPersistence,
                    hasDataPersistence ? '✅ 支持' : '⚠️ 需确认');
                
                // 检查用户列表更新
                const userTable = iframeDoc.querySelector('table, .user-list, .user-grid');
                this.updateTestStatus('dataTests', 3, !!userTable,
                    userTable ? '✅ 存在' : '❌ 缺失');
                
                await this.sleep(500);
            }

            async checkFileExists(filePath) {
                try {
                    const response = await fetch(filePath);
                    return response.ok;
                } catch (error) {
                    return false;
                }
            }

            updateTestStatus(sectionId, itemIndex, passed, statusText) {
                this.totalTests++;
                if (passed) this.passedTests++;
                else this.failedTests++;
                
                this.testResults.push({ 
                    section: sectionId, 
                    index: itemIndex, 
                    passed, 
                    status: statusText 
                });
                
                const section = document.getElementById(sectionId);
                const items = section.querySelectorAll('.test-item');
                const statusElement = items[itemIndex].querySelector('.status-indicator');
                
                statusElement.textContent = statusText;
                statusElement.className = `status-indicator ${passed ? 'status-pass' : 'status-fail'}`;
                
                if (passed) {
                    this.log(`✅ ${items[itemIndex].querySelector('.test-name').textContent}`);
                } else {
                    this.log(`❌ ${items[itemIndex].querySelector('.test-name').textContent}`);
                }
            }

            generateTestReport() {
                this.log('');
                this.log('📊 生成测试报告...');
                this.log('='.repeat(50));
                
                const successRate = this.totalTests > 0 ? Math.round((this.passedTests / this.totalTests) * 100) : 0;
                
                this.log(`总测试数: ${this.totalTests}`);
                this.log(`通过测试: ${this.passedTests}`);
                this.log(`失败测试: ${this.failedTests}`);
                this.log(`成功率: ${successRate}%`);
                
                // 更新汇总显示
                document.getElementById('totalTests').textContent = this.totalTests;
                document.getElementById('passedTests').textContent = this.passedTests;
                document.getElementById('failedTests').textContent = this.failedTests;
                document.getElementById('successRate').textContent = successRate + '%';
                document.getElementById('testSummary').style.display = 'block';
                
                this.log('');
                if (successRate >= 90) {
                    this.log('🎉 用户注册模块功能完整，可以正常使用！');
                } else if (successRate >= 70) {
                    this.log('⚠️ 用户注册模块基本可用，但有部分功能需要完善');
                } else if (successRate >= 50) {
                    this.log('⚠️ 用户注册模块部分可用，需要重要改进');
                } else {
                    this.log('❌ 用户注册模块功能不完整，需要大量开发工作');
                }
                
                this.log('');
                this.log('📋 功能完整性分析:');
                this.log('• 页面结构: 基本完整，有用户管理页面和新增用户功能');
                this.log('• 核心功能: 具备新增用户、角色分配、邀请用户等功能');
                this.log('• 安全机制: 需要加强密码验证和重复注册检查');
                this.log('• 数据处理: 支持本地存储，需要完善服务器端集成');
                
                this.log('');
                this.log('🔧 建议改进项目:');
                this.log('1. 完善密码强度验证规则');
                this.log('2. 添加邮箱格式验证');
                this.log('3. 实现重复注册检查机制');
                this.log('4. 集成后端API进行数据持久化');
                this.log('5. 添加用户注册成功后的邮件通知');
                
                this.log('='.repeat(50));
            }

            log(message) {
                const output = document.getElementById('testLog');
                const div = document.createElement('div');
                div.style.marginBottom = '4px';
                
                if (message.includes('✅')) {
                    div.style.color = '#10b981';
                } else if (message.includes('❌')) {
                    div.style.color = '#ef4444';
                } else if (message.includes('⚠️')) {
                    div.style.color = '#f59e0b';
                } else if (message.includes('📋') || message.includes('👤') || message.includes('📊')) {
                    div.style.color = '#3b82f6';
                    div.style.fontWeight = 'bold';
                } else if (message.includes('🎉')) {
                    div.style.color = '#10b981';
                    div.style.fontWeight = 'bold';
                } else if (message.includes('=') || message.includes('•')) {
                    div.style.color = '#6b7280';
                }
                
                div.textContent = message;
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 全局测试实例
        const registrationTester = new UserRegistrationTester();

        // 全局函数
        async function runRegistrationTest() {
            await registrationTester.runRegistrationTest();
        }

        function clearLog() {
            const output = document.getElementById('testLog');
            output.innerHTML = `
                <div style="color: #10b981; font-weight: bold;">👤 用户注册模块测试控制台</div>
                <div style="color: #6b7280; margin-top: 8px;">日志已清空，准备开始新的测试...</div>
            `;
            
            document.getElementById('testSummary').style.display = 'none';
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('👤 用户注册模块测试页面已加载');
            setTimeout(() => {
                runRegistrationTest();
            }, 2000);
        });
    </script>
</body>
</html>

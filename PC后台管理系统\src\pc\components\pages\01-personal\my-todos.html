<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的代办 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 待办头部控制 */
        .todo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        /* 筛选和搜索区域 */
        .filter-search-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 6px;
            background: #f8fafc;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #e5e7eb;
        }

        .tab.active {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .tab:hover:not(.active) {
            background: #f1f5f9;
            color: #1f2937;
        }

        /* 搜索框 */
        .search-box {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            width: 280px;
            outline: none;
            transition: border-color 0.2s;
        }

        .search-box input:focus {
            border-color: #1f2937;
        }

        .search-btn {
            padding: 8px 16px;
            background: #1f2937;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .search-btn:hover {
            background: #374151;
        }

        /* 视图切换按钮 */
        .view-toggle {
            display: flex;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: white;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn.active {
            background: #1f2937;
            color: white;
        }

        .view-btn:hover:not(.active) {
            background: #f9fafb;
        }

        /* 表格样式 */
        .todos-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .todos-table th,
        .todos-table td {
            padding: 16px 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.6;
        }

        .todos-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .todos-table td {
            font-size: 14px;
            color: #1f2937;
        }

        .todos-table tbody tr:hover {
            background: #f9fafb;
        }

        /* 优先级徽章 */
        .priority-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-badge.priority-high {
            background: #fee2e2;
            color: #dc2626;
        }

        .priority-badge.priority-medium {
            background: #fef3c7;
            color: #d97706;
        }

        .priority-badge.priority-low {
            background: #e0f2fe;
            color: #0369a1;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-badge.status-in-progress {
            background: #dbeafe;
            color: #2563eb;
        }

        .status-badge.status-completed {
            background: #d1fae5;
            color: #059669;
        }

        /* 小按钮 */
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 4px;
        }

        .btn-sm.btn-primary {
            background: #1f2937;
            color: white;
        }

        .btn-sm.btn-primary:hover {
            background: #374151;
        }

        .btn-sm.btn-secondary {
            background: #f8fafc;
            color: #6b7280;
            border: 1px solid #e5e7eb;
        }

        .btn-sm.btn-secondary:hover {
            background: #f1f5f9;
            color: #1f2937;
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 待办事项卡片样式 */
        .todo-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: box-shadow 0.2s;
        }

        .todo-card:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .todo-card.completed {
            opacity: 0.7;
            background: #f9fafb;
        }

        .todo-card .todo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .todo-card .todo-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .todo-card .todo-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .todo-card .todo-description {
            color: #6b7280;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .todo-card .todo-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-badge.high {
            background: #fee2e2;
            color: #dc2626;
        }

        .priority-badge.medium {
            background: #fef3c7;
            color: #d97706;
        }

        .priority-badge.low {
            background: #e0f2fe;
            color: #0369a1;
        }

        .status-select {
            padding: 4px 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            font-size: 12px;
            background: white;
        }

        .todo-status.todo {
            color: #d97706;
        }

        .todo-status.in-progress {
            color: #2563eb;
        }

        .todo-status.completed {
            color: #059669;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #e5e7eb;
        }

        .btn-outline:hover {
            background: #f9fafb;
            color: #1f2937;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }

        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }

        .table-responsive {
            overflow-x: auto;
        }

        /* 优先级徽章样式 */
        .priority-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .priority-badge.high {
            background: #fee2e2;
            color: #991b1b;
        }

        .priority-badge.medium {
            background: #fef3c7;
            color: #92400e;
        }

        .priority-badge.low {
            background: #d1fae5;
            color: #065f46;
        }

        /* 状态徽章样式 */
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-badge.todo {
            background: #f3f4f6;
            color: #374151;
        }

        .status-badge.in-progress {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.completed {
            background: #d1fae5;
            color: #065f46;
        }

        /* 状态选择器样式 */
        .status-select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            background: white;
        }

        /* 按钮样式增强 */
        .btn-secondary {
            background: #6b7280;
            color: #ffffff;
            border-color: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            border-color: #4b5563;
        }

        /* Toast 样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            background: #059669;
        }

        .toast.error {
            background: #dc2626;
        }

        .toast.warning {
            background: #d97706;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .todo-card .todo-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .todo-card .todo-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item active">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">我的代办</h1>
                    <p class="breadcrumb-description">管理您的待办事项和任务</p>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <div class="todo-container">

                <!-- 统计卡片 -->
                <div class="stats-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div style="width: 40px; height: 40px; background: #3b82f6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-tasks" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 600; color: #1f2937;" id="total-todos">0</div>
                                <div style="font-size: 14px; color: #6b7280;">总待办</div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div style="width: 40px; height: 40px; background: #f59e0b; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-clock" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 600; color: #1f2937;" id="pending-todos">0</div>
                                <div style="font-size: 14px; color: #6b7280;">待处理</div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div style="width: 40px; height: 40px; background: #10b981; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-check-circle" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 600; color: #1f2937;" id="completed-todos">0</div>
                                <div style="font-size: 14px; color: #6b7280;">已完成</div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div style="width: 40px; height: 40px; background: #ef4444; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-exclamation-triangle" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 600; color: #1f2937;" id="overdue-todos">0</div>
                                <div style="font-size: 14px; color: #6b7280;">已逾期</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工具栏 -->
                <div class="todo-toolbar" style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center;">
                    <button class="btn btn-primary" onclick="myTodos.addTodo()">
                        <i class="fas fa-plus"></i> 添加待办
                    </button>
                    <button class="btn btn-secondary" onclick="myTodos.exportTodos()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                </div>

                <!-- 筛选部分 -->
                <div class="filter-section" style="margin-bottom: 16px;">
                    <div class="view-toggle">
                        <button class="btn btn-sm" data-view="list">
                            <i class="fas fa-list"></i> 列表视图
                        </button>
                        <button class="btn btn-sm active" data-view="table">
                            <i class="fas fa-table"></i> 表格视图
                        </button>
                    </div>
                    <div style="display: flex; gap: 8px; margin-left: 16px;">
                        <button class="btn btn-sm active" data-filter="all">全部</button>
                        <button class="btn btn-sm" data-filter="todo">待办</button>
                        <button class="btn btn-sm" data-filter="in-progress">进行中</button>
                        <button class="btn btn-sm" data-filter="completed">已完成</button>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="search-container" style="margin-bottom: 24px;">
                    <input type="text" id="search-input" placeholder="搜索待办事项..." 
                           style="width: 300px; padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                </div>

                <!-- 待办事项容器 -->
                <div id="todos-container">
                    <!-- 动态内容将在这里渲染 -->
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- 待办事项模态框 -->
    <div id="todo-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; align-items: center; justify-content: center;">
        <div class="modal-content" style="background: white; border-radius: 8px; padding: 24px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                <h3 id="modal-title" style="margin: 0; font-size: 18px; font-weight: 600; color: #1f2937;">添加待办事项</h3>
                <button onclick="myTodos.closeModal()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6b7280;">&times;</button>
            </div>
            
            <form id="todo-form">
                <div class="form-group">
                    <label for="todo-title" class="form-label">标题 *</label>
                    <input type="text" id="todo-title" name="title" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="todo-description" class="form-label">描述 *</label>
                    <textarea id="todo-description" name="description" class="form-input form-textarea" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="todo-priority" class="form-label">优先级</label>
                    <select id="todo-priority" name="priority" class="form-input form-select">
                        <option value="low">低</option>
                        <option value="medium" selected>中</option>
                        <option value="high">高</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="todo-status" class="form-label">状态</label>
                    <select id="todo-status" name="status" class="form-input form-select">
                        <option value="todo" selected>待办</option>
                        <option value="in-progress">进行中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="todo-category" class="form-label">分类</label>
                    <select id="todo-category" name="category" class="form-input form-select">
                        <option value="work" selected>工作</option>
                        <option value="personal">个人</option>
                        <option value="customer">客户服务</option>
                    </select>
                </div>
                
                <div class="form-group" style="margin-bottom: 24px;">
                    <label for="todo-due-date" class="form-label">截止日期</label>
                    <input type="date" id="todo-due-date" name="dueDate" class="form-input">
                </div>
                
                <div class="modal-footer" style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button type="button" id="cancel-todo-btn" class="btn btn-secondary">取消</button>
                    <button type="submit" id="save-todo-btn" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 设计系统对象 - 提供Toast通知功能
        const ds = {
            showToast: function(message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;
                document.body.appendChild(toast);
                
                setTimeout(() => toast.classList.add('show'), 100);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);
            }
        };

        class MyTodosPage {
            constructor() {
                this.todos = [];
                this.currentView = 'table';
                this.currentFilter = 'all';
                this.nextId = 1;

                this.loadData();
                this.init();
            }

            // 数据管理
            loadData() {
                // 加载待办事项数据
                this.todos = JSON.parse(localStorage.getItem('todos') || '[]');

                // 初始化默认数据
                if (this.todos.length === 0) {
                    this.todos = [
                        {
                            id: 1,
                            title: '检查智能家居设备联网状态',
                            priority: 'high',
                            status: 'todo',
                            dueDate: '2025-01-15',
                            category: 'work',
                            description: '确保所有智能设备都正常连接到网络',
                            createdAt: new Date(Date.now() - 86400000 * 2).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 2).toISOString()
                        },
                        {
                            id: 2,
                            title: '更新产品知识库文档',
                            priority: 'medium',
                            status: 'in-progress',
                            dueDate: '2025-01-20',
                            category: 'work',
                            description: '整理最新产品规格和安装指南',
                            createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
                            updatedAt: new Date(Date.now() - 3600000).toISOString()
                        },
                        {
                            id: 3,
                            title: '客户反馈处理',
                            priority: 'high',
                            status: 'todo',
                            dueDate: '2025-01-12',
                            category: 'customer',
                            description: '回复客户关于产品使用的问题',
                            createdAt: new Date(Date.now() - 86400000).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000).toISOString()
                        },
                        {
                            id: 4,
                            title: '技术培训会议',
                            priority: 'low',
                            status: 'completed',
                            dueDate: '2025-01-10',
                            category: 'personal',
                            description: '参加新产品技术培训',
                            createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
                            updatedAt: new Date(Date.now() - 86400000 * 3).toISOString()
                        },
                        {
                            id: 5,
                            title: '设备采购清单审核',
                            priority: 'medium',
                            status: 'todo',
                            dueDate: '2025-01-25',
                            category: 'work',
                            description: '审核本月设备采购清单并提交申请',
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        }
                    ];
                    this.saveTodos();
                }

                // 设置下一个ID
                this.nextId = this.todos.length > 0 ? Math.max(...this.todos.map(t => t.id)) + 1 : 1;
            }

            saveTodos() {
                localStorage.setItem('todos', JSON.stringify(this.todos));
            }

            getNextTodoId() {
                return this.nextId++;
            }

            // 统计功能
            updateStatistics() {
                const totalTodos = this.todos.length;
                const pendingTodos = this.todos.filter(t => t.status === 'todo' || t.status === 'in-progress').length;
                const completedTodos = this.todos.filter(t => t.status === 'completed').length;

                // 计算逾期待办
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const overdueTodos = this.todos.filter(t => {
                    if (t.status === 'completed') return false;
                    if (!t.dueDate) return false;
                    const dueDate = new Date(t.dueDate);
                    return dueDate < today;
                }).length;

                // 更新统计显示
                const totalEl = document.getElementById('total-todos');
                const pendingEl = document.getElementById('pending-todos');
                const completedEl = document.getElementById('completed-todos');
                const overdueEl = document.getElementById('overdue-todos');

                if (totalEl) totalEl.textContent = totalTodos;
                if (pendingEl) pendingEl.textContent = pendingTodos;
                if (completedEl) completedEl.textContent = completedTodos;
                if (overdueEl) overdueEl.textContent = overdueTodos;
            }

            init() {
                this.bindEvents();
                this.updateStatistics();
                this.renderTodos();
            }

            bindEvents() {
                // 视图切换
                const viewBtns = document.querySelectorAll('.view-toggle .btn');
                viewBtns.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        viewBtns.forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        this.currentView = btn.getAttribute('data-view');
                        this.renderTodos();
                    });
                });

                // 状态筛选
                const filterBtns = document.querySelectorAll('.filter-section .btn');
                filterBtns.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        filterBtns.forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        this.currentFilter = btn.getAttribute('data-filter');
                        this.renderTodos();
                    });
                });

                // 搜索功能
                const searchInput = document.getElementById('search-input');
                searchInput.addEventListener('input', (e) => {
                    this.renderTodos(e.target.value);
                });

                // 模态框事件绑定
                this.bindModalEvents();
            }

            bindModalEvents() {
                // 取消按钮
                document.getElementById('cancel-todo-btn').addEventListener('click', () => {
                    this.closeModal();
                });

                // 保存按钮
                document.getElementById('save-todo-btn').addEventListener('click', () => {
                    this.saveTodo();
                });

                // 表单提交
                document.getElementById('todo-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveTodo();
                });

                // 模态框关闭
                document.getElementById('todo-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'todo-modal') {
                        this.closeModal();
                    }
                });
            }

            getFilteredTodos(searchTerm = '') {
                let filteredTodos = [...this.todos];

                // 状态筛选
                if (this.currentFilter !== 'all') {
                    filteredTodos = filteredTodos.filter(todo => todo.status === this.currentFilter);
                }

                // 搜索筛选
                if (searchTerm) {
                    const keyword = searchTerm.toLowerCase();
                    filteredTodos = filteredTodos.filter(todo =>
                        todo.title.toLowerCase().includes(keyword) ||
                        todo.description.toLowerCase().includes(keyword) ||
                        this.getCategoryText(todo.category).toLowerCase().includes(keyword)
                    );
                }

                // 按创建时间倒序排列
                filteredTodos.sort((a, b) => {
                    const aTime = new Date(a.createdAt || 0);
                    const bTime = new Date(b.createdAt || 0);
                    return bTime - aTime;
                });

                return filteredTodos;
            }

            renderTodos(searchTerm = '') {
                const filteredTodos = this.getFilteredTodos(searchTerm);

                const container = document.getElementById('todos-container');
                if (!container) return;

                if (filteredTodos.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 60px 20px; color: #6b7280;">
                            <i class="fas fa-tasks fa-3x" style="display: block; margin-bottom: 16px; opacity: 0.3;"></i>
                            <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 500;">暂无待办事项</h3>
                            <p style="margin: 0; font-size: 14px;">点击"添加待办"按钮创建您的第一个待办事项</p>
                        </div>
                    `;
                    return;
                }

                if (this.currentView === 'list') {
                    this.renderListView(container, filteredTodos);
                } else {
                    this.renderTableView(container, filteredTodos);
                }

                // 更新统计数据
                this.updateStatistics();
            }

            renderListView(container, todos) {
                container.innerHTML = todos.map(todo => `
                    <div class="todo-card ${todo.status}" data-id="${todo.id}">
                        <div class="todo-header">
                            <h4 class="todo-title">${todo.title}</h4>
                            <div class="todo-actions">
                                <span class="priority-badge ${todo.priority}">${this.getPriorityText(todo.priority)}</span>
                                <select class="status-select" onchange="myTodos.updateStatus(${todo.id}, this.value)">
                                    <option value="todo" ${todo.status === 'todo' ? 'selected' : ''}>待办</option>
                                    <option value="in-progress" ${todo.status === 'in-progress' ? 'selected' : ''}>进行中</option>
                                    <option value="completed" ${todo.status === 'completed' ? 'selected' : ''}>已完成</option>
                                </select>
                                <button class="btn btn-sm btn-outline" onclick="myTodos.editTodo(${todo.id})">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="myTodos.deleteTodo(${todo.id})">删除</button>
                            </div>
                        </div>
                        <p class="todo-description">${todo.description}</p>
                        <div class="todo-footer">
                            <span class="todo-category">${this.getCategoryText(todo.category)}</span>
                            <span class="todo-due-date">截止: ${todo.dueDate}</span>
                            <span class="todo-status ${todo.status}">${this.getStatusText(todo.status)}</span>
                        </div>
                    </div>
                `).join('');
            }

            renderTableView(container, todos) {
                container.innerHTML = `
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>描述</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>分类</th>
                                    <th>截止日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${todos.map(todo => `
                                    <tr data-id="${todo.id}">
                                        <td>${todo.title}</td>
                                        <td>${todo.description}</td>
                                        <td><span class="priority-badge ${todo.priority}">${this.getPriorityText(todo.priority)}</span></td>
                                        <td>
                                            <select class="status-select" onchange="myTodos.updateStatus(${todo.id}, this.value)">
                                                <option value="todo" ${todo.status === 'todo' ? 'selected' : ''}>待办</option>
                                                <option value="in-progress" ${todo.status === 'in-progress' ? 'selected' : ''}>进行中</option>
                                                <option value="completed" ${todo.status === 'completed' ? 'selected' : ''}>已完成</option>
                                            </select>
                                        </td>
                                        <td>${this.getCategoryText(todo.category)}</td>
                                        <td>${todo.dueDate}</td>
                                        <td>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-sm btn-outline" onclick="myTodos.viewTodo(${todo.id})" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-secondary" onclick="myTodos.editTodo(${todo.id})" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="myTodos.deleteTodo(${todo.id})" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }

            getPriorityText(priority) {
                const texts = {
                    'high': '高',
                    'medium': '中',
                    'low': '低'
                };
                return texts[priority] || priority;
            }

            getStatusText(status) {
                const texts = {
                    'todo': '待办',
                    'in-progress': '进行中',
                    'completed': '已完成'
                };
                return texts[status] || status;
            }

            getCategoryText(category) {
                const texts = {
                    'work': '工作',
                    'personal': '个人',
                    'customer': '客户服务'
                };
                return texts[category] || category;
            }

            addTodo() {
                // 重置表单
                document.getElementById('todo-form').reset();
                document.getElementById('todo-form').removeAttribute('data-edit-mode');
                document.getElementById('todo-form').removeAttribute('data-edit-id');
                document.getElementById('modal-title').textContent = '添加待办事项';

                // 设置默认截止日期为明天
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                document.getElementById('todo-due-date').value = tomorrow.toISOString().split('T')[0];

                // 显示模态框
                document.getElementById('todo-modal').style.display = 'flex';
            }

            viewTodo(id) {
                const todo = this.todos.find(t => t.id === parseInt(id));
                if (!todo) {
                    ds.showToast('待办事项不存在', 'error');
                    return;
                }

                // 创建详情模态框
                const modal = document.createElement('div');
                modal.className = 'todo-detail-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="modal-content" onclick="event.stopPropagation()" style="background: white; border-radius: 12px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                            <div class="modal-header" style="padding: 24px 24px 0 24px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">待办事项详情</h3>
                                <button onclick="this.closest('.todo-detail-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 4px;">&times;</button>
                            </div>

                            <div class="modal-body" style="padding: 0 24px 24px 24px;">
                                <div style="display: grid; gap: 20px;">
                                    <div>
                                        <h4 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #1f2937;">${todo.title}</h4>
                                        <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                            <span class="priority-badge ${todo.priority}" style="padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">${this.getPriorityText(todo.priority)}</span>
                                            <span class="status-badge ${todo.status}" style="padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">${this.getStatusText(todo.status)}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">描述</h5>
                                        <p style="margin: 0; color: #6b7280; line-height: 1.6;">${todo.description}</p>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">分类</h5>
                                            <p style="margin: 0; color: #6b7280;">${this.getCategoryText(todo.category)}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">截止日期</h5>
                                            <p style="margin: 0; color: #6b7280;">${todo.dueDate || '未设置'}</p>
                                        </div>
                                    </div>

                                    ${todo.createdAt ? `
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">创建时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${new Date(todo.createdAt).toLocaleString('zh-CN')}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">更新时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${new Date(todo.updatedAt).toLocaleString('zh-CN')}</p>
                                        </div>
                                    </div>
                                    ` : ''}
                                </div>

                                <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; display: flex; gap: 8px; justify-content: flex-end;">
                                    <button onclick="this.closest('.todo-detail-modal').remove()" style="padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">关闭</button>
                                    <button onclick="myTodos.editTodo(${todo.id}); this.closest('.todo-detail-modal').remove();" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">编辑</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            }

            async editTodo(id) {
                const todo = this.todos.find(t => t.id === id);
                if (!todo) {
                    ds.showToast('待办事项不存在', 'error');
                    return;
                }

                // 填充表单
                document.getElementById('todo-title').value = todo.title;
                document.getElementById('todo-description').value = todo.description;
                document.getElementById('todo-priority').value = todo.priority;
                document.getElementById('todo-status').value = todo.status;
                document.getElementById('todo-category').value = todo.category;
                document.getElementById('todo-due-date').value = todo.dueDate;

                // 设置编辑模式
                const form = document.getElementById('todo-form');
                form.setAttribute('data-edit-mode', 'true');
                form.setAttribute('data-edit-id', id);
                document.getElementById('modal-title').textContent = '编辑待办事项';

                // 显示模态框
                document.getElementById('todo-modal').style.display = 'flex';
            }

            deleteTodo(id) {
                const todo = this.todos.find(t => t.id === parseInt(id));
                if (!todo) {
                    ds.showToast('待办事项不存在', 'error');
                    return;
                }

                if (!confirm(`确定要删除待办事项"${todo.title}"吗？此操作不可恢复。`)) {
                    return;
                }

                try {
                    // 从数组中删除
                    this.todos = this.todos.filter(todo => todo.id !== parseInt(id));

                    // 保存到本地存储
                    this.saveTodos();

                    // 重新渲染
                    this.renderTodos();

                    ds.showToast('待办事项删除成功', 'success');
                } catch (error) {
                    ds.showToast('删除待办事项失败', 'error');
                    console.error('删除待办事项失败:', error);
                }
            }

            updateStatus(id, newStatus) {
                try {
                    const todo = this.todos.find(t => t.id === parseInt(id));
                    if (todo) {
                        todo.status = newStatus;
                        todo.updatedAt = new Date().toISOString();

                        // 保存到本地存储
                        this.saveTodos();

                        // 重新渲染
                        this.renderTodos();

                        ds.showToast('状态更新成功', 'success');
                    } else {
                        ds.showToast('待办事项不存在', 'error');
                    }
                } catch (error) {
                    ds.showToast('状态更新失败', 'error');
                    console.error('状态更新失败:', error);
                }
            }

            saveTodo() {
                const form = document.getElementById('todo-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const formData = new FormData(form);

                const todoData = {
                    title: formData.get('title').trim(),
                    description: formData.get('description').trim(),
                    priority: formData.get('priority'),
                    status: formData.get('status'),
                    category: formData.get('category'),
                    dueDate: formData.get('dueDate')
                };

                // 基本验证
                if (!todoData.title || !todoData.description) {
                    ds.showToast('标题和描述为必填项', 'error');
                    return;
                }

                // 截止日期验证
                if (todoData.dueDate) {
                    const dueDate = new Date(todoData.dueDate);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (dueDate < today) {
                        if (!confirm('截止日期已过期，确定要保存吗？')) {
                            return;
                        }
                    }
                }

                try {
                    const isEditMode = form.hasAttribute('data-edit-mode');

                    if (isEditMode) {
                        // 编辑模式
                        const editId = parseInt(form.getAttribute('data-edit-id'));
                        const todoIndex = this.todos.findIndex(t => t.id === editId);

                        if (todoIndex !== -1) {
                            this.todos[todoIndex] = {
                                ...this.todos[todoIndex],
                                ...todoData,
                                updatedAt: new Date().toISOString()
                            };
                            ds.showToast('待办事项更新成功', 'success');
                        } else {
                            ds.showToast('待办事项不存在', 'error');
                            return;
                        }
                    } else {
                        // 新建模式
                        const newTodo = {
                            id: this.getNextTodoId(),
                            ...todoData,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        };
                        this.todos.unshift(newTodo);
                        ds.showToast('待办事项创建成功', 'success');
                    }

                    // 保存到本地存储
                    this.saveTodos();

                    this.closeModal();
                    this.renderTodos();
                } catch (error) {
                    ds.showToast('保存失败', 'error');
                    console.error('保存失败:', error);
                }
            }

            closeModal() {
                document.getElementById('todo-modal').style.display = 'none';
                document.getElementById('todo-form').reset();
                document.getElementById('todo-form').removeAttribute('data-edit-mode');
                document.getElementById('todo-form').removeAttribute('data-edit-id');
            }

            exportTodos() {
                try {
                    ds.showToast('正在导出待办事项数据...', 'info');

                    // 获取当前筛选的待办事项
                    const filteredTodos = this.getFilteredTodos();

                    // 生成CSV内容
                    const headers = ['ID', '标题', '描述', '优先级', '状态', '分类', '截止日期', '创建时间', '更新时间'];
                    const csvContent = [
                        headers.join(','),
                        ...filteredTodos.map(todo => [
                            todo.id,
                            `"${todo.title.replace(/"/g, '""')}"`,
                            `"${todo.description.replace(/"/g, '""')}"`,
                            this.getPriorityText(todo.priority),
                            this.getStatusText(todo.status),
                            this.getCategoryText(todo.category),
                            todo.dueDate,
                            todo.createdAt ? new Date(todo.createdAt).toLocaleString('zh-CN') : '',
                            todo.updatedAt ? new Date(todo.updatedAt).toLocaleString('zh-CN') : ''
                        ].join(','))
                    ].join('\n');

                    // 创建下载链接
                    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `todos_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    ds.showToast(`待办事项导出成功 (${filteredTodos.length}条记录)`, 'success');
                } catch (error) {
                    ds.showToast('导出失败', 'error');
                    console.error('导出失败:', error);
                }
            }
        }

        // 初始化页面
        const myTodos = new MyTodosPage();
    </script>
</body>
</html>

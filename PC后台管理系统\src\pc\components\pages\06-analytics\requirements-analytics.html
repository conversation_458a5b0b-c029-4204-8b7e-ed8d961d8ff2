<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求分析 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../design-system/design-tokens.css">
    <link rel="stylesheet" href="../../../design-system/components.css">

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item active">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="content-header">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">需求分析</h1>
                            <p class="breadcrumb-description">客户需求数据分析和趋势洞察</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-body">
                <!-- 统计卡片 -->
                <div class="analytics-stats-grid">
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-requirements">0</div>
                                    <div class="text-sm text-secondary">总需求数</div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-clipboard-list fa-2x"></i>
                                </div>
                            </div>
                            <div class="metric-change positive">+12% 本月</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="pending-requirements">0</div>
                                    <div class="text-sm text-secondary">待处理需求</div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                            <div class="metric-change negative">需要关注</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="completed-requirements">0</div>
                                    <div class="text-sm text-secondary">已完成需求</div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                            <div class="metric-change positive">+8% 本周</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="avg-response-time">0</div>
                                    <div class="text-sm text-secondary">平均响应时间(小时)</div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-stopwatch fa-2x"></i>
                                </div>
                            </div>
                            <div class="metric-change positive">-15% 改善</div>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="tabs-container">
                    <div class="tabs-nav" data-tab-group="requirements-analytics">
                        <button class="tab-button active" data-tab-trigger="overview">需求概览</button>
                        <button class="tab-button" data-tab-trigger="trends">趋势分析</button>
                        <button class="tab-button" data-tab-trigger="categories">分类分析</button>
                        <button class="tab-button" data-tab-trigger="performance">处理效率</button>
                    </div>
                </div>

                <!-- 需求概览标签页 -->
                <div class="tab-content active" data-tab-content="overview" data-tab-group="requirements-analytics">
                    <div class="analytics-grid">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3 class="chart-title">需求状态分布</h3>
                                <div class="chart-filters">
                                    <select class="form-control form-control-sm">
                                        <option>最近30天</option>
                                        <option>最近7天</option>
                                        <option>最近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-content" id="requirements-status-chart">
                                <div class="text-center">
                                    <i class="fas fa-chart-pie fa-3x mb-3" style="opacity: 0.3;"></i>
                                    <div>需求状态分布图表</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">关键指标</h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div class="metric-card">
                                        <div class="metric-value" id="completion-rate">0%</div>
                                        <div class="metric-label">完成率</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value" id="satisfaction-score">0</div>
                                        <div class="metric-label">满意度评分</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value" id="urgent-count">0</div>
                                        <div class="metric-label">紧急需求</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势分析标签页 -->
                <div class="tab-content" data-tab-content="trends" data-tab-group="requirements-analytics">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">需求趋势分析</h3>
                            <div class="chart-filters">
                                <select class="form-control form-control-sm">
                                    <option>按天</option>
                                    <option>按周</option>
                                    <option>按月</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-content" id="requirements-trend-chart">
                            <div class="text-center">
                                <i class="fas fa-chart-line fa-3x mb-3" style="opacity: 0.3;"></i>
                                <div>需求趋势图表</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分类分析标签页 -->
                <div class="tab-content" data-tab-content="categories" data-tab-group="requirements-analytics">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">需求分类分析</h3>
                        </div>
                        <div class="chart-content" id="requirements-category-chart">
                            <div class="text-center">
                                <i class="fas fa-chart-bar fa-3x mb-3" style="opacity: 0.3;"></i>
                                <div>需求分类图表</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 处理效率标签页 -->
                <div class="tab-content" data-tab-content="performance" data-tab-group="requirements-analytics">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">处理效率分析</h3>
                        </div>
                        <div class="chart-content" id="requirements-performance-chart">
                            <div class="text-center">
                                <i class="fas fa-tachometer-alt fa-3x mb-3" style="opacity: 0.3;"></i>
                                <div>处理效率图表</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../../../design-system/components.js"></script>
    <script>
        // 需求分析页面逻辑
        class RequirementsAnalyticsPage {
            constructor() {
                this.init();
            }

            async init() {
                await this.loadStatistics();
                await this.loadCharts();
                this.bindEvents();
            }

            bindEvents() {
                // 图表筛选器变化
                document.querySelectorAll('.chart-filters select').forEach(select => {
                    select.addEventListener('change', () => {
                        this.updateCharts();
                    });
                });
            }

            async loadStatistics() {
                try {
                    const response = await api.get('/analytics/requirements/statistics');
                    const stats = response.data || {};

                    document.getElementById('total-requirements').textContent = stats.total_requirements || 0;
                    document.getElementById('pending-requirements').textContent = stats.pending_requirements || 0;
                    document.getElementById('completed-requirements').textContent = stats.completed_requirements || 0;
                    document.getElementById('avg-response-time').textContent = stats.avg_response_time || 0;
                    document.getElementById('completion-rate').textContent = (stats.completion_rate || 0) + '%';
                    document.getElementById('satisfaction-score').textContent = stats.satisfaction_score || 0;
                    document.getElementById('urgent-count').textContent = stats.urgent_count || 0;

                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            async loadCharts() {
                try {
                    // 这里可以集成真实的图表库，如 Chart.js 或 ECharts
                    console.log('加载图表数据...');
                } catch (error) {
                    console.error('加载图表失败:', error);
                }
            }

            updateCharts() {
                // 更新图表数据
                console.log('更新图表...');
            }
        }

        // 初始化页面
        const requirementsAnalyticsPage = new RequirementsAnalyticsPage();
    </script>

    <style>
        .space-y-4 > * + * {
            margin-top: var(--space-4);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .analytics-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 设计系统模拟
        const ds = {
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        };

        class RequirementsAnalytics {
            constructor() {
                this.requirements = [];
                this.charts = {};

                this.loadData();
                this.init();
            }

            loadData() {
                // 加载需求数据
                this.requirements = JSON.parse(localStorage.getItem('customer_requirements') || '[]');

                // 初始化默认数据
                if (this.requirements.length === 0) {
                    this.requirements = [
                        {
                            id: 1,
                            title: '智能照明控制系统',
                            category: 'lighting',
                            priority: 'high',
                            status: 'completed',
                            customerName: '张三',
                            submittedAt: new Date(Date.now() - 86400000 * 30).toISOString(),
                            completedAt: new Date(Date.now() - 86400000 * 5).toISOString(),
                            responseTime: 25,
                            satisfaction: 5
                        },
                        {
                            id: 2,
                            title: '智能安防监控需求',
                            category: 'security',
                            priority: 'high',
                            status: 'in-progress',
                            customerName: '李四',
                            submittedAt: new Date(Date.now() - 86400000 * 15).toISOString(),
                            responseTime: 15,
                            satisfaction: null
                        },
                        {
                            id: 3,
                            title: '智能家电控制',
                            category: 'appliance',
                            priority: 'medium',
                            status: 'pending',
                            customerName: '王五',
                            submittedAt: new Date(Date.now() - 86400000 * 7).toISOString(),
                            responseTime: 7,
                            satisfaction: null
                        },
                        {
                            id: 4,
                            title: '智能门锁系统',
                            category: 'security',
                            priority: 'high',
                            status: 'completed',
                            customerName: '赵六',
                            submittedAt: new Date(Date.now() - 86400000 * 45).toISOString(),
                            completedAt: new Date(Date.now() - 86400000 * 20).toISOString(),
                            responseTime: 25,
                            satisfaction: 4
                        },
                        {
                            id: 5,
                            title: '智能温控系统',
                            category: 'climate',
                            priority: 'medium',
                            status: 'completed',
                            customerName: '钱七',
                            submittedAt: new Date(Date.now() - 86400000 * 60).toISOString(),
                            completedAt: new Date(Date.now() - 86400000 * 35).toISOString(),
                            responseTime: 25,
                            satisfaction: 5
                        }
                    ];
                    this.saveRequirements();
                }
            }

            saveRequirements() {
                localStorage.setItem('customer_requirements', JSON.stringify(this.requirements));
            }

            init() {
                this.updateStatistics();
                this.initCharts();
                ds.showToast('需求分析数据加载完成', 'success');
            }

            updateStatistics() {
                const totalRequirements = this.requirements.length;
                const pendingRequirements = this.requirements.filter(r => r.status === 'pending').length;
                const completedRequirements = this.requirements.filter(r => r.status === 'completed').length;
                const avgResponseTime = this.requirements.reduce((sum, r) => sum + r.responseTime, 0) / totalRequirements;

                const totalEl = document.getElementById('total-requirements');
                const pendingEl = document.getElementById('pending-requirements');
                const completedEl = document.getElementById('completed-requirements');
                const avgTimeEl = document.getElementById('avg-response-time');

                if (totalEl) totalEl.textContent = totalRequirements;
                if (pendingEl) pendingEl.textContent = pendingRequirements;
                if (completedEl) completedEl.textContent = completedRequirements;
                if (avgTimeEl) avgTimeEl.textContent = Math.round(avgResponseTime);
            }

            initCharts() {
                this.initCategoryChart();
                this.initTrendChart();
            }

            initCategoryChart() {
                const ctx = document.getElementById('category-chart');
                if (!ctx) return;

                // 统计需求分类
                const categoryCount = {};
                this.requirements.forEach(req => {
                    categoryCount[req.category] = (categoryCount[req.category] || 0) + 1;
                });

                const categoryNames = {
                    'lighting': '智能照明',
                    'security': '安防监控',
                    'appliance': '智能家电',
                    'climate': '温控系统'
                };

                this.charts.category = new Chart(ctx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: Object.keys(categoryCount).map(key => categoryNames[key] || key),
                        datasets: [{
                            data: Object.values(categoryCount),
                            backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            initTrendChart() {
                const ctx = document.getElementById('trend-chart');
                if (!ctx) return;

                // 生成过去6个月的需求趋势
                const months = [];
                const data = [];
                const now = new Date();

                for (let i = 5; i >= 0; i--) {
                    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                    months.push(date.toLocaleDateString('zh-CN', { month: 'short' }));

                    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
                    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

                    const monthlyRequirements = this.requirements.filter(req => {
                        const reqDate = new Date(req.submittedAt);
                        return reqDate >= monthStart && reqDate <= monthEnd;
                    }).length;

                    data.push(monthlyRequirements);
                }

                this.charts.trend = new Chart(ctx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: months,
                        datasets: [{
                            label: '需求数量',
                            data: data,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        }

        // 初始化需求分析
        const requirementsAnalytics = new RequirementsAnalytics();
    </script>
</body>
</html>

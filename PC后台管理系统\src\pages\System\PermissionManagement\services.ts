/**
 * 权限管理API服务
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

import { request } from '@umijs/max';
import type {
  SmartHomeRole,
  PermissionModule,
  PermissionOperation,
  User,
  UserListParams,
  UserListResponse,
  PermissionCheckRequest,
  PermissionCheckResponse,
  RolePermissionConfig,
  PermissionUpdateRequest,
  ProjectSpace,
  ProjectMember,
  Invitation,
  InvitationFormData,
  PermissionLog,
  PermissionStats,
  ApiResponse
} from './types';

// API基础配置
const API_PREFIX = '/api/v1';

/**
 * 用户管理API
 */
export const userAPI = {
  // 获取用户列表
  async getUsers(params: UserListParams): Promise<UserListResponse> {
    return request(`${API_PREFIX}/users`, {
      method: 'GET',
      params,
    });
  },

  // 获取用户详情
  async getUser(userId: string): Promise<User> {
    return request(`${API_PREFIX}/users/${userId}`, {
      method: 'GET',
    });
  },

  // 创建用户
  async createUser(userData: Partial<User>): Promise<User> {
    return request(`${API_PREFIX}/users`, {
      method: 'POST',
      data: userData,
    });
  },

  // 更新用户
  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    return request(`${API_PREFIX}/users/${userId}`, {
      method: 'PUT',
      data: userData,
    });
  },

  // 删除用户
  async deleteUser(userId: string): Promise<void> {
    return request(`${API_PREFIX}/users/${userId}`, {
      method: 'DELETE',
    });
  },

  // 批量更新用户角色
  async batchUpdateUserRoles(userIds: string[], role: SmartHomeRole): Promise<void> {
    return request(`${API_PREFIX}/users/batch/role`, {
      method: 'PUT',
      data: { userIds, role },
    });
  },

  // 重置用户密码
  async resetPassword(userId: string): Promise<{ temporaryPassword: string }> {
    return request(`${API_PREFIX}/users/${userId}/reset-password`, {
      method: 'POST',
    });
  },
};

/**
 * 权限管理API
 */
export const permissionAPI = {
  // 获取角色权限配置
  async getRolePermissions(role: SmartHomeRole): Promise<RolePermissionConfig> {
    return request(`${API_PREFIX}/permissions/roles/${role}`, {
      method: 'GET',
    });
  },

  // 更新角色权限
  async updateRolePermissions(data: PermissionUpdateRequest): Promise<ApiResponse> {
    return request(`${API_PREFIX}/permissions/roles/${data.role}`, {
      method: 'PUT',
      data,
    });
  },

  // 检查用户权限
  async checkPermission(data: PermissionCheckRequest): Promise<PermissionCheckResponse> {
    return request(`${API_PREFIX}/permissions/check`, {
      method: 'POST',
      data,
    });
  },

  // 批量检查权限
  async batchCheckPermissions(requests: PermissionCheckRequest[]): Promise<PermissionCheckResponse[]> {
    return request(`${API_PREFIX}/permissions/batch-check`, {
      method: 'POST',
      data: { requests },
    });
  },

  // 获取用户权限列表
  async getUserPermissions(userId: string, projectId?: string): Promise<{
    permissions: Record<PermissionModule, PermissionOperation[]>;
    inheritedFrom: SmartHomeRole;
  }> {
    return request(`${API_PREFIX}/permissions/users/${userId}`, {
      method: 'GET',
      params: projectId ? { projectId } : {},
    });
  },

  // 获取权限统计信息
  async getPermissionStats(): Promise<PermissionStats> {
    return request(`${API_PREFIX}/permissions/stats`, {
      method: 'GET',
    });
  },

  // 获取权限日志
  async getPermissionLogs(params: {
    userId?: string;
    module?: PermissionModule;
    action?: string;
    startDate?: string;
    endDate?: string;
    current?: number;
    pageSize?: number;
  }): Promise<{
    logs: PermissionLog[];
    total: number;
  }> {
    return request(`${API_PREFIX}/permissions/logs`, {
      method: 'GET',
      params,
    });
  },
};

/**
 * 项目空间API
 */
export const projectAPI = {
  // 获取项目列表
  async getProjects(params: {
    ownerId?: string;
    status?: string;
    current?: number;
    pageSize?: number;
  }): Promise<{
    projects: ProjectSpace[];
    total: number;
  }> {
    return request(`${API_PREFIX}/projects`, {
      method: 'GET',
      params,
    });
  },

  // 获取项目详情
  async getProject(projectId: string): Promise<ProjectSpace> {
    return request(`${API_PREFIX}/projects/${projectId}`, {
      method: 'GET',
    });
  },

  // 创建项目
  async createProject(projectData: Partial<ProjectSpace>): Promise<ProjectSpace> {
    return request(`${API_PREFIX}/projects`, {
      method: 'POST',
      data: projectData,
    });
  },

  // 更新项目
  async updateProject(projectId: string, projectData: Partial<ProjectSpace>): Promise<ProjectSpace> {
    return request(`${API_PREFIX}/projects/${projectId}`, {
      method: 'PUT',
      data: projectData,
    });
  },

  // 删除项目
  async deleteProject(projectId: string): Promise<void> {
    return request(`${API_PREFIX}/projects/${projectId}`, {
      method: 'DELETE',
    });
  },

  // 获取项目成员
  async getProjectMembers(projectId: string): Promise<ProjectMember[]> {
    return request(`${API_PREFIX}/projects/${projectId}/members`, {
      method: 'GET',
    });
  },

  // 添加项目成员
  async addProjectMember(projectId: string, memberData: {
    userId: string;
    role: SmartHomeRole;
    permissions?: Record<PermissionModule, PermissionOperation[]>;
  }): Promise<ProjectMember> {
    return request(`${API_PREFIX}/projects/${projectId}/members`, {
      method: 'POST',
      data: memberData,
    });
  },

  // 更新项目成员权限
  async updateProjectMember(projectId: string, memberId: string, memberData: {
    role?: SmartHomeRole;
    permissions?: Record<PermissionModule, PermissionOperation[]>;
  }): Promise<ProjectMember> {
    return request(`${API_PREFIX}/projects/${projectId}/members/${memberId}`, {
      method: 'PUT',
      data: memberData,
    });
  },

  // 移除项目成员
  async removeProjectMember(projectId: string, memberId: string): Promise<void> {
    return request(`${API_PREFIX}/projects/${projectId}/members/${memberId}`, {
      method: 'DELETE',
    });
  },
};

/**
 * 邀请管理API
 */
export const invitationAPI = {
  // 发送邀请
  async sendInvitation(data: InvitationFormData): Promise<Invitation> {
    return request(`${API_PREFIX}/invitations`, {
      method: 'POST',
      data,
    });
  },

  // 获取邀请列表
  async getInvitations(params: {
    projectId?: string;
    inviterId?: string;
    inviteeId?: string;
    status?: string;
    current?: number;
    pageSize?: number;
  }): Promise<{
    invitations: Invitation[];
    total: number;
  }> {
    return request(`${API_PREFIX}/invitations`, {
      method: 'GET',
      params,
    });
  },

  // 获取邀请详情
  async getInvitation(invitationId: string): Promise<Invitation> {
    return request(`${API_PREFIX}/invitations/${invitationId}`, {
      method: 'GET',
    });
  },

  // 接受邀请
  async acceptInvitation(invitationId: string): Promise<ApiResponse> {
    return request(`${API_PREFIX}/invitations/${invitationId}/accept`, {
      method: 'POST',
    });
  },

  // 拒绝邀请
  async rejectInvitation(invitationId: string, reason?: string): Promise<ApiResponse> {
    return request(`${API_PREFIX}/invitations/${invitationId}/reject`, {
      method: 'POST',
      data: { reason },
    });
  },

  // 撤销邀请
  async revokeInvitation(invitationId: string): Promise<ApiResponse> {
    return request(`${API_PREFIX}/invitations/${invitationId}/revoke`, {
      method: 'POST',
    });
  },

  // 重新发送邀请
  async resendInvitation(invitationId: string): Promise<ApiResponse> {
    return request(`${API_PREFIX}/invitations/${invitationId}/resend`, {
      method: 'POST',
    });
  },
};

/**
 * 认证API
 */
export const authAPI = {
  // 用户登录
  async login(credentials: {
    username: string;
    password: string;
    remember?: boolean;
  }): Promise<{
    user: User;
    token: string;
    refreshToken: string;
  }> {
    return request(`${API_PREFIX}/auth/login`, {
      method: 'POST',
      data: credentials,
    });
  },

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<{
    token: string;
    refreshToken: string;
  }> {
    return request(`${API_PREFIX}/auth/refresh`, {
      method: 'POST',
      data: { refreshToken },
    });
  },

  // 用户登出
  async logout(): Promise<void> {
    return request(`${API_PREFIX}/auth/logout`, {
      method: 'POST',
    });
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return request(`${API_PREFIX}/auth/me`, {
      method: 'GET',
    });
  },

  // 修改密码
  async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<ApiResponse> {
    return request(`${API_PREFIX}/auth/change-password`, {
      method: 'POST',
      data,
    });
  },
};

/**
 * 系统配置API
 */
export const systemAPI = {
  // 获取系统配置
  async getSystemConfig(): Promise<Record<string, any>> {
    return request(`${API_PREFIX}/system/config`, {
      method: 'GET',
    });
  },

  // 更新系统配置
  async updateSystemConfig(config: Record<string, any>): Promise<ApiResponse> {
    return request(`${API_PREFIX}/system/config`, {
      method: 'PUT',
      data: config,
    });
  },

  // 获取系统状态
  async getSystemStatus(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    version: string;
    database: 'connected' | 'disconnected';
    cache: 'connected' | 'disconnected';
  }> {
    return request(`${API_PREFIX}/system/status`, {
      method: 'GET',
    });
  },

  // 清理系统缓存
  async clearCache(): Promise<ApiResponse> {
    return request(`${API_PREFIX}/system/cache/clear`, {
      method: 'POST',
    });
  },
};

// 导出所有API
export default {
  user: userAPI,
  permission: permissionAPI,
  project: projectAPI,
  invitation: invitationAPI,
  auth: authAPI,
  system: systemAPI,
};

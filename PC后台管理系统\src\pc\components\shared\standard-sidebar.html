<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准侧边栏演示 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 页面状态检查 -->
    <script>
        console.log('页面开始加载...');
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(229, 231, 235, 0.8);
            flex-shrink: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        .sidebar-header {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.6);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .logo-icon i {
            color: white;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 12px 0;
        }

        .nav-section {
            margin-bottom: 16px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 4px 16px;
            margin: 4px 8px 2px;
            background: rgba(107, 114, 128, 0.08);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 3px;
            border-left: 2px solid rgba(107, 114, 128, 0.3);
            line-height: 1.2;
        }

        .nav-item {
            display: block;
            padding: 6px 16px;
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 1px 8px;
            position: relative;
            line-height: 1.3;
        }

        .nav-item:hover {
            background: rgba(243, 244, 246, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            color: #1f2937;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            color: #1f2937;
            font-weight: 600;
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateX(6px);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-content {
            text-align: center;
            max-width: 600px;
        }

        .demo-content h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .demo-content p {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 32px;
        }

        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 标准侧边栏组件 - 所有PC端页面必须使用此结构 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon"><i class="fas fa-home"></i></div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../pages/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../pages/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../pages/design-products.html" class="nav-item">设计商品</a>
                    <a href="../pages/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../pages/design-center.html" class="nav-item">设计中心</a>
                    <a href="../pages/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../pages/project-center.html" class="nav-item">项目中心</a>
                    <a href="../pages/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../pages/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../pages/products.html" class="nav-item">商品管理</a>
                    <a href="../pages/orders.html" class="nav-item">订单管理</a>
                    <a href="../pages/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../pages/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../pages/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../pages/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../pages/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../pages/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../pages/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../pages/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../pages/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../pages/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../pages/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../pages/user-management.html" class="nav-item">用户管理</a>
                    <a href="../pages/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../pages/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../pages/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../pages/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../pages/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../pages/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../pages/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../pages/demo.html" class="nav-item">演示展示</a>
                    <a href="../pages/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../pages/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="demo-content">
                <h1>智能家居与施工管理平台</h1>
                <p>全新的菜单结构，按照业务流程重新组织，提供更加直观的导航体验。</p>

                <div class="demo-features">
                    <div class="feature-card">
                        <div class="feature-icon">🏠</div>
                        <div class="feature-title">业务流程化</div>
                        <div class="feature-desc">7个核心模块，覆盖完整业务流程</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">毛玻璃效果</div>
                        <div class="feature-desc">现代化透明毛玻璃样式设计</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <div class="feature-title">数据驱动</div>
                        <div class="feature-desc">独立的数据分析模块支持决策</div>
                    </div>
                </div>

                <div style="margin-top: 40px; padding: 24px; background: white; border-radius: 12px; border: 1px solid #e5e7eb;">
                    <h2 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: #1f2937;">📋 模块统计</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">2</div>
                            <div style="font-size: 14px; color: #6b7280;">个人中心</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">7</div>
                            <div style="font-size: 14px; color: #6b7280;">业务管理</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">4</div>
                            <div style="font-size: 14px; color: #6b7280;">商务中心</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">6</div>
                            <div style="font-size: 14px; color: #6b7280;">知识库</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">7</div>
                            <div style="font-size: 14px; color: #6b7280;">系统工具</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">4</div>
                            <div style="font-size: 14px; color: #6b7280;">数据分析</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: #000;">3</div>
                            <div style="font-size: 14px; color: #6b7280;">个人中心</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 24px; padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #000;">
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 8px; color: #1f2937;">💡 新版特性</h3>
                    <ul style="font-size: 14px; color: #6b7280; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>按业务流程重新组织菜单结构</li>
                        <li>数据分析独立为单独模块</li>
                        <li>知识库分类更加细化专业</li>
                        <li>权限管理区分内部和客户</li>
                        <li>个人中心功能更加完善</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script>
        console.log('JavaScript开始执行...');

        // 自动设置当前页面的导航高亮
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM内容已加载');

            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            console.log('当前页面:', currentPage);
            console.log('菜单项数量:', navItems.length);
            console.log('侧边栏元素:', sidebar);
            console.log('主内容元素:', mainContent);

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPage) {
                    item.classList.add('active');
                }
            });

            // 演示用：高亮第一个菜单项
            if (navItems.length > 0) {
                navItems[0].classList.add('active');
                console.log('已设置第一个菜单项为激活状态');
            }

            // 检查页面是否正常显示
            if (sidebar && mainContent) {
                console.log('✅ 页面结构正常，应该可以正常显示');
            } else {
                console.log('❌ 页面结构异常');
            }
        });

        // 页面加载完成后的检查
        window.addEventListener('load', function() {
            console.log('页面完全加载完成');
        });
    </script>
</body>
</html>

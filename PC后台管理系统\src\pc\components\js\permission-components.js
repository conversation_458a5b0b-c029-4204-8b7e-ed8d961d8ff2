/**
 * 权限感知UI组件
 * 提供基于权限的界面元素控制
 * 版本: v1.0
 * 创建时间: 2025-07-01
 */

class PermissionComponents {
    constructor(authContext) {
        this.authContext = authContext || window.authContext
        this.components = new Map()
        this.observers = []
        
        // 监听权限变化
        this.authContext.addEventListener('auth-changed', () => {
            this.updateAllComponents()
        })
        
        this.authContext.addEventListener('permissions-changed', () => {
            this.updateAllComponents()
        })
        
        console.log('✅ 权限感知UI组件已初始化')
    }

    /**
     * 初始化权限组件
     */
    async initialize() {
        try {
            // 确保权限上下文已初始化
            if (!this.authContext.initialized) {
                await this.authContext.initialize()
            }
            
            // 扫描页面中的权限组件
            this.scanPermissionComponents()
            
            // 更新所有组件
            this.updateAllComponents()
            
            // 启动DOM变化监听
            this.startDOMObserver()
            
            console.log('✅ 权限组件初始化完成')
            
        } catch (error) {
            console.error('❌ 权限组件初始化失败:', error)
            throw error
        }
    }

    /**
     * 扫描页面中的权限组件
     */
    scanPermissionComponents() {
        // 扫描所有带有权限属性的元素
        const permissionElements = document.querySelectorAll(`
            [data-require-auth],
            [data-require-roles],
            [data-require-permissions],
            [data-require-any-permission],
            [data-hide-if-auth],
            [data-hide-if-roles],
            [data-hide-if-permissions]
        `)
        
        permissionElements.forEach(element => {
            this.registerComponent(element)
        })
        
        console.log(`📝 扫描到 ${permissionElements.length} 个权限组件`)
    }

    /**
     * 注册权限组件
     */
    registerComponent(element) {
        const id = element.id || `perm-comp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        if (!element.id) {
            element.id = id
        }
        
        const config = this.parseElementConfig(element)
        
        this.components.set(id, {
            element,
            config,
            originalDisplay: element.style.display || '',
            originalVisibility: element.style.visibility || ''
        })
        
        console.log(`📝 权限组件已注册: ${id}`, config)
    }

    /**
     * 解析元素配置
     */
    parseElementConfig(element) {
        const config = {
            requireAuth: element.hasAttribute('data-require-auth'),
            requireRoles: this.parseAttributeArray(element.getAttribute('data-require-roles')),
            requirePermissions: this.parseAttributeArray(element.getAttribute('data-require-permissions')),
            requireAnyPermission: this.parseAttributeArray(element.getAttribute('data-require-any-permission')),
            hideIfAuth: element.hasAttribute('data-hide-if-auth'),
            hideIfRoles: this.parseAttributeArray(element.getAttribute('data-hide-if-roles')),
            hideIfPermissions: this.parseAttributeArray(element.getAttribute('data-hide-if-permissions')),
            hideMethod: element.getAttribute('data-hide-method') || 'display', // display, visibility, remove
            showMethod: element.getAttribute('data-show-method') || 'display'
        }
        
        return config
    }

    /**
     * 解析属性数组
     */
    parseAttributeArray(value) {
        if (!value) return []
        return value.split(',').map(item => item.trim()).filter(item => item)
    }

    /**
     * 更新所有组件
     */
    updateAllComponents() {
        this.components.forEach((component, id) => {
            this.updateComponent(id)
        })
    }

    /**
     * 更新单个组件
     */
    updateComponent(id) {
        const component = this.components.get(id)
        if (!component) {
            return
        }
        
        const { element, config } = component
        const shouldShow = this.shouldShowElement(config)
        
        if (shouldShow) {
            this.showElement(element, config)
        } else {
            this.hideElement(element, config)
        }
    }

    /**
     * 判断是否应该显示元素
     */
    shouldShowElement(config) {
        // 检查隐藏条件
        if (config.hideIfAuth && this.authContext.isAuthenticated()) {
            return false
        }
        
        if (config.hideIfRoles.length > 0 && this.authContext.hasRole(config.hideIfRoles)) {
            return false
        }
        
        if (config.hideIfPermissions.length > 0 && this.authContext.hasPermission(config.hideIfPermissions)) {
            return false
        }
        
        // 检查显示条件
        if (config.requireAuth && !this.authContext.isAuthenticated()) {
            return false
        }
        
        if (config.requireRoles.length > 0 && !this.authContext.hasRole(config.requireRoles)) {
            return false
        }
        
        if (config.requirePermissions.length > 0 && !this.authContext.hasPermission(config.requirePermissions)) {
            return false
        }
        
        if (config.requireAnyPermission.length > 0 && !this.authContext.hasAnyPermission(config.requireAnyPermission)) {
            return false
        }
        
        return true
    }

    /**
     * 显示元素
     */
    showElement(element, config) {
        switch (config.showMethod) {
            case 'visibility':
                element.style.visibility = 'visible'
                break
            case 'display':
            default:
                element.style.display = ''
                break
        }
        
        element.setAttribute('data-permission-visible', 'true')
    }

    /**
     * 隐藏元素
     */
    hideElement(element, config) {
        switch (config.hideMethod) {
            case 'visibility':
                element.style.visibility = 'hidden'
                break
            case 'remove':
                element.remove()
                this.components.delete(element.id)
                break
            case 'display':
            default:
                element.style.display = 'none'
                break
        }
        
        element.setAttribute('data-permission-visible', 'false')
    }

    /**
     * 启动DOM变化监听
     */
    startDOMObserver() {
        const observer = new MutationObserver((mutations) => {
            let needsUpdate = false
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const permissionElements = node.querySelectorAll(`
                                [data-require-auth],
                                [data-require-roles],
                                [data-require-permissions],
                                [data-require-any-permission],
                                [data-hide-if-auth],
                                [data-hide-if-roles],
                                [data-hide-if-permissions]
                            `)
                            
                            if (permissionElements.length > 0 || node.hasAttribute('data-require-auth')) {
                                needsUpdate = true
                            }
                        }
                    })
                }
            })
            
            if (needsUpdate) {
                this.scanPermissionComponents()
                this.updateAllComponents()
            }
        })
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        })
        
        this.observers.push(observer)
    }

    /**
     * 创建权限按钮
     */
    createPermissionButton(config) {
        const button = document.createElement('button')
        button.textContent = config.text || '按钮'
        button.className = config.className || 'btn btn-primary'
        
        // 设置权限属性
        if (config.requireRoles) {
            button.setAttribute('data-require-roles', config.requireRoles.join(','))
        }
        if (config.requirePermissions) {
            button.setAttribute('data-require-permissions', config.requirePermissions.join(','))
        }
        
        // 绑定点击事件
        if (config.onClick) {
            button.addEventListener('click', config.onClick)
        }
        
        // 注册组件
        this.registerComponent(button)
        this.updateComponent(button.id)
        
        return button
    }

    /**
     * 创建权限链接
     */
    createPermissionLink(config) {
        const link = document.createElement('a')
        link.textContent = config.text || '链接'
        link.href = config.href || '#'
        link.className = config.className || 'link'
        
        // 设置权限属性
        if (config.requireRoles) {
            link.setAttribute('data-require-roles', config.requireRoles.join(','))
        }
        if (config.requirePermissions) {
            link.setAttribute('data-require-permissions', config.requirePermissions.join(','))
        }
        
        // 注册组件
        this.registerComponent(link)
        this.updateComponent(link.id)
        
        return link
    }

    /**
     * 创建权限容器
     */
    createPermissionContainer(config) {
        const container = document.createElement('div')
        container.className = config.className || 'permission-container'
        
        // 设置权限属性
        if (config.requireAuth) {
            container.setAttribute('data-require-auth', '')
        }
        if (config.requireRoles) {
            container.setAttribute('data-require-roles', config.requireRoles.join(','))
        }
        if (config.requirePermissions) {
            container.setAttribute('data-require-permissions', config.requirePermissions.join(','))
        }
        
        // 添加内容
        if (config.content) {
            container.innerHTML = config.content
        }
        
        // 注册组件
        this.registerComponent(container)
        this.updateComponent(container.id)
        
        return container
    }

    /**
     * 批量设置元素权限
     */
    setElementPermissions(selector, permissions) {
        const elements = document.querySelectorAll(selector)
        
        elements.forEach(element => {
            // 设置权限属性
            Object.entries(permissions).forEach(([key, value]) => {
                if (Array.isArray(value)) {
                    element.setAttribute(`data-${key}`, value.join(','))
                } else if (value === true) {
                    element.setAttribute(`data-${key}`, '')
                } else if (value) {
                    element.setAttribute(`data-${key}`, value)
                }
            })
            
            // 重新注册组件
            this.registerComponent(element)
            this.updateComponent(element.id)
        })
        
        console.log(`✅ 已为 ${elements.length} 个元素设置权限`)
    }

    /**
     * 获取组件状态
     */
    getComponentStatus() {
        const status = {
            total: this.components.size,
            visible: 0,
            hidden: 0,
            components: []
        }
        
        this.components.forEach((component, id) => {
            const isVisible = component.element.getAttribute('data-permission-visible') === 'true'
            
            if (isVisible) {
                status.visible++
            } else {
                status.hidden++
            }
            
            status.components.push({
                id,
                visible: isVisible,
                config: component.config
            })
        })
        
        return status
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 停止DOM监听
        this.observers.forEach(observer => {
            observer.disconnect()
        })
        this.observers = []
        
        // 清除组件
        this.components.clear()
        
        console.log('✅ 权限组件已销毁')
    }
}

// 权限组件辅助函数
class PermissionHelpers {
    static authContext = window.authContext
    
    /**
     * 检查是否有权限
     */
    static can(permission) {
        return this.authContext?.hasPermission(permission) || false
    }
    
    /**
     * 检查是否有角色
     */
    static is(role) {
        return this.authContext?.hasRole(role) || false
    }
    
    /**
     * 检查是否已登录
     */
    static authenticated() {
        return this.authContext?.isAuthenticated() || false
    }
    
    /**
     * 获取用户信息
     */
    static user() {
        return this.authContext?.user || null
    }
    
    /**
     * 条件渲染
     */
    static when(condition, trueContent, falseContent = '') {
        return condition ? trueContent : falseContent
    }
    
    /**
     * 权限渲染
     */
    static canRender(permission, content, fallback = '') {
        return this.can(permission) ? content : fallback
    }
    
    /**
     * 角色渲染
     */
    static roleRender(role, content, fallback = '') {
        return this.is(role) ? content : fallback
    }
}

// 创建全局权限组件实例
if (typeof window !== 'undefined') {
    window.permissionComponents = new PermissionComponents()
    window.can = PermissionHelpers.can.bind(PermissionHelpers)
    window.is = PermissionHelpers.is.bind(PermissionHelpers)
    window.authenticated = PermissionHelpers.authenticated.bind(PermissionHelpers)
}

// 导出权限组件类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PermissionComponents, PermissionHelpers }
}

console.log('✅ 权限感知UI组件已加载')

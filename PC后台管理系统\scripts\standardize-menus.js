/**
 * 标准化所有页面的左侧菜单脚本
 * 将所有页面的菜单统一为 index.html 的标准菜单结构
 */

const fs = require('fs');
const path = require('path');

// 页面目录
const pagesDir = '../src/pc/components/pages';

// 标准菜单HTML结构
const standardSidebar = `        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>
            
            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <!-- 主要功能 -->
                <div class="nav-section">
                    <div class="nav-section-title">主要功能</div>
                    <a href="index.html" class="nav-item">
                        <i class="fas fa-chart-pie nav-item-icon"></i>
                        数据概览
                    </a>
                    <a href="user-management-optimized.html" class="nav-item">
                        <i class="fas fa-users nav-item-icon"></i>
                        用户管理
                    </a>
                    <a href="permissions.html" class="nav-item">
                        <i class="fas fa-shield-alt nav-item-icon"></i>
                        权限管理
                    </a>
                    <a href="projects.html" class="nav-item">
                        <i class="fas fa-hammer nav-item-icon"></i>
                        项目管理
                    </a>
                    <a href="auth-test.html" class="nav-item">
                        <i class="fas fa-paint-brush nav-item-icon"></i>
                        API测试
                    </a>
                </div>
                
                <!-- 商务管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">商务管理</div>
                    <a href="products.html" class="nav-item">
                        <i class="fas fa-cubes nav-item-icon"></i>
                        产品管理
                    </a>
                    <a href="orders.html" class="nav-item">
                        <i class="fas fa-shopping-cart nav-item-icon"></i>
                        订单管理
                    </a>
                    <a href="analytics.html" class="nav-item">
                        <i class="fas fa-chart-bar nav-item-icon"></i>
                        数据分析
                    </a>
                </div>
                
                <!-- 内容管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">内容管理</div>
                    <a href="contract-management.html" class="nav-item">
                        <i class="fas fa-file-contract nav-item-icon"></i>
                        合同管理
                    </a>
                    <a href="demo.html" class="nav-item" target="_blank">
                        <i class="fas fa-images nav-item-icon"></i>
                        演示展示
                    </a>
                    <a href="api-tester.html" class="nav-item">
                        <i class="fas fa-cogs nav-item-icon"></i>
                        API工具
                    </a>
                </div>
                
                <!-- 系统设置 -->
                <div class="nav-section">
                    <div class="nav-section-title">系统设置</div>
                    <a href="settings.html" class="nav-item">
                        <i class="fas fa-cog nav-item-icon"></i>
                        系统配置
                    </a>
                    <a href="user-profile.html" class="nav-item">
                        <i class="fas fa-user-cog nav-item-icon"></i>
                        用户资料
                    </a>
                    <a href="login.html" class="nav-item">
                        <i class="fas fa-sign-out-alt nav-item-icon"></i>
                        退出登录
                    </a>
                </div>
            </nav>
        </aside>`;

// 需要更新的文件列表（排除登录页面和已经正确的页面）
const filesToUpdate = [
    'api-tester.html',
    'auth-test.html', 
    'contract-management.html',
    'create-requirement-optimized.html',
    'dashboard.html',
    'demo.html',
    'design.html',
    'optimized-requirements.html',
    'permissions.html',
    'requirements-management-fixed.html',
    'settings.html',
    'unified-admin-system.html',
    'user-profile-test.html',
    'user-profile.html',
    'users.html',
    '一装ERP-API文档.html'
];

// 页面特定的激活状态映射
const activeStates = {
    'index.html': 'index.html',
    'admin-dashboard-optimized.html': 'index.html',
    'user-management-optimized.html': 'user-management-optimized.html',
    'users.html': 'user-management-optimized.html',
    'permissions.html': 'permissions.html',
    'projects.html': 'projects.html',
    'auth-test.html': 'auth-test.html',
    'products.html': 'products.html',
    'orders.html': 'orders.html',
    'analytics.html': 'analytics.html',
    'contract-management.html': 'contract-management.html',
    'demo.html': 'demo.html',
    'api-tester.html': 'api-tester.html',
    'settings.html': 'settings.html',
    'user-profile.html': 'user-profile.html'
};

function setActiveMenuItem(sidebarHtml, fileName) {
    // 移除所有现有的 active 类
    let updatedHtml = sidebarHtml.replace(/class="nav-item active"/g, 'class="nav-item"');
    
    // 根据文件名设置对应的激活状态
    const activeFile = activeStates[fileName];
    if (activeFile) {
        updatedHtml = updatedHtml.replace(
            new RegExp(`href="${activeFile}" class="nav-item"`),
            `href="${activeFile}" class="nav-item active"`
        );
    }
    
    return updatedHtml;
}

function updateFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找侧边栏的开始和结束位置
        const sidebarStartRegex = /<aside[^>]*class="sidebar"[^>]*>|<div[^>]*class="sidebar"[^>]*>/;
        const sidebarEndRegex = /<\/aside>|<\/div>/;
        
        const startMatch = content.match(sidebarStartRegex);
        if (!startMatch) {
            console.log(`⏭️  跳过: ${fileName} (未找到侧边栏)`);
            return false;
        }
        
        const startIndex = startMatch.index;
        const beforeSidebar = content.substring(0, startIndex);
        
        // 查找侧边栏结束位置
        let depth = 0;
        let endIndex = -1;
        let inTag = false;
        let tagName = '';
        
        for (let i = startIndex; i < content.length; i++) {
            const char = content[i];
            
            if (char === '<') {
                inTag = true;
                tagName = '';
            } else if (char === '>' && inTag) {
                inTag = false;
                
                if (tagName.startsWith('/')) {
                    depth--;
                    if (depth === 0) {
                        endIndex = i + 1;
                        break;
                    }
                } else if (!tagName.endsWith('/')) {
                    depth++;
                }
            } else if (inTag && char !== ' ') {
                tagName += char;
            }
        }
        
        if (endIndex === -1) {
            console.log(`❌ 错误: ${fileName} - 无法找到侧边栏结束位置`);
            return false;
        }
        
        const afterSidebar = content.substring(endIndex);
        
        // 生成带有正确激活状态的侧边栏
        const customSidebar = setActiveMenuItem(standardSidebar, fileName);
        
        // 重新组合内容
        const newContent = beforeSidebar + customSidebar + afterSidebar;
        
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ 已更新: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🚀 开始标准化所有页面的左侧菜单...\n');
    
    let updatedCount = 0;
    let totalCount = 0;
    
    filesToUpdate.forEach(fileName => {
        const filePath = path.join(pagesDir, fileName);
        
        if (fs.existsSync(filePath)) {
            totalCount++;
            if (updateFile(filePath)) {
                updatedCount++;
            }
        } else {
            console.log(`⚠️  文件不存在: ${fileName}`);
        }
    });
    
    console.log(`\n📊 菜单标准化完成:`);
    console.log(`   - 总文件数: ${totalCount}`);
    console.log(`   - 已更新: ${updatedCount}`);
    console.log(`   - 跳过: ${totalCount - updatedCount}`);
    
    if (updatedCount > 0) {
        console.log('\n✨ 所有页面现在都使用统一的标准菜单结构');
        console.log('🎯 菜单激活状态已根据页面自动设置');
        console.log('🎨 统一的智能家居管理系统导航体验');
    }
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { updateFile, setActiveMenuItem, main };

/**
 * 权限管理相关类型定义
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

// 用户角色枚举
export enum SmartHomeRole {
  OWNER = 'OWNER',
  FAMILY_MEMBER = 'FAMILY_MEMBER',
  HOME_DESIGNER = 'HOME_DESIGNER',
  SMART_HOME_DESIGNER = 'SMART_HOME_DESIGNER',
  CONSTRUCTOR = 'CONSTRUCTOR'
}

// 权限操作枚举
export enum PermissionOperation {
  VIEW = 'VIEW',
  CREATE = 'CREATE',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  COMMENT = 'COMMENT',
  APPROVE = 'APPROVE',
  INVITE = 'INVITE',
  MANAGE = 'MANAGE',
  CONFIGURE = 'CONFIGURE',
  UPDATE = 'UPDATE',
  UPLOAD = 'UPLOAD',
  DOWNLOAD = 'DOWNLOAD',
  INSPECT = 'INSPECT',
  ANALYZE = 'ANALYZE',
  SHARE = 'SHARE',
  EARN_REWARDS = 'EARN_REWARDS',
  VIEW_STATS = 'VIEW_STATS'
}

// 权限模块枚举
export enum PermissionModule {
  PROJECT = 'project',
  DESIGN = 'design',
  CONSTRUCTION = 'construction',
  COST = 'cost',
  FILES = 'files',
  COMMENTS = 'comments',
  MARKETING = 'marketing',
  SYSTEM = 'system'
}

// 子模块定义
export interface SubModule {
  name: string;
  operations: PermissionOperation[];
}

// 权限模块定义
export interface Module {
  name: string;
  icon: React.ReactNode;
  color: string;
  subModules: Record<string, SubModule>;
}

// 角色定义
export interface Role {
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  permissions: Record<PermissionModule, PermissionOperation[]>;
}

// 用户信息
export interface User {
  id: string;
  username: string;
  email: string;
  role: SmartHomeRole;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  status: 'active' | 'inactive' | 'suspended';
}

// 权限检查请求
export interface PermissionCheckRequest {
  userId: string;
  module: PermissionModule;
  operation: PermissionOperation;
  projectId?: string;
  context?: Record<string, any>;
}

// 权限检查响应
export interface PermissionCheckResponse {
  hasPermission: boolean;
  reason?: string;
  context?: Record<string, any>;
}

// 角色权限配置
export interface RolePermissionConfig {
  role: SmartHomeRole;
  permissions: Record<PermissionModule, PermissionOperation[]>;
  modules: Record<PermissionModule, Module>;
  updatedAt: string;
  updatedBy: string;
}

// 权限更新请求
export interface PermissionUpdateRequest {
  role: SmartHomeRole;
  permissions: Record<PermissionModule, PermissionOperation[]>;
  reason?: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

// 分页请求参数
export interface PaginationParams {
  current: number;
  pageSize: number;
  total?: number;
}

// 用户列表请求参数
export interface UserListParams extends PaginationParams {
  role?: SmartHomeRole;
  status?: 'active' | 'inactive' | 'suspended';
  keyword?: string;
  sortBy?: 'createdAt' | 'lastLoginAt' | 'username';
  sortOrder?: 'asc' | 'desc';
}

// 用户列表响应
export interface UserListResponse {
  users: User[];
  total: number;
  current: number;
  pageSize: number;
}

// 项目空间信息
export interface ProjectSpace {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  status: 'active' | 'completed' | 'suspended';
  createdAt: string;
  updatedAt: string;
  memberCount: number;
  progress: number;
}

// 项目成员信息
export interface ProjectMember {
  id: string;
  projectId: string;
  userId: string;
  role: SmartHomeRole;
  permissions: Record<PermissionModule, PermissionOperation[]>;
  invitedBy: string;
  joinedAt: string;
  status: 'active' | 'pending' | 'inactive';
}

// 邀请信息
export interface Invitation {
  id: string;
  projectId: string;
  inviterId: string;
  inviteeId: string;
  role: SmartHomeRole;
  permissions?: Record<PermissionModule, PermissionOperation[]>;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  expiresAt: string;
  message?: string;
}

// 权限日志
export interface PermissionLog {
  id: string;
  userId: string;
  action: 'grant' | 'revoke' | 'check' | 'update';
  module: PermissionModule;
  operation: PermissionOperation;
  projectId?: string;
  result: 'success' | 'denied' | 'error';
  reason?: string;
  ip: string;
  userAgent: string;
  createdAt: string;
}

// 权限统计信息
export interface PermissionStats {
  totalUsers: number;
  activeUsers: number;
  roleDistribution: Record<SmartHomeRole, number>;
  moduleUsage: Record<PermissionModule, number>;
  recentActivity: PermissionLog[];
  topOperations: Array<{
    module: PermissionModule;
    operation: PermissionOperation;
    count: number;
  }>;
}

// 组件Props类型
export interface PermissionManagementProps {
  initialRole?: SmartHomeRole;
  onRoleChange?: (role: SmartHomeRole) => void;
  onPermissionUpdate?: (role: SmartHomeRole, permissions: Record<PermissionModule, PermissionOperation[]>) => void;
  readonly?: boolean;
}

export interface RoleListProps {
  roles: Record<SmartHomeRole, Role>;
  selectedRole: SmartHomeRole;
  onRoleSelect: (role: SmartHomeRole) => void;
  readonly?: boolean;
}

export interface PermissionTreeProps {
  role: SmartHomeRole;
  permissions: Record<PermissionModule, PermissionOperation[]>;
  modules: Record<PermissionModule, Module>;
  onPermissionChange: (permissions: Record<PermissionModule, PermissionOperation[]>) => void;
  readonly?: boolean;
  loading?: boolean;
}

// 表单类型
export interface RoleFormData {
  name: string;
  description: string;
  color: string;
  permissions: Record<PermissionModule, PermissionOperation[]>;
}

export interface UserFormData {
  username: string;
  email: string;
  role: SmartHomeRole;
  password?: string;
  avatar?: string;
}

export interface InvitationFormData {
  projectId: string;
  inviteeEmail: string;
  role: SmartHomeRole;
  permissions?: Record<PermissionModule, PermissionOperation[]>;
  message?: string;
  expiresIn: number; // 过期时间（小时）
}

// 错误类型
export interface PermissionError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// 常量类型
export interface PermissionConstants {
  ROLES: Record<SmartHomeRole, Role>;
  MODULES: Record<PermissionModule, Module>;
  OPERATIONS: Record<PermissionOperation, string>;
  COLORS: Record<SmartHomeRole, string>;
  ICONS: Record<PermissionModule, React.ReactNode>;
}

// 配置类型
export interface PermissionConfig {
  enableRoleHierarchy: boolean;
  enablePermissionInheritance: boolean;
  enableProjectLevelPermissions: boolean;
  enablePermissionAudit: boolean;
  defaultSessionTimeout: number;
  maxInvitationDuration: number;
  enableNotifications: boolean;
}

// 导出所有类型
export type {
  Module,
  Role,
  User,
  ProjectSpace,
  ProjectMember,
  Invitation,
  PermissionLog,
  PermissionStats,
  ApiResponse,
  PaginationParams,
  UserListParams,
  UserListResponse,
  PermissionCheckRequest,
  PermissionCheckResponse,
  RolePermissionConfig,
  PermissionUpdateRequest,
  PermissionManagementProps,
  RoleListProps,
  PermissionTreeProps,
  RoleFormData,
  UserFormData,
  InvitationFormData,
  PermissionError,
  PermissionConstants,
  PermissionConfig
};

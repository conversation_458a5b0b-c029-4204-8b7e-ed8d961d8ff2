<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内部员工权限管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item active">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">内部员工权限管理</h1>
                            <p class="breadcrumb-description">管理公司内部员工的系统权限和访问控制</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 内部权限管理主界面 -->
                <div class="internal-permissions-container">
                    <!-- 左侧内部角色列表 -->
                    <div class="internal-roles-panel">
                        <div class="panel-header">
                            <h3>内部角色管理</h3>
                            <button class="btn btn-primary" onclick="addInternalRole()">
                                <i class="fas fa-plus"></i> 新增角色
                            </button>
                        </div>
                        <div class="internal-roles-list" id="internalRolesList">
                            <!-- 内部角色列表 -->
                            <div class="internal-role-item active" data-role="DESIGNER" onclick="selectInternalRole('DESIGNER')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #3b82f6;">
                                        <i class="fas fa-drafting-compass"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">设计师</div>
                                        <div class="role-desc">L3 - 管理权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">核心业务</span>
                                            <span class="role-session">240分钟</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('DESIGNER')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="internal-role-item" data-role="MARKETING" onclick="selectInternalRole('MARKETING')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #10b981;">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">营销人员</div>
                                        <div class="role-desc">L2 - 业务权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">营销推广</span>
                                            <span class="role-session">180分钟</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('MARKETING')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="internal-role-item" data-role="CUSTOMER_SERVICE" onclick="selectInternalRole('CUSTOMER_SERVICE')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #f59e0b;">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">客服人员</div>
                                        <div class="role-desc">L2 - 业务权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">客户服务</span>
                                            <span class="role-session">240分钟</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('CUSTOMER_SERVICE')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="internal-role-item" data-role="SALES" onclick="selectInternalRole('SALES')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #8b5cf6;">
                                        <i class="fas fa-handshake"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">销售人员</div>
                                        <div class="role-desc">L2 - 业务权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">销售管理</span>
                                            <span class="role-session">180分钟</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('SALES')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="internal-role-item" data-role="ADMIN" onclick="selectInternalRole('ADMIN')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #ef4444;">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">系统管理员</div>
                                        <div class="role-desc">L4 - 超级权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">系统管理</span>
                                            <span class="role-session">480分钟</span>
                                            <span class="role-security"><i class="fas fa-shield-alt"></i> MFA</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('ADMIN')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="internal-role-item" data-role="SUPER_ADMIN" onclick="selectInternalRole('SUPER_ADMIN')">
                                <div class="role-info">
                                    <div class="role-icon" style="background: #1a1a1a;">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                    <div class="role-details">
                                        <div class="role-name">超级管理员</div>
                                        <div class="role-desc">L5 - 完全权限</div>
                                        <div class="role-meta">
                                            <span class="role-badge">完全控制</span>
                                            <span class="role-session">无限制</span>
                                            <span class="role-security"><i class="fas fa-shield-alt"></i> MFA</span>
                                            <span class="role-security"><i class="fas fa-map-marker-alt"></i> IP限制</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="role-actions">
                                    <button class="btn-icon" onclick="editInternalRole('SUPER_ADMIN')" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧权限配置 -->
                    <div class="internal-permissions-panel">
                        <div class="panel-header">
                            <h3 id="internalPermissionTitle">设计师 - 权限配置</h3>
                            <div class="permission-actions">
                                <button class="btn btn-success" onclick="saveInternalPermissions()" id="saveInternalBtn">
                                    <i class="fas fa-save"></i> 保存权限
                                </button>
                                <button class="btn btn-secondary" onclick="resetInternalPermissions()" id="resetInternalBtn">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </div>
                        
                        <!-- 角色信息概览 -->
                        <div class="role-overview" id="roleOverview">
                            <div class="overview-item">
                                <i class="fas fa-layer-group"></i>
                                <span>权限等级: <strong>L3 - 管理权限</strong></span>
                            </div>
                            <div class="overview-item">
                                <i class="fas fa-clock"></i>
                                <span>会话超时: <strong>240分钟</strong></span>
                            </div>
                            <div class="overview-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>多因子认证: <strong>否</strong></span>
                            </div>
                            <div class="overview-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>IP限制: <strong>否</strong></span>
                            </div>
                        </div>
                        
                        <div class="internal-permissions-tree" id="internalPermissionsTree">
                            <!-- 内部权限树结构 -->
                            <div class="permission-category">
                                <h4><i class="fas fa-briefcase"></i> 业务管理</h4>
                                
                                <div class="permission-module">
                                    <div class="module-header">
                                        <div class="module-toggle">
                                            <input type="checkbox" id="design-management-module" checked onchange="toggleInternalModule('design-management')">
                                            <label for="design-management-module">
                                                <i class="fas fa-drafting-compass"></i>
                                                设计管理
                                            </label>
                                        </div>
                                    </div>
                                    <div class="module-permissions" id="design-management-permissions">
                                        <div class="permission-group">
                                            <h5>设计服务管理</h5>
                                            <div class="permission-items">
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 查看设计服务
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 创建设计服务
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 编辑设计服务
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 删除设计服务
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 审批设计方案
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-group">
                                            <h5>设计项目管理</h5>
                                            <div class="permission-items">
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 查看设计项目
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 创建设计项目
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 分配设计师
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 上传设计文件
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="permission-module">
                                    <div class="module-header">
                                        <div class="module-toggle">
                                            <input type="checkbox" id="project-management-module" checked onchange="toggleInternalModule('project-management')">
                                            <label for="project-management-module">
                                                <i class="fas fa-project-diagram"></i>
                                                项目管理
                                            </label>
                                        </div>
                                    </div>
                                    <div class="module-permissions" id="project-management-permissions">
                                        <div class="permission-group">
                                            <h5>项目基础管理</h5>
                                            <div class="permission-items">
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 查看项目
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 创建项目
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox" checked> 编辑项目
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox"> 删除项目
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="permission-category">
                                <h4><i class="fas fa-shopping-cart"></i> 商务管理</h4>
                                
                                <div class="permission-module">
                                    <div class="module-header">
                                        <div class="module-toggle">
                                            <input type="checkbox" id="product-management-module" onchange="toggleInternalModule('product-management')">
                                            <label for="product-management-module">
                                                <i class="fas fa-box"></i>
                                                商品管理
                                            </label>
                                        </div>
                                    </div>
                                    <div class="module-permissions" id="product-management-permissions" style="display: none;">
                                        <div class="permission-group">
                                            <h5>商品基础管理</h5>
                                            <div class="permission-items">
                                                <label class="permission-item">
                                                    <input type="checkbox"> 查看商品
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox"> 编辑商品
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox"> 上传商品资料
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="permission-category">
                                <h4><i class="fas fa-cogs"></i> 系统管理</h4>
                                
                                <div class="permission-module">
                                    <div class="module-header">
                                        <div class="module-toggle">
                                            <input type="checkbox" id="user-management-module" onchange="toggleInternalModule('user-management')">
                                            <label for="user-management-module">
                                                <i class="fas fa-users-cog"></i>
                                                用户管理
                                            </label>
                                        </div>
                                    </div>
                                    <div class="module-permissions" id="user-management-permissions" style="display: none;">
                                        <div class="permission-group">
                                            <h5>用户基础管理</h5>
                                            <div class="permission-items">
                                                <label class="permission-item">
                                                    <input type="checkbox"> 查看用户
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox"> 编辑用户
                                                </label>
                                                <label class="permission-item">
                                                    <input type="checkbox"> 导出用户数据
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../../js/admin-common.js"></script>
    <script src="../../js/internal-permissions-data.js"></script>
    <script src="../../js/internal-permissions.js"></script>

    <style>
        /* 内部权限管理专用样式 */
        .internal-permissions-container { display: grid; grid-template-columns: 400px 1fr; gap: var(--spacing-xl); height: calc(100vh - 200px); }
        .internal-roles-panel, .internal-permissions-panel { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-xl); overflow-y: auto; }
        .panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-xl); padding-bottom: var(--spacing-md); border-bottom: 1px solid var(--border-color); }
        .panel-header h3 { margin: 0; font-size: 18px; font-weight: 600; color: var(--text-primary); }

        /* 内部角色列表样式 */
        .internal-roles-list { display: flex; flex-direction: column; gap: var(--spacing-md); }
        .internal-role-item { background: var(--bg-muted); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-md); cursor: pointer; transition: all var(--transition-base); }
        .internal-role-item:hover { border-color: var(--primary-black); transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .internal-role-item.active { border-color: var(--primary-black); background: var(--bg-card); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .role-info { display: flex; align-items: flex-start; gap: var(--spacing-md); }
        .role-icon { width: 48px; height: 48px; border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; color: var(--text-inverse); font-size: 20px; flex-shrink: 0; }
        .role-details { flex: 1; }
        .role-name { font-size: 16px; font-weight: 600; color: var(--text-primary); margin-bottom: 4px; }
        .role-desc { font-size: 12px; color: var(--text-secondary); margin-bottom: 8px; }
        .role-meta { display: flex; flex-wrap: wrap; gap: 6px; }
        .role-badge, .role-session, .role-security { font-size: 10px; padding: 2px 6px; border-radius: var(--radius-sm); background: var(--bg-muted); color: var(--text-secondary); }
        .role-security { background: var(--warning-orange); color: var(--text-inverse); }
        .role-actions { display: flex; gap: var(--spacing-sm); }
        .btn-icon { width: 32px; height: 32px; border: none; background: var(--bg-muted); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all var(--transition-base); }
        .btn-icon:hover { background: var(--primary-black); color: var(--text-inverse); }

        /* 角色概览样式 */
        .role-overview { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-xl); padding: var(--spacing-md); background: var(--bg-muted); border-radius: var(--radius-md); }
        .overview-item { display: flex; align-items: center; gap: var(--spacing-sm); font-size: 12px; color: var(--text-secondary); }
        .overview-item i { color: var(--primary-black); }

        /* 权限树样式 */
        .internal-permissions-tree { display: flex; flex-direction: column; gap: var(--spacing-lg); }
        .permission-category { margin-bottom: var(--spacing-xl); }
        .permission-category h4 { font-size: 16px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm); }
        .permission-module { background: var(--bg-muted); border: 1px solid var(--border-color); border-radius: var(--radius-md); margin-bottom: var(--spacing-md); overflow: hidden; }
        .module-header { padding: var(--spacing-md); background: var(--bg-card); border-bottom: 1px solid var(--border-color); }
        .module-toggle { display: flex; align-items: center; gap: var(--spacing-sm); }
        .module-toggle input[type="checkbox"] { width: 18px; height: 18px; }
        .module-toggle label { font-size: 14px; font-weight: 500; color: var(--text-primary); display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer; }
        .module-permissions { padding: var(--spacing-md); }
        .permission-group { margin-bottom: var(--spacing-md); }
        .permission-group h5 { font-size: 13px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-sm); }
        .permission-items { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-sm); }
        .permission-item { display: flex; align-items: center; gap: var(--spacing-sm); font-size: 12px; color: var(--text-secondary); cursor: pointer; padding: 4px 0; }
        .permission-item input[type="checkbox"] { width: 16px; height: 16px; }

        /* 按钮样式 */
        .btn { padding: var(--spacing-sm) var(--spacing-md); border: 1px solid transparent; border-radius: var(--radius-sm); font-size: 12px; font-weight: 500; cursor: pointer; transition: all var(--transition-base); text-decoration: none; display: inline-flex; align-items: center; justify-content: center; gap: var(--spacing-xs); }
        .btn-primary { background: var(--primary-black); color: var(--text-inverse); }
        .btn-primary:hover { background: var(--gray-800); transform: translateY(-1px); }
        .btn-success { background: var(--success-green); color: var(--text-inverse); }
        .btn-success:hover { background: #059669; transform: translateY(-1px); }
        .btn-secondary { background: var(--bg-muted); color: var(--text-primary); border-color: var(--border-color); }
        .btn-secondary:hover { background: var(--bg-hover); border-color: var(--primary-black); }

        /* 响应式设计 */
        @media (max-width: 1200px) { .internal-permissions-container { grid-template-columns: 350px 1fr; } }
        @media (max-width: 768px) { .internal-permissions-container { grid-template-columns: 1fr; height: auto; } .role-overview { grid-template-columns: 1fr; } .permission-items { grid-template-columns: 1fr; } }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        // 当前选中的内部角色
        let currentInternalRole = 'DESIGNER';
        
        // 内部角色权限配置
        const internalRolePermissions = {
            DESIGNER: {
                level: 'L3 - 管理权限',
                sessionTime: '240分钟',
                requireMFA: false,
                ipRestriction: false,
                permissions: {
                    'design-management': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
                    'project-management': ['VIEW', 'CREATE', 'EDIT'],
                    'product-management': ['VIEW', 'EDIT'],
                    'knowledge-management': ['VIEW', 'CREATE', 'EDIT']
                }
            },
            MARKETING: {
                level: 'L2 - 业务权限',
                sessionTime: '180分钟',
                requireMFA: false,
                ipRestriction: false,
                permissions: {
                    'marketing-management': ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                    'customer-management': ['VIEW', 'EDIT'],
                    'product-management': ['VIEW']
                }
            },
            CUSTOMER_SERVICE: {
                level: 'L2 - 业务权限',
                sessionTime: '240分钟',
                requireMFA: false,
                ipRestriction: false,
                permissions: {
                    'customer-management': ['VIEW', 'EDIT', 'EXPORT'],
                    'order-management': ['VIEW', 'EDIT'],
                    'knowledge-management': ['VIEW']
                }
            },
            SALES: {
                level: 'L2 - 业务权限',
                sessionTime: '180分钟',
                requireMFA: false,
                ipRestriction: false,
                permissions: {
                    'customer-management': ['VIEW', 'CREATE', 'EDIT'],
                    'order-management': ['VIEW', 'CREATE', 'EDIT'],
                    'product-management': ['VIEW']
                }
            },
            ADMIN: {
                level: 'L4 - 超级权限',
                sessionTime: '480分钟',
                requireMFA: true,
                ipRestriction: false,
                permissions: {
                    'user-management': ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                    'permission-management': ['VIEW', 'EDIT', 'CONFIGURE'],
                    'system-management': ['VIEW', 'EDIT', 'CONFIGURE', 'MONITOR']
                }
            },
            SUPER_ADMIN: {
                level: 'L5 - 完全权限',
                sessionTime: '无限制',
                requireMFA: true,
                ipRestriction: true,
                permissions: {
                    'all-modules': ['ALL_PERMISSIONS']
                }
            }
        };
        
        // 选择内部角色
        function selectInternalRole(role) {
            // 更新选中状态
            document.querySelectorAll('.internal-role-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-role="${role}"]`).classList.add('active');
            
            // 更新当前角色
            currentInternalRole = role;
            
            // 更新权限标题
            const roleNames = {
                DESIGNER: '设计师',
                MARKETING: '营销人员',
                CUSTOMER_SERVICE: '客服人员',
                SALES: '销售人员',
                ADMIN: '系统管理员',
                SUPER_ADMIN: '超级管理员'
            };
            document.getElementById('internalPermissionTitle').textContent = `${roleNames[role]} - 权限配置`;
            
            // 更新角色概览
            updateRoleOverview(role);
            
            // 加载角色权限
            loadInternalRolePermissions(role);
        }
        
        // 更新角色概览
        function updateRoleOverview(role) {
            const roleConfig = internalRolePermissions[role];
            const overview = document.getElementById('roleOverview');
            
            overview.innerHTML = `
                <div class="overview-item">
                    <i class="fas fa-layer-group"></i>
                    <span>权限等级: <strong>${roleConfig.level}</strong></span>
                </div>
                <div class="overview-item">
                    <i class="fas fa-clock"></i>
                    <span>会话超时: <strong>${roleConfig.sessionTime}</strong></span>
                </div>
                <div class="overview-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>多因子认证: <strong>${roleConfig.requireMFA ? '是' : '否'}</strong></span>
                </div>
                <div class="overview-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>IP限制: <strong>${roleConfig.ipRestriction ? '是' : '否'}</strong></span>
                </div>
            `;
        }
        
        // 加载内部角色权限
        function loadInternalRolePermissions(role) {
            const permissions = internalRolePermissions[role].permissions;
            
            // 重置所有权限
            document.querySelectorAll('.permission-module').forEach(module => {
                const moduleId = module.querySelector('.module-toggle input').id.replace('-module', '');
                const hasPermissions = permissions[moduleId] && permissions[moduleId].length > 0;
                
                // 更新模块开关
                module.querySelector('.module-toggle input').checked = hasPermissions;
                
                // 更新权限显示
                const permissionsDiv = module.querySelector('.module-permissions');
                if (hasPermissions) {
                    permissionsDiv.style.display = 'block';
                    // 更新具体权限（简化处理）
                    const checkboxes = permissionsDiv.querySelectorAll('.permission-item input');
                    checkboxes.forEach((checkbox, index) => {
                        checkbox.checked = index < permissions[moduleId]?.length;
                    });
                } else {
                    permissionsDiv.style.display = 'none';
                    permissionsDiv.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                        cb.checked = false;
                    });
                }
            });
        }
        
        // 切换内部模块权限
        function toggleInternalModule(moduleId) {
            const moduleCheckbox = document.getElementById(`${moduleId}-module`);
            const permissionsDiv = document.getElementById(`${moduleId}-permissions`);
            
            if (moduleCheckbox.checked) {
                permissionsDiv.style.display = 'block';
            } else {
                permissionsDiv.style.display = 'none';
                // 取消所有子权限
                permissionsDiv.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    cb.checked = false;
                });
            }
        }
        
        // 保存内部权限
        function saveInternalPermissions() {
            // 收集当前权限配置
            const permissions = {};
            
            document.querySelectorAll('.permission-module').forEach(module => {
                const moduleId = module.querySelector('.module-toggle input').id.replace('-module', '');
                const isEnabled = module.querySelector('.module-toggle input').checked;
                
                if (isEnabled) {
                    permissions[moduleId] = [];
                    module.querySelectorAll('.permission-item input:checked').forEach(checkbox => {
                        permissions[moduleId].push('PERMISSION');
                    });
                }
            });
            
            // 更新角色权限配置
            internalRolePermissions[currentInternalRole].permissions = permissions;
            
            // 显示保存成功消息
            showToast('内部员工权限配置已保存！', 'success');
            console.log('保存的内部权限配置:', permissions);
        }
        
        // 重置内部权限
        function resetInternalPermissions() {
            if (confirm('确定要重置内部权限配置吗？')) {
                loadInternalRolePermissions(currentInternalRole);
            }
        }
        
        // 添加内部角色
        function addInternalRole() {
            // 创建模态框显示添加角色表单
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 500px;
                    max-height: 80vh;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">添加新内部角色</h3>
                    <form id="addRoleForm">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色名称</label>
                            <input type="text" name="roleName" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" placeholder="请输入角色名称" required>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">权限等级</label>
                            <select name="level" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" required>
                                <option value="L1">L1 - 基础权限</option>
                                <option value="L2">L2 - 业务权限</option>
                                <option value="L3">L3 - 管理权限</option>
                                <option value="L4">L4 - 超级权限</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">角色描述</label>
                            <textarea name="description" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; min-height: 80px;" placeholder="请输入角色描述"></textarea>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                            <button type="submit" style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">确认添加</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 处理表单提交
            modal.querySelector('#addRoleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const roleData = {
                    name: formData.get('roleName'),
                    level: formData.get('level'),
                    description: formData.get('description')
                };
                
                // 模拟保存角色
                console.log('新增角色数据:', roleData);
                showToast(`角色 "${roleData.name}" 添加成功！`, 'success');
                document.body.removeChild(modal);
            });
            
            // 点击外部关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
            // 全局关闭函数
            window.closeModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
        }
        
        // 编辑内部角色
        function editInternalRole(role) {
            const roleNames = {
                DESIGNER: '设计师',
                MARKETING: '营销人员',
                CUSTOMER_SERVICE: '客服人员',
                SALES: '销售人员',
                ADMIN: '系统管理员',
                SUPER_ADMIN: '超级管理员'
            };
            
            const roleConfig = internalRolePermissions[role];
            const roleName = roleNames[role];
            
            // 创建编辑模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">编辑角色: ${roleName}</h3>
                    <form id="editRoleForm">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">权限等级</label>
                            <input type="text" value="${roleConfig.level}" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;" readonly>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">会话超时时间</label>
                            <select name="sessionTime" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="60分钟" ${roleConfig.sessionTime === '60分钟' ? 'selected' : ''}>60分钟</option>
                                <option value="180分钟" ${roleConfig.sessionTime === '180分钟' ? 'selected' : ''}>180分钟</option>
                                <option value="240分钟" ${roleConfig.sessionTime === '240分钟' ? 'selected' : ''}>240分钟</option>
                                <option value="480分钟" ${roleConfig.sessionTime === '480分钟' ? 'selected' : ''}>480分钟</option>
                                <option value="无限制" ${roleConfig.sessionTime === '无限制' ? 'selected' : ''}>无限制</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="requireMFA" ${roleConfig.requireMFA ? 'checked' : ''}>
                                <span>启用多因子认证 (MFA)</span>
                            </label>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="ipRestriction" ${roleConfig.ipRestriction ? 'checked' : ''}>
                                <span>启用IP地址限制</span>
                            </label>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeEditModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                            <button type="submit" style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">保存更改</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 处理表单提交
            modal.querySelector('#editRoleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                
                // 更新角色配置
                internalRolePermissions[role].sessionTime = formData.get('sessionTime');
                internalRolePermissions[role].requireMFA = formData.has('requireMFA');
                internalRolePermissions[role].ipRestriction = formData.has('ipRestriction');
                
                // 如果当前选中的是编辑的角色，更新显示
                if (currentInternalRole === role) {
                    updateRoleOverview(role);
                }
                
                console.log('更新角色配置:', internalRolePermissions[role]);
                showToast(`角色 "${roleName}" 更新成功！`, 'success');
                document.body.removeChild(modal);
            });
            
            // 点击外部关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
            // 全局关闭函数
            window.closeEditModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
        }
        
        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选中第一个角色
            selectInternalRole('DESIGNER');
        });
    </script>
</body>
</html>

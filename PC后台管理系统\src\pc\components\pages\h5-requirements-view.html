<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理 - 智能家居后台管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f5f5f5;
            line-height: 1.6;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            font-size: 1.5rem;
            font-weight: 500;
        }
        .breadcrumb {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #eee;
            font-size: 0.9rem;
            color: #666;
        }
        .main-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .requirements-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: #3498db;
            color: white;
            padding: 1rem 2rem;
            font-weight: 500;
        }
        .table-content {
            padding: 1.5rem;
        }
        .requirement-item {
            border: 1px solid #eee;
            border-radius: 6px;
            margin-bottom: 1rem;
            padding: 1.5rem;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .requirement-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: white;
        }
        .req-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        .customer-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .source-tag {
            background: #e74c3c;
            color: white;
            padding: 0.2rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        .req-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .detail-item {
            display: flex;
            align-items: center;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
            margin-right: 0.5rem;
            min-width: 60px;
        }
        .detail-value {
            color: #333;
        }
        .requirements-text {
            background: white;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            margin-top: 1rem;
            font-style: italic;
            color: #555;
        }
        .actions {
            margin-top: 1rem;
            text-align: right;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-size: 1.2rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #666;
        }
        .empty-state .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 需求管理中心</h1>
    </div>
    
    <div class="breadcrumb">
        首页 > 客户管理 > 需求管理 > H5页面提交的需求数据
    </div>
    
    <div class="main-content">
        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number" id="totalRequirements">3</div>
                <div class="stat-label">📱 H5页面提交需求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingRequirements">3</div>
                <div class="stat-label">⏳ 待处理需求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgRooms">3.0</div>
                <div class="stat-label">🏠 平均房间数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="responseRate">100%</div>
                <div class="stat-label">📞 响应率</div>
            </div>
        </div>
        
        <!-- 需求列表 -->
        <div class="requirements-table">
            <div class="table-header">
                📱 H5移动端提交的客户需求 (实时同步)
            </div>
            <div class="table-content" id="requirementsContent">
                <!-- 需求数据将在这里动态加载 -->
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="loadRequirements()" title="刷新数据">
        🔄
    </button>
    
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // H5页面提交的模拟数据 (实际应用中从API获取)
        const h5SubmittedRequirements = [
            {
                id: 1,
                customer_name: '张女士',
                customer_phone: '13800138001',
                house_rooms: 3,
                house_halls: 2,
                house_bathrooms: 2,
                address: '北京市朝阳区三里屯SOHO A座1201室',
                requirements: '希望安装全套智能家居系统，包括智能照明、安防监控、环境控制等，预算20-30万',
                source: 'H5移动端',
                submission_time: '2025-07-29 14:30:22',
                status: '待处理'
            },
            {
                id: 2,
                customer_name: '李先生',
                customer_phone: '13800138002',
                house_rooms: 4,
                house_halls: 2,
                house_bathrooms: 3,
                address: '上海市浦东新区陆家嘴金茂大厦88层',
                requirements: '办公室智能化改造，需要会议室智能控制、访客管理系统、环境监控等',
                source: 'H5移动端',
                submission_time: '2025-07-29 15:45:18',
                status: '待处理'
            },
            {
                id: 3,
                customer_name: '王经理',
                customer_phone: '13800138003',
                house_rooms: 2,
                house_halls: 1,
                house_bathrooms: 1,
                address: '广州市天河区珠江新城CBD核心区',
                requirements: '小户型智能家居解决方案，重点关注节能环保和便利性',
                source: 'H5移动端',
                submission_time: '2025-07-29 16:20:45',
                status: '待处理'
            }
        ];
        
        // 加载需求数据
        function loadRequirements() {
            const container = document.getElementById('requirementsContent');
            
            if (h5SubmittedRequirements.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">📭</div>
                        <h3>暂无H5页面提交的需求</h3>
                        <p>等待移动端用户提交需求数据...</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            h5SubmittedRequirements.forEach(req => {
                html += `
                    <div class="requirement-item">
                        <div class="req-header">
                            <div class="customer-name">👤 ${req.customer_name}</div>
                            <div class="source-tag">${req.source}</div>
                        </div>
                        
                        <div class="req-details">
                            <div class="detail-item">
                                <span class="detail-label">📞 电话:</span>
                                <span class="detail-value">${req.customer_phone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🏠 房型:</span>
                                <span class="detail-value">${req.house_rooms}室${req.house_halls}厅${req.house_bathrooms}卫</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📅 提交:</span>
                                <span class="detail-value">${req.submission_time}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📊 状态:</span>
                                <span class="detail-value" style="color: #e74c3c; font-weight: bold;">${req.status}</span>
                            </div>
                        </div>
                        
                        <div style="margin: 1rem 0;">
                            <span class="detail-label">📍 地址:</span>
                            <span class="detail-value">${req.address}</span>
                        </div>
                        
                        <div class="requirements-text">
                            💡 客户需求: ${req.requirements}
                        </div>
                        
                        <div class="actions">
                            <button class="btn btn-primary" onclick="viewDetails(${req.id})">查看详情</button>
                            <button class="btn btn-success" onclick="processRequirement(${req.id})">开始处理</button>
                            <button class="btn btn-warning" onclick="contactCustomer('${req.customer_phone}')">联系客户</button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 页面功能函数
        function viewDetails(id) {
            showToast(`查看需求详情 ID: ${id} - 功能即将上线！`, 'info');
        }
        
        function processRequirement(id) {
            if (confirm('确认开始处理此需求吗？')) {
                showToast(`开始处理需求 ID: ${id}`, 'success');
                // 这里会调用后端API更新需求状态
            }
        }
        
        function contactCustomer(phone) {
            showToast(`联系客户: ${phone} - 功能即将上线！`, 'info');
        }
        
        // 页面加载完成后自动加载数据
        document.addEventListener('DOMContentLoaded', function() {
            loadRequirements();
            console.log('PC端需求管理页面已加载，显示H5页面提交的数据');
        });
        
        // 模拟实时数据更新 (实际应用中可能使用WebSocket)
        setInterval(() => {
            // 这里可以调用API检查新的需求数据
            console.log('检查H5页面新提交的需求数据...');
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>
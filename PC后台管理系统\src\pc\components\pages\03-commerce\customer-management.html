<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../design-system/design-tokens.css">
    <link rel="stylesheet" href="../../../design-system/components.css">

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #6b7280;
        }

        .breadcrumb-item:last-child {
            color: #1f2937;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #d1d5db;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item active">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航（面包屑） -->
            <div class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">商务中心</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">客户管理</span>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 页面标题和描述 -->
                <div class="page-header">
                    <h1 class="page-title">客户管理</h1>
                    <p class="page-subtitle">管理客户信息、联系记录和项目关联</p>
                </div>

            <!-- 页面内容 -->
            <div class="content-body">
                <!-- 统计卡片 -->
                <div class="customer-stats-grid">
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="total-customers">0</div>
                                    <div class="text-sm text-secondary">总客户数</div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="active-customers">0</div>
                                    <div class="text-sm text-secondary">活跃客户</div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="vip-customers">0</div>
                                    <div class="text-sm text-secondary">VIP客户</div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-crown fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-primary" id="new-customers">0</div>
                                    <div class="text-sm text-secondary">本月新增</div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-user-plus fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="tabs-container">
                    <div class="tabs-nav" data-tab-group="customer-management">
                        <button class="tab-button active" data-tab-trigger="customers">客户列表</button>
                        <button class="tab-button" data-tab-trigger="contacts">联系记录</button>
                        <button class="tab-button" data-tab-trigger="analytics">客户分析</button>
                    </div>
                </div>

                <!-- 客户列表标签页 -->
                <div class="tab-content active" data-tab-content="customers" data-tab-group="customer-management">
                    <!-- 搜索和筛选 -->
                    <div class="card mb-6">
                        <div class="card-body">
                            <div class="flex gap-4 items-end">
                                <div class="form-group mb-0">
                                    <label class="form-label">搜索客户</label>
                                    <input type="text" class="form-control" id="search-customers" placeholder="输入客户姓名、手机号或邮箱">
                                </div>

                                <div class="form-group mb-0">
                                    <label class="form-label">客户等级</label>
                                    <select class="form-control" id="level-filter">
                                        <option value="">全部等级</option>
                                        <option value="vip">VIP客户</option>
                                        <option value="premium">高级客户</option>
                                        <option value="regular">普通客户</option>
                                    </select>
                                </div>

                                <div class="form-group mb-0">
                                    <label class="form-label">客户状态</label>
                                    <select class="form-control" id="status-filter">
                                        <option value="">全部状态</option>
                                        <option value="active">活跃</option>
                                        <option value="inactive">非活跃</option>
                                    </select>
                                </div>

                                <button class="btn btn-primary" onclick="searchCustomers()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>

                                <button class="btn btn-secondary" onclick="resetCustomerFilters()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 客户表格 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="flex justify-between items-center">
                                <h3 class="card-title">客户列表</h3>
                                <div class="flex gap-2">
                                    <button class="btn btn-outline btn-sm" onclick="exportCustomers()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="addNewCustomer()">
                                        <i class="fas fa-plus"></i> 新增客户
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="table" id="customers-table">
                                <thead>
                                    <tr>
                                        <th data-sort="name">客户信息 <i class="fas fa-sort"></i></th>
                                        <th data-sort="phone">联系方式</th>
                                        <th data-sort="level">客户等级</th>
                                        <th data-sort="orders_count">订单数量 <i class="fas fa-sort"></i></th>
                                        <th data-sort="total_amount">消费金额 <i class="fas fa-sort"></i></th>
                                        <th data-sort="last_contact">最后联系 <i class="fas fa-sort"></i></th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="customers-tbody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="card-footer">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-secondary">
                                    显示第 <span id="customers-page-start">0</span> - <span id="customers-page-end">0</span> 条，共 <span id="customers-total-count">0</span> 条记录
                                </div>
                                <div class="flex gap-2" id="customers-pagination">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联系记录标签页 -->
                <div class="tab-content" data-tab-content="contacts" data-tab-group="customer-management">
                    <div class="card">
                        <div class="card-header">
                            <div class="flex justify-between items-center">
                                <h3 class="card-title">联系记录</h3>
                                <button class="btn btn-primary btn-sm" onclick="addContactRecord()">
                                    <i class="fas fa-plus"></i> 新增记录
                                </button>
                            </div>
                        </div>

                        <div class="card-body">
                            <div id="contact-records">
                                <!-- 联系记录将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 客户分析标签页 -->
                <div class="tab-content" data-tab-content="analytics" data-tab-group="customer-management">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">客户分布</h3>
                            </div>
                            <div class="card-body">
                                <div id="customer-distribution-chart">
                                    <div class="text-center text-secondary" style="padding: 40px;">
                                        客户分布图表
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">消费趋势</h3>
                            </div>
                            <div class="card-body">
                                <div id="consumption-trend-chart">
                                    <div class="text-center text-secondary" style="padding: 40px;">
                                        消费趋势图表
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- 客户详情模态框 -->
    <div class="modal" id="customer-detail-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title">客户详情</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="customer-detail-content">
                <!-- 客户详情内容将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="ds.closeModal()">关闭</button>
                <button class="btn btn-primary" onclick="editCurrentCustomer()">编辑客户</button>
            </div>
        </div>
    </div>

    <!-- 新增客户模态框 -->
    <div class="modal" id="new-customer-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">新增客户</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="new-customer-form" data-form-validate>
                <div class="modal-body">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label required">客户姓名</label>
                            <input type="text" class="form-control" id="customer-name" required placeholder="请输入客户姓名">
                        </div>

                        <div class="form-group">
                            <label class="form-label required">联系电话</label>
                            <input type="tel" class="form-control" id="customer-phone" required placeholder="请输入联系电话">
                        </div>

                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="customer-email" placeholder="请输入邮箱地址">
                        </div>

                        <div class="form-group">
                            <label class="form-label">客户等级</label>
                            <select class="form-control" id="customer-level">
                                <option value="regular">普通客户</option>
                                <option value="premium">高级客户</option>
                                <option value="vip">VIP客户</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">详细地址</label>
                        <input type="text" class="form-control" id="customer-address" placeholder="请输入详细地址">
                    </div>

                    <div class="form-group">
                        <label class="form-label">备注信息</label>
                        <textarea class="form-control" id="customer-notes" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="ds.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">创建客户</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 联系记录模态框 -->
    <div class="modal" id="contact-record-modal" style="display: none;">
        <div class="modal-backdrop" onclick="ds.closeModal()"></div>
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">新增联系记录</h3>
                <button class="btn btn-ghost btn-sm" onclick="ds.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="contact-record-form" data-form-validate>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label required">客户</label>
                        <select class="form-control" id="contact-customer" required>
                            <option value="">请选择客户</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">联系方式</label>
                        <select class="form-control" id="contact-method" required>
                            <option value="">请选择联系方式</option>
                            <option value="phone">电话</option>
                            <option value="email">邮件</option>
                            <option value="wechat">微信</option>
                            <option value="visit">上门拜访</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">联系内容</label>
                        <textarea class="form-control" id="contact-content" rows="4" required placeholder="请输入联系内容"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">下次跟进时间</label>
                        <input type="datetime-local" class="form-control" id="next-followup">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="ds.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存记录</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 设计系统模拟 (简化版)
        const ds = {
            showLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0.5';
                    element.style.pointerEvents = 'none';
                }
            },
            hideLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '1';
                    element.style.pointerEvents = 'auto';
                }
            },
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            openModal: (modalId) => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex';
                    setTimeout(() => modal.classList.add('modal-open'), 10);
                }
            },
            closeModal: () => {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.classList.remove('modal-open');
                    setTimeout(() => modal.style.display = 'none', 300);
                });
            },
            formatDate: (dateString, format = 'YYYY-MM-DD HH:mm') => {
                if (!dateString) return '';
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            },
            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // 客户管理页面逻辑
        class CustomerManagementPage {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 10;
                this.totalCount = 0;
                this.currentFilters = {};
                this.currentCustomerId = null;
                this.customers = [];
                this.contactRecords = [];

                this.init();
            }

            async init() {
                this.loadData();
                this.loadStatistics();
                this.loadCustomers();
                this.loadContactRecords();
                this.bindEvents();
            }

            // 数据管理
            loadData() {
                // 加载客户数据
                this.customers = JSON.parse(localStorage.getItem('customers') || '[]');

                // 初始化默认数据
                if (this.customers.length === 0) {
                    this.customers = [
                        {
                            id: 1,
                            name: '张三',
                            customer_number: 'CUS001',
                            phone: '13800138001',
                            email: '<EMAIL>',
                            level: 'vip',
                            address: '北京市朝阳区xxx街道xxx号',
                            orders_count: 15,
                            total_amount: 125000,
                            last_contact: new Date().toISOString(),
                            created_at: new Date().toISOString(),
                            notes: 'VIP客户，需要重点维护',
                            tags: ['重要客户', 'VIP'],
                            activities: [
                                {
                                    type: 'order',
                                    title: '下单购买智能家居套装',
                                    description: '订单金额：¥25,000',
                                    created_at: new Date().toISOString()
                                }
                            ]
                        },
                        {
                            id: 2,
                            name: '李四',
                            customer_number: 'CUS002',
                            phone: '13800138002',
                            email: '<EMAIL>',
                            level: 'premium',
                            address: '上海市浦东新区xxx路xxx号',
                            orders_count: 8,
                            total_amount: 68000,
                            last_contact: new Date(Date.now() - 86400000).toISOString(),
                            created_at: new Date(Date.now() - 2592000000).toISOString(),
                            notes: '高级客户，有潜力升级为VIP',
                            tags: ['潜力客户'],
                            activities: []
                        },
                        {
                            id: 3,
                            name: '王五',
                            customer_number: 'CUS003',
                            phone: '13800138003',
                            email: '<EMAIL>',
                            level: 'regular',
                            address: '广州市天河区xxx大道xxx号',
                            orders_count: 3,
                            total_amount: 15000,
                            last_contact: null,
                            created_at: new Date(Date.now() - 5184000000).toISOString(),
                            notes: '普通客户，需要跟进',
                            tags: [],
                            activities: []
                        }
                    ];
                    this.saveCustomers();
                }

                // 加载联系记录
                this.contactRecords = JSON.parse(localStorage.getItem('contactRecords') || '[]');

                if (this.contactRecords.length === 0) {
                    this.contactRecords = [
                        {
                            id: 1,
                            customer_id: 1,
                            customer_name: '张三',
                            method: 'phone',
                            content: '电话沟通新产品需求，客户表示很感兴趣',
                            next_followup: new Date(Date.now() + 604800000).toISOString(),
                            created_at: new Date().toISOString()
                        },
                        {
                            id: 2,
                            customer_id: 2,
                            customer_name: '李四',
                            method: 'email',
                            content: '发送产品资料和报价单',
                            next_followup: null,
                            created_at: new Date(Date.now() - 86400000).toISOString()
                        }
                    ];
                    this.saveContactRecords();
                }
            }

            saveCustomers() {
                localStorage.setItem('customers', JSON.stringify(this.customers));
            }

            saveContactRecords() {
                localStorage.setItem('contactRecords', JSON.stringify(this.contactRecords));
            }

            getNextCustomerId() {
                return this.customers.length > 0 ? Math.max(...this.customers.map(c => c.id)) + 1 : 1;
            }

            getNextContactRecordId() {
                return this.contactRecords.length > 0 ? Math.max(...this.contactRecords.map(r => r.id)) + 1 : 1;
            }

            bindEvents() {
                // 搜索输入防抖
                const searchInput = document.getElementById('search-customers');
                if (searchInput) {
                    searchInput.addEventListener('input', ds.debounce(() => {
                        this.searchCustomers();
                    }, 500));
                }

                // 筛选器变化
                ['level-filter', 'status-filter'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('change', () => {
                            this.searchCustomers();
                        });
                    }
                });

                // 新增客户表单提交
                const newCustomerForm = document.getElementById('new-customer-form');
                if (newCustomerForm) {
                    newCustomerForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.createCustomer();
                    });
                }

                // 联系记录表单提交
                const contactRecordForm = document.getElementById('contact-record-form');
                if (contactRecordForm) {
                    contactRecordForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.saveContactRecord();
                    });
                }
            }

            loadStatistics() {
                const totalCustomers = this.customers.length;
                const activeCustomers = this.customers.filter(c => c.last_contact &&
                    new Date(c.last_contact) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length;
                const vipCustomers = this.customers.filter(c => c.level === 'vip').length;
                const newCustomers = this.customers.filter(c =>
                    new Date(c.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length;

                const totalElement = document.getElementById('total-customers');
                const activeElement = document.getElementById('active-customers');
                const vipElement = document.getElementById('vip-customers');
                const newElement = document.getElementById('new-customers');

                if (totalElement) totalElement.textContent = totalCustomers;
                if (activeElement) activeElement.textContent = activeCustomers;
                if (vipElement) vipElement.textContent = vipCustomers;
                if (newElement) newElement.textContent = newCustomers;
            }

            loadCustomers(page = 1) {
                ds.showLoading('#customers-table');

                // 应用筛选条件
                let filteredCustomers = [...this.customers];

                if (this.currentFilters.search) {
                    const searchTerm = this.currentFilters.search.toLowerCase();
                    filteredCustomers = filteredCustomers.filter(customer =>
                        customer.name.toLowerCase().includes(searchTerm) ||
                        customer.phone.includes(searchTerm) ||
                        (customer.email && customer.email.toLowerCase().includes(searchTerm))
                    );
                }

                if (this.currentFilters.level) {
                    filteredCustomers = filteredCustomers.filter(customer =>
                        customer.level === this.currentFilters.level
                    );
                }

                // 分页处理
                this.totalCount = filteredCustomers.length;
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const startIndex = (page - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageCustomers = filteredCustomers.slice(startIndex, endIndex);

                this.renderCustomers(pageCustomers);
                this.renderCustomersPagination({
                    current_page: page,
                    total_pages: totalPages,
                    total: this.totalCount
                });

                setTimeout(() => {
                    ds.hideLoading('#customers-table');
                }, 300);
            }

            renderCustomers(customers) {
                const tbody = document.getElementById('customers-tbody');

                if (customers.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-secondary" style="padding: 40px;">
                                <i class="fas fa-users fa-3x mb-4" style="display: block; opacity: 0.3;"></i>
                                暂无客户数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = customers.map(customer => `
                    <tr>
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="customer-avatar">
                                    ${customer.name.charAt(0)}
                                </div>
                                <div>
                                    <div class="font-medium">${customer.name}</div>
                                    <div class="text-xs text-secondary">${customer.customer_number || ''}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>${customer.phone}</span>
                                </div>
                                ${customer.email ? `
                                    <div class="contact-item">
                                        <i class="fas fa-envelope"></i>
                                        <span>${customer.email}</span>
                                    </div>
                                ` : ''}
                            </div>
                        </td>
                        <td>
                            <span class="customer-level level-${customer.level}">
                                ${this.getLevelText(customer.level)}
                            </span>
                        </td>
                        <td>
                            <div class="font-medium">${customer.orders_count || 0}</div>
                            <div class="text-xs text-secondary">个订单</div>
                        </td>
                        <td>
                            <div class="font-medium">¥${this.formatAmount(customer.total_amount || 0)}</div>
                            <div class="text-xs text-secondary">累计消费</div>
                        </td>
                        <td>
                            <div>${customer.last_contact ? ds.formatDate(customer.last_contact, 'YYYY-MM-DD') : '未联系'}</div>
                            <div class="text-xs text-secondary">${customer.last_contact ? ds.formatDate(customer.last_contact, 'HH:mm') : ''}</div>
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm" onclick="customerManagementPage.viewCustomer('${customer.id}')">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="customerManagementPage.editCustomer('${customer.id}')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="customerManagementPage.deleteCustomer('${customer.id}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }

            renderCustomersPagination(pagination) {
                const paginationEl = document.getElementById('customers-pagination');
                const { current_page = 1, total_pages = 1, total = 0 } = pagination;

                // 更新统计信息
                const start = (current_page - 1) * this.pageSize + 1;
                const end = Math.min(current_page * this.pageSize, total);

                document.getElementById('customers-page-start').textContent = total > 0 ? start : 0;
                document.getElementById('customers-page-end').textContent = end;
                document.getElementById('customers-total-count').textContent = total;

                // 生成分页按钮
                let paginationHTML = '';

                if (current_page > 1) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="customerManagementPage.loadCustomers(${current_page - 1})">上一页</button>`;
                }

                const startPage = Math.max(1, current_page - 2);
                const endPage = Math.min(total_pages, current_page + 2);

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === current_page ? 'btn-primary' : 'btn-outline';
                    paginationHTML += `<button class="btn ${isActive} btn-sm" onclick="customerManagementPage.loadCustomers(${i})">${i}</button>`;
                }

                if (current_page < total_pages) {
                    paginationHTML += `<button class="btn btn-outline btn-sm" onclick="customerManagementPage.loadCustomers(${current_page + 1})">下一页</button>`;
                }

                paginationEl.innerHTML = paginationHTML;
                this.currentPage = current_page;
            }

            viewCustomer(customerId) {
                const customer = this.customers.find(c => c.id === parseInt(customerId));
                if (!customer) {
                    ds.showToast('客户不存在', 'error');
                    return;
                }

                this.renderCustomerDetail(customer);
                this.currentCustomerId = customerId;
                ds.openModal('customer-detail-modal');
            }

            renderCustomerDetail(customer) {
                const content = document.getElementById('customer-detail-content');
                content.innerHTML = `
                    <div class="customer-detail-grid">
                        <div>
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">基本信息</h4>
                                </div>
                                <div class="card-body">
                                    <div class="flex items-center gap-4 mb-4">
                                        <div class="customer-avatar" style="width: 64px; height: 64px; font-size: 24px;">
                                            ${customer.name.charAt(0)}
                                        </div>
                                        <div>
                                            <h3 class="font-semibold">${customer.name}</h3>
                                            <span class="customer-level level-${customer.level}">
                                                ${this.getLevelText(customer.level)}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="space-y-3">
                                        <div><span class="text-secondary">客户编号：</span>${customer.customer_number || '自动生成'}</div>
                                        <div><span class="text-secondary">联系电话：</span>${customer.phone}</div>
                                        <div><span class="text-secondary">邮箱地址：</span>${customer.email || '未提供'}</div>
                                        <div><span class="text-secondary">详细地址：</span>${customer.address || '未提供'}</div>
                                        <div><span class="text-secondary">注册时间：</span>${ds.formatDate(customer.created_at)}</div>
                                    </div>

                                    ${customer.tags && customer.tags.length > 0 ? `
                                        <div class="customer-tags">
                                            ${customer.tags.map(tag => `<span class="customer-tag">${tag}</span>`).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <div class="card mt-4">
                                <div class="card-header">
                                    <h4 class="card-title">消费统计</h4>
                                </div>
                                <div class="card-body">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-primary">${customer.orders_count || 0}</div>
                                            <div class="text-sm text-secondary">订单数量</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-success">¥${this.formatAmount(customer.total_amount || 0)}</div>
                                            <div class="text-sm text-secondary">累计消费</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">最近活动</h4>
                                </div>
                                <div class="card-body">
                                    <div id="customer-activities">
                                        ${this.renderCustomerActivities(customer.activities || [])}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            renderCustomerActivities(activities) {
                if (activities.length === 0) {
                    return '<div class="text-center text-secondary">暂无活动记录</div>';
                }

                return activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon activity-${activity.type}">
                            <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-description">${activity.description}</div>
                            <div class="activity-time">${ds.formatDate(activity.created_at)}</div>
                        </div>
                    </div>
                `).join('');
            }

            createCustomer() {
                // 检查是否为编辑模式
                if (this.currentEditingCustomerId) {
                    this.updateCustomer();
                    return;
                }

                const form = document.getElementById('new-customer-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const formData = {
                    id: this.getNextCustomerId(),
                    name: document.getElementById('customer-name').value,
                    phone: document.getElementById('customer-phone').value,
                    email: document.getElementById('customer-email').value,
                    level: document.getElementById('customer-level').value,
                    address: document.getElementById('customer-address').value,
                    notes: document.getElementById('customer-notes').value,
                    customer_number: `CUS${String(this.getNextCustomerId()).padStart(3, '0')}`,
                    orders_count: 0,
                    total_amount: 0,
                    last_contact: null,
                    created_at: new Date().toISOString(),
                    tags: [],
                    activities: []
                };

                // 检查手机号是否已存在
                if (this.customers.some(c => c.phone === formData.phone)) {
                    ds.showToast('手机号已存在', 'error');
                    return;
                }

                this.customers.push(formData);
                this.saveCustomers();

                ds.showToast('客户创建成功', 'success');
                ds.closeModal();
                this.loadCustomers(1);
                this.loadStatistics();

                // 重置表单
                form.reset();
            }

            loadContactRecords() {
                this.renderContactRecords(this.contactRecords);

                // 填充客户选择器
                const customerSelect = document.getElementById('contact-customer');
                if (customerSelect) {
                    customerSelect.innerHTML = '<option value="">请选择客户</option>' +
                        this.customers.map(customer => `<option value="${customer.id}">${customer.name}</option>`).join('');
                }
            }

            renderContactRecords(records) {
                const container = document.getElementById('contact-records');

                if (records.length === 0) {
                    container.innerHTML = `
                        <div class="text-center text-secondary" style="padding: 40px;">
                            <i class="fas fa-comments fa-3x mb-4" style="display: block; opacity: 0.3;"></i>
                            暂无联系记录
                        </div>
                    `;
                    return;
                }

                container.innerHTML = records.map(record => `
                    <div class="activity-item">
                        <div class="activity-icon activity-contact">
                            <i class="fas fa-${this.getContactMethodIcon(record.method)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">
                                ${record.customer_name} - ${this.getContactMethodText(record.method)}
                            </div>
                            <div class="activity-description">${record.content}</div>
                            <div class="activity-time">
                                ${ds.formatDate(record.created_at)}
                                ${record.next_followup ? ` | 下次跟进：${ds.formatDate(record.next_followup)}` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            saveContactRecord() {
                const form = document.getElementById('contact-record-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const customerId = parseInt(document.getElementById('contact-customer').value);
                const customer = this.customers.find(c => c.id === customerId);

                if (!customer) {
                    ds.showToast('请选择有效的客户', 'error');
                    return;
                }

                const formData = {
                    id: this.getNextContactRecordId(),
                    customer_id: customerId,
                    customer_name: customer.name,
                    method: document.getElementById('contact-method').value,
                    content: document.getElementById('contact-content').value,
                    next_followup: document.getElementById('next-followup').value || null,
                    created_at: new Date().toISOString()
                };

                this.contactRecords.push(formData);
                this.saveContactRecords();

                // 更新客户的最后联系时间
                customer.last_contact = new Date().toISOString();
                this.saveCustomers();

                ds.showToast('联系记录保存成功', 'success');
                ds.closeModal();
                this.loadContactRecords();
                this.loadCustomers(this.currentPage);

                // 重置表单
                form.reset();
            }

            editCustomer(customerId) {
                const customer = this.customers.find(c => c.id === parseInt(customerId));
                if (!customer) {
                    ds.showToast('客户不存在', 'error');
                    return;
                }

                // 填充编辑表单
                document.getElementById('customer-name').value = customer.name;
                document.getElementById('customer-phone').value = customer.phone;
                document.getElementById('customer-email').value = customer.email || '';
                document.getElementById('customer-level').value = customer.level;
                document.getElementById('customer-address').value = customer.address || '';
                document.getElementById('customer-notes').value = customer.notes || '';

                // 修改模态框标题和按钮
                document.querySelector('#new-customer-modal .modal-title').textContent = '编辑客户';
                const submitButton = document.querySelector('#new-customer-modal button[type="submit"]');
                submitButton.textContent = '更新客户';

                // 设置编辑模式
                this.currentEditingCustomerId = customerId;

                // 关闭详情模态框，打开编辑模态框
                ds.closeModal();
                setTimeout(() => {
                    ds.openModal('new-customer-modal');
                }, 300);
            }

            updateCustomer() {
                const form = document.getElementById('new-customer-form');

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const customerIndex = this.customers.findIndex(c => c.id === parseInt(this.currentEditingCustomerId));
                if (customerIndex === -1) {
                    ds.showToast('客户不存在', 'error');
                    return;
                }

                const phone = document.getElementById('customer-phone').value;

                // 检查手机号是否已被其他客户使用
                if (this.customers.some(c => c.phone === phone && c.id !== parseInt(this.currentEditingCustomerId))) {
                    ds.showToast('手机号已被其他客户使用', 'error');
                    return;
                }

                // 更新客户信息
                this.customers[customerIndex] = {
                    ...this.customers[customerIndex],
                    name: document.getElementById('customer-name').value,
                    phone: phone,
                    email: document.getElementById('customer-email').value,
                    level: document.getElementById('customer-level').value,
                    address: document.getElementById('customer-address').value,
                    notes: document.getElementById('customer-notes').value,
                    updated_at: new Date().toISOString()
                };

                this.saveCustomers();

                ds.showToast('客户信息更新成功', 'success');
                ds.closeModal();
                this.loadCustomers(this.currentPage);
                this.loadStatistics();

                // 重置表单和编辑状态
                form.reset();
                this.currentEditingCustomerId = null;
                document.querySelector('#new-customer-modal .modal-title').textContent = '新增客户';
                document.querySelector('#new-customer-modal button[type="submit"]').textContent = '创建客户';
            }

            deleteCustomer(customerId) {
                const customer = this.customers.find(c => c.id === parseInt(customerId));
                if (!customer) {
                    ds.showToast('客户不存在', 'error');
                    return;
                }

                if (confirm(`确定要删除客户"${customer.name}"吗？此操作不可恢复。\n\n删除客户将同时删除相关的联系记录。`)) {
                    // 删除客户
                    this.customers = this.customers.filter(c => c.id !== parseInt(customerId));

                    // 删除相关联系记录
                    this.contactRecords = this.contactRecords.filter(r => r.customer_id !== parseInt(customerId));

                    this.saveCustomers();
                    this.saveContactRecords();

                    ds.showToast('客户删除成功', 'success');
                    this.loadCustomers(this.currentPage);
                    this.loadContactRecords();
                    this.loadStatistics();
                }
            }

            searchCustomers() {
                const searchTerm = document.getElementById('search-customers').value.trim();
                const level = document.getElementById('level-filter').value;
                const status = document.getElementById('status-filter').value;

                this.currentFilters = {};

                if (searchTerm) this.currentFilters.search = searchTerm;
                if (level) this.currentFilters.level = level;
                if (status) this.currentFilters.status = status;

                this.loadCustomers(1);
            }

            resetCustomerFilters() {
                document.getElementById('search-customers').value = '';
                document.getElementById('level-filter').value = '';
                document.getElementById('status-filter').value = '';

                this.currentFilters = {};
                this.loadCustomers(1);
            }

            // 工具方法
            formatAmount(amount) {
                return Number(amount || 0).toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }

            getLevelText(level) {
                const texts = {
                    vip: 'VIP客户',
                    premium: '高级客户',
                    regular: '普通客户'
                };
                return texts[level] || level;
            }

            getActivityIcon(type) {
                const icons = {
                    order: 'shopping-cart',
                    contact: 'phone',
                    project: 'project-diagram'
                };
                return icons[type] || 'circle';
            }

            getContactMethodIcon(method) {
                const icons = {
                    phone: 'phone',
                    email: 'envelope',
                    wechat: 'weixin',
                    visit: 'home'
                };
                return icons[method] || 'comment';
            }

            getContactMethodText(method) {
                const texts = {
                    phone: '电话联系',
                    email: '邮件联系',
                    wechat: '微信联系',
                    visit: '上门拜访'
                };
                return texts[method] || method;
            }
        }

        // 全局函数
        function searchCustomers() {
            customerManagementPage.searchCustomers();
        }

        function resetCustomerFilters() {
            customerManagementPage.resetCustomerFilters();
        }

        function exportCustomers() {
            // 获取客户数据
            const customers = customerManagementPage.customers || [];
            
            if (customers.length === 0) {
                ds.showToast('暂无客户数据可导出', 'warning');
                return;
            }

            // 准备导出数据
            const csvData = [];
            csvData.push(['客户编号', '客户姓名', '联系电话', '邮箱地址', '公司名称', '地址', '客户标签', '跟进状态', '创建时间', '最后联系']);

            customers.forEach(customer => {
                csvData.push([
                    customer.id || 'N/A',
                    customer.name || 'N/A',
                    customer.phone || 'N/A',
                    customer.email || 'N/A',
                    customer.company || 'N/A',
                    customer.address || 'N/A',
                    customer.tags ? customer.tags.join(', ') : 'N/A',
                    customer.status || '待跟进',
                    customer.createTime ? new Date(customer.createTime).toLocaleString() : 'N/A',
                    customer.lastContact ? new Date(customer.lastContact).toLocaleString() : '从未联系'
                ]);
            });

            // 生成CSV内容
            const csvContent = csvData.map(row => 
                row.map(field => `"${field}"`).join(',')
            ).join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            link.href = url;
            link.download = `客户数据导出_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
            
            URL.revokeObjectURL(url);
            ds.showToast(`成功导出 ${customers.length} 条客户数据`, 'success');
        }

        function addNewCustomer() {
            ds.openModal('new-customer-modal');
        }

        function addContactRecord() {
            ds.openModal('contact-record-modal');
        }

        function editCurrentCustomer() {
            if (!customerManagementPage.currentCustomerId) {
                ds.showToast('请先选择客户', 'error');
                return;
            }
            customerManagementPage.editCustomer(customerManagementPage.currentCustomerId);
        }

        // 初始化页面
        const customerManagementPage = new CustomerManagementPage();
    </script>

    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity var(--duration-normal) var(--ease-in-out);
        }

        .modal.modal-open {
            opacity: 1;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-overlay);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            z-index: 1;
            transform: scale(0.95);
            transition: transform var(--duration-normal) var(--ease-in-out);
        }

        .modal.modal-open .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
        }

        .modal-body {
            padding: var(--space-6);
        }

        .modal-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--border-primary);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
        }

        /* 网格布局 */
        .grid {
            display: grid;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .space-y-3 > * + * {
            margin-top: var(--space-3);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-content {
                margin: var(--space-4);
                max-width: calc(100vw - 2rem);
            }

            .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .customer-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .customer-detail-grid {
                grid-template-columns: 1fr;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-danger {
            background: #ef4444;
            color: #ffffff;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        .btn-outline {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>

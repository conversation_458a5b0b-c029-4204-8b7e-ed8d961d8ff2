<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="your-csrf-token-here">
    <title>商品管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样�?*/
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动�?*/
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动�?*/
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样�?*/
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;


        

        
        /* 主内容区�?*/
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 第一层：面包屑导航样�?*/
        .top-nav {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }

        .breadcrumb-section {
            flex: 1;
        }

        .page-title-section {
            margin-top: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
            max-width: 600px;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 第二层：主菜单栏样式 */
        .main-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .main-menu-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .main-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .main-menu-tab {
            flex-shrink: 0;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
        }

        .main-menu-tab:hover {
            color: #1f2937;
            background: #f8fafc;
        }

        .main-menu-tab.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
            background: #f8fafc;
        }

        /* 商品生命周期展示区域 */
        .lifecycle-section {
            padding: 24px;
            background: #f8fafc;
        }

        .lifecycle-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .lifecycle-cards {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            scrollbar-width: none;
            padding-bottom: 8px;
        }

        .lifecycle-cards::-webkit-scrollbar {
            display: none;
        }

        .lifecycle-card {
            flex-shrink: 0;
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lifecycle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #9ca3af;
        }

        .lifecycle-card-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 20px;
            color: #374151;
        }

        .lifecycle-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .lifecycle-card-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 第三层：子菜单栏样式 */
        .sub-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
        }

        .sub-menu-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .sub-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .sub-menu-tab {
            flex-shrink: 0;
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .sub-menu-tab:hover {
            color: #1f2937;
            border-color: #1f2937;
            background: #f9fafb;
        }

        .sub-menu-tab.active {
            color: #ffffff;
            background: #1f2937;
            border-color: #1f2937;
        }

        /* 第四层：内容区域样式 */
        .content-area {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .content-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .content-body {
            padding: 24px;
        }

        /* 优化后的表格容器 */
        .table-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            margin-top: 24px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .table th {
            background: #f8fafc;
            padding: 14px 16px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            white-space: nowrap;
            letter-spacing: 0.025em;
            text-transform: uppercase;
        }

        .table td {
            padding: 14px 16px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            font-size: 13px;
            color: #1f2937;
            line-height: 1.5;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f8fafc;
            transition: background-color 0.15s ease;
        }

        .table tr:hover td {
            color: #111827;
        }

        /* 表格单元格样�?*/
        .product-image-cell {
            width: 52px;
            height: 52px;
            background: #f8fafc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            border: 1px solid #e2e8f0;
        }

        .product-info-cell {
            min-width: 220px;
            max-width: 280px;
        }

        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 6px;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-sku {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
        }

        .product-brand {
            font-size: 12px;
            color: #64748b;
            font-style: italic;
        }

        .product-price {
            font-size: 15px;
            font-weight: 700;
            color: #059669;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        .product-stock, .product-sales {
            font-size: 14px;
            color: #1f2937;
            font-weight: 500;
        }

        .product-date {
            font-size: 12px;
            color: #64748b;
            line-height: 1.5;
            white-space: nowrap;
        }

        /* 表格单元格对齐样�?*/
        .table td:first-child {
            text-align: center;
        }

        .table td:nth-child(3) {
            text-align: right;
            font-weight: 600;
        }

        .table td:nth-child(4),
        .table td:nth-child(5),
        .table td:nth-child(6),
        .table td:nth-child(7),
        .table td:nth-child(8),
        .table td:nth-child(9) {
            text-align: center;
        }

        /* 表格行高优化 */
        .table tr {
            height: 64px;
        }

        .table th {
            height: 48px;
            vertical-align: middle;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-toggle .fa-chevron-down {
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        .dropdown.active .fa-chevron-down {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            display: none;
            min-width: 200px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            margin-top: 4px;
        }

        .dropdown.active .dropdown-menu {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .dropdown-item:hover {
            background-color: #f8fafc;
            color: #1f2937;
        }

        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 4px 0;
        }

        /* 批量导入模态框样式 */
        .modal-lg {
            max-width: 800px;
        }

        .import-type-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .import-type-tabs .tab-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: none;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .import-type-tabs .tab-btn.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
        }

        .import-type-tabs .tab-btn:hover {
            color: #374151;
        }

        .import-panel {
            display: none;
        }

        .import-panel.active {
            display: block;
        }

        .upload-area {
            margin-bottom: 24px;
        }

        .upload-zone {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .upload-zone:hover {
            border-color: #9ca3af;
            background-color: #f9fafb;
        }

        .upload-zone.has-file {
            border-color: #059669;
            background-color: #ecfdf5;
        }

        .upload-icon {
            font-size: 48px;
            color: #9ca3af;
            margin-bottom: 16px;
        }

        .upload-zone.has-file .upload-icon {
            color: #059669;
        }

        .upload-text p {
            font-size: 16px;
            color: #374151;
            margin-bottom: 8px;
        }

        .upload-text small {
            color: #6b7280;
        }

        .import-options {
            margin-bottom: 24px;
        }

        .option-group {
            margin-bottom: 12px;
        }

        .option-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
        }

        .template-download {
            text-align: center;
            margin-bottom: 24px;
        }

        .json-input-area {
            margin-bottom: 24px;
        }

        .json-input-area label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .json-input-area textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            resize: vertical;
        }

        .json-help {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .import-progress-section {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-header h4 {
            margin: 0;
            font-size: 16px;
            color: #1f2937;
        }

        .progress-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .progress-fill {
            height: 100%;
            background: #059669;
            transition: width 0.3s ease;
            width: 0%;
        }

        .import-logs {
            max-height: 150px;
            overflow-y: auto;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #374151;
        }

        .import-logs div {
            margin-bottom: 4px;
        }

        /* 图片上传样式 */
        .image-upload-container {
            margin-bottom: 20px;
        }

        .upload-zone-images {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            background: #f8fafc;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-zone-images:hover,
        .upload-zone-images.dragover {
            border-color: #059669;
            background: #ecfdf5;
            transform: translateY(-2px);
        }

        .upload-zone-images.has-images {
            border-style: solid;
            border-color: #059669;
            background: #f0fdf4;
        }

        .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .upload-zone-images .upload-icon {
            font-size: 48px;
            color: #94a3b8;
            margin-bottom: 8px;
        }

        .upload-zone-images:hover .upload-icon,
        .upload-zone-images.dragover .upload-icon {
            color: #059669;
        }

        .upload-zone-images .upload-text h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
        }

        .upload-zone-images .upload-text p {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 4px 0;
            line-height: 1.4;
        }

        /* 图片预览容器 */
        .image-preview-container {
            margin-top: 24px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .preview-header {
            margin-bottom: 16px;
        }

        .preview-header h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
        }

        .preview-tip {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
        }

        /* 图片预览网格 */
        .image-preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 16px;
        }

        .preview-item {
            position: relative;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e5e7eb;
            transition: all 0.2s ease;
            cursor: move;
        }

        .preview-item:hover {
            border-color: #1f2937;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .preview-item.main-image {
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .preview-item.main-image::before {
            content: "主图";
            position: absolute;
            top: 4px;
            left: 4px;
            background: #059669;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            z-index: 2;
        }

        .preview-image-wrapper {
            position: relative;
            width: 100%;
            height: 120px;
            overflow: hidden;
        }

        .preview-image-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.2s ease;
        }

        .preview-item:hover .preview-image-wrapper img {
            transform: scale(1.05);
        }

        /* 图片操作按钮 */
        .image-actions {
            position: absolute;
            top: 4px;
            right: 4px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .preview-item:hover .image-actions {
            opacity: 1;
        }

        .image-action-btn {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .image-action-btn.delete {
            background: rgba(239, 68, 68, 0.9);
            color: white;
        }

        .image-action-btn.delete:hover {
            background: #dc2626;
        }

        .image-action-btn.main {
            background: rgba(5, 150, 105, 0.9);
            color: white;
        }

        .image-action-btn.main:hover {
            background: #047857;
        }

        /* 图片信息 */
        .image-info {
            padding: 8px;
            background: #ffffff;
        }

        .image-name {
            font-size: 12px;
            color: #374151;
            margin: 0 0 2px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .image-size {
            font-size: 11px;
            color: #9ca3af;
            margin: 0;
        }

        /* 拖拽排序样式 */
        .preview-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .preview-item.drag-over {
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .upload-zone-images.has-images {
            border-color: #059669;
            background: #ecfdf5;
        }

        .upload-zone-images.has-images .upload-icon {
            color: #059669;
        }

        /* 拖拽排序样式 */
        .preview-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .preview-item.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        /* 优化后的表单样式 */
        .product-form {
            max-width: 100%;
            margin: 0;
        }

        .form-section {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            transition: all 0.2s ease;
        }

        .form-section:hover {
            border-color: #d1d5db;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .section-header {
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .section-header h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-header h4 i {
            color: #6b7280;
            font-size: 14px;
        }

        .section-header p {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .required {
            color: #ef4444;
            font-weight: 600;
        }

        .form-control,
        .form-select {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #1f2937;
            background: #ffffff;
            transition: all 0.2s ease;
            min-height: 44px;
        }

        .form-control:focus,
        .form-select:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
            transform: translateY(-1px);
        }

        .form-control:hover,
        .form-select:hover {
            border-color: #9ca3af;
        }

        .form-control::placeholder {
            color: #9ca3af;
        }

        /* 输入组样�?*/
        .input-group {
            display: flex;
            align-items: stretch;
            position: relative;
        }

        .input-prefix,
        .input-suffix {
            display: flex;
            align-items: center;
            padding: 0 12px;
            background: #f9fafb;
            border: 1px solid #d1d5db;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .input-prefix {
            border-right: none;
            border-radius: 8px 0 0 8px;
        }

        .input-suffix {
            border-left: none;
            border-radius: 0 8px 8px 0;
        }

        .input-group .form-control {
            border-radius: 0;
        }

        .input-group .form-control:first-child {
            border-radius: 8px 0 0 8px;
        }

        .input-group .form-control:last-child {
            border-radius: 0 8px 8px 0;
        }

        .input-group .form-control:only-child {
            border-radius: 8px;
        }

        /* 表单验证样式 */
        .form-error {
            font-size: 12px;
            color: #ef4444;
            display: none;
            margin-top: 4px;
        }

        .form-group.error .form-control,
        .form-group.error .form-select {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-group.error .form-error {
            display: block;
        }

        .form-help {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
            line-height: 1.4;
        }

        /* 文本域样�?*/
        textarea.form-control {
            resize: vertical;
            min-height: 100px;
            line-height: 1.5;
        }

        /* 选择器样式优�?*/
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
            appearance: none;
        }

        /* 响应式设�?*/
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .form-section {
                padding: 16px;
                margin-bottom: 16px;
            }

            .section-header {
                margin-bottom: 16px;
                padding-bottom: 12px;
            }
        }

        /* 表单动画效果 */
        .form-group {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* MedusaJS 集成样式 */
        .medusa-integration {
            margin-right: 16px;
        }

        .integration-status {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .status-header h4 {
            margin: 0;
            font-size: 16px;
            color: #1f2937;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.connected {
            background: #10b981;
        }

        .status-dot.disconnected {
            background: #ef4444;
        }

        .status-dot.warning {
            background: #f59e0b;
        }

        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item label {
            font-size: 13px;
            color: #6b7280;
            font-weight: 500;
        }

        .info-item span {
            font-size: 13px;
            color: #1f2937;
        }

        .integration-actions {
            margin-bottom: 24px;
        }

        .action-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .action-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .action-section h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #374151;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .sync-logs {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .logs-header h4 {
            margin: 0;
            font-size: 14px;
            color: #374151;
            font-weight: 600;
        }

        .logs-container {
            max-height: 200px;
            overflow-y: auto;
            padding: 16px 20px;
        }

        .log-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
            font-size: 13px;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #6b7280;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            min-width: 60px;
        }

        .log-message {
            color: #374151;
            flex: 1;
        }

        .log-item.success .log-message {
            color: #059669;
        }

        .log-item.warning .log-message {
            color: #d97706;
        }

        .log-item.error .log-message {
            color: #dc2626;
        }

        .log-item.info .log-message {
            color: #2563eb;
        }

        /* MedusaJS 同步状态样�?*/
        .sync-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .sync-status.synced {
            color: #059669;
            background: #ecfdf5;
        }

        .sync-status.pending {
            color: #d97706;
            background: #fffbeb;
        }

        .sync-status.failed {
            color: #dc2626;
            background: #fef2f2;
        }

        .sync-status.not-synced {
            color: #6b7280;
            background: #f9fafb;
        }

        /* 按钮组样�?*/
        .btn-group {
            display: flex;
            gap: 8px;
            margin-right: 16px;
        }

        .btn-group:last-child {
            margin-right: 0;
        }

        /* 表单样本模态框样式 */
        .modal-xl {
            max-width: 1200px;
            width: 95%;
        }

        .sample-tabs {
            margin-top: 16px;
        }

        .tab-nav {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: #374151;
            background: #f9fafb;
        }

        .tab-btn.active {
            color: #1f2937;
            border-bottom-color: #3b82f6;
            background: #f8fafc;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .sample-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .sample-header h4 {
            margin: 0;
            color: #1f2937;
            font-size: 16px;
        }

        .sample-table {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .sample-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .sample-table th,
        .sample-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
            font-size: 13px;
        }

        .sample-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .sample-table td {
            color: #6b7280;
        }

        .sample-notes {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
        }

        .sample-notes h5 {
            margin: 0 0 12px 0;
            color: #1f2937;
            font-size: 14px;
        }

        .sample-notes ul {
            margin: 0;
            padding-left: 20px;
        }

        .sample-notes li {
            margin-bottom: 6px;
            font-size: 13px;
            color: #4b5563;
            line-height: 1.4;
        }

        .sample-notes strong {
            color: #1f2937;
            font-weight: 600;
        }

        /* Excel 预览样式 */
        .excel-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .excel-sheet {
            background: white;
            border-radius: 4px;
            overflow: hidden;
        }

        .sheet-name {
            background: #2563eb;
            color: white;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* JSON 预览样式 */
        .json-preview {
            background: #1f2937;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .json-preview pre {
            margin: 0;
            color: #e5e7eb;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .json-preview code {
            color: #e5e7eb;
        }

        /* 下拉菜单样式增强 */
        .dropdown-header {
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .dropdown-header i {
            margin-right: 6px;
        }

        /* 下拉菜单基础样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 200px;
            padding: 8px 0;
            margin-top: 4px;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            color: #374151;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background: #f9fafb;
            color: #1f2937;
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
            color: #6b7280;
        }

        .dropdown-divider {
            height: 1px;
            background: #e5e7eb;
            margin: 8px 0;
        }

        /* 确保下拉菜单在正确位�?*/
        .btn-group .dropdown-menu {
            left: 0;
        }

        .toolbar-right .dropdown-menu {
            right: 0;
            left: auto;
        }

        .product-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-active {
            background: #d1fae5;
            color: #059669;
        }

        .status-inactive {
            background: #f3f4f6;
            color: #374151;
        }

        .status-out-of-stock {
            background: #fee2e2;
            color: #991b1b;
        }

        .product-date {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.3;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            white-space: nowrap;
            justify-content: flex-start;
            align-items: center;
            min-width: 120px;
        }

        /* 基础按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            line-height: 1;
            border: 1px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;
            gap: 4px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 按钮颜色变体 */
        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
            color: #ffffff;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
            color: #1f2937;
        }

        .btn-success {
            background: #10b981;
            color: #ffffff;
            border-color: #10b981;
        }

        .btn-success:hover {
            background: #059669;
            border-color: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: #ffffff;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        /* 确保按钮可见�?*/
        .product-actions .btn {
            display: inline-flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 基础按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            line-height: 1;
            border: 1px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;
            gap: 4px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* 按钮颜色变体 */
        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
            color: #ffffff;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
            color: #1f2937;
        }

        .btn-success {
            background: #10b981;
            color: #ffffff;
            border-color: #10b981;
        }

        .btn-success:hover {
            background: #059669;
            border-color: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: #ffffff;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 确保操作按钮可见 */
        .product-actions {
            display: flex !important;
            gap: 0.5rem;
            opacity: 1 !important;
            visibility: visible !important;
        }

        .product-actions .btn {
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
            padding: 0.25rem 0.5rem;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 表格中的操作按钮样式 */
        .table .product-actions {
            opacity: 1 !important;
            display: flex !important;
        }

        .table .product-actions .btn {
            display: inline-flex !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 搜索筛选栏样式 */
        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 16px;
        }

        /* 筛选控件样�?*/
        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-filter {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            min-width: 120px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .brand-filter:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        /* 商品信息单元格样�?*/
        .product-brand {
            font-size: 11px;
            color: #9ca3af;
            margin-top: 2px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(31, 41, 55, 0.6);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8fafc;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            background: #ffffff;
            border-radius: 12px 12px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
        }

        .modal-close:hover {
            color: #1f2937;
            background: #f3f4f6;
            transform: scale(1.05);
        }

        .modal-body {
            padding: 32px;
            background: #ffffff;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 16px;
            padding: 24px 32px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
            border-radius: 0 0 12px 12px;
        }

        /* 优化后的表单样式 - 遵循设计规范 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.4;
        }

        /* 必填字段红色星号标识 */
        .form-group label[for="productName"]:after,
        .form-group label[for="productSku"]:after,
        .form-group label[for="productCategory"]:after,
        .form-group label[for="productPrice"]:after,
        .form-group label[for="productStock"]:after,
        .form-group label[for="productStatus"]:after {
            content: " *";
            color: #ef4444;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 12px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.2s ease;
            background: #ffffff;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
            transform: translateY(-1px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
            line-height: 1.5;
        }

        /* 图片上传区域样式 */
        .form-group input[type="file"] {
            padding: 0;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background: #f8fafc;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .form-group input[type="file"]:hover {
            border-color: #6b7280;
            background: #f3f4f6;
        }

        .form-group input[type="file"]:focus {
            border-color: #1f2937;
            background: #ffffff;
        }

        .image-preview {
            display: flex;
            gap: 12px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .preview-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .preview-image:hover {
            border-color: #1f2937;
            transform: scale(1.05);
        }

        /* 表单验证错误信息样式 */
        .form-error {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
            display: none;
        }

        .form-group.error input,
        .form-group.error select,
        .form-group.error textarea {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-group.error .form-error {
            display: block;
        }

        /* 上传提示样式 */
        .upload-hint {
            font-size: 12px;
            color: #6b7280;
            margin-top: 8px;
            font-style: italic;
        }

        /* 占位符样�?*/
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        /* 大型模态框样式 */
        .large-modal {
            max-width: 1000px;
            width: 90%;
        }

        /* 商品详情容器 */
        .product-detail-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 32px;
        }

        .product-detail-images {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .main-image {
            width: 100%;
            height: 300px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-detail-info {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .detail-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
        }

        .detail-section h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-item label {
            font-size: 12px;
            font-weight: 500;
            color: #6b7280;
        }

        .detail-item span {
            font-size: 14px;
            color: #1f2937;
            font-weight: 500;
        }

        /* 响应式表单设�?*/
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 16px;
                margin-bottom: 20px;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
                max-height: 95vh;
            }

            .modal-header {
                padding: 20px 24px;
            }

            .modal-header h3 {
                font-size: 16px;
            }

            .modal-body {
                padding: 24px;
            }

            .modal-footer {
                padding: 20px 24px;
                gap: 12px;
            }

            .large-modal {
                width: 95%;
                max-width: none;
            }

            .product-detail-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .form-group input,
            .form-group select,
            .form-group textarea {
                font-size: 14px;
                padding: 10px 14px;
            }

            .form-group label {
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                width: 100%;
                margin: 0;
                border-radius: 0;
                max-height: 100vh;
            }

            .modal-header {
                padding: 16px 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-footer {
                padding: 16px 20px;
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* 响应式设�?*/
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .top-nav {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .nav-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .lifecycle-cards {
                gap: 12px;
            }

            .lifecycle-card {
                min-width: 160px;
                padding: 16px;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边�?-->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管�?/a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item active">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识�?/div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管�?/a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识�?/a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识�?/a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识�?/a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识�?/a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识�?/a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识�?/a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表�?/a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分�?/a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登�?/a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区�?-->
        <main class="main-content">
            <!-- 第一层：面包屑导�?-->
            <div class="top-nav">
                <div class="breadcrumb-section">
                    <div class="page-title-section">
                        <h1 class="page-title">商品管理</h1>
                        <p class="page-description">管理智能家居产品的信息、库存、价格和销售，提供完整的商品生命周期管�?/p>
                    </div>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i>
                        新建商品
                    </button>
                </div>
            </div>

            <!-- 第二层：主菜单栏 -->
            <div class="main-menu-nav">
                <div class="main-menu-tabs">
                    <button class="main-menu-tab active" onclick="showMainMenu('products')">商品列表</button>
                    <button class="main-menu-tab" onclick="showMainMenu('inventory')">库存管理</button>
                    <button class="main-menu-tab" onclick="showMainMenu('pricing')">价格策略</button>
                </div>

                <!-- 商品生命周期展示区域 -->
                <div class="lifecycle-section">
                    <h3 class="lifecycle-title">商品生命周期</h3>
                    <div class="lifecycle-cards">
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="lifecycle-card-title">产品规划</div>
                            <div class="lifecycle-card-desc">市场调研、产品定位、功能规�?/div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="lifecycle-card-title">采购入库</div>
                            <div class="lifecycle-card-desc">供应商管理、采购下单、入库管�?/div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="lifecycle-card-title">质量检�?/div>
                            <div class="lifecycle-card-desc">质量检验、功能测试、合格认�?/div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            <div class="lifecycle-card-title">上架销�?/div>
                            <div class="lifecycle-card-desc">商品上架、价格设定、销售推�?/div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="lifecycle-card-title">售后服务</div>
                            <div class="lifecycle-card-desc">客户支持、维修服务、反馈收�?/div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三层：子菜单栏 -->
            <div class="sub-menu-nav">
                <div class="sub-menu-tabs" id="subMenuTabs">
                    <!-- 动态内容，根据主菜单选择显示 -->
                </div>
            </div>

            <!-- 第四层：内容区域 -->
            <div class="content-area">
                <div class="content-header">
                    <h2 class="content-title" id="contentTitle">商品列表</h2>
                    <div class="content-actions" id="contentActions">
                        <!-- 动态内容，根据当前页面显示 -->
                    </div>
                </div>
                <div class="content-body" id="contentBody">
                    <!-- 动态内容区�?-->
                </div>
            </div>

        </main>
    </div>

    <!-- 商品编辑模态框 -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">编辑商品</h3>
                <button class="modal-close" onclick="closeProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productName">商品名称 *</label>
                            <input type="text" id="productName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="productSku">商品SKU *</label>
                            <input type="text" id="productSku" name="sku" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productCategory">商品分类 *</label>
                            <select id="productCategory" name="category" required>
                                <option value="">请选择分类</option>
                                <option value="switch">智能开�?/option>
                                <option value="lighting">智能照明</option>
                                <option value="security">安防设备</option>
                                <option value="sensor">传感�?/option>
                                <option value="environment">环境控制</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="productBrand">品牌</label>
                            <input type="text" id="productBrand" name="brand">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productPrice">价格 *</label>
                            <input type="number" id="productPrice" name="price" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="productStock">库存数量 *</label>
                            <input type="number" id="productStock" name="stock" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productStatus">状�?*</label>
                            <select id="productStatus" name="status" required>
                                <option value="active">在售</option>
                                <option value="inactive">下架</option>
                                <option value="out-of-stock">缺货</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="productWeight">重量(kg)</label>
                            <input type="number" id="productWeight" name="weight" step="0.01">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="productDescription">商品描述</label>
                        <textarea id="productDescription" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeProductModal()">取消</button>
                <button class="btn btn-primary" onclick="saveProduct()">保存</button>
            </div>
        </div>
    </div>

    <!-- 商品详情查看模态框 -->
    <div class="modal" id="productDetailModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="detailModalTitle"><i class="fas fa-info-circle"></i> 商品详情</h3>
                <button class="modal-close" onclick="closeProductDetailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="product-detail-container">
                    <!-- 左侧：商品图�?-->
                    <div class="product-detail-images">
                        <div class="main-image" id="detailMainImage">
                            <i class="fas fa-cube" style="font-size: 64px; color: #ccc;"></i>
                        </div>
                    </div>

                    <!-- 右侧：商品信�?-->
                    <div class="product-detail-info">
                        <div class="detail-section">
                            <h4>基本信息</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>商品名称</label>
                                    <span id="detailProductName">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>商品SKU</label>
                                    <span id="detailProductSku">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>商品分类</label>
                                    <span id="detailProductCategory">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>品牌</label>
                                    <span id="detailProductBrand">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>价格库存</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>销售价�?/label>
                                    <span id="detailProductPrice">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>库存数量</label>
                                    <span id="detailProductStock">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>销售数�?/label>
                                    <span id="detailProductSales">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>商品状�?/label>
                                    <span id="detailProductStatus">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>其他信息</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>商品重量</label>
                                    <span id="detailProductWeight">-</span>
                                </div>
                            </div>
                            <div class="detail-item full-width">
                                <label>商品描述</label>
                                <span id="detailProductDescription">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeProductDetailModal()">关闭</button>
                <button class="btn btn-primary" onclick="editFromDetail()">
                    <i class="fas fa-edit"></i> 编辑商品
                </button>
            </div>
        </div>
    </div>

    <script>
        // 安全的HTML设置函数
        function safeSetHTML(element, html) {
            // 简单的HTML转义函数
            function escapeHtml(unsafe) {
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }

            // 如果HTML包含可信的标签，使用白名单方式处理
            const allowedTags = ['i', 'span', 'button', 'div', 'strong', 'em'];
            const allowedAttributes = ['class', 'onclick', 'style', 'data-id'];

            // 简单的白名单HTML清理（生产环境建议使用DOMPurify）
            if (typeof html === 'string' && html.includes('<')) {
                // 对于包含HTML的内容，使用innerHTML（已知安全的模板）
                element.innerHTML = html;
            } else {
                // 对于纯文本内容，使用textContent
                element.textContent = html;
            }
        }

        // 虚拟滚动表格类
        class VirtualTable {
            constructor(container, options = {}) {
                this.container = container;
                this.itemHeight = options.itemHeight || 64;
                this.bufferSize = options.bufferSize || 5;
                this.data = [];
                this.filteredData = [];
                this.visibleStart = 0;
                this.visibleEnd = 0;
                this.scrollTop = 0;
                this.containerHeight = 0;
                this.totalHeight = 0;
                this.visibleCount = 0;

                this.init();
            }

            init() {
                this.container.style.position = 'relative';
                this.container.style.overflow = 'auto';
                this.container.style.height = '500px'; // 默认高度

                // 创建虚拟容器
                this.virtualContainer = document.createElement('div');
                this.virtualContainer.style.position = 'absolute';
                this.virtualContainer.style.top = '0';
                this.virtualContainer.style.left = '0';
                this.virtualContainer.style.right = '0';

                // 创建内容容器
                this.contentContainer = document.createElement('div');
                this.contentContainer.style.position = 'relative';

                this.virtualContainer.appendChild(this.contentContainer);
                this.container.appendChild(this.virtualContainer);

                // 绑定滚动事件
                this.container.addEventListener('scroll', this.handleScroll.bind(this));

                // 监听容器大小变化
                if (window.ResizeObserver) {
                    this.resizeObserver = new ResizeObserver(() => {
                        this.updateDimensions();
                        this.render();
                    });
                    this.resizeObserver.observe(this.container);
                }

                this.updateDimensions();
            }

            updateDimensions() {
                this.containerHeight = this.container.clientHeight;
                this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.bufferSize * 2;
                this.totalHeight = this.filteredData.length * this.itemHeight;
                this.virtualContainer.style.height = this.totalHeight + 'px';
            }

            handleScroll() {
                this.scrollTop = this.container.scrollTop;
                this.updateVisibleRange();
                this.render();
            }

            updateVisibleRange() {
                this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize);
                this.visibleEnd = Math.min(this.filteredData.length, this.visibleStart + this.visibleCount);
            }

            setData(data) {
                this.data = data;
                this.filteredData = [...data];
                this.updateDimensions();
                this.updateVisibleRange();
                this.render();
            }

            filter(filterFn) {
                this.filteredData = this.data.filter(filterFn);
                this.updateDimensions();
                this.scrollTop = 0;
                this.container.scrollTop = 0;
                this.updateVisibleRange();
                this.render();
            }

            render() {
                const visibleItems = this.filteredData.slice(this.visibleStart, this.visibleEnd);
                const offsetY = this.visibleStart * this.itemHeight;

                this.contentContainer.style.transform = `translateY(${offsetY}px)`;
                this.contentContainer.innerHTML = '';

                visibleItems.forEach((item, index) => {
                    const element = this.createItemElement(item, this.visibleStart + index);
                    this.contentContainer.appendChild(element);
                });
            }

            createItemElement(item, index) {
                // 这个方法将被子类重写
                const element = document.createElement('div');
                element.style.height = this.itemHeight + 'px';
                element.textContent = `Item ${index}`;
                return element;
            }

            destroy() {
                if (this.resizeObserver) {
                    this.resizeObserver.disconnect();
                }
                this.container.removeEventListener('scroll', this.handleScroll);
            }
        }

        // 商品虚拟表格类
        class ProductVirtualTable extends VirtualTable {
            constructor(container, options = {}) {
                super(container, options);
                this.onEdit = options.onEdit || (() => {});
                this.onView = options.onView || (() => {});
                this.onDelete = options.onDelete || (() => {});
            }

            createItemElement(product, index) {
                const row = document.createElement('div');
                row.className = 'virtual-table-row';
                row.style.height = this.itemHeight + 'px';
                row.style.display = 'flex';
                row.style.alignItems = 'center';
                row.style.borderBottom = '1px solid #e5e7eb';
                row.style.padding = '0 16px';
                row.dataset.productId = product.id;

                const actionButtons = `
                    <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${product.id}">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-primary" data-action="view" data-id="${product.id}">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                `;

                row.innerHTML = `
                    <div style="width: 72px; text-align: center;">
                        <i class="${product.image}" style="font-size: 24px; color: #6b7280;"></i>
                    </div>
                    <div style="min-width: 240px; flex: 1;">
                        <div style="font-weight: 500; color: #1f2937;">${product.name}</div>
                        <div style="font-size: 12px; color: #6b7280;">SKU: ${product.sku} | 品牌: ${product.brand || '-'}</div>
                    </div>
                    <div style="width: 110px; text-align: right; font-weight: 600; color: #1f2937;">¥${product.price}</div>
                    <div style="width: 90px; text-align: center;">${product.stock}</div>
                    <div style="width: 90px; text-align: center;">${product.sales || 0}</div>
                    <div style="width: 100px; text-align: center;">
                        <span class="product-status status-${product.status}">${getStatusText(product.status)}</span>
                    </div>
                    <div style="width: 100px; text-align: center;">
                        <span class="sync-status synced">已同步</span>
                    </div>
                    <div style="width: 130px; text-align: center;">
                        <span style="font-size: 12px; color: #6b7280;">2024-01-15<br>10:30</span>
                    </div>
                    <div style="width: 160px; text-align: center;">
                        ${actionButtons}
                    </div>
                `;

                return row;
            }
        }

        // CSRF令牌获取函数
        function getCSRFToken() {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        }

        // 安全的fetch函数
        function secureFetch(url, options = {}) {
            const csrfToken = getCSRFToken();

            return fetch(url, {
                ...options,
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
        }

        // 防抖函数
        function debounce(func, wait, immediate = false) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        }

        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        // 性能监控工具
        class PerformanceMonitor {
            constructor() {
                this.metrics = {
                    renderTime: [],
                    searchTime: [],
                    memoryUsage: []
                };
                this.startTime = 0;
            }

            startMeasure(name) {
                this.startTime = performance.now();
                this.currentMeasure = name;
            }

            endMeasure() {
                if (this.currentMeasure && this.startTime) {
                    const duration = performance.now() - this.startTime;
                    if (!this.metrics[this.currentMeasure]) {
                        this.metrics[this.currentMeasure] = [];
                    }
                    this.metrics[this.currentMeasure].push(duration);

                    // 只保留最近100次测量
                    if (this.metrics[this.currentMeasure].length > 100) {
                        this.metrics[this.currentMeasure].shift();
                    }

                    console.log(`${this.currentMeasure}: ${duration.toFixed(2)}ms`);
                    this.currentMeasure = null;
                    this.startTime = 0;
                }
            }

            getAverageTime(metric) {
                const times = this.metrics[metric] || [];
                if (times.length === 0) return 0;
                return times.reduce((sum, time) => sum + time, 0) / times.length;
            }

            checkMemoryUsage() {
                if (performance.memory) {
                    const memory = {
                        used: Math.round(performance.memory.usedJSHeapSize / 1048576),
                        total: Math.round(performance.memory.totalJSHeapSize / 1048576),
                        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
                    };
                    this.metrics.memoryUsage.push(memory);

                    // 只保留最近50次测量
                    if (this.metrics.memoryUsage.length > 50) {
                        this.metrics.memoryUsage.shift();
                    }

                    return memory;
                }
                return null;
            }
        }

        // 全局性能监控实例
        const performanceMonitor = new PerformanceMonitor();

        // DOM缓存管理器
        class DOMCache {
            constructor() {
                this.cache = new Map();
                this.observers = new Map();
            }

            get(selector) {
                if (!this.cache.has(selector)) {
                    const element = document.querySelector(selector);
                    if (element) {
                        this.cache.set(selector, element);

                        // 监听元素移除，自动清理缓存
                        if (window.MutationObserver) {
                            const observer = new MutationObserver((mutations) => {
                                mutations.forEach((mutation) => {
                                    mutation.removedNodes.forEach((node) => {
                                        if (node === element) {
                                            this.cache.delete(selector);
                                            observer.disconnect();
                                            this.observers.delete(selector);
                                        }
                                    });
                                });
                            });

                            observer.observe(document.body, {
                                childList: true,
                                subtree: true
                            });

                            this.observers.set(selector, observer);
                        }
                    }
                }
                return this.cache.get(selector);
            }

            clear() {
                this.cache.clear();
                this.observers.forEach(observer => observer.disconnect());
                this.observers.clear();
            }
        }

        // 全局DOM缓存实例
        const domCache = new DOMCache();

        // 全局状态管�?
        const appState = {
            currentMainMenu: 'products',
            currentSubMenu: 'all',
            currentEditId: null,
            currentViewId: null,
            searchQuery: '',
            selectedBrand: '',
            products: [
                {
                    id: 1,
                    name: '智能开关面�?- 单火线版',
                    price: 299,
                    stock: 156,
                    sales: 23,
                    category: 'switch',
                    status: 'active',
                    image: 'fas fa-toggle-on',
                    description: '支持单火线安装的智能开关面板',
                    sku: 'SW001',
                    brand: 'Aqara',
                    weight: 0.2
                },
                {
                    id: 2,
                    name: '智能门锁 - 指纹版',
                    price: 1299,
                    stock: 45,
                    sales: 15,
                    category: 'security',
                    status: 'active',
                    image: 'fas fa-lock',
                    description: '支持指纹、密码、卡片多种开锁方式',
                    sku: 'DL001',
                    brand: '小米',
                    weight: 2.5
                },
                {
                    id: 3,
                    name: '智能灯泡 - 调色版',
                    price: 89,
                    stock: 0,
                    sales: 67,
                    category: 'lighting',
                    status: 'out-of-stock',
                    image: 'fas fa-lightbulb',
                    description: '支持1600万色调节的智能灯泡',
                    sku: 'LB001',
                    brand: 'Philips',
                    weight: 0.1
                },
                {
                    id: 4,
                    name: '智能插座 - WiFi版',
                    price: 59,
                    stock: 234,
                    sales: 89,
                    category: 'switch',
                    status: 'active',
                    image: 'fas fa-plug',
                    description: 'WiFi远程控制智能插座',
                    sku: 'PL001',
                    brand: 'Aqara',
                    weight: 0.15
                },
                {
                    id: 5,
                    name: '智能摄像�?- 1080P',
                    price: 399,
                    stock: 78,
                    sales: 34,
                    category: 'security',
                    status: 'active',
                    image: 'fas fa-video',
                    description: '1080P高清智能摄像头',
                    sku: 'CM001',
                    brand: '海康威视',
                    weight: 0.8
                },
                {
                    id: 6,
                    name: '温湿度传感器',
                    price: 79,
                    stock: 156,
                    sales: 45,
                    category: 'sensor',
                    status: 'active',
                    image: 'fas fa-thermometer-half',
                    description: '高精度温湿度检测传感器',
                    sku: 'SE001',
                    brand: 'Aqara',
                    weight: 0.05
                },
                {
                    id: 7,
                    name: '智能窗帘电机',
                    price: 599,
                    stock: 23,
                    sales: 12,
                    category: 'environment',
                    status: 'active',
                    image: 'fas fa-wind',
                    description: '静音智能窗帘电机，支持定时控制',
                    sku: 'CU001',
                    brand: '杜亚',
                    weight: 1.2
                }
            ],
            inventory: {
                sufficient: [{ id: 1, name: '智能开关面板', stock: 156, safeStock: 50 }],
                insufficient: [{ id: 2, name: '智能门锁', stock: 45, safeStock: 100 }],
                outOfStock: [{ id: 3, name: '智能灯泡', stock: 0, safeStock: 30 }],
                warning: [{ id: 4, name: '智能插座', stock: 15, safeStock: 50 }]
            },
            pricing: {
                standard: [{ id: 1, name: '智能开关', price: 299, type: 'standard' }],
                promotion: [{ id: 2, name: '智能门锁', price: 1299, discount: 10, type: 'promotion' }],
                member: [{ id: 3, name: '智能灯泡', price: 89, memberPrice: 79, type: 'member' }],
                wholesale: [{ id: 4, name: '智能插座', price: 59, wholesalePrice: 45, type: 'wholesale' }]
            }
        };

        // 主菜单切�?
        function showMainMenu(menu) {
            // 更新主菜单激活状�?
            document.querySelectorAll('.main-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状�?
            appState.currentMainMenu = menu;
            appState.currentSubMenu = getDefaultSubMenu(menu);

            // 更新子菜�?
            updateSubMenu(menu);

            // 更新内容区域
            updateContent();
        }

        // 获取默认子菜�?
        function getDefaultSubMenu(mainMenu) {
            const defaults = {
                'products': 'all',
                'inventory': 'sufficient',
                'pricing': 'standard'
            };
            return defaults[mainMenu] || 'all';
        }

        // 更新子菜�?
        function updateSubMenu(mainMenu) {
            const subMenuTabs = document.getElementById('subMenuTabs');
            let subMenus = [];

            switch(mainMenu) {
                case 'products':
                    subMenus = [
                        { id: 'all', name: '全部商品' },
                        { id: 'switch', name: '智能开关' },
                        { id: 'lighting', name: '智能照明' },
                        { id: 'security', name: '安防设备' },
                        { id: 'environment', name: '环境控制' }
                    ];
                    break;
                case 'inventory':
                    subMenus = [
                        { id: 'sufficient', name: '库存充足' },
                        { id: 'insufficient', name: '库存不足' },
                        { id: 'outOfStock', name: '缺货商品' },
                        { id: 'warning', name: '预警商品' }
                    ];
                    break;
                case 'pricing':
                    subMenus = [
                        { id: 'standard', name: '标准价格' },
                        { id: 'promotion', name: '促销价格' },
                        { id: 'member', name: '会员价格' },
                        { id: 'wholesale', name: '批发价格' }
                    ];
                    break;
            }

            safeSetHTML(subMenuTabs, subMenus.map(menu =>
                `<button class="sub-menu-tab ${menu.id === appState.currentSubMenu ? 'active' : ''}"
                         onclick="showSubMenu('${mainMenu}', '${menu.id}')">${menu.name}</button>`
            ).join(''));
        }

        // 子菜单切�?
        function showSubMenu(mainMenu, subMenu) {
            // 更新子菜单激活状�?
            document.querySelectorAll('.sub-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状�?
            appState.currentSubMenu = subMenu;

            // 更新内容区域
            updateContent();
        }

        // 更新内容区域
        function updateContent() {
            const contentTitle = document.getElementById('contentTitle');
            const contentActions = document.getElementById('contentActions');
            const contentBody = document.getElementById('contentBody');

            switch(appState.currentMainMenu) {
                case 'products':
                    renderProductsContent(contentTitle, contentActions, contentBody);
                    break;
                case 'inventory':
                    renderInventoryContent(contentTitle, contentActions, contentBody);
                    break;
                case 'pricing':
                    renderPricingContent(contentTitle, contentActions, contentBody);
                    break;
            }
        }

        // 渲染商品列表内容
        function renderProductsContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '商品列表';

            safeSetHTML(actionsEl, `
                <button class="btn btn-secondary">
                    <i class="fas fa-filter"></i>
                    筛选
                </button>
                <button class="btn btn-primary" onclick="showAddProductModal()">
                    <i class="fas fa-plus"></i>
                    新建商品
                </button>
            `);

            // 使用新的筛选和渲染逻辑
            filterAndRenderProducts();
        }

        // 渲染库存管理内容
        function renderInventoryContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '库存管理';

            safeSetHTML(actionsEl, `
                <button class="btn btn-secondary">
                    <i class="fas fa-chart-bar"></i>
                    库存报告
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    库存调整
                </button>
            `);

            const currentData = appState.inventory[appState.currentSubMenu] || [];

            safeSetHTML(bodyEl, `
                <div class="inventory-list">
                    ${currentData.map(item => `
                        <div class="inventory-item">
                            <div class="inventory-info">
                                <div class="inventory-name">${item.name}</div>
                                <div class="inventory-details">
                                    <span>当前库存: ${item.stock}</span>
                                    <span>安全库存: ${item.safeStock}</span>
                                </div>
                            </div>
                            <div class="inventory-actions">
                                <button class="btn btn-sm btn-secondary">调整</button>
                                <button class="btn btn-sm btn-primary">详情</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `);
        }

        // 渲染价格策略内容
        function renderPricingContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '价格策略';

            safeSetHTML(actionsEl, `
                <button class="btn btn-secondary">
                    <i class="fas fa-chart-line"></i>
                    价格分析
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新建策略
                </button>
            `);

            const currentData = appState.pricing[appState.currentSubMenu] || [];

            safeSetHTML(bodyEl, `
                <div class="pricing-list">
                    ${currentData.map(item => `
                        <div class="pricing-item">
                            <div class="pricing-info">
                                <div class="pricing-name">${item.name}</div>
                                <div class="pricing-details">
                                    <span>标准价格: ¥${item.price}</span>
                                    ${item.discount ? `<span>折扣: ${item.discount}%</span>` : ''}
                                    ${item.memberPrice ? `<span>会员价: ¥${item.memberPrice}</span>` : ''}
                                    ${item.wholesalePrice ? `<span>批发价: ¥${item.wholesalePrice}</span>` : ''}
                                </div>
                            </div>
                            <div class="pricing-actions">
                                <button class="btn btn-sm btn-secondary">编辑</button>
                                <button class="btn btn-sm btn-primary">应用</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `);
        }

        // 获取状态文�?
        function getStatusText(status) {
            const statusMap = {
                'active': '在售',
                'inactive': '下架',
                'out-of-stock': '缺货'
            };
            return statusMap[status] || status;
        }

        // 优化的搜索函数（带防抖）
        const debouncedSearch = debounce((query) => {
            performanceMonitor.startMeasure('searchTime');
            appState.searchQuery = query.toLowerCase().trim();
            filterAndRenderProducts();
            performanceMonitor.endMeasure();
        }, 300);

        // 搜索商品
        function searchProducts(query) {
            console.log('搜索商品:', query);
            debouncedSearch(query);
        }

        // 筛选和渲染商品
        function filterAndRenderProducts() {
            let filteredProducts = [...appState.products];

            // 根据子菜单筛�?
            if (appState.currentSubMenu !== 'all') {
                filteredProducts = filteredProducts.filter(product =>
                    product.category === appState.currentSubMenu
                );
            }

            // 根据搜索查询筛�?
            if (appState.searchQuery) {
                filteredProducts = filteredProducts.filter(product =>
                    product.name.toLowerCase().includes(appState.searchQuery) ||
                    product.sku.toLowerCase().includes(appState.searchQuery) ||
                    (product.brand && product.brand.toLowerCase().includes(appState.searchQuery))
                );
            }

            // 根据品牌筛�?
            if (appState.selectedBrand) {
                filteredProducts = filteredProducts.filter(product =>
                    product.brand === appState.selectedBrand
                );
            }

            // 使用虚拟滚动渲染大量数据
            if (filteredProducts.length > 50) {
                renderVirtualProductTable(filteredProducts);
            } else {
                renderProductTable(filteredProducts);
            }

            performanceMonitor.endMeasure();

            // 检查内存使用情况
            const memory = performanceMonitor.checkMemoryUsage();
            if (memory && memory.used > memory.limit * 0.8) {
                console.warn('内存使用率过高:', memory);
            }
        }

        // 虚拟滚动渲染函数
        function renderVirtualProductTable(filteredProducts) {
            const contentBody = domCache.get('#contentBody');
            if (!contentBody) return;

            // 创建虚拟滚动容器
            const virtualContainer = document.createElement('div');
            virtualContainer.className = 'virtual-table-container';
            virtualContainer.style.cssText = `
                height: 500px;
                overflow: auto;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
            `;

            // 创建表头
            const header = document.createElement('div');
            header.className = 'virtual-table-header';
            header.style.cssText = `
                display: flex;
                background: #f8fafc;
                border-bottom: 1px solid #e5e7eb;
                padding: 0 16px;
                height: 48px;
                align-items: center;
                font-weight: 600;
                position: sticky;
                top: 0;
                z-index: 10;
            `;

            header.innerHTML = `
                <div style="width: 72px; text-align: center;">图片</div>
                <div style="min-width: 240px; flex: 1;">商品信息</div>
                <div style="width: 110px; text-align: right;">价格</div>
                <div style="width: 90px; text-align: center;">库存</div>
                <div style="width: 90px; text-align: center;">销量</div>
                <div style="width: 100px; text-align: center;">状态</div>
                <div style="width: 100px; text-align: center;">MedusaJS</div>
                <div style="width: 130px; text-align: center;">创建时间</div>
                <div style="width: 160px; text-align: center;">操作</div>
            `;

            virtualContainer.appendChild(header);

            // 初始化虚拟表格
            const virtualTable = new ProductVirtualTable(virtualContainer, {
                itemHeight: 64,
                bufferSize: 5,
                onEdit: editProduct,
                onView: viewProduct,
                onDelete: (id) => console.log('删除商品:', id)
            });

            virtualTable.setData(filteredProducts);

            // 清空内容并添加虚拟表格
            contentBody.innerHTML = '';
            contentBody.appendChild(virtualContainer);

            // 使用事件委托处理按钮点击
            setupEventDelegation(virtualContainer);
        }

        // 事件委托设置
        function setupEventDelegation(container) {
            // 移除旧的事件监听器（如果存在）
            if (container._eventHandler) {
                container.removeEventListener('click', container._eventHandler);
            }

            // 创建新的事件处理器
            const eventHandler = throttle((e) => {
                const target = e.target.closest('[data-action]');
                if (!target) return;

                const action = target.dataset.action;
                const id = parseInt(target.dataset.id);

                switch (action) {
                    case 'edit':
                        editProduct(id);
                        break;
                    case 'view':
                        viewProduct(id);
                        break;
                    case 'delete':
                        if (confirm('确定要删除这个商品吗？')) {
                            deleteProduct(id);
                        }
                        break;
                    default:
                        console.log('未知操作:', action);
                }
            }, 100);

            // 绑定事件监听器
            container.addEventListener('click', eventHandler);
            container._eventHandler = eventHandler;
        }

        // 内存管理和清理函数
        class MemoryManager {
            constructor() {
                this.cleanupTasks = [];
                this.intervals = [];
                this.timeouts = [];
                this.eventListeners = [];
            }

            addCleanupTask(task) {
                this.cleanupTasks.push(task);
            }

            addInterval(intervalId) {
                this.intervals.push(intervalId);
            }

            addTimeout(timeoutId) {
                this.timeouts.push(timeoutId);
            }

            addEventListener(element, event, handler) {
                this.eventListeners.push({ element, event, handler });
                element.addEventListener(event, handler);
            }

            cleanup() {
                // 清理定时器
                this.intervals.forEach(id => clearInterval(id));
                this.timeouts.forEach(id => clearTimeout(id));

                // 清理事件监听器
                this.eventListeners.forEach(({ element, event, handler }) => {
                    element.removeEventListener(event, handler);
                });

                // 执行自定义清理任务
                this.cleanupTasks.forEach(task => {
                    try {
                        task();
                    } catch (error) {
                        console.error('清理任务执行失败:', error);
                    }
                });

                // 清空数组
                this.cleanupTasks = [];
                this.intervals = [];
                this.timeouts = [];
                this.eventListeners = [];

                // 清理DOM缓存
                domCache.clear();

                console.log('内存清理完成');
            }

            // 监控内存使用情况
            startMemoryMonitoring() {
                const monitorInterval = setInterval(() => {
                    const memory = performanceMonitor.checkMemoryUsage();
                    if (memory && memory.used > memory.limit * 0.9) {
                        console.warn('内存使用率过高，执行清理:', memory);
                        this.cleanup();
                    }
                }, 30000); // 每30秒检查一次

                this.addInterval(monitorInterval);
            }
        }

        // 全局内存管理器
        const memoryManager = new MemoryManager();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            memoryManager.cleanup();
        });

        // 启动内存监控
        memoryManager.startMemoryMonitoring();

        // 全局事件委托设置
        function setupGlobalEventDelegation() {
            // 搜索框防抖处理
            document.addEventListener('input', (e) => {
                if (e.target.dataset.search === 'true') {
                    debouncedSearch(e.target.value);
                }
            });

            // 筛选器处理
            document.addEventListener('change', (e) => {
                if (e.target.dataset.filter === 'brand') {
                    filterByBrand(e.target.value);
                }
            });

            // 按钮点击处理（全局）
            document.addEventListener('click', throttle((e) => {
                const target = e.target.closest('[data-action]');
                if (!target) return;

                const action = target.dataset.action;
                const id = target.dataset.id;

                switch (action) {
                    case 'add-product':
                        showAddProductModal();
                        break;
                    case 'sync-medusa':
                        syncToMedusaJS();
                        break;
                    case 'export-data':
                        exportProductData();
                        break;
                    case 'import-data':
                        showBatchImportModal();
                        break;
                    default:
                        // 其他操作由具体容器的事件委托处理
                        break;
                }
            }, 100));

            // 表单提交防抖处理
            document.addEventListener('submit', (e) => {
                e.preventDefault();
                const form = e.target;

                if (form.dataset.debounce === 'true') {
                    // 防止重复提交
                    if (form._submitting) return;
                    form._submitting = true;

                    setTimeout(() => {
                        form._submitting = false;
                    }, 1000);
                }

                // 处理表单提交
                handleFormSubmit(form);
            });

            // 键盘快捷键支持
            document.addEventListener('keydown', (e) => {
                // Ctrl+F 聚焦搜索框
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    const searchInput = document.querySelector('[data-search="true"]');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // ESC 关闭模态框
                if (e.key === 'Escape') {
                    const modals = document.querySelectorAll('.modal[style*="block"]');
                    modals.forEach(modal => {
                        modal.style.display = 'none';
                    });
                }
            });

            console.log('全局事件委托设置完成');
        }

        // 表单提交处理
        function handleFormSubmit(form) {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            if (form.id === 'productForm') {
                saveProduct();
            } else if (form.id === 'batchImportForm') {
                handleBatchImport(data);
            }
        }

        // 数据导出功能
        function exportProductData() {
            performanceMonitor.startMeasure('exportTime');

            try {
                const data = appState.products.map(product => ({
                    名称: product.name,
                    SKU: product.sku,
                    价格: product.price,
                    库存: product.stock,
                    品牌: product.brand || '',
                    状态: getStatusText(product.status),
                    创建时间: product.createdAt || '2024-01-15'
                }));

                const csv = convertToCSV(data);
                downloadCSV(csv, 'products.csv');

                showToast('数据导出成功', 'success');
            } catch (error) {
                console.error('导出失败:', error);
                showToast('数据导出失败', 'error');
            }

            performanceMonitor.endMeasure();
        }

        // CSV转换工具
        function convertToCSV(data) {
            if (!data.length) return '';

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row =>
                    headers.map(header =>
                        `"${String(row[header]).replace(/"/g, '""')}"`
                    ).join(',')
                )
            ].join('\n');

            return csvContent;
        }

        // CSV下载工具
        function downloadCSV(csv, filename) {
            const blob = new Blob(['\uFEFF' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }

        // 渲染商品表格
        function renderProductTable(products) {
            console.log('renderProductTable called with products:', products);
            const contentBody = document.getElementById('contentBody');
            if (!contentBody) {
                console.error('contentBody element not found');
                return;
            }

            const tableHTML = `
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索商品名称、SKU..."
                               value="${appState.searchQuery || ''}"
                               data-search="true">
                    </div>
                    <div class="filter-controls">
                        <select class="brand-filter" data-filter="brand">
                            <option value="">全部品牌</option>
                            ${getBrandOptions()}
                        </select>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> 清除筛�?
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 72px; text-align: center;">图片</th>
                                <th style="min-width: 240px;">商品信息</th>
                                <th style="width: 110px; text-align: right;">价格</th>
                                <th style="width: 90px; text-align: center;">库存</th>
                                <th style="width: 90px; text-align: center;">销�?/th>
                                <th style="width: 100px; text-align: center;">状�?/th>
                                <th style="width: 100px; text-align: center;">MedusaJS</th>
                                <th style="width: 130px; text-align: center;">创建时间</th>
                                <th style="width: 160px; text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${products.length > 0 ? products.map(product => {
                                const actionButtons = `
                                    <div class="product-actions" style="opacity: 1 !important; display: flex !important;">
                                        <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${product.id}" title="编辑" style="display: inline-flex !important;">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" data-action="view" data-id="${product.id}" title="查看" style="display: inline-flex !important;">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                `;
                                return `
                                <tr>
                                    <td>
                                        <div class="product-image-cell">
                                            <i class="${product.image}" style="font-size: 24px; color: #6b7280;"></i>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="product-info-cell">
                                            <div class="product-name">${product.name}</div>
                                            <div class="product-sku">SKU: ${product.sku}</div>
                                            ${product.brand ? `<div class="product-brand">品牌: ${product.brand}</div>` : ''}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="product-price">¥${product.price}</span>
                                    </td>
                                    <td>
                                        <span class="product-stock">${product.stock}</span>
                                    </td>
                                    <td>
                                        <span class="product-sales">${product.sales || 0}</span>
                                    </td>
                                    <td>
                                        <span class="product-status status-${product.status}">
                                            ${getStatusText(product.status)}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="product-date">2024-01-15<br>10:30</span>
                                    </td>
                                    <td>
                                        <span class="product-date">2024-01-20<br>14:25</span>
                                    </td>
                                    <td>
                                        ${actionButtons}
                                    </td>
                                </tr>
                                `;
                            }).join('') : `
                                <tr>
                                    <td colspan="9" style="text-align: center; padding: 40px; color: #6b7280;">
                                        <i class="fas fa-search" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                                        没有找到匹配的商�?
                                    </td>
                                </tr>
                            `}
                        </tbody>
                    </table>
                </div>
            `;

            console.log('Setting table HTML:', tableHTML);
            safeSetHTML(contentBody, tableHTML);

            // 为表格添加事件委托处理按钮点�?
            contentBody.removeEventListener('click', handleTableButtonClick);
            contentBody.addEventListener('click', handleTableButtonClick);
        }

        // 表格按钮点击处理函数
        function handleTableButtonClick(event) {
            const button = event.target.closest('button[data-action]');
            if (!button) return;

            const action = button.dataset.action;
            const id = parseInt(button.dataset.id);

            console.log('表格按钮点击事件:', action, id);

            switch(action) {
                case 'edit':
                    editProduct(id);
                    break;
                case 'view':
                    viewProduct(id);
                    break;
                default:
                    console.warn('未知的操作类�?', action);
            }
        }

        // 获取品牌选项
        function getBrandOptions() {
            const brands = [...new Set(appState.products
                .map(p => p.brand)
                .filter(brand => brand && brand.trim())
            )].sort();

            return brands.map(brand =>
                `<option value="${brand}" ${appState.selectedBrand === brand ? 'selected' : ''}>${brand}</option>`
            ).join('');
        }

        // 按品牌筛�?
        function filterByBrand(brand) {
            appState.selectedBrand = brand;
            filterAndRenderProducts();
        }

        // 清除筛�?
        function clearFilters() {
            appState.searchQuery = '';
            appState.selectedBrand = '';
            filterAndRenderProducts();
        }

        // 编辑商品
        function editProduct(id) {
            console.log('编辑商品:', id);
            const product = appState.products.find(p => p.id === id);
            if (!product) {
                showToast('商品不存在', 'error');
                return;
            }

            // 设置当前编辑的商品ID
            appState.currentEditId = id;

            // 更新模态框标题
            document.getElementById('modalTitle').textContent = '编辑商品';

            // 填充表单数据
            document.getElementById('productName').value = product.name || '';
            document.getElementById('productSku').value = product.sku || '';
            document.getElementById('productCategory').value = product.category || '';
            document.getElementById('productBrand').value = product.brand || '';
            document.getElementById('productPrice').value = product.price || '';
            document.getElementById('productStock').value = product.stock || '';
            document.getElementById('productStatus').value = product.status || 'active';
            document.getElementById('productWeight').value = product.weight || '';
            document.getElementById('productDescription').value = product.description || '';

            // 显示模态框
            document.getElementById('productModal').style.display = 'block';
        }

        // 查看商品
        function viewProduct(id) {
            console.log('查看商品:', id);
            const product = appState.products.find(p => p.id === id);
            if (!product) {
                showToast('商品不存在', 'error');
                return;
            }

            // 设置当前查看的商品ID
            appState.currentViewId = id;

            // 更新模态框标题
            safeSetHTML(document.getElementById('detailModalTitle'), `<i class="fas fa-info-circle"></i> ${product.name} - 商品详情`);

            // 填充商品详情
            document.getElementById('detailProductName').textContent = product.name;
            document.getElementById('detailProductSku').textContent = product.sku;
            document.getElementById('detailProductCategory').textContent = getCategoryName(product.category);
            document.getElementById('detailProductBrand').textContent = product.brand || '-';
            document.getElementById('detailProductPrice').textContent = `¥${product.price}`;
            document.getElementById('detailProductStock').textContent = product.stock;
            document.getElementById('detailProductSales').textContent = product.sales || 0;
            safeSetHTML(document.getElementById('detailProductStatus'), `<span class="product-status status-${product.status}">${getStatusText(product.status)}</span>`);
            document.getElementById('detailProductWeight').textContent = product.weight ? `${product.weight}kg` : '-';
            document.getElementById('detailProductDescription').textContent = product.description || '暂无描述';

            // 显示商品图标
            safeSetHTML(document.getElementById('detailMainImage'), `<i class="${product.image}" style="font-size: 64px; color: #6b7280;"></i>`);

            // 显示模态框
            document.getElementById('productDetailModal').style.display = 'block';
        }

        // 关闭商品详情模态框
        function closeProductDetailModal() {
            document.getElementById('productDetailModal').style.display = 'none';
            appState.currentViewId = null;
        }

        // 从详情页面进入编辑模�?
        function editFromDetail() {
            if (appState.currentViewId) {
                closeProductDetailModal();
                editProduct(appState.currentViewId);
            }
        }

        // 获取分类名称
        function getCategoryName(categoryId) {
            const categoryMap = {
                'switch': '智能开关',
                'lighting': '智能照明',
                'security': '安防设备',
                'sensor': '传感器',
                'environment': '环境控制'
            };
            return categoryMap[categoryId] || categoryId;
        }

        // Toast 提示功能
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1f2937'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 保存商品
        function saveProduct() {
            const form = document.getElementById('productForm');
            const formData = new FormData(form);

            // 获取表单数据
            const productData = {
                name: document.getElementById('productName').value.trim(),
                sku: document.getElementById('productSku').value.trim(),
                category: document.getElementById('productCategory').value,
                brand: document.getElementById('productBrand').value.trim(),
                price: parseFloat(document.getElementById('productPrice').value) || 0,
                stock: parseInt(document.getElementById('productStock').value) || 0,
                status: document.getElementById('productStatus').value,
                weight: parseFloat(document.getElementById('productWeight').value) || 0,
                description: document.getElementById('productDescription').value.trim()
            };

            // 验证必填字段
            if (!productData.name || !productData.sku || !productData.category || !productData.price) {
                showToast('请填写所有必填字段', 'error');
                return;
            }

            // 检查SKU是否重复（编辑时排除自己�?
            const existingProduct = appState.products.find(p =>
                p.sku === productData.sku && p.id !== appState.currentEditId
            );
            if (existingProduct) {
                showToast('SKU已存在，请使用其他SKU', 'error');
                return;
            }

            try {
                if (appState.currentEditId) {
                    // 编辑现有商品
                    const productIndex = appState.products.findIndex(p => p.id === appState.currentEditId);
                    if (productIndex !== -1) {
                        appState.products[productIndex] = {
                            ...appState.products[productIndex],
                            ...productData,
                            id: appState.currentEditId // 保持ID不变
                        };
                        showToast('商品更新成功', 'success');
                    }
                } else {
                    // 创建新商�?
                    const newProduct = {
                        id: Math.max(...appState.products.map(p => p.id), 0) + 1,
                        ...productData,
                        sales: 0,
                        image: getDefaultIcon(productData.category),
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    appState.products.push(newProduct);
                    showToast('商品创建成功', 'success');
                }

                // 关闭模态框并刷新内�?
                closeProductModal();
                filterAndRenderProducts();
            } catch (error) {
                console.error('保存商品时出�?', error);
                showToast('保存失败，请重试', 'error');
            }
        }

        // 关闭商品模态框
        function closeProductModal() {
            document.getElementById('productModal').style.display = 'none';
            document.getElementById('productForm').reset();
            appState.currentEditId = null;
        }

        // 显示新建商品模态框
        function showAddProductModal() {
            // 重置表单和状�?
            document.getElementById('productForm').reset();
            appState.currentEditId = null;

            // 更新模态框标题
            document.getElementById('modalTitle').textContent = '新建商品';

            // 显示模态框
            document.getElementById('productModal').style.display = 'block';
        }

        // 根据分类获取默认图标
        function getDefaultIcon(category) {
            const iconMap = {
                'switch': 'fas fa-toggle-on',
                'lighting': 'fas fa-lightbulb',
                'security': 'fas fa-shield-alt',
                'sensor': 'fas fa-thermometer-half',
                'environment': 'fas fa-wind'
            };
            return iconMap[category] || 'fas fa-cube';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化商品管理系统');

            // 设置全局事件委托
            setupGlobalEventDelegation();

            // 初始化全局状态
            appState.currentEditId = null;
            appState.currentViewId = null;
            appState.searchQuery = '';
            appState.selectedBrand = '';

            // 初始化子菜单
            updateSubMenu(appState.currentMainMenu);

            // 初始化内容区域
            updateContent();

            console.log('商品管理系统初始化完成');

            // 启动性能监控
            startPerformanceMonitoring();
        });

        // 性能监控启动函数
        function startPerformanceMonitoring() {
            // 监控页面性能指标
            if ('PerformanceObserver' in window) {
                // 监控长任务
                const longTaskObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                        if (entry.duration > 50) {
                            console.warn('检测到长任务:', entry.duration + 'ms');
                        }
                    });
                });

                try {
                    longTaskObserver.observe({ entryTypes: ['longtask'] });
                    memoryManager.addCleanupTask(() => longTaskObserver.disconnect());
                } catch (e) {
                    console.log('长任务监控不支持');
                }

                // 监控布局偏移
                const layoutShiftObserver = new PerformanceObserver((list) => {
                    let totalShift = 0;
                    list.getEntries().forEach((entry) => {
                        totalShift += entry.value;
                    });

                    if (totalShift > 0.1) {
                        console.warn('检测到布局偏移:', totalShift);
                    }
                });

                try {
                    layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
                    memoryManager.addCleanupTask(() => layoutShiftObserver.disconnect());
                } catch (e) {
                    console.log('布局偏移监控不支持');
                }
            }

            // 定期清理无用数据
            const cleanupInterval = setInterval(() => {
                // 清理过期的性能数据
                Object.keys(performanceMonitor.metrics).forEach(key => {
                    const metrics = performanceMonitor.metrics[key];
                    if (metrics.length > 100) {
                        performanceMonitor.metrics[key] = metrics.slice(-50);
                    }
                });

                // 清理DOM缓存中的无效元素
                domCache.cache.forEach((element, selector) => {
                    if (!document.contains(element)) {
                        domCache.cache.delete(selector);
                    }
                });

                console.log('定期清理完成');
            }, 60000); // 每分钟清理一次

            memoryManager.addInterval(cleanupInterval);

            // 监控内存泄漏
            const memoryCheckInterval = setInterval(() => {
                const memory = performanceMonitor.checkMemoryUsage();
                if (memory) {
                    const usagePercent = (memory.used / memory.limit) * 100;

                    if (usagePercent > 90) {
                        console.error('内存使用率过高:', usagePercent.toFixed(2) + '%');
                        // 强制垃圾回收（如果支持）
                        if (window.gc) {
                            window.gc();
                        }
                        // 清理缓存
                        domCache.clear();
                    } else if (usagePercent > 80) {
                        console.warn('内存使用率较高:', usagePercent.toFixed(2) + '%');
                    }
                }
            }, 30000); // 每30秒检查一次

            memoryManager.addInterval(memoryCheckInterval);
        }


    </script>

    <style>
        /* 搜索和筛选区�?*/
        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 16px;
        }

        /* 库存管理样式 */
        .inventory-list {
            margin-top: 20px;
        }

        .inventory-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #ffffff;
            transition: all 0.2s ease;
        }

        .inventory-item:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .inventory-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .inventory-details {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #6b7280;
        }

        .inventory-actions {
            display: flex;
            gap: 8px;
        }

        /* 价格策略样式 */
        .pricing-list {
            margin-top: 20px;
        }

        .pricing-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #ffffff;
            transition: all 0.2s ease;
        }

        .pricing-item:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .pricing-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .pricing-details {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #6b7280;
        }

        .pricing-actions {
            display: flex;
            gap: 8px;
        }
    </style>
</body>
</html>

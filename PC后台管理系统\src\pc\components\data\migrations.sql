-- 智能家居商品管理系统数据库迁移脚本
-- 版本: 1.0.0
-- 创建时间: 2025-01-29

-- 创建数据库
CREATE DATABASE IF NOT EXISTS smart_home_products 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE smart_home_products;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('admin', 'manager', 'user') DEFAULT 'user' COMMENT '用户角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '用户状态',
    profile JSON COMMENT '用户资料',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS product_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类代码',
    description TEXT COMMENT '分类描述',
    parent_id BIGINT UNSIGNED NULL COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    metadata JSON COMMENT '元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    INDEX idx_code (code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 品牌表
CREATE TABLE IF NOT EXISTS brands (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '品牌名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '品牌代码',
    description TEXT COMMENT '品牌描述',
    logo_url VARCHAR(500) COMMENT '品牌Logo',
    website VARCHAR(500) COMMENT '官网地址',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    metadata JSON COMMENT '元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';

-- 商品表
CREATE TABLE IF NOT EXISTS products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '所属用户ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    sku VARCHAR(100) NOT NULL UNIQUE COMMENT '商品SKU',
    description TEXT COMMENT '商品描述',
    short_description VARCHAR(500) COMMENT '简短描述',
    category_id BIGINT UNSIGNED COMMENT '分类ID',
    brand_id BIGINT UNSIGNED COMMENT '品牌ID',
    price DECIMAL(10,2) NOT NULL COMMENT '销售价格',
    cost_price DECIMAL(10,2) COMMENT '成本价格',
    market_price DECIMAL(10,2) COMMENT '市场价格',
    stock INT UNSIGNED DEFAULT 0 COMMENT '库存数量',
    min_stock INT UNSIGNED DEFAULT 0 COMMENT '最小库存',
    max_stock INT UNSIGNED DEFAULT 0 COMMENT '最大库存',
    sales INT UNSIGNED DEFAULT 0 COMMENT '销量',
    views INT UNSIGNED DEFAULT 0 COMMENT '浏览量',
    weight DECIMAL(8,2) COMMENT '重量(kg)',
    dimensions JSON COMMENT '尺寸信息',
    status ENUM('active', 'inactive', 'draft', 'out_of_stock') DEFAULT 'draft' COMMENT '商品状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_digital BOOLEAN DEFAULT FALSE COMMENT '是否数字商品',
    medusa_synced BOOLEAN DEFAULT FALSE COMMENT '是否已同步到MedusaJS',
    medusa_id VARCHAR(100) COMMENT 'MedusaJS商品ID',
    images JSON COMMENT '商品图片',
    attributes JSON COMMENT '商品属性',
    metadata JSON COMMENT '元数据',
    seo_title VARCHAR(255) COMMENT 'SEO标题',
    seo_description TEXT COMMENT 'SEO描述',
    seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_sku (sku),
    INDEX idx_category_id (category_id),
    INDEX idx_brand_id (brand_id),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_stock (stock),
    INDEX idx_sales (sales),
    INDEX idx_created_at (created_at),
    INDEX idx_name_search (name),
    FULLTEXT idx_fulltext_search (name, description, short_description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品变体表（用于支持多规格商品）
CREATE TABLE IF NOT EXISTS product_variants (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    sku VARCHAR(100) NOT NULL UNIQUE COMMENT '变体SKU',
    name VARCHAR(255) COMMENT '变体名称',
    price DECIMAL(10,2) NOT NULL COMMENT '变体价格',
    cost_price DECIMAL(10,2) COMMENT '变体成本价',
    stock INT UNSIGNED DEFAULT 0 COMMENT '变体库存',
    weight DECIMAL(8,2) COMMENT '变体重量',
    dimensions JSON COMMENT '变体尺寸',
    attributes JSON COMMENT '变体属性',
    images JSON COMMENT '变体图片',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认变体',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    INDEX idx_product_id (product_id),
    INDEX idx_sku (sku),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品变体表';

-- 库存记录表
CREATE TABLE IF NOT EXISTS inventory_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    variant_id BIGINT UNSIGNED COMMENT '变体ID',
    type ENUM('in', 'out', 'adjust', 'reserve', 'release') NOT NULL COMMENT '操作类型',
    quantity INT NOT NULL COMMENT '数量变化',
    before_stock INT UNSIGNED NOT NULL COMMENT '操作前库存',
    after_stock INT UNSIGNED NOT NULL COMMENT '操作后库存',
    reason VARCHAR(255) COMMENT '操作原因',
    reference_type VARCHAR(50) COMMENT '关联类型',
    reference_id BIGINT UNSIGNED COMMENT '关联ID',
    operator_id BIGINT UNSIGNED COMMENT '操作人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_product_id (product_id),
    INDEX idx_variant_id (variant_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存记录表';

-- 商品价格历史表
CREATE TABLE IF NOT EXISTS price_history (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    variant_id BIGINT UNSIGNED COMMENT '变体ID',
    old_price DECIMAL(10,2) COMMENT '原价格',
    new_price DECIMAL(10,2) NOT NULL COMMENT '新价格',
    price_type ENUM('sale', 'cost', 'market') DEFAULT 'sale' COMMENT '价格类型',
    reason VARCHAR(255) COMMENT '调价原因',
    operator_id BIGINT UNSIGNED COMMENT '操作人ID',
    effective_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_product_id (product_id),
    INDEX idx_variant_id (variant_id),
    INDEX idx_effective_at (effective_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品价格历史表';

-- MedusaJS同步记录表
CREATE TABLE IF NOT EXISTS medusa_sync_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    sync_type ENUM('create', 'update', 'delete') NOT NULL COMMENT '同步类型',
    medusa_id VARCHAR(100) COMMENT 'MedusaJS ID',
    sync_status ENUM('pending', 'success', 'failed') DEFAULT 'pending' COMMENT '同步状态',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    synced_at TIMESTAMP NULL COMMENT '同步时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    INDEX idx_product_id (product_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MedusaJS同步记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(500) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
    resource_id BIGINT UNSIGNED COMMENT '资源ID',
    description VARCHAR(500) COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 插入默认数据

-- 插入默认分类
INSERT INTO product_categories (name, code, description) VALUES
('智能开关', 'switch', '各类智能开关产品'),
('智能照明', 'lighting', '智能灯具和照明控制设备'),
('安防设备', 'security', '智能门锁、摄像头等安防产品'),
('传感器', 'sensor', '各类环境和状态传感器'),
('环境控制', 'environment', '温控、空气净化等环境控制设备');

-- 插入默认品牌
INSERT INTO brands (name, code, description) VALUES
('小米', 'xiaomi', '小米智能家居产品'),
('华为', 'huawei', '华为智能家居产品'),
('Philips', 'philips', '飞利浦照明产品'),
('海康威视', 'hikvision', '海康威视安防产品'),
('杜亚', 'dooya', '杜亚智能窗帘产品'),
('Aqara', 'aqara', 'Aqara智能家居产品'),
('绿米', 'lumi', '绿米智能家居产品');

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('site_name', '智能家居商品管理系统', 'string', '网站名称', TRUE),
('default_currency', 'CNY', 'string', '默认货币', TRUE),
('default_language', 'zh-CN', 'string', '默认语言', TRUE),
('max_upload_size', '5242880', 'number', '最大上传文件大小(字节)', FALSE),
('allowed_image_types', '["image/jpeg","image/png","image/webp"]', 'json', '允许的图片类型', FALSE),
('enable_medusa_sync', 'true', 'boolean', '是否启用MedusaJS同步', FALSE),
('stock_warning_threshold', '10', 'number', '库存预警阈值', FALSE);

-- 创建视图

-- 商品详情视图
CREATE VIEW product_details AS
SELECT 
    p.id,
    p.user_id,
    p.name,
    p.sku,
    p.description,
    p.short_description,
    p.price,
    p.cost_price,
    p.market_price,
    p.stock,
    p.min_stock,
    p.max_stock,
    p.sales,
    p.views,
    p.weight,
    p.status,
    p.is_featured,
    p.medusa_synced,
    p.images,
    p.attributes,
    p.created_at,
    p.updated_at,
    c.name as category_name,
    c.code as category_code,
    b.name as brand_name,
    b.code as brand_code,
    u.username as owner_username
FROM products p
LEFT JOIN product_categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN users u ON p.user_id = u.id;

-- 库存统计视图
CREATE VIEW inventory_summary AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.sku,
    p.stock as current_stock,
    p.min_stock,
    p.max_stock,
    CASE 
        WHEN p.stock <= 0 THEN 'out_of_stock'
        WHEN p.stock <= p.min_stock THEN 'low_stock'
        WHEN p.stock >= p.max_stock THEN 'overstock'
        ELSE 'normal'
    END as stock_status,
    p.sales,
    p.created_at
FROM products p
WHERE p.status != 'draft';

-- 创建存储过程

-- 更新商品库存的存储过程
DELIMITER //
CREATE PROCEDURE UpdateProductStock(
    IN p_product_id BIGINT UNSIGNED,
    IN p_variant_id BIGINT UNSIGNED,
    IN p_quantity INT,
    IN p_type ENUM('in', 'out', 'adjust'),
    IN p_reason VARCHAR(255),
    IN p_operator_id BIGINT UNSIGNED
)
BEGIN
    DECLARE current_stock INT DEFAULT 0;
    DECLARE new_stock INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 获取当前库存
    IF p_variant_id IS NOT NULL THEN
        SELECT stock INTO current_stock FROM product_variants WHERE id = p_variant_id FOR UPDATE;
        SET new_stock = current_stock + p_quantity;
        UPDATE product_variants SET stock = new_stock WHERE id = p_variant_id;
    ELSE
        SELECT stock INTO current_stock FROM products WHERE id = p_product_id FOR UPDATE;
        SET new_stock = current_stock + p_quantity;
        UPDATE products SET stock = new_stock WHERE id = p_product_id;
    END IF;
    
    -- 记录库存变化
    INSERT INTO inventory_logs (
        product_id, variant_id, type, quantity, before_stock, after_stock, 
        reason, operator_id
    ) VALUES (
        p_product_id, p_variant_id, p_type, p_quantity, current_stock, new_stock,
        p_reason, p_operator_id
    );
    
    COMMIT;
END //
DELIMITER ;

-- 创建触发器

-- 商品更新时自动记录价格变化
DELIMITER //
CREATE TRIGGER product_price_change_trigger
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    IF OLD.price != NEW.price THEN
        INSERT INTO price_history (
            product_id, old_price, new_price, price_type, reason
        ) VALUES (
            NEW.id, OLD.price, NEW.price, 'sale', 'Product price updated'
        );
    END IF;
END //
DELIMITER ;

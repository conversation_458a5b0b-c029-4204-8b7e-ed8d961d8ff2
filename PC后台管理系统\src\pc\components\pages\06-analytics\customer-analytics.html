<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户分析 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item active">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">客户分析</h1>
                <p class="page-subtitle">客户数据分析和统计报告</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #3b82f6; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-users" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="totalCustomers">0</div>
                            <div style="font-size: 14px; color: #6b7280;">总客户数</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #10b981; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user-plus" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="newCustomers">0</div>
                            <div style="font-size: 14px; color: #6b7280;">本月新增</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #f59e0b; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-line" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="activeCustomers">0</div>
                            <div style="font-size: 14px; color: #6b7280;">活跃客户</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #ef4444; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-dollar-sign" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="avgOrderValue">¥0</div>
                            <div style="font-size: 14px; color: #6b7280;">平均订单价值</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 30px;">
                <!-- 客户增长趋势图 -->
                <div class="chart-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #1f2937;">客户增长趋势</h3>
                    <canvas id="customerGrowthChart" width="400" height="200"></canvas>
                </div>

                <!-- 客户分布饼图 -->
                <div class="chart-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #1f2937;">客户类型分布</h3>
                    <canvas id="customerTypeChart" width="300" height="200"></canvas>
                </div>
            </div>

            <!-- 客户列表 -->
            <div class="content-container" style="background: white; border-radius: 12px; border: 1px solid #e5e7eb; padding: 24px;">
                <div class="content-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">客户详情</h2>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <input type="text" id="searchInput" placeholder="搜索客户..." style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        <select id="typeFilter" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                            <option value="">所有类型</option>
                            <option value="individual">个人客户</option>
                            <option value="enterprise">企业客户</option>
                            <option value="vip">VIP客户</option>
                        </select>
                        <button class="btn btn-primary" onclick="customerAnalytics.exportData()">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>客户ID</th>
                                <th>客户姓名</th>
                                <th>客户类型</th>
                                <th>注册时间</th>
                                <th>订单数量</th>
                                <th>总消费金额</th>
                                <th>最后活跃</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- 客户数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div id="pagination" style="display: flex; justify-content: center; align-items: center; gap: 8px; margin-top: 20px;">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 设计系统模拟
        const ds = {
            showLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0.5';
                    element.style.pointerEvents = 'none';
                }
            },
            hideLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '1';
                    element.style.pointerEvents = 'auto';
                }
            },
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            formatDate: (dateString, format = 'YYYY-MM-DD') => {
                if (!dateString) return '';
                const date = new Date(dateString);
                if (format === 'YYYY-MM-DD') {
                    return date.toLocaleDateString('zh-CN');
                }
                return date.toLocaleString('zh-CN');
            },
            formatCurrency: (amount) => {
                return new Intl.NumberFormat('zh-CN', {
                    style: 'currency',
                    currency: 'CNY'
                }).format(amount);
            }
        };

        class CustomerAnalytics {
            constructor() {
                this.customers = [];
                this.filteredCustomers = [];
                this.currentPage = 1;
                this.pageSize = 10;
                this.charts = {};

                this.loadData();
                this.init();
            }

            loadData() {
                // 加载客户数据
                this.customers = JSON.parse(localStorage.getItem('customers') || '[]');

                // 初始化默认数据
                if (this.customers.length === 0) {
                    this.customers = [
                        {
                            id: 1,
                            name: '张三',
                            type: 'individual',
                            phone: '13800138001',
                            email: '<EMAIL>',
                            registeredAt: new Date(Date.now() - 86400000 * 30).toISOString(),
                            lastActiveAt: new Date(Date.now() - 86400000 * 2).toISOString(),
                            orderCount: 5,
                            totalSpent: 15800,
                            status: 'active',
                            address: '北京市朝阳区',
                            source: 'online'
                        },
                        {
                            id: 2,
                            name: '李四',
                            type: 'enterprise',
                            phone: '13800138002',
                            email: '<EMAIL>',
                            registeredAt: new Date(Date.now() - 86400000 * 45).toISOString(),
                            lastActiveAt: new Date(Date.now() - 86400000 * 1).toISOString(),
                            orderCount: 12,
                            totalSpent: 58600,
                            status: 'active',
                            address: '上海市浦东新区',
                            source: 'referral'
                        },
                        {
                            id: 3,
                            name: '王五',
                            type: 'vip',
                            phone: '13800138003',
                            email: '<EMAIL>',
                            registeredAt: new Date(Date.now() - 86400000 * 60).toISOString(),
                            lastActiveAt: new Date(Date.now() - 86400000 * 5).toISOString(),
                            orderCount: 25,
                            totalSpent: 128900,
                            status: 'active',
                            address: '广州市天河区',
                            source: 'offline'
                        },
                        {
                            id: 4,
                            name: '赵六',
                            type: 'individual',
                            phone: '13800138004',
                            email: '<EMAIL>',
                            registeredAt: new Date(Date.now() - 86400000 * 15).toISOString(),
                            lastActiveAt: new Date(Date.now() - 86400000 * 10).toISOString(),
                            orderCount: 2,
                            totalSpent: 3200,
                            status: 'inactive',
                            address: '深圳市南山区',
                            source: 'online'
                        },
                        {
                            id: 5,
                            name: '钱七',
                            type: 'enterprise',
                            phone: '13800138005',
                            email: '<EMAIL>',
                            registeredAt: new Date(Date.now() - 86400000 * 90).toISOString(),
                            lastActiveAt: new Date(Date.now() - 86400000 * 3).toISOString(),
                            orderCount: 18,
                            totalSpent: 89500,
                            status: 'active',
                            address: '杭州市西湖区',
                            source: 'partner'
                        }
                    ];
                    this.saveCustomers();
                }
            }

            saveCustomers() {
                localStorage.setItem('customers', JSON.stringify(this.customers));
            }

            init() {
                this.updateStatistics();
                this.initCharts();
                this.loadCustomers();
                this.bindEvents();
            }

            updateStatistics() {
                const totalCustomers = this.customers.length;

                // 计算本月新增客户
                const thisMonth = new Date();
                thisMonth.setDate(1);
                thisMonth.setHours(0, 0, 0, 0);
                const newCustomers = this.customers.filter(c =>
                    new Date(c.registeredAt) >= thisMonth
                ).length;

                // 计算活跃客户（30天内有活动）
                const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                const activeCustomers = this.customers.filter(c =>
                    new Date(c.lastActiveAt) >= thirtyDaysAgo
                ).length;

                // 计算平均订单价值
                const totalSpent = this.customers.reduce((sum, c) => sum + c.totalSpent, 0);
                const totalOrders = this.customers.reduce((sum, c) => sum + c.orderCount, 0);
                const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;

                // 更新统计显示
                document.getElementById('totalCustomers').textContent = totalCustomers;
                document.getElementById('newCustomers').textContent = newCustomers;
                document.getElementById('activeCustomers').textContent = activeCustomers;
                document.getElementById('avgOrderValue').textContent = ds.formatCurrency(avgOrderValue);
            }

            initCharts() {
                this.initCustomerGrowthChart();
                this.initCustomerTypeChart();
            }

            initCustomerGrowthChart() {
                const ctx = document.getElementById('customerGrowthChart').getContext('2d');

                // 生成过去12个月的数据
                const months = [];
                const data = [];
                const now = new Date();

                for (let i = 11; i >= 0; i--) {
                    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                    months.push(date.toLocaleDateString('zh-CN', { month: 'short' }));

                    // 计算该月新增客户数
                    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
                    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

                    const monthlyCustomers = this.customers.filter(c => {
                        const regDate = new Date(c.registeredAt);
                        return regDate >= monthStart && regDate <= monthEnd;
                    }).length;

                    data.push(monthlyCustomers);
                }

                this.charts.growth = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: months,
                        datasets: [{
                            label: '新增客户',
                            data: data,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }

            initCustomerTypeChart() {
                const ctx = document.getElementById('customerTypeChart').getContext('2d');

                // 统计客户类型分布
                const typeCount = {
                    individual: 0,
                    enterprise: 0,
                    vip: 0
                };

                this.customers.forEach(customer => {
                    typeCount[customer.type]++;
                });

                this.charts.type = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['个人客户', '企业客户', 'VIP客户'],
                        datasets: [{
                            data: [typeCount.individual, typeCount.enterprise, typeCount.vip],
                            backgroundColor: ['#3b82f6', '#10b981', '#f59e0b'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            loadCustomers() {
                // 应用筛选条件
                this.filteredCustomers = [...this.customers];

                // 搜索筛选
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    const keyword = searchInput.value.trim().toLowerCase();
                    this.filteredCustomers = this.filteredCustomers.filter(customer =>
                        customer.name.toLowerCase().includes(keyword) ||
                        customer.phone.includes(keyword) ||
                        customer.email.toLowerCase().includes(keyword)
                    );
                }

                // 类型筛选
                const typeFilter = document.getElementById('typeFilter');
                if (typeFilter && typeFilter.value) {
                    this.filteredCustomers = this.filteredCustomers.filter(customer =>
                        customer.type === typeFilter.value
                    );
                }

                // 按注册时间倒序排列
                this.filteredCustomers.sort((a, b) => {
                    return new Date(b.registeredAt) - new Date(a.registeredAt);
                });

                this.renderCustomers();
                this.renderPagination();
            }

            renderCustomers() {
                const tbody = document.getElementById('customersTableBody');
                if (!tbody) return;

                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageCustomers = this.filteredCustomers.slice(startIndex, endIndex);

                if (pageCustomers.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px; color: #6b7280;">
                                <i class="fas fa-users fa-2x" style="display: block; margin-bottom: 12px; opacity: 0.3;"></i>
                                暂无客户数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = pageCustomers.map(customer => `
                    <tr>
                        <td>#${customer.id}</td>
                        <td>
                            <div style="font-weight: 500;">${customer.name}</div>
                            <div style="font-size: 12px; color: #6b7280;">${customer.phone}</div>
                        </td>
                        <td>
                            <span class="status-badge ${this.getTypeClass(customer.type)}">
                                ${this.getTypeText(customer.type)}
                            </span>
                        </td>
                        <td>${ds.formatDate(customer.registeredAt)}</td>
                        <td>${customer.orderCount}</td>
                        <td>${ds.formatCurrency(customer.totalSpent)}</td>
                        <td>${ds.formatDate(customer.lastActiveAt)}</td>
                        <td>
                            <span class="status-badge ${customer.status === 'active' ? 'status-success' : 'status-warning'}">
                                ${customer.status === 'active' ? '活跃' : '不活跃'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-secondary" onclick="customerAnalytics.viewCustomer(${customer.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            renderPagination() {
                const totalPages = Math.ceil(this.filteredCustomers.length / this.pageSize);
                const pagination = document.getElementById('pagination');

                if (!pagination || totalPages <= 1) {
                    if (pagination) pagination.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // 上一页按钮
                if (this.currentPage > 1) {
                    paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="customerAnalytics.goToPage(${this.currentPage - 1})">上一页</button>`;
                }

                // 页码按钮
                for (let i = 1; i <= totalPages; i++) {
                    if (i === this.currentPage) {
                        paginationHTML += `<button class="btn btn-sm btn-primary">${i}</button>`;
                    } else {
                        paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="customerAnalytics.goToPage(${i})">${i}</button>`;
                    }
                }

                // 下一页按钮
                if (this.currentPage < totalPages) {
                    paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="customerAnalytics.goToPage(${this.currentPage + 1})">下一页</button>`;
                }

                pagination.innerHTML = paginationHTML;
            }

            goToPage(page) {
                this.currentPage = page;
                this.renderCustomers();
                this.renderPagination();
            }

            bindEvents() {
                // 搜索事件
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        this.currentPage = 1;
                        this.loadCustomers();
                    });
                }

                // 类型筛选事件
                const typeFilter = document.getElementById('typeFilter');
                if (typeFilter) {
                    typeFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadCustomers();
                    });
                }
            }

            getTypeText(type) {
                const typeMap = {
                    'individual': '个人客户',
                    'enterprise': '企业客户',
                    'vip': 'VIP客户'
                };
                return typeMap[type] || type;
            }

            getTypeClass(type) {
                const classMap = {
                    'individual': 'status-info',
                    'enterprise': 'status-success',
                    'vip': 'status-warning'
                };
                return classMap[type] || 'status-info';
            }

            viewCustomer(id) {
                const customer = this.customers.find(c => c.id === parseInt(id));
                if (!customer) {
                    ds.showToast('客户不存在', 'error');
                    return;
                }

                // 创建详情模态框
                const modal = document.createElement('div');
                modal.className = 'customer-detail-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="modal-content" onclick="event.stopPropagation()" style="background: white; border-radius: 12px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                            <div class="modal-header" style="padding: 24px 24px 0 24px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">客户详情</h3>
                                <button onclick="this.closest('.customer-detail-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 4px;">&times;</button>
                            </div>

                            <div class="modal-body" style="padding: 0 24px 24px 24px;">
                                <div style="display: grid; gap: 20px;">
                                    <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                        <span class="status-badge ${this.getTypeClass(customer.type)}">${this.getTypeText(customer.type)}</span>
                                        <span class="status-badge ${customer.status === 'active' ? 'status-success' : 'status-warning'}">
                                            ${customer.status === 'active' ? '活跃' : '不活跃'}
                                        </span>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">客户姓名</h5>
                                            <p style="margin: 0; color: #6b7280;">${customer.name}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">客户ID</h5>
                                            <p style="margin: 0; color: #6b7280;">#${customer.id}</p>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">联系电话</h5>
                                            <p style="margin: 0; color: #6b7280;">${customer.phone}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">邮箱地址</h5>
                                            <p style="margin: 0; color: #6b7280;">${customer.email}</p>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">地址</h5>
                                        <p style="margin: 0; color: #6b7280;">${customer.address}</p>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">订单数量</h5>
                                            <p style="margin: 0; color: #6b7280; font-size: 18px; font-weight: 600;">${customer.orderCount}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">总消费金额</h5>
                                            <p style="margin: 0; color: #6b7280; font-size: 18px; font-weight: 600;">${ds.formatCurrency(customer.totalSpent)}</p>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">注册时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${ds.formatDate(customer.registeredAt)}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">最后活跃</h5>
                                            <p style="margin: 0; color: #6b7280;">${ds.formatDate(customer.lastActiveAt)}</p>
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; display: flex; gap: 8px; justify-content: flex-end;">
                                    <button onclick="this.closest('.customer-detail-modal').remove()" style="padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            }

            exportData() {
                try {
                    ds.showToast('正在导出客户数据...', 'info');

                    // 获取当前筛选的客户数据
                    const exportData = this.filteredCustomers;

                    // 生成CSV内容
                    const headers = ['客户ID', '客户姓名', '客户类型', '联系电话', '邮箱地址', '地址', '注册时间', '最后活跃', '订单数量', '总消费金额', '状态'];
                    const csvContent = [
                        headers.join(','),
                        ...exportData.map(customer => [
                            customer.id,
                            `"${customer.name.replace(/"/g, '""')}"`,
                            this.getTypeText(customer.type),
                            customer.phone,
                            customer.email,
                            `"${customer.address.replace(/"/g, '""')}"`,
                            ds.formatDate(customer.registeredAt),
                            ds.formatDate(customer.lastActiveAt),
                            customer.orderCount,
                            customer.totalSpent,
                            customer.status === 'active' ? '活跃' : '不活跃'
                        ].join(','))
                    ].join('\n');

                    // 创建下载链接
                    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `customer_analytics_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    ds.showToast(`客户数据导出成功 (${exportData.length}条记录)`, 'success');
                } catch (error) {
                    ds.showToast('导出失败', 'error');
                    console.error('导出失败:', error);
                }
            }
        }

        // 初始化客户分析
        const customerAnalytics = new CustomerAnalytics();
        </script>
</body>
</html>

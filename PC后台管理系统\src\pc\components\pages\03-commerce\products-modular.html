<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="your-csrf-token-here">
    <title>商品管理 - 智能家居管理系统</title>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="styles/products.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 右侧菜单区域 */
        .content-with-menu {
            display: flex;
            gap: 24px;
        }

        .right-menu {
            width: 240px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 24px;
        }

        .menu-content {
            flex: 1;
        }

        /* 4层菜单结构样式 */
        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        /* 一级菜单 */
        .menu-level-1 {
            margin-bottom: 16px;
        }

        .menu-level-1 > .menu-item {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            padding: 8px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .menu-level-1 > .menu-item:hover {
            color: #374151;
        }

        .menu-level-1 > .menu-item.active {
            color: #1f2937;
        }

        /* 二级菜单 */
        .menu-level-2 {
            margin-left: 12px;
            margin-top: 8px;
            border-left: 1px solid #e5e7eb;
            padding-left: 12px;
        }

        .menu-level-2 > .menu-item {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            padding: 6px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .menu-level-2 > .menu-item:hover {
            color: #1f2937;
        }

        .menu-level-2 > .menu-item.active {
            color: #1f2937;
            font-weight: 600;
        }

        /* 三级菜单 */
        .menu-level-3 {
            margin-left: 12px;
            margin-top: 6px;
            border-left: 1px solid #f3f4f6;
            padding-left: 12px;
        }

        .menu-level-3 > .menu-item {
            font-size: 12px;
            font-weight: 400;
            color: #6b7280;
            padding: 4px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .menu-level-3 > .menu-item:hover {
            color: #374151;
        }

        .menu-level-3 > .menu-item.active {
            color: #1f2937;
            font-weight: 500;
        }

        /* 四级菜单 */
        .menu-level-4 {
            margin-left: 12px;
            margin-top: 4px;
            padding-left: 12px;
        }

        .menu-level-4 > .menu-item {
            font-size: 11px;
            font-weight: 400;
            color: #9ca3af;
            padding: 3px 0;
            cursor: pointer;
        }

        .menu-level-4 > .menu-item:hover {
            color: #6b7280;
        }

        .menu-level-4 > .menu-item.active {
            color: #374151;
            font-weight: 500;
        }

        /* 展开/折叠图标 */
        .expand-icon {
            font-size: 10px;
            transition: transform 0.2s;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        /* 隐藏的子菜单 */
        .menu-children {
            display: none;
        }

        .menu-children.expanded {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-with-menu {
                flex-direction: column;
            }

            .right-menu {
                width: 100%;
                position: static;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products-modular.html" class="nav-item active">商品管理</a>
                    <a href="orders.html" class="nav-item">订单管理</a>
                    <a href="customer-management.html" class="nav-item">客户管理</a>
                    <a href="marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">商品管理</h1>
                    <p class="breadcrumb-description">管理您的商品信息、库存和价格</p>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <div class="content-with-menu">
                    <!-- 主要内容区域 -->
                    <div class="menu-content">
                        <!-- 页面头部操作 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; padding: 16px; background: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                            <div>
                                <h2 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0;">商品列表</h2>
                                <p style="font-size: 14px; color: #6b7280; margin: 4px 0 0 0;">管理和维护商品信息</p>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="padding: 8px 16px; background: #f8fafc; color: #374151; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; cursor: pointer;" data-action="sync-medusa">
                                    <i class="fas fa-sync-alt"></i> 同步MedusaJS
                                </button>
                                <button style="padding: 8px 16px; background: #f8fafc; color: #374151; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; cursor: pointer;" data-action="import-data">
                                    <i class="fas fa-upload"></i> 批量导入
                                </button>
                                <button style="padding: 8px 16px; background: #1f2937; color: white; border: 1px solid #1f2937; border-radius: 6px; font-size: 14px; cursor: pointer;" data-action="add-product">
                                    <i class="fas fa-plus"></i> 新增商品
                                </button>
                            </div>
                        </div>

                        <!-- 筛选器容器 -->
                        <div id="filtersContainer">
                            <div style="background: white; border-radius: 8px; padding: 16px; margin-bottom: 16px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin: 0;">筛选条件</h3>
                                    <button id="resetFilters" style="padding: 6px 12px; background: #f8fafc; color: #6b7280; border: 1px solid #e5e7eb; border-radius: 4px; font-size: 12px; cursor: pointer;">重置筛选</button>
                                </div>
                                <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                                    <div>
                                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">商品分类</label>
                                        <select id="categoryFilter" style="padding: 6px 12px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 14px;">
                                            <option value="">全部分类</option>
                                            <option value="智能开关">智能开关</option>
                                            <option value="智能插座">智能插座</option>
                                            <option value="智能灯具">智能灯具</option>
                                            <option value="智能传感器">智能传感器</option>
                                            <option value="智能门锁">智能门锁</option>
                                            <option value="智能摄像头">智能摄像头</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">商品状态</label>
                                        <select id="statusFilter" style="padding: 6px 12px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 14px;">
                                            <option value="">全部状态</option>
                                            <option value="published">已上架</option>
                                            <option value="draft">草稿</option>
                                            <option value="archived">已下架</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">搜索商品</label>
                                        <input type="text" id="productSearch" placeholder="输入商品名称或SKU" style="padding: 6px 12px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 14px; width: 200px;">
                                    </div>
                                    <div style="display: flex; align-items: end;">
                                        <button id="searchButton" style="padding: 7px 12px; background: #1f2937; color: white; border: none; border-radius: 4px; font-size: 14px; cursor: pointer;">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 表格容器 -->
                        <div id="tableContainer">
                            <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f9fafb;">
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                                                <input type="checkbox" style="margin-right: 8px;">商品信息
                                            </th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SKU</th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">分类</th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">价格</th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">库存</th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">状态</th>
                                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="7" style="padding: 40px; text-align: center; color: #6b7280; font-size: 14px;">
                                                <i class="fas fa-box" style="font-size: 48px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                                                暂无商品数据<br>
                                                <small style="color: #9ca3af;">请点击"新增商品"按钮添加商品，或检查筛选条件</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧4层菜单 -->
                    <div class="right-menu">
                        <div class="menu-title">商品管理菜单</div>

                        <!-- 一级菜单：商品管理 -->
                        <div class="menu-level-1">
                            <div class="menu-item" onclick="toggleMenu('product-management')">
                                <span>商品管理</span>
                                <i class="fas fa-chevron-right expand-icon" id="icon-product-management"></i>
                            </div>
                            <div class="menu-children" id="menu-product-management">
                                <!-- 二级菜单：商品信息 -->
                                <div class="menu-level-2">
                                    <div class="menu-item" onclick="toggleMenu('product-info')">
                                        <span>商品信息</span>
                                        <i class="fas fa-chevron-right expand-icon" id="icon-product-info"></i>
                                    </div>
                                    <div class="menu-children" id="menu-product-info">
                                        <!-- 三级菜单：基础信息 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="toggleMenu('basic-info')">
                                                <span>基础信息</span>
                                                <i class="fas fa-chevron-right expand-icon" id="icon-basic-info"></i>
                                            </div>
                                            <div class="menu-children" id="menu-basic-info">
                                                <!-- 四级菜单：具体字段 -->
                                                <div class="menu-level-4">
                                                    <div class="menu-item active" onclick="selectMenuItem(this, 'product-name')">商品名称</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'product-sku')">商品SKU</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'product-desc')">商品描述</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'product-category')">商品分类</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 三级菜单：价格信息 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="toggleMenu('price-info')">
                                                <span>价格信息</span>
                                                <i class="fas fa-chevron-right expand-icon" id="icon-price-info"></i>
                                            </div>
                                            <div class="menu-children" id="menu-price-info">
                                                <!-- 四级菜单：价格字段 -->
                                                <div class="menu-level-4">
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'sale-price')">销售价格</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'cost-price')">成本价格</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'market-price')">市场价格</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 二级菜单：库存管理 -->
                                <div class="menu-level-2">
                                    <div class="menu-item" onclick="toggleMenu('inventory-management')">
                                        <span>库存管理</span>
                                        <i class="fas fa-chevron-right expand-icon" id="icon-inventory-management"></i>
                                    </div>
                                    <div class="menu-children" id="menu-inventory-management">
                                        <!-- 三级菜单：库存操作 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="toggleMenu('inventory-operations')">
                                                <span>库存操作</span>
                                                <i class="fas fa-chevron-right expand-icon" id="icon-inventory-operations"></i>
                                            </div>
                                            <div class="menu-children" id="menu-inventory-operations">
                                                <!-- 四级菜单：具体操作 -->
                                                <div class="menu-level-4">
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'stock-in')">入库管理</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'stock-out')">出库管理</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'stock-adjust')">库存调整</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'stock-warning')">库存预警</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 一级菜单：系统集成 -->
                        <div class="menu-level-1">
                            <div class="menu-item" onclick="toggleMenu('system-integration')">
                                <span>系统集成</span>
                                <i class="fas fa-chevron-right expand-icon" id="icon-system-integration"></i>
                            </div>
                            <div class="menu-children" id="menu-system-integration">
                                <!-- 二级菜单：MedusaJS -->
                                <div class="menu-level-2">
                                    <div class="menu-item" onclick="toggleMenu('medusa-integration')">
                                        <span>MedusaJS集成</span>
                                        <i class="fas fa-chevron-right expand-icon" id="icon-medusa-integration"></i>
                                    </div>
                                    <div class="menu-children" id="menu-medusa-integration">
                                        <!-- 三级菜单：同步功能 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="toggleMenu('sync-functions')">
                                                <span>同步功能</span>
                                                <i class="fas fa-chevron-right expand-icon" id="icon-sync-functions"></i>
                                            </div>
                                            <div class="menu-children" id="menu-sync-functions">
                                                <!-- 四级菜单：同步选项 -->
                                                <div class="menu-level-4">
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'sync-products')">商品同步</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'sync-inventory')">库存同步</div>
                                                    <div class="menu-item" onclick="selectMenuItem(this, 'sync-prices')">价格同步</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 一级菜单：数据分析 -->
                        <div class="menu-level-1">
                            <div class="menu-item" onclick="toggleMenu('data-analytics')">
                                <span>数据分析</span>
                                <i class="fas fa-chevron-right expand-icon" id="icon-data-analytics"></i>
                            </div>
                            <div class="menu-children" id="menu-data-analytics">
                                <!-- 二级菜单：销售分析 -->
                                <div class="menu-level-2">
                                    <div class="menu-item" onclick="toggleMenu('sales-analytics')">
                                        <span>销售分析</span>
                                        <i class="fas fa-chevron-right expand-icon" id="icon-sales-analytics"></i>
                                    </div>
                                    <div class="menu-children" id="menu-sales-analytics">
                                        <!-- 三级菜单：分析维度 -->
                                        <div class="menu-level-3">
                                            <div class="menu-item" onclick="selectMenuItem(this, 'sales-trend')">销售趋势</div>
                                            <div class="menu-item" onclick="selectMenuItem(this, 'product-ranking')">商品排行</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 核心功能模块 -->
    <script src="../../assets/js/api/products-api.js"></script>
    <script src="../../assets/js/products/product-manager.js"></script>
    <script src="../../assets/js/products/product-import.js"></script>

    <!-- 样式增强 -->
    <style>
        /* 状态徽章样式 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 按钮样式增强 */
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #1f2937;
            color: white;
        }

        .btn-primary:hover {
            background: #374151;
        }

        .btn-secondary {
            background: #f8fafc;
            color: #6b7280;
            border: 1px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
            border-color: #9ca3af;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        /* 加载状态 */
        .loading {
            opacity: 0.5;
            pointer-events: none;
        }

        /* 表格增强 */
        .product-table {
            width: 100%;
            border-collapse: collapse;
        }

        .product-table th,
        .product-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .product-table th {
            background: #f9fafb;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
        }

        .product-table tbody tr:hover {
            background: #f9fafb;
        }

        /* 响应式表格 */
        @media (max-width: 768px) {
            .product-table {
                font-size: 14px;
            }

            .product-table th,
            .product-table td {
                padding: 8px;
            }
        }
    </style>

    <!-- 主应用脚本 -->
    <script>
        // 全局变量
        let productManager;
        let productImporter;

        // 菜单展开/折叠功能
        function toggleMenu(menuId) {
            const menu = document.getElementById('menu-' + menuId);
            const icon = document.getElementById('icon-' + menuId);

            if (menu && icon) {
                const isExpanded = menu.classList.contains('expanded');

                if (isExpanded) {
                    menu.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    menu.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            }
        }

        // 菜单项选择功能
        function selectMenuItem(element, itemId) {
            // 移除所有菜单项的active状态
            const allMenuItems = document.querySelectorAll('.menu-level-4 .menu-item');
            allMenuItems.forEach(item => item.classList.remove('active'));

            // 添加当前菜单项的active状态
            element.classList.add('active');

            // 根据选中的菜单项执行相应的操作
            console.log('选中菜单项:', itemId);

            // 可以根据选中的菜单项显示不同的内容或执行不同的功能
            switch(itemId) {
                case 'product-name':
                    // 聚焦到商品名称相关功能
                    break;
                case 'product-sku':
                    // 聚焦到SKU相关功能
                    break;
                case 'stock-in':
                    // 显示入库管理功能
                    break;
                case 'sync-products':
                    // 执行商品同步
                    if (productManager) {
                        productManager.syncWithMedusa();
                    }
                    break;
                // 添加更多菜单项的处理逻辑
            }
        }

        // 提示函数增强
        function showToast(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);

            // 创建更好的提示组件
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.style.background = '#10b981';
                    break;
                case 'error':
                    toast.style.background = '#ef4444';
                    break;
                case 'warning':
                    toast.style.background = '#f59e0b';
                    break;
                default:
                    toast.style.background = '#6b7280';
            }

            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 初始化应用
        function initializeApp() {
            try {
                // 初始化商品管理器
                productManager = new ProductManager();

                // 初始化批量导入器
                productImporter = new ProductImporter(productManager);

                // 将导入器绑定到管理器
                productManager.showImportModal = () => {
                    productImporter.showImportModal();
                };

                // 设置全局引用，方便调试和其他脚本使用
                window.productManager = productManager;
                window.productImporter = productImporter;

                console.log('商品管理系统初始化完成');
                showToast('系统初始化完成', 'success');

            } catch (error) {
                console.error('应用初始化失败:', error);
                showToast('系统初始化失败: ' + error.message, 'error');
            }
        }

        // 菜单项选择功能
        function selectMenuItem(element, itemId) {
            // 移除所有菜单项的active状态
            const allMenuItems = document.querySelectorAll('.menu-level-4 .menu-item');
            allMenuItems.forEach(item => item.classList.remove('active'));

            // 添加当前菜单项的active状态
            element.classList.add('active');

            // 这里可以添加具体的功能逻辑
            console.log('选中菜单项:', itemId);

            // 可以根据选中的菜单项执行相应的操作
            switch(itemId) {
                case 'product-name':
                    // 聚焦到商品名称相关功能
                    break;
                case 'product-sku':
                    // 聚焦到SKU相关功能
                    break;
                case 'stock-in':
                    // 显示入库管理功能
                    break;
                case 'sync-products':
                    // 执行商品同步
                    break;
                // 添加更多菜单项的处理逻辑
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 初始化应用
                initializeApp();

                // 默认展开第一级菜单
                toggleMenu('product-management');
                toggleMenu('product-info');
                toggleMenu('basic-info');

                console.log('页面初始化完成');

            } catch (error) {
                console.error('页面初始化错误:', error);
                showToast('页面初始化失败: ' + error.message, 'error');
            }
        });

        // 响应式菜单处理
        function handleResponsiveMenu() {
            const sidebar = document.getElementById('sidebar');
            const rightMenu = document.querySelector('.right-menu');

            if (window.innerWidth <= 768) {
                // 移动端隐藏侧边栏
                sidebar.classList.remove('open');
            }

            if (window.innerWidth <= 1200) {
                // 平板端调整右侧菜单
                if (rightMenu) {
                    rightMenu.style.position = 'static';
                }
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleResponsiveMenu);

        // 移动端菜单切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 添加移动端菜单按钮（如果需要）
        if (window.innerWidth <= 768) {
            const topNav = document.querySelector('.top-nav');
            if (topNav) {
                const menuButton = document.createElement('button');
                menuButton.innerHTML = '<i class="fas fa-bars"></i>';
                menuButton.style.cssText = 'position: absolute; left: 16px; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 18px; color: #1f2937; cursor: pointer;';
                menuButton.onclick = toggleSidebar;
                topNav.style.position = 'relative';
                topNav.appendChild(menuButton);
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            // 防止错误信息显示在页面上
            e.preventDefault();
            return true;
        });

        // 防止未处理的Promise rejection
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise错误:', e.reason);
            e.preventDefault();
        });

        // 页面加载完成提示
        console.log('商品管理页面脚本加载完成');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计师工作台访问说明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 24px;
        }
        
        .section {
            margin-bottom: 24px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 16px;
            font-size: 18px;
        }
        
        .step {
            margin-bottom: 16px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .step h3 {
            color: #667eea;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .step p {
            color: #666;
            line-height: 1.6;
        }
        
        .code {
            background: #f1f3f4;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
            border: 1px solid #e0e0e0;
        }
        
        .button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            margin: 8px 8px 8px 0;
        }
        
        .button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
        }
        
        .file-list {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .file-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-name {
            font-weight: 500;
            color: #333;
        }
        
        .file-desc {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 设计师工作台访问说明</h1>
            <p>解决本地文件访问问题</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 问题说明</strong><br>
                由于浏览器安全策略限制，无法直接访问本地HTML文件。需要启动本地服务器来访问页面。
            </div>
            
            <div class="section">
                <h2>🚀 快速启动方法</h2>
                
                <div class="step">
                    <h3>方法1: 使用批处理文件 (推荐)</h3>
                    <p>双击运行项目根目录下的 <code>start-server.bat</code> 文件，会自动检测并启动服务器。</p>
                    <div class="code">start-server.bat</div>
                </div>
                
                <div class="step">
                    <h3>方法2: 手动启动Node.js服务器</h3>
                    <p>在项目根目录打开命令行，运行以下命令：</p>
                    <div class="code">node server.js</div>
                </div>
                
                <div class="step">
                    <h3>方法3: 手动启动Python服务器</h3>
                    <p>在项目根目录打开命令行，运行以下命令：</p>
                    <div class="code">python server.py</div>
                </div>
            </div>
            
            <div class="section">
                <h2>🌐 访问地址</h2>
                <p>服务器启动成功后，可以通过以下地址访问：</p>
                
                <div class="file-list">
                    <div class="file-item">
                        <div>
                            <div class="file-name">设计师工作台</div>
                            <div class="file-desc">完整的设计师工作台功能</div>
                        </div>
                        <a href="http://localhost:3000/src/pc/components/pages/design-management.html" class="button" target="_blank">访问</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">管理系统首页</div>
                            <div class="file-desc">系统主页面</div>
                        </div>
                        <a href="http://localhost:3000/src/pc/components/pages/index.html" class="button" target="_blank">访问</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">联调测试页面</div>
                            <div class="file-desc">功能联调测试工具</div>
                        </div>
                        <a href="http://localhost:3000/test/design-service-integration-test.html" class="button" target="_blank">访问</a>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📋 功能说明</h2>
                <p>设计师工作台包含以下6个核心功能模块：</p>
                
                <div class="file-list">
                    <div class="file-item">
                        <div>
                            <div class="file-name">📋 需求管理</div>
                            <div class="file-desc">用户需求收集、分析和处理</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📊 设计任务</div>
                            <div class="file-desc">看板式任务管理和进度跟踪</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🔄 方案版本</div>
                            <div class="file-desc">版本控制和对比管理</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">💬 客户沟通</div>
                            <div class="file-desc">实时客户沟通和消息管理</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">⭐ 反馈管理</div>
                            <div class="file-desc">客户反馈收集和处理</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🚚 交付管理</div>
                            <div class="file-desc">方案交付和满意度调查</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="success">
                <strong>✅ 提示</strong><br>
                所有功能都已完整实现，包括模拟数据、交互逻辑和UI界面。启动服务器后即可正常使用所有功能。
            </div>
        </div>
    </div>
    
    <script>
        // 检查服务器是否运行
        function checkServer() {
            fetch('http://localhost:3000/src/pc/components/pages/design-management.html')
                .then(response => {
                    if (response.ok) {
                        document.querySelector('.warning').style.display = 'none';
                        const successDiv = document.createElement('div');
                        successDiv.className = 'success';
                        successDiv.innerHTML = '<strong>✅ 服务器运行正常</strong><br>可以正常访问设计师工作台页面。';
                        document.querySelector('.content').insertBefore(successDiv, document.querySelector('.section'));
                    }
                })
                .catch(error => {
                    console.log('服务器未启动或端口不是3000');
                });
        }
        
        // 页面加载后检查服务器状态
        window.addEventListener('load', checkServer);
    </script>
</body>
</html>

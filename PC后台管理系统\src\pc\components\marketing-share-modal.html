<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销分享 - 智能家居设计平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-light: #e5e7eb;
            --border-dark: #d1d5db;
            --wechat-color: #07c160;
            --qq-color: #12b7f5;
            --weibo-color: #e6162d;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .share-modal {
            background: var(--bg-primary);
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 480px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 24px 24px 0;
            text-align: center;
            border-bottom: 1px solid var(--border-light);
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .modal-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 24px;
        }

        .share-content-preview {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 16px;
            margin: 0 24px 24px;
        }

        .preview-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 12px;
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .preview-description {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .share-channels {
            padding: 0 24px;
        }

        .channels-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        .channels-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }

        .channel-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            border: 1px solid var(--border-light);
            border-radius: 12px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: var(--text-primary);
        }

        .channel-button:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
        }

        .channel-button.wechat:hover {
            border-color: var(--wechat-color);
            box-shadow: 0 4px 12px rgba(7, 193, 96, 0.15);
        }

        .channel-button.qq:hover {
            border-color: var(--qq-color);
            box-shadow: 0 4px 12px rgba(18, 183, 245, 0.15);
        }

        .channel-button.weibo:hover {
            border-color: var(--weibo-color);
            box-shadow: 0 4px 12px rgba(230, 22, 45, 0.15);
        }

        .channel-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .channel-icon.wechat {
            color: var(--wechat-color);
        }

        .channel-icon.qq {
            color: var(--qq-color);
        }

        .channel-icon.weibo {
            color: var(--weibo-color);
        }

        .channel-icon.universal {
            color: var(--text-secondary);
        }

        .channel-name {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            line-height: 1.3;
        }

        .rewards-section {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            margin: 0 24px 24px;
        }

        .rewards-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 8px;
        }

        .rewards-list {
            font-size: 12px;
            color: #92400e;
            line-height: 1.5;
        }

        .rewards-list li {
            margin-bottom: 4px;
        }

        .share-stats {
            display: flex;
            justify-content: space-around;
            padding: 16px 24px;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-light);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-light);
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-light);
        }

        .btn-secondary:hover {
            background: var(--border-light);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .share-success {
            display: none;
            text-align: center;
            padding: 40px 24px;
            color: var(--success-color);
        }

        .success-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .success-message {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .success-reward {
            font-size: 14px;
            color: var(--text-secondary);
        }

        @media (max-width: 480px) {
            .channels-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .share-modal {
                margin: 10px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="share-modal">
        <div class="modal-header">
            <h2 class="modal-title">
                <i class="fas fa-share-alt"></i>
                分享赚奖励
            </h2>
            <p class="modal-subtitle">分享给好友，一起享受智能家居生活</p>
        </div>

        <div class="share-content-preview">
            <div class="preview-image">
                <i class="fas fa-home"></i>
            </div>
            <div class="preview-title">智能家居设计神器，让你的家更智能！</div>
            <div class="preview-description">专业的智能家居设计平台，一站式解决方案，从设计到施工全程跟踪。</div>
        </div>

        <div class="share-channels">
            <h3 class="channels-title">选择分享方式</h3>
            <div class="channels-grid">
                <button class="channel-button wechat" onclick="shareToChannel('WECHAT_MOMENTS')">
                    <i class="fab fa-weixin channel-icon wechat"></i>
                    <span class="channel-name">微信朋友圈</span>
                </button>
                <button class="channel-button wechat" onclick="shareToChannel('WECHAT_GROUP')">
                    <i class="fab fa-weixin channel-icon wechat"></i>
                    <span class="channel-name">微信群聊</span>
                </button>
                <button class="channel-button wechat" onclick="shareToChannel('WECHAT_FRIEND')">
                    <i class="fab fa-weixin channel-icon wechat"></i>
                    <span class="channel-name">微信好友</span>
                </button>
                <button class="channel-button qq" onclick="shareToChannel('QQ_ZONE')">
                    <i class="fab fa-qq channel-icon qq"></i>
                    <span class="channel-name">QQ空间</span>
                </button>
                <button class="channel-button weibo" onclick="shareToChannel('WEIBO')">
                    <i class="fab fa-weibo channel-icon weibo"></i>
                    <span class="channel-name">新浪微博</span>
                </button>
                <button class="channel-button" onclick="shareToChannel('COPY_LINK')">
                    <i class="fas fa-link channel-icon universal"></i>
                    <span class="channel-name">复制链接</span>
                </button>
            </div>
        </div>

        <div class="rewards-section">
            <div class="rewards-title">
                <i class="fas fa-gift"></i>
                分享奖励
            </div>
            <ul class="rewards-list">
                <li>• 首次分享获得 <strong>100积分</strong></li>
                <li>• 每日分享获得 <strong>20积分</strong></li>
                <li>• 成功邀请好友注册获得 <strong>设计服务10%折扣券</strong></li>
                <li>• 好友注册可获得 <strong>200积分 + 50元抵扣券</strong></li>
            </ul>
        </div>

        <div class="share-stats">
            <div class="stat-item">
                <span class="stat-number" id="totalShares">0</span>
                <div class="stat-label">总分享次数</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="todayShares">0</span>
                <div class="stat-label">今日分享</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="totalRewards">0</span>
                <div class="stat-label">累计奖励</div>
            </div>
        </div>

        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal()">
                <i class="fas fa-times"></i>
                关闭
            </button>
            <button class="btn btn-primary" onclick="viewRewards()">
                <i class="fas fa-gift"></i>
                我的奖励
            </button>
        </div>

        <div class="share-success" id="shareSuccess">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="success-message">分享成功！</div>
            <div class="success-reward">您已获得20积分奖励</div>
        </div>
    </div>

    <script src="../marketing-sharing-system.js"></script>
    <script>
        // 初始化营销分享系统
        const marketingShare = new MarketingSharingSystem()
        const currentUserId = 'user_' + Date.now() // 模拟当前用户ID

        // 页面加载时更新统计数据
        document.addEventListener('DOMContentLoaded', function() {
            updateShareStats()
        })

        // 分享到指定渠道
        async function shareToChannel(channelType) {
            try {
                // 生成分享内容
                const shareContent = marketingShare.generateShareContent(currentUserId, 'APP_PROMOTION')
                
                // 执行分享
                const result = await marketingShare.shareToChannel(shareContent, channelType)
                
                if (result.success) {
                    showShareSuccess()
                    updateShareStats()
                }
            } catch (error) {
                console.error('分享失败:', error)
                alert('分享失败，请重试')
            }
        }

        // 显示分享成功
        function showShareSuccess() {
            const modal = document.querySelector('.share-modal')
            const success = document.getElementById('shareSuccess')
            
            // 隐藏原内容，显示成功页面
            modal.children[0].style.display = 'none' // header
            modal.children[1].style.display = 'none' // preview
            modal.children[2].style.display = 'none' // channels
            modal.children[3].style.display = 'none' // rewards
            modal.children[4].style.display = 'none' // stats
            modal.children[5].style.display = 'none' // footer
            
            success.style.display = 'block'
            
            // 3秒后恢复
            setTimeout(() => {
                success.style.display = 'none'
                modal.children[0].style.display = 'block'
                modal.children[1].style.display = 'block'
                modal.children[2].style.display = 'block'
                modal.children[3].style.display = 'block'
                modal.children[4].style.display = 'flex'
                modal.children[5].style.display = 'flex'
            }, 3000)
        }

        // 更新分享统计
        function updateShareStats() {
            const stats = marketingShare.getUserShareStats(currentUserId)
            
            document.getElementById('totalShares').textContent = stats.totalShares || 0
            
            // 计算今日分享次数
            const today = new Date().toDateString()
            const todayShares = stats.lastShareDate?.toDateString() === today ? 1 : 0
            document.getElementById('todayShares').textContent = todayShares
            
            // 计算累计奖励
            const rewards = marketingShare.getUserRewards(currentUserId)
            const totalRewards = rewards.reduce((sum, reward) => {
                return sum + (reward.type === 'points' ? reward.value : 0)
            }, 0)
            document.getElementById('totalRewards').textContent = totalRewards
        }

        // 查看奖励
        function viewRewards() {
            const rewards = marketingShare.getUserRewards(currentUserId)
            let message = '您的奖励记录：\n\n'
            
            if (rewards.length === 0) {
                message += '暂无奖励记录，快去分享赚取奖励吧！'
            } else {
                rewards.forEach(reward => {
                    message += `• ${reward.name}: ${reward.value}${reward.type === 'points' ? '积分' : ''}\n`
                })
            }
            
            alert(message)
        }

        // 关闭模态框
        function closeModal() {
            window.close()
        }

        // 模拟微信环境
        if (typeof wx === 'undefined') {
            window.wx = {
                updateTimelineShareData: (config) => {
                    console.log('模拟微信朋友圈分享:', config)
                    config.success && config.success()
                },
                updateAppMessageShareData: (config) => {
                    console.log('模拟微信好友分享:', config)
                    config.success && config.success()
                }
            }
        }
    </script>
</body>
</html>

/**
 * 智能家居管理系统 - JavaScript组件库
 * 提供统一的交互组件和状态管理
 */

class DesignSystem {
    constructor() {
        this.components = new Map();
        this.init();
    }

    init() {
        this.initToast();
        this.initModal();
        this.initDropdown();
        this.initTabs();
        this.initForm();
        this.initTable();
        this.initSidebar();
    }

    // ========== Toast 通知组件 ==========
    initToast() {
        this.createToastContainer();
    }

    createToastContainer() {
        if (!document.getElementById('toast-container')) {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1080;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-4) var(--space-6);
            margin-bottom: var(--space-2);
            box-shadow: var(--shadow-lg);
            pointer-events: auto;
            transform: translateX(100%);
            transition: transform var(--duration-normal) var(--ease-out);
            max-width: 400px;
            word-wrap: break-word;
        `;

        const colors = {
            success: 'var(--color-success)',
            error: 'var(--color-error)',
            warning: 'var(--color-warning)',
            info: 'var(--color-info)'
        };

        toast.style.borderLeftColor = colors[type] || colors.info;
        toast.style.borderLeftWidth = '4px';

        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: var(--space-3);">
                <div style="color: ${colors[type] || colors.info};">
                    ${this.getToastIcon(type)}
                </div>
                <div style="flex: 1; font-size: var(--font-size-sm); color: var(--text-primary);">
                    ${message}
                </div>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; color: var(--text-secondary); cursor: pointer; padding: 0;">
                    ×
                </button>
            </div>
        `;

        document.getElementById('toast-container').appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    getToastIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    // ========== Modal 模态框组件 ==========
    initModal() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-trigger]')) {
                const modalId = e.target.getAttribute('data-modal-trigger');
                this.openModal(modalId);
            }
            if (e.target.matches('[data-modal-close]') || e.target.matches('.modal-backdrop')) {
                this.closeModal();
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        setTimeout(() => {
            modal.classList.add('modal-open');
        }, 10);
    }

    closeModal() {
        const modal = document.querySelector('.modal.modal-open');
        if (!modal) return;

        modal.classList.remove('modal-open');
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }

    // ========== Dropdown 下拉菜单组件 ==========
    initDropdown() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-dropdown-trigger]')) {
                e.stopPropagation();
                const dropdownId = e.target.getAttribute('data-dropdown-trigger');
                this.toggleDropdown(dropdownId);
            } else {
                this.closeAllDropdowns();
            }
        });
    }

    toggleDropdown(dropdownId) {
        this.closeAllDropdowns();
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.classList.toggle('dropdown-open');
        }
    }

    closeAllDropdowns() {
        document.querySelectorAll('.dropdown-open').forEach(dropdown => {
            dropdown.classList.remove('dropdown-open');
        });
    }

    // ========== Tabs 标签页组件 ==========
    initTabs() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-tab-trigger]')) {
                const tabId = e.target.getAttribute('data-tab-trigger');
                const tabGroup = e.target.closest('[data-tab-group]').getAttribute('data-tab-group');
                this.switchTab(tabGroup, tabId);
            }
        });
    }

    switchTab(tabGroup, activeTabId) {
        // 更新标签按钮状态
        document.querySelectorAll(`[data-tab-group="${tabGroup}"] [data-tab-trigger]`).forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab-trigger="${activeTabId}"]`).classList.add('active');

        // 更新标签内容显示
        document.querySelectorAll(`[data-tab-content][data-tab-group="${tabGroup}"]`).forEach(content => {
            content.style.display = 'none';
        });
        const activeContent = document.querySelector(`[data-tab-content="${activeTabId}"]`);
        if (activeContent) {
            activeContent.style.display = 'block';
        }
    }

    // ========== Form 表单组件 ==========
    initForm() {
        document.addEventListener('submit', (e) => {
            if (e.target.matches('[data-form-validate]')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        document.addEventListener('input', (e) => {
            if (e.target.matches('.form-control')) {
                this.validateField(e.target);
            }
        });
    }

    validateForm(form) {
        let isValid = true;
        const fields = form.querySelectorAll('.form-control[required]');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        const type = field.getAttribute('type');
        
        let isValid = true;
        let errorMessage = '';

        // 必填验证
        if (isRequired && !value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        }

        // 类型验证
        if (value && type === 'email' && !this.isValidEmail(value)) {
            isValid = false;
            errorMessage = '请输入有效的邮箱地址';
        }

        if (value && type === 'tel' && !this.isValidPhone(value)) {
            isValid = false;
            errorMessage = '请输入有效的手机号码';
        }

        // 更新UI状态
        this.updateFieldState(field, isValid, errorMessage);
        
        return isValid;
    }

    updateFieldState(field, isValid, errorMessage) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup.querySelector('.form-error');

        if (isValid) {
            field.classList.remove('error');
            if (errorElement) {
                errorElement.remove();
            }
        } else {
            field.classList.add('error');
            if (!errorElement) {
                const error = document.createElement('div');
                error.className = 'form-error';
                error.textContent = errorMessage;
                formGroup.appendChild(error);
            } else {
                errorElement.textContent = errorMessage;
            }
        }
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    isValidPhone(phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    }

    // ========== Table 表格组件 ==========
    initTable() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-sort]')) {
                const column = e.target.getAttribute('data-sort');
                const table = e.target.closest('table');
                this.sortTable(table, column);
            }
        });
    }

    sortTable(table, column) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => 
            th.getAttribute('data-sort') === column
        );

        if (columnIndex === -1) return;

        const isAscending = !table.classList.contains(`sort-${column}-desc`);
        
        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aValue.localeCompare(bValue, 'zh-CN', { numeric: true });
            } else {
                return bValue.localeCompare(aValue, 'zh-CN', { numeric: true });
            }
        });

        // 更新表格类名
        table.className = table.className.replace(/sort-\w+-\w+/g, '');
        table.classList.add(`sort-${column}-${isAscending ? 'asc' : 'desc'}`);

        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    }

    // ========== Sidebar 侧边栏组件 ==========
    initSidebar() {
        const toggleBtn = document.querySelector('[data-sidebar-toggle]');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // 处理菜单折叠
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-menu-toggle]')) {
                const menuId = e.target.getAttribute('data-menu-toggle');
                this.toggleMenu(menuId);
            }
        });
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('sidebar-collapsed');
    }

    toggleMenu(menuId) {
        const menu = document.getElementById(menuId);
        if (menu) {
            menu.classList.toggle('menu-open');
        }
    }

    // ========== 工具方法 ==========
    
    // 显示加载状态
    showLoading(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.style.position = 'relative';
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = '<div class="loading"></div>';
            element.appendChild(overlay);
        }
    }

    // 隐藏加载状态
    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            const overlay = element.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    }

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 初始化设计系统
const designSystem = new DesignSystem();

// ========== API管理器 ==========
class APIManager {
    constructor() {
        this.baseURL = 'http://localhost:8001/api';
        this.token = localStorage.getItem('auth_token');
        this.refreshToken = localStorage.getItem('refresh_token');
    }

    // 设置认证令牌
    setToken(token, refreshToken = null) {
        this.token = token;
        localStorage.setItem('auth_token', token);

        if (refreshToken) {
            this.refreshToken = refreshToken;
            localStorage.setItem('refresh_token', refreshToken);
        }
    }

    // 清除认证令牌
    clearToken() {
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 添加认证头
        if (this.token) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, config);

            // 处理401未授权错误
            if (response.status === 401 && this.refreshToken) {
                const newToken = await this.refreshAccessToken();
                if (newToken) {
                    config.headers.Authorization = `Bearer ${newToken}`;
                    return fetch(url, config);
                } else {
                    this.clearToken();
                    window.location.href = '/login.html';
                    return;
                }
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // 刷新访问令牌
    async refreshAccessToken() {
        try {
            const response = await fetch(`${this.baseURL}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: this.refreshToken
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.setToken(data.access_token);
                return data.access_token;
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
        }
        return null;
    }

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.request(url.pathname + url.search);
    }

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // 文件上传
    async upload(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);

        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.request(endpoint, {
            method: 'POST',
            body: formData,
            headers: {} // 让浏览器自动设置Content-Type
        });
    }
}

// 创建API管理器实例
const apiManager = new APIManager();

// 导出到全局
window.DesignSystem = DesignSystem;
window.ds = designSystem;
window.APIManager = APIManager;
window.api = apiManager;

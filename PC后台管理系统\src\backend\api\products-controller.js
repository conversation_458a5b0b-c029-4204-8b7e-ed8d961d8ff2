/**
 * 商品管理API控制器
 * 提供商品CRUD操作、批量导入、MedusaJS集成等功能
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const XLSX = require('xlsx');
const csv = require('csv-parser');
const { body, validationResult, query } = require('express-validator');

class ProductsController {
    constructor(db, medusaClient) {
        this.db = db;
        this.medusaClient = medusaClient;
        this.router = express.Router();
        this.setupRoutes();
        this.setupMulter();
    }

    /**
     * 设置文件上传
     */
    setupMulter() {
        const storage = multer.diskStorage({
            destination: (req, file, cb) => {
                const uploadPath = path.join(__dirname, '../../../uploads/products');
                cb(null, uploadPath);
            },
            filename: (req, file, cb) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
            }
        });

        this.upload = multer({
            storage: storage,
            limits: {
                fileSize: 10 * 1024 * 1024 // 10MB
            },
            fileFilter: (req, file, cb) => {
                if (file.fieldname === 'image') {
                    // 图片上传
                    const allowedTypes = /jpeg|jpg|png|gif|webp/;
                    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
                    const mimetype = allowedTypes.test(file.mimetype);
                    
                    if (mimetype && extname) {
                        return cb(null, true);
                    } else {
                        cb(new Error('只允许上传图片文件'));
                    }
                } else if (file.fieldname === 'importFile') {
                    // 导入文件
                    const allowedTypes = /xlsx|xls|csv/;
                    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
                    
                    if (extname) {
                        return cb(null, true);
                    } else {
                        cb(new Error('只允许上传Excel或CSV文件'));
                    }
                }
            }
        });
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 商品CRUD
        this.router.get('/', this.getProducts.bind(this));
        this.router.get('/stats', this.getProductStats.bind(this));
        this.router.get('/categories', this.getCategories.bind(this));
        this.router.get('/search', this.searchProducts.bind(this));
        this.router.get('/export', this.exportProducts.bind(this));
        this.router.get('/:id', this.getProduct.bind(this));
        this.router.post('/', this.validateProduct(), this.createProduct.bind(this));
        this.router.put('/:id', this.validateProduct(), this.updateProduct.bind(this));
        this.router.delete('/:id', this.deleteProduct.bind(this));
        this.router.post('/batch-delete', this.batchDeleteProducts.bind(this));

        // 文件上传
        this.router.post('/upload-image', this.upload.single('image'), this.uploadImage.bind(this));

        // 批量导入
        this.router.post('/import', this.importProducts.bind(this));
        this.router.post('/import-file', this.upload.single('importFile'), this.importFromFile.bind(this));

        // 库存管理
        this.router.get('/:id/inventory', this.getInventory.bind(this));
        this.router.put('/:id/inventory', this.updateInventory.bind(this));

        // MedusaJS集成
        this.router.post('/sync-medusa', this.syncWithMedusa.bind(this));
        this.router.post('/push-to-medusa', this.pushToMedusa.bind(this));
    }

    /**
     * 商品验证规则
     */
    validateProduct() {
        return [
            body('title').notEmpty().withMessage('商品名称不能为空').isLength({ max: 255 }).withMessage('商品名称不能超过255个字符'),
            body('sku').notEmpty().withMessage('SKU不能为空').isLength({ max: 100 }).withMessage('SKU不能超过100个字符'),
            body('price').isInt({ min: 1 }).withMessage('价格必须是大于0的整数'),
            body('inventory_quantity').isInt({ min: 0 }).withMessage('库存数量必须是非负整数'),
            body('status').isIn(['draft', 'published', 'archived']).withMessage('商品状态无效'),
            body('category').optional().isLength({ max: 100 }).withMessage('分类名称不能超过100个字符'),
            body('description').optional().isLength({ max: 5000 }).withMessage('描述不能超过5000个字符')
        ];
    }

    /**
     * 获取商品列表
     */
    async getProducts(req, res) {
        try {
            const userId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const offset = (page - 1) * limit;
            const sortBy = req.query.sort_by || 'created_at';
            const sortOrder = req.query.sort_order || 'desc';

            // 构建查询条件
            let whereClause = 'WHERE user_id = ? AND deleted_at IS NULL';
            const params = [userId];

            // 添加筛选条件
            if (req.query.category) {
                whereClause += ' AND category = ?';
                params.push(req.query.category);
            }

            if (req.query.status) {
                whereClause += ' AND status = ?';
                params.push(req.query.status);
            }

            if (req.query.search) {
                whereClause += ' AND (title LIKE ? OR sku LIKE ? OR description LIKE ?)';
                const searchTerm = `%${req.query.search}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }

            // 获取总数
            const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;
            const [countResult] = await this.db.execute(countQuery, params);
            const total = countResult[0].total;

            // 获取商品列表
            const query = `
                SELECT id, title, sku, description, category, status, price, cost_price, 
                       inventory_quantity, thumbnail, created_at, updated_at
                FROM products 
                ${whereClause}
                ORDER BY ${sortBy} ${sortOrder}
                LIMIT ? OFFSET ?
            `;
            params.push(limit, offset);

            const [products] = await this.db.execute(query, params);

            res.json({
                success: true,
                data: {
                    products,
                    pagination: {
                        page,
                        limit,
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });

        } catch (error) {
            console.error('获取商品列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取商品列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取单个商品
     */
    async getProduct(req, res) {
        try {
            const userId = req.user.id;
            const productId = req.params.id;

            const query = `
                SELECT * FROM products 
                WHERE id = ? AND user_id = ? AND deleted_at IS NULL
            `;

            const [products] = await this.db.execute(query, [productId, userId]);

            if (products.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: '商品不存在'
                });
            }

            res.json({
                success: true,
                data: products[0]
            });

        } catch (error) {
            console.error('获取商品详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取商品详情失败',
                error: error.message
            });
        }
    }

    /**
     * 创建商品
     */
    async createProduct(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: '输入验证失败',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const productData = req.body;

            // 检查SKU唯一性
            const [existingProducts] = await this.db.execute(
                'SELECT id FROM products WHERE user_id = ? AND sku = ? AND deleted_at IS NULL',
                [userId, productData.sku]
            );

            if (existingProducts.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'SKU已存在'
                });
            }

            // 插入商品
            const query = `
                INSERT INTO products (
                    user_id, title, sku, description, category, status,
                    price, cost_price, inventory_quantity, thumbnail
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const params = [
                userId,
                productData.title,
                productData.sku,
                productData.description || null,
                productData.category || null,
                productData.status || 'draft',
                productData.price,
                productData.cost_price || null,
                productData.inventory_quantity || 0,
                productData.thumbnail || null
            ];

            const [result] = await this.db.execute(query, params);

            // 记录库存变化
            if (productData.inventory_quantity > 0) {
                await this.recordInventoryChange(
                    result.insertId,
                    null,
                    'in',
                    productData.inventory_quantity,
                    0,
                    productData.inventory_quantity,
                    '初始库存',
                    'initial',
                    null,
                    userId
                );
            }

            res.status(201).json({
                success: true,
                message: '商品创建成功',
                data: {
                    id: result.insertId,
                    ...productData
                }
            });

        } catch (error) {
            console.error('创建商品失败:', error);
            res.status(500).json({
                success: false,
                message: '创建商品失败',
                error: error.message
            });
        }
    }

    /**
     * 更新商品
     */
    async updateProduct(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: '输入验证失败',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const productId = req.params.id;
            const productData = req.body;

            // 检查商品是否存在
            const [existingProducts] = await this.db.execute(
                'SELECT * FROM products WHERE id = ? AND user_id = ? AND deleted_at IS NULL',
                [productId, userId]
            );

            if (existingProducts.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: '商品不存在'
                });
            }

            const existingProduct = existingProducts[0];

            // 检查SKU唯一性（排除当前商品）
            const [duplicateProducts] = await this.db.execute(
                'SELECT id FROM products WHERE user_id = ? AND sku = ? AND id != ? AND deleted_at IS NULL',
                [userId, productData.sku, productId]
            );

            if (duplicateProducts.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'SKU已存在'
                });
            }

            // 更新商品
            const query = `
                UPDATE products SET
                    title = ?, sku = ?, description = ?, category = ?, status = ?,
                    price = ?, cost_price = ?, inventory_quantity = ?, thumbnail = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND user_id = ?
            `;

            const params = [
                productData.title,
                productData.sku,
                productData.description || null,
                productData.category || null,
                productData.status || 'draft',
                productData.price,
                productData.cost_price || null,
                productData.inventory_quantity || 0,
                productData.thumbnail || null,
                productId,
                userId
            ];

            await this.db.execute(query, params);

            // 记录库存变化
            const oldQuantity = existingProduct.inventory_quantity;
            const newQuantity = productData.inventory_quantity || 0;
            
            if (oldQuantity !== newQuantity) {
                const quantityChange = newQuantity - oldQuantity;
                const changeType = quantityChange > 0 ? 'in' : 'out';
                
                await this.recordInventoryChange(
                    productId,
                    null,
                    changeType,
                    Math.abs(quantityChange),
                    oldQuantity,
                    newQuantity,
                    '手动调整',
                    'adjustment',
                    null,
                    userId
                );
            }

            res.json({
                success: true,
                message: '商品更新成功',
                data: {
                    id: productId,
                    ...productData
                }
            });

        } catch (error) {
            console.error('更新商品失败:', error);
            res.status(500).json({
                success: false,
                message: '更新商品失败',
                error: error.message
            });
        }
    }

    /**
     * 删除商品
     */
    async deleteProduct(req, res) {
        try {
            const userId = req.user.id;
            const productId = req.params.id;

            // 检查商品是否存在
            const [existingProducts] = await this.db.execute(
                'SELECT id FROM products WHERE id = ? AND user_id = ? AND deleted_at IS NULL',
                [productId, userId]
            );

            if (existingProducts.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: '商品不存在'
                });
            }

            // 软删除
            await this.db.execute(
                'UPDATE products SET deleted_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
                [productId, userId]
            );

            res.json({
                success: true,
                message: '商品删除成功'
            });

        } catch (error) {
            console.error('删除商品失败:', error);
            res.status(500).json({
                success: false,
                message: '删除商品失败',
                error: error.message
            });
        }
    }

    /**
     * 记录库存变化
     */
    async recordInventoryChange(productId, variantId, type, quantity, quantityBefore, quantityAfter, reason, referenceType, referenceId, operatorId) {
        const query = `
            INSERT INTO inventory_records (
                product_id, variant_id, type, quantity, quantity_before, quantity_after,
                reason, reference_type, reference_id, operator_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const params = [
            productId,
            variantId,
            type,
            type === 'out' ? -quantity : quantity,
            quantityBefore,
            quantityAfter,
            reason,
            referenceType,
            referenceId,
            operatorId
        ];

        await this.db.execute(query, params);
    }

    /**
     * 获取路由器
     */
    getRouter() {
        return this.router;
    }
}

module.exports = ProductsController;

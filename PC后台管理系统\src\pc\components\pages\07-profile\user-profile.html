<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item active">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">个人资料</h1>
                            <p class="breadcrumb-description">管理个人信息、安全设置和偏好配置</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 个人资料统计 -->
                <div class="profile-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">15</span>
                            <span class="stat-label">连续登录天数</span>
                            <span class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                连续登录
                            </span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">高</span>
                            <span class="stat-label">安全等级</span>
                            <span class="stat-trend positive">
                                <i class="fas fa-lock"></i>
                                已启用2FA
                            </span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">4.8</span>
                            <span class="stat-label">工作评分</span>
                            <span class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                +0.2
                            </span>
                        </div>
                    </div>
                </div>

                <div class="modules-grid">
                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--primary-black);">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div>
                                <h3 class="module-title">基本信息</h3>
                                <p class="module-description">管理个人基本信息和联系方式</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value">100%</span>
                                <span class="stat-label">信息完整度</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">已认证</span>
                                <span class="stat-label">身份状态</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="editBasicInfo()">编辑信息</button>
                            <button class="btn btn-secondary" onclick="uploadAvatar()">更换头像</button>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--accent-blue);">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div>
                                <h3 class="module-title">账户安全</h3>
                                <p class="module-description">管理密码和安全设置</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value" id="securityLevel">高</span>
                                <span class="stat-label">安全等级</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="mfaStatus">已启用</span>
                                <span class="stat-label">双因子认证</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="changePassword()">修改密码</button>
                            <button class="btn btn-secondary" onclick="toggleMFA()">安全设置</button>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--success-green);">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div>
                                <h3 class="module-title">偏好设置</h3>
                                <p class="module-description">个性化界面和功能偏好</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value">中文</span>
                                <span class="stat-label">界面语言</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">浅色</span>
                                <span class="stat-label">主题模式</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="preferences()">偏好设置</button>
                            <button class="btn btn-secondary" onclick="themeSettings()">主题设置</button>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--warning-orange);">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <h3 class="module-title">通知设置</h3>
                                <p class="module-description">管理系统通知和提醒</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value">已开启</span>
                                <span class="stat-label">系统通知</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">已开启</span>
                                <span class="stat-label">邮件通知</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="notificationSettings()">通知设置</button>
                            <button class="btn btn-secondary" onclick="emailSettings()">邮件设置</button>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--gray-600);">
                                <i class="fas fa-history"></i>
                            </div>
                            <div>
                                <h3 class="module-title">活动记录</h3>
                                <p class="module-description">查看账户活动和登录历史</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value">今天</span>
                                <span class="stat-label">最后登录</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">156</span>
                                <span class="stat-label">总登录次数</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="activityLog()">活动记录</button>
                            <button class="btn btn-secondary" onclick="loginHistory()">登录历史</button>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <div class="module-icon" style="background: var(--accent-purple);">
                                <i class="fas fa-download"></i>
                            </div>
                            <div>
                                <h3 class="module-title">数据管理</h3>
                                <p class="module-description">导出个人数据和账户管理</p>
                            </div>
                        </div>
                        <div class="module-stats">
                            <div class="stat-item">
                                <span class="stat-value">可导出</span>
                                <span class="stat-label">数据状态</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">正常</span>
                                <span class="stat-label">账户状态</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="exportData()">导出数据</button>
                            <button class="btn btn-secondary" onclick="accountManagement()">账户管理</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 编辑基本信息模态框 -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑基本信息</h3>
                <button class="modal-close" onclick="closeEditProfileModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm">
                    <div class="form-group">
                        <label for="editName">姓名 *</label>
                        <input type="text" id="editName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">邮箱 *</label>
                        <input type="email" id="editEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editPhone">手机号</label>
                        <input type="tel" id="editPhone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="editDepartment">部门</label>
                        <input type="text" id="editDepartment" name="department">
                    </div>
                    <div class="form-group">
                        <label for="editPosition">职位</label>
                        <input type="text" id="editPosition" name="position">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEditProfileModal()">取消</button>
                <button class="btn btn-primary" onclick="saveProfile()">保存</button>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div class="modal" id="changePasswordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>修改密码</h3>
                <button class="modal-close" onclick="closeChangePasswordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="form-group">
                        <label for="currentPassword">当前密码 *</label>
                        <input type="password" id="currentPassword" name="currentPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码 *</label>
                        <input type="password" id="newPassword" name="newPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码 *</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                    </div>
                    <div class="password-strength" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <div class="strength-text">密码强度：<span id="strengthText">弱</span></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeChangePasswordModal()">取消</button>
                <button class="btn btn-primary" onclick="savePassword()">修改密码</button>
            </div>
        </div>
    </div>

    <!-- 头像上传模态框 -->
    <div class="modal" id="uploadAvatarModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>更换头像</h3>
                <button class="modal-close" onclick="closeUploadAvatarModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="avatar-upload">
                    <div class="avatar-preview">
                        <img id="avatarPreview" src="/assets/default-avatar.png" alt="头像预览">
                        <div class="avatar-overlay">
                            <i class="fas fa-camera"></i>
                            <span>点击选择图片</span>
                        </div>
                    </div>
                    <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                    <div class="upload-tips">
                        <p>• 支持 JPG、PNG 格式</p>
                        <p>• 建议尺寸：200x200 像素</p>
                        <p>• 文件大小不超过 2MB</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUploadAvatarModal()">取消</button>
                <button class="btn btn-primary" onclick="saveAvatar()">保存头像</button>
            </div>
        </div>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script src="../../js/admin-common.js"></script>
    <script>
        // 用户资料管理类
        class UserProfileManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.currentUser = null;
                this.init();
            }

            async init() {
                await this.loadUserProfile();
                this.bindEvents();
                this.updateUI();
            }

            // 加载用户资料
            async loadUserProfile() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/user/profile`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        this.currentUser = await response.json();
                        console.log('用户资料加载成功:', this.currentUser);
                    } else {
                        console.error('加载用户资料失败');
                        // 使用模拟数据
                        this.currentUser = this.getMockUserData();
                    }
                } catch (error) {
                    console.error('API调用失败:', error);
                    // 使用模拟数据
                    this.currentUser = this.getMockUserData();
                }
            }

            // 模拟用户数据
            getMockUserData() {
                return {
                    id: 1,
                    username: 'admin',
                    email: '<EMAIL>',
                    name: '系统管理员',
                    avatar: '/assets/default-avatar.png',
                    phone: '138****8888',
                    department: '技术部',
                    position: '系统管理员',
                    lastLogin: new Date().toISOString(),
                    loginDays: 15,
                    securityLevel: '高',
                    mfaEnabled: true,
                    workScore: 4.8,
                    profileComplete: 100,
                    verified: true,
                    language: 'zh-CN',
                    theme: 'light',
                    notifications: true,
                    emailNotifications: true
                };
            }

            // 更新UI显示
            updateUI() {
                if (!this.currentUser) return;

                // 更新统计数据
                document.querySelector('.profile-stats .stat-card:nth-child(1) .stat-value').textContent = this.currentUser.loginDays;
                document.querySelector('.profile-stats .stat-card:nth-child(2) .stat-value').textContent = this.currentUser.securityLevel;
                document.querySelector('.profile-stats .stat-card:nth-child(3) .stat-value').textContent = this.currentUser.workScore;

                // 更新安全状态
                document.getElementById('securityLevel').textContent = this.currentUser.securityLevel;
                document.getElementById('mfaStatus').textContent = this.currentUser.mfaEnabled ? '已启用' : '未启用';

                // 更新基本信息完整度
                const completePercent = document.querySelector('.module-card:nth-child(1) .stat-item:nth-child(1) .stat-value');
                if (completePercent) {
                    completePercent.textContent = `${this.currentUser.profileComplete}%`;
                }

                // 更新认证状态
                const verifyStatus = document.querySelector('.module-card:nth-child(1) .stat-item:nth-child(2) .stat-value');
                if (verifyStatus) {
                    verifyStatus.textContent = this.currentUser.verified ? '已认证' : '未认证';
                }
            }

            // 绑定事件
            bindEvents() {
                const cards = document.querySelectorAll('.stat-card, .module-card');
                cards.forEach(card => {
                    card.addEventListener('mouseenter', function() { 
                        this.style.transform = 'translateY(-4px)'; 
                    });
                    card.addEventListener('mouseleave', function() { 
                        this.style.transform = 'translateY(0)'; 
                    });
                });
            }

            // 编辑基本信息
            editBasicInfo() {
                // 填充当前用户信息到表单
                document.getElementById('editName').value = this.currentUser.name || '';
                document.getElementById('editEmail').value = this.currentUser.email || '';
                document.getElementById('editPhone').value = this.currentUser.phone || '';
                document.getElementById('editDepartment').value = this.currentUser.department || '';
                document.getElementById('editPosition').value = this.currentUser.position || '';

                // 显示模态框
                document.getElementById('editProfileModal').style.display = 'block';
            }

            // 保存基本信息
            async saveProfile() {
                const form = document.getElementById('editProfileForm');
                const formData = new FormData(form);
                const profileData = Object.fromEntries(formData);

                if (!profileData.name || !profileData.email) {
                    AdminMessage.error('请填写必填字段！');
                    return;
                }

                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/user/profile`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(profileData)
                    });

                    if (response.ok) {
                        Object.assign(this.currentUser, profileData);
                        this.updateUI();
                        this.closeEditProfileModal();
                        AdminMessage.success('基本信息更新成功！');
                    } else {
                        AdminMessage.error('更新失败，请稍后重试');
                    }
                } catch (error) {
                    console.error('更新基本信息失败:', error);
                    // 模拟更新成功
                    Object.assign(this.currentUser, profileData);
                    this.updateUI();
                    this.closeEditProfileModal();
                    AdminMessage.success('基本信息更新成功！（模拟）');
                }
            }

            // 关闭编辑资料模态框
            closeEditProfileModal() {
                document.getElementById('editProfileModal').style.display = 'none';
                document.getElementById('editProfileForm').reset();
            }

            // 上传头像
            uploadAvatar() {
                document.getElementById('uploadAvatarModal').style.display = 'block';
                this.bindAvatarEvents();
            }

            // 绑定头像相关事件
            bindAvatarEvents() {
                const avatarPreview = document.querySelector('.avatar-preview');
                const avatarInput = document.getElementById('avatarInput');

                if (avatarPreview && !avatarPreview.hasAttribute('data-bound')) {
                    avatarPreview.addEventListener('click', () => {
                        avatarInput.click();
                    });
                    avatarPreview.setAttribute('data-bound', 'true');
                }

                if (avatarInput && !avatarInput.hasAttribute('data-bound')) {
                    avatarInput.addEventListener('change', (e) => {
                        this.previewAvatar(e);
                    });
                    avatarInput.setAttribute('data-bound', 'true');
                }
            }

            // 预览头像
            previewAvatar(e) {
                const file = e.target.files[0];
                if (file) {
                    // 检查文件类型
                    if (!file.type.startsWith('image/')) {
                        AdminMessage.error('请选择图片文件！');
                        return;
                    }

                    // 检查文件大小 (2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        AdminMessage.error('图片大小不能超过2MB！');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        document.getElementById('avatarPreview').src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }

            // 保存头像
            async saveAvatar() {
                const avatarInput = document.getElementById('avatarInput');
                const file = avatarInput.files[0];

                if (!file) {
                    AdminMessage.error('请先选择头像图片！');
                    return;
                }

                const formData = new FormData();
                formData.append('avatar', file);

                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/user/avatar`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: formData
                    });

                    if (response.ok) {
                        const result = await response.json();
                        this.currentUser.avatar = result.avatarUrl || URL.createObjectURL(file);
                        this.updateUI();
                        this.closeUploadAvatarModal();
                        AdminMessage.success('头像上传成功！');
                    } else {
                        AdminMessage.error('头像上传失败，请稍后重试');
                    }
                } catch (error) {
                    console.error('头像上传失败:', error);
                    // 模拟上传成功
                    this.currentUser.avatar = URL.createObjectURL(file);
                    this.updateUI();
                    this.closeUploadAvatarModal();
                    AdminMessage.success('头像上传成功！（模拟）');
                }
            }

            // 关闭头像上传模态框
            closeUploadAvatarModal() {
                document.getElementById('uploadAvatarModal').style.display = 'none';
                document.getElementById('avatarInput').value = '';
                document.getElementById('avatarPreview').src = this.currentUser.avatar || '/assets/default-avatar.png';
            }

            // 修改密码
            changePassword() {
                document.getElementById('changePasswordModal').style.display = 'block';
                this.bindPasswordEvents();
            }

            // 绑定密码相关事件
            bindPasswordEvents() {
                const newPasswordInput = document.getElementById('newPassword');
                if (newPasswordInput && !newPasswordInput.hasAttribute('data-bound')) {
                    newPasswordInput.addEventListener('input', () => this.checkPasswordStrength());
                    newPasswordInput.setAttribute('data-bound', 'true');
                }
            }

            // 检查密码强度
            checkPasswordStrength() {
                const password = document.getElementById('newPassword').value;
                const strengthFill = document.querySelector('.strength-fill');
                const strengthText = document.getElementById('strengthText');

                let strength = 0;
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;

                strengthFill.className = 'strength-fill';
                if (strength <= 2) {
                    strengthFill.classList.add('weak');
                    strengthText.textContent = '弱';
                } else if (strength <= 3) {
                    strengthFill.classList.add('medium');
                    strengthText.textContent = '中';
                } else {
                    strengthFill.classList.add('strong');
                    strengthText.textContent = '强';
                }
            }

            // 重置密码强度指示器
            resetPasswordStrength() {
                const strengthFill = document.querySelector('.strength-fill');
                const strengthText = document.getElementById('strengthText');
                if (strengthFill) {
                    strengthFill.className = 'strength-fill';
                    strengthText.textContent = '弱';
                }
            }

            // 保存密码
            async savePassword() {
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!currentPassword || !newPassword || !confirmPassword) {
                    AdminMessage.error('请填写所有字段！');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    AdminMessage.error('两次输入的密码不一致！');
                    return;
                }

                if (newPassword.length < 6) {
                    AdminMessage.error('密码长度至少6位！');
                    return;
                }

                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/user/password`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            oldPassword: currentPassword,
                            newPassword: newPassword
                        })
                    });

                    if (response.ok) {
                        this.closeChangePasswordModal();
                        AdminMessage.success('密码修改成功！请重新登录');
                        // 清除token，跳转到登录页
                        setTimeout(() => {
                            localStorage.removeItem('token');
                            window.location.href = 'login.html';
                        }, 2000);
                    } else {
                        AdminMessage.error('密码修改失败，请检查当前密码是否正确');
                    }
                } catch (error) {
                    console.error('修改密码失败:', error);
                    this.closeChangePasswordModal();
                    AdminMessage.success('密码修改成功！（模拟）');
                }
            }

            // 关闭修改密码模态框
            closeChangePasswordModal() {
                document.getElementById('changePasswordModal').style.display = 'none';
                document.getElementById('changePasswordForm').reset();
                this.resetPasswordStrength();
            }

            // 切换双因子认证
            async toggleMFA() {
                const action = this.currentUser.mfaEnabled ? '关闭' : '开启';
                if (confirm(`确定要${action}双因子认证吗？`)) {
                    try {
                        const token = localStorage.getItem('token');
                        const response = await fetch(`${this.apiBase}/user/mfa`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                enabled: !this.currentUser.mfaEnabled
                            })
                        });

                        if (response.ok) {
                            this.currentUser.mfaEnabled = !this.currentUser.mfaEnabled;
                            showToast(`双因子认证已${action}！`, 'success');
                            this.updateUI();
                        } else {
                            showToast(`${action}双因子认证失败，请稍后重试`, 'error');
                        }
                    } catch (error) {
                        console.error('MFA设置失败:', error);
                        // 模拟切换成功
                        this.currentUser.mfaEnabled = !this.currentUser.mfaEnabled;
                        showToast(`双因子认证已${action}！（模拟）`, 'success');
                        this.updateUI();
                    }
                }
            }
        }

        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 全局函数（保持兼容性）
        let userProfileManager;

        function editBasicInfo() {
            userProfileManager.editBasicInfo();
        }

        function uploadAvatar() {
            userProfileManager.uploadAvatar();
        }

        function changePassword() {
            userProfileManager.changePassword();
        }

        function toggleMFA() {
            userProfileManager.toggleMFA();
        }

        function preferences() {
            // 创建偏好设置模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 500px;
                    max-height: 80vh;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">偏好设置</h3>
                    <form id="preferencesForm">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">语言偏好</label>
                            <select name="language" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="zh-CN" selected>中文（简体）</option>
                                <option value="zh-TW">中文（繁体）</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500;">时区设置</label>
                            <select name="timezone" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                                <option value="America/New_York">纽约时间 (UTC-5)</option>
                                <option value="Europe/London">伦敦时间 (UTC+0)</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="showTips" checked>
                                <span>显示操作提示</span>
                            </label>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closePreferencesModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                            <button type="submit" style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">保存</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            modal.querySelector('#preferencesForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                console.log('偏好设置已保存:', Object.fromEntries(formData));
                showToast('偏好设置已保存！', 'success');
                document.body.removeChild(modal);
            });
            
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
            window.closePreferencesModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
        }

        function themeSettings() {
            // 创建主题设置模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    width: 90%;
                    max-width: 400px;
                ">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">主题设置</h3>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div onclick="selectTheme('light')" style="
                            padding: 16px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            cursor: pointer;
                            text-align: center;
                            transition: all 0.2s;
                        " onmouseover="this.style.borderColor='#1f2937'" onmouseout="this.style.borderColor='#e5e7eb'">
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #f8fafc, #e2e8f0); border-radius: 4px; margin-bottom: 8px;"></div>
                            <span style="font-weight: 500;">明亮主题</span>
                        </div>
                        <div onclick="selectTheme('dark')" style="
                            padding: 16px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            cursor: pointer;
                            text-align: center;
                            transition: all 0.2s;
                        " onmouseover="this.style.borderColor='#1f2937'" onmouseout="this.style.borderColor='#e5e7eb'">
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #1f2937, #374151); border-radius: 4px; margin-bottom: 8px;"></div>
                            <span style="font-weight: 500;">深色主题</span>
                        </div>
                        <div onclick="selectTheme('blue')" style="
                            padding: 16px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            cursor: pointer;
                            text-align: center;
                            transition: all 0.2s;
                        " onmouseover="this.style.borderColor='#1f2937'" onmouseout="this.style.borderColor='#e5e7eb'">
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #3b82f6, #1d4ed8); border-radius: 4px; margin-bottom: 8px;"></div>
                            <span style="font-weight: 500;">蓝色主题</span>
                        </div>
                        <div onclick="selectTheme('green')" style="
                            padding: 16px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            cursor: pointer;
                            text-align: center;
                            transition: all 0.2s;
                        " onmouseover="this.style.borderColor='#1f2937'" onmouseout="this.style.borderColor='#e5e7eb'">
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #10b981, #059669); border-radius: 4px; margin-bottom: 8px;"></div>
                            <span style="font-weight: 500;">绿色主题</span>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
                        <button onclick="closeThemeModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">关闭</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            window.selectTheme = function(theme) {
                console.log('选中主题:', theme);
                showToast(`已切换到${theme === 'light' ? '明亮' : theme === 'dark' ? '深色' : theme === 'blue' ? '蓝色' : '绿色'}主题！`, 'success');
                document.body.removeChild(modal);
            };
            
            window.closeThemeModal = function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            };
            
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        function notificationSettings() {
            showToast('正在跳转到通知设置页面...', 'info');
            setTimeout(() => {
                console.log('模拟跳转到通知设置页面');
            }, 1000);
        }

        function emailSettings() {
            showToast('正在跳转到邮件设置页面...', 'info');
            setTimeout(() => {
                console.log('模拟跳转到邮件设置页面');
            }, 1000);
        }

        function activityLog() {
            showToast('正在跳转到活动记录页面...', 'info');
            setTimeout(() => {
                console.log('模拟跳转到活动记录页面');
            }, 1000);
        }

        function loginHistory() {
            showToast('正在跳转到登录历史页面...', 'info');
            setTimeout(() => {
                console.log('模拟跳转到登录历史页面');
            }, 1000);
        }

        function exportData() {
            showToast('正在准备数据导出...', 'info');
            setTimeout(() => {
                const data = {
                    profile: userProfileManager?.currentUser || {},
                    exportTime: new Date().toISOString(),
                    version: '1.0.0'
                };
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `user-data-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showToast('数据导出成功！', 'success');
            }, 1500);
        }

        function accountManagement() {
            showToast('正在跳转到账户管理页面...', 'info');
            setTimeout(() => {
                console.log('模拟跳转到账户管理页面');
            }, 1000);
        }

        // 全局函数，供模态框调用
        let userProfileManager;

        function closeEditProfileModal() {
            if (userProfileManager) userProfileManager.closeEditProfileModal();
        }

        function saveProfile() {
            if (userProfileManager) userProfileManager.saveProfile();
        }

        function closeChangePasswordModal() {
            if (userProfileManager) userProfileManager.closeChangePasswordModal();
        }

        function savePassword() {
            if (userProfileManager) userProfileManager.savePassword();
        }

        function closeUploadAvatarModal() {
            if (userProfileManager) userProfileManager.closeUploadAvatarModal();
        }

        function saveAvatar() {
            if (userProfileManager) userProfileManager.saveAvatar();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            userProfileManager = new UserProfileManager();

            // 绑定模态框关闭事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>

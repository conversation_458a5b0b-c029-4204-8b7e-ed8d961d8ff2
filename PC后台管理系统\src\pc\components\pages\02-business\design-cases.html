<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计案例 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }



        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航样式 */
        .top-nav {
            margin-bottom: 24px;
            margin-top: 20px;
        }

        .nav-breadcrumb {
            flex: 1;
        }

        .breadcrumb-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        



        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 案例卡片样式 */
        .cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .case-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.2s;
        }

        .case-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .case-image {
            width: 100%;
            height: 180px;
            overflow: hidden;
        }

        .case-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .case-image-placeholder {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .case-image-placeholder i {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .case-image-placeholder span {
            font-size: 14px;
            font-weight: 500;
        }

        .case-content {
            padding: 16px;
        }

        .case-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .case-meta {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: #6b7280;
        }

        .case-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .case-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
        }

        .tag {
            display: inline-block;
            padding: 2px 8px;
            font-size: 12px;
            background: #f3f4f6;
            color: #374151;
            border-radius: 4px;
        }

        .case-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-danger {
            background: #dc2626;
            color: #ffffff;
            border-color: #dc2626;
        }

        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }

        /* 统计栏样式 */
        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        /* 筛选器样式 */
        .filters {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .filter-row {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
            min-width: 150px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            color: #1f2937;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .nav-actions {
                flex-direction: column;
                gap: 8px;
            }

            .cases-grid {
                grid-template-columns: 1fr;
            }

            .stats-bar {
                grid-template-columns: repeat(2, 1fr);
            }

            .filter-row {
                flex-direction: column;
            }

            .case-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item active">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">设计案例</h1>
                            <p class="breadcrumb-description">智能家居设计案例库管理和展示平台</p>
                        </div>
                    </nav>
                    <div class="nav-actions">
                        <button class="btn btn-secondary" onclick="exportCases()">导出案例</button>
                        <button class="btn btn-primary" onclick="createNewCase()">添加案例</button>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">

                <!-- 统计数据 -->
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">总案例数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">本月新增</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">热门案例</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98.5%</div>
                        <div class="stat-label">客户满意度</div>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="filters">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="filter-label">案例类型</label>
                            <select class="filter-select">
                                <option>全部类型</option>
                                <option>户型优化</option>
                                <option>全屋智能</option>
                                <option>照明设计</option>
                                <option>安防系统</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">房屋面积</label>
                            <select class="filter-select">
                                <option>全部面积</option>
                                <option>50-80㎡</option>
                                <option>80-120㎡</option>
                                <option>120-200㎡</option>
                                <option>200㎡以上</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">设计师</label>
                            <select class="filter-select">
                                <option>全部设计师</option>
                                <option>张设计师</option>
                                <option>李设计师</option>
                                <option>王设计师</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">状态</label>
                            <select class="filter-select">
                                <option>全部状态</option>
                                <option>已发布</option>
                                <option>草稿</option>
                                <option>审核中</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 案例网格 -->
                <div class="cases-grid">
                    <div class="case-card">
                        <div class="case-image">
                            <div class="case-image-placeholder">
                                <i class="fas fa-home"></i>
                                <span>现代简约</span>
                            </div>
                        </div>
                        <div class="case-content">
                            <h3 class="case-title">现代简约三居室智能家居</h3>
                            <div class="case-meta">
                                <span>120㎡</span>
                                <span>张设计师</span>
                                <span>2024-01-15</span>
                            </div>
                            <p class="case-description">
                                采用现代简约风格，集成全屋智能控制系统，包括智能照明、空调控制、安防监控等功能。
                            </p>
                            <div class="case-tags">
                                <span class="tag">全屋智能</span>
                                <span class="tag">现代简约</span>
                                <span class="tag">三居室</span>
                            </div>
                            <div class="case-actions">
                                <button class="btn btn-secondary btn-small" onclick="viewCaseDetail('case-001')">查看详情</button>
                                <button class="btn btn-primary btn-small" onclick="editCase('case-001')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteCase('case-001')">删除</button>
                            </div>
                        </div>
                    </div>

                    <div class="case-card">
                        <div class="case-image">
                            <div class="case-image-placeholder">
                                <i class="fas fa-lightbulb"></i>
                                <span>北欧风格</span>
                            </div>
                        </div>
                        <div class="case-content">
                            <h3 class="case-title">北欧风格智能照明设计</h3>
                            <div class="case-meta">
                                <span>85㎡</span>
                                <span>李设计师</span>
                                <span>2024-01-12</span>
                            </div>
                            <p class="case-description">
                                北欧风格两居室，重点设计智能照明系统，营造温馨舒适的居住环境。
                            </p>
                            <div class="case-tags">
                                <span class="tag">智能照明</span>
                                <span class="tag">北欧风格</span>
                                <span class="tag">两居室</span>
                            </div>
                            <div class="case-actions">
                                <button class="btn btn-secondary btn-small" onclick="viewCaseDetail('case-002')">查看详情</button>
                                <button class="btn btn-primary btn-small" onclick="editCase('case-002')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteCase('case-002')">删除</button>
                            </div>
                        </div>
                    </div>

                    <div class="case-card">
                        <div class="case-image">
                            <div class="case-image-placeholder">
                                <i class="fas fa-building"></i>
                                <span>豪华别墅</span>
                            </div>
                        </div>
                        <div class="case-content">
                            <h3 class="case-title">豪华别墅全屋智能系统</h3>
                            <div class="case-meta">
                                <span>350㎡</span>
                                <span>王设计师</span>
                                <span>2024-01-10</span>
                            </div>
                            <p class="case-description">
                                大型别墅智能化改造，包含智能安防、环境控制、影音娱乐等完整系统。
                            </p>
                            <div class="case-tags">
                                <span class="tag">别墅</span>
                                <span class="tag">全屋智能</span>
                                <span class="tag">豪华装修</span>
                            </div>
                            <div class="case-actions">
                                <button class="btn btn-secondary btn-small" onclick="viewCaseDetail('case-003')">查看详情</button>
                                <button class="btn btn-primary btn-small" onclick="editCase('case-003')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteCase('case-003')">删除</button>
                            </div>
                        </div>
                    </div>

                    <div class="case-card">
                        <div class="case-image">
                            <div class="case-image-placeholder">
                                <i class="fas fa-cube"></i>
                                <span>小户型设计</span>
                            </div>
                        </div>
                        <div class="case-content">
                            <h3 class="case-title">小户型智能收纳设计</h3>
                            <div class="case-meta">
                                <span>65㎡</span>
                                <span>赵设计师</span>
                                <span>2024-01-08</span>
                            </div>
                            <p class="case-description">
                                小户型空间优化设计，结合智能家居技术，最大化利用空间。
                            </p>
                            <div class="case-tags">
                                <span class="tag">小户型</span>
                                <span class="tag">空间优化</span>
                                <span class="tag">智能收纳</span>
                            </div>
                            <div class="case-actions">
                                <button class="btn btn-secondary btn-small" onclick="viewCaseDetail('case-004')">查看详情</button>
                                <button class="btn btn-primary btn-small" onclick="editCase('case-004')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteCase('case-004')">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 案例管理功能
        function createNewCase() {
            alert('创建新案例功能');
            // 这里可以打开创建案例的模态框或跳转到创建页面
        }

        function exportCases() {
            alert('导出案例功能');
            // 这里可以实现案例数据导出功能
        }

        function viewCaseDetail(caseId) {
            alert(`查看案例详情: ${caseId}`);
            // 这里可以跳转到案例详情页面
            // window.location.href = `case-detail.html?id=${caseId}`;
        }

        function editCase(caseId) {
            alert(`编辑案例: ${caseId}`);
            // 这里可以跳转到案例编辑页面
            // window.location.href = `case-edit.html?id=${caseId}`;
        }

        function deleteCase(caseId) {
            if (confirm('确定要删除这个案例吗？此操作不可撤销。')) {
                alert(`删除案例: ${caseId}`);
                // 这里可以调用删除API
                // 删除成功后刷新页面或移除对应的卡片
            }
        }

        // 筛选功能
        function filterCases() {
            const filters = {
                type: document.querySelector('.filter-select:nth-of-type(1)').value,
                area: document.querySelector('.filter-select:nth-of-type(2)').value,
                designer: document.querySelector('.filter-select:nth-of-type(3)').value,
                status: document.querySelector('.filter-select:nth-of-type(4)').value
            };

            console.log('筛选条件:', filters);
            // 这里可以根据筛选条件重新加载案例数据
        }

        // 自动设置当前页面的导航高亮
        document.addEventListener('DOMContentLoaded', function() {
            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPage) {
                    item.classList.add('active');
                }
            });

            // 绑定筛选器事件
            const filterSelects = document.querySelectorAll('.filter-select');
            filterSelects.forEach(select => {
                select.addEventListener('change', filterCases);
            });

            console.log('设计案例页面初始化完成');
        });
    </script>
</body>
</html>

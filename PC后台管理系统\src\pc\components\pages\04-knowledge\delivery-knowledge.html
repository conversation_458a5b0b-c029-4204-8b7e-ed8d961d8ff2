<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交底知识库 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.5;
            font-weight: bold;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.5;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 页面标题 */
        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        /* 主要内容布局 */
        .content-layout {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            gap: 24px;
            height: calc(100vh - 200px);
        }

        /* 左侧分类列表 */
        .category-panel {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .panel-header {
            padding: 16px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #1f2937;
        }

        .category-list {
            padding: 8px 0;
            max-height: 400px;
            overflow-y: auto;
        }

        .category-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.2s;
            border-left: 3px solid transparent;
        }

        .category-item:hover {
            background: #f8fafc;
        }

        .category-item.active {
            background: #f8fafc;
            border-left-color: #3b82f6;
            color: #3b82f6;
        }

        /* 中间内容区域 */
        .content-panel {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .content-header {
            padding: 16px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-box {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .content-list {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
        }

        .content-item {
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-title {
            font-weight: 500;
            color: #1f2937;
        }

        .content-meta {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .content-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .action-btn:hover {
            background: #f8fafc;
        }

        /* 右侧信息面板 */
        .info-panel {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .info-content {
            padding: 16px;
        }

        .info-section {
            margin-bottom: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #6b7280;
            font-size: 14px;
        }

        .info-value {
            color: #1f2937;
            font-weight: 500;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #ffffff;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            color: #6b7280;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            background-color: #f9fafb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        .file-list {
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background-color: #f3f4f6;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-item span {
            flex: 1;
            font-size: 14px;
            color: #374151;
        }

        .btn-remove {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .detail-group {
            margin-bottom: 16px;
        }

        .detail-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
            display: block;
        }

        .detail-value {
            color: #1f2937;
            line-height: 1.5;
        }

        .content-text {
            white-space: pre-wrap;
            background-color: #f9fafb;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-layout {
                grid-template-columns: 250px 1fr 250px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .content-layout {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="delivery-knowledge.html" class="nav-item active">交底知识库</a>
                    <a href="wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">交底知识库</h1>
                <p class="page-subtitle">智能家居项目交底流程、标准和注意事项</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">交底文档</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">流程模板</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">视频教程</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">案例分析</div>
                </div>
            </div>

            <!-- 主要内容布局 -->
            <div class="content-layout">
                <!-- 左侧分类列表 -->
                <div class="category-panel">
                    <div class="panel-header">交底分类</div>
                    <div class="category-list">
                        <div class="category-item active">水电交底</div>
                        <div class="category-item">安全交底</div>
                        <div class="category-item">技术交底</div>
                        <div class="category-item">质量交底</div>
                        <div class="category-item">进度交底</div>
                        <div class="category-item">材料交底</div>
                        <div class="category-item">设备交底</div>
                        <div class="category-item">验收交底</div>
                        <div class="category-item">维护交底</div>
                        <div class="category-item">应急交底</div>
                    </div>
                </div>

                <!-- 中间内容区域 -->
                <div class="content-panel">
                    <div class="content-header">
                        <div class="search-box">
                            <input type="text" class="search-input" placeholder="搜索交底内容...">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                        <button class="btn btn-primary" onclick="showAddDeliveryModal()">+ 新增交底</button>
                    </div>
                    <div class="content-list">
                        <div class="content-item" data-id="1" data-title="App开发技术交底规范" data-category="技术交底" data-date="2024-01-15">
                            <div>
                                <div class="content-title">App开发技术交底规范</div>
                                <div class="content-meta">2024-01-15 | 技术交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn" onclick="viewDelivery(1)">查看</button>
                                <button class="action-btn" onclick="editDelivery(1)">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">智能家居系统安装交底</div>
                                <div class="content-meta">2024-01-14 | 安装交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">电气工程安全交底</div>
                                <div class="content-meta">2024-01-13 | 安全交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">智能照明系统调试交底</div>
                                <div class="content-meta">2024-01-12 | 调试交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">弱电系统施工交底</div>
                                <div class="content-meta">2024-01-11 | 施工交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">智能门锁安装交底</div>
                                <div class="content-meta">2024-01-10 | 安装交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">网络布线质量交底</div>
                                <div class="content-meta">2024-01-09 | 质量交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">智能窗帘系统交底</div>
                                <div class="content-meta">2024-01-08 | 系统交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                        <div class="content-item">
                            <div>
                                <div class="content-title">安防系统配置交底</div>
                                <div class="content-meta">2024-01-07 | 配置交底</div>
                            </div>
                            <div class="content-actions">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧信息面板 -->
                <div class="info-panel">
                    <div class="panel-header">统计信息</div>
                    <div class="info-content">
                        <div class="info-section">
                            <div class="info-title">本月数据</div>
                            <div class="info-item">
                                <span class="info-label">新增交底</span>
                                <span class="info-value">12</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">更新交底</span>
                                <span class="info-value">8</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">查看次数</span>
                                <span class="info-value">156</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <div class="info-title">热门交底</div>
                            <div class="info-item">
                                <span class="info-label">水电安装交底</span>
                                <span class="info-value">45次</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">安全操作交底</span>
                                <span class="info-value">38次</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">质量标准交底</span>
                                <span class="info-value">32次</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <div class="info-title">最近更新</div>
                            <div class="info-item">
                                <span class="info-label">智能照明交底</span>
                                <span class="info-value">2小时前</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">网络配置交底</span>
                                <span class="info-value">5小时前</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">设备调试交底</span>
                                <span class="info-value">1天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑交底模态框 -->
    <div id="deliveryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="deliveryModalTitle">新增交底</h3>
                <span class="modal-close" onclick="closeDeliveryModal()">&times;</span>
            </div>
            <form id="deliveryForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">交底标题</label>
                        <input type="text" id="deliveryTitle" name="title" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">交底分类</label>
                        <select id="deliveryCategory" name="category" class="form-input form-select" required>
                            <option value="">请选择分类</option>
                            <option value="水电交底">水电交底</option>
                            <option value="安全交底">安全交底</option>
                            <option value="技术交底">技术交底</option>
                            <option value="质量交底">质量交底</option>
                            <option value="进度交底">进度交底</option>
                            <option value="材料交底">材料交底</option>
                            <option value="设备交底">设备交底</option>
                            <option value="验收交底">验收交底</option>
                            <option value="维护交底">维护交底</option>
                            <option value="应急交底">应急交底</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">交底内容</label>
                        <textarea id="deliveryContent" name="content" class="form-input form-textarea" rows="8" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">附件上传</label>
                        <input type="file" id="deliveryFiles" multiple accept=".pdf,.doc,.docx,.jpg,.png">
                        <div id="fileList" class="file-list"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeDeliveryModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 查看交底模态框 -->
    <div id="viewDeliveryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>交底详情</h3>
                <span class="modal-close" onclick="closeViewModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="detail-group">
                    <label class="detail-label">交底标题：</label>
                    <div id="viewTitle" class="detail-value"></div>
                </div>
                <div class="detail-group">
                    <label class="detail-label">交底分类：</label>
                    <div id="viewCategory" class="detail-value"></div>
                </div>
                <div class="detail-group">
                    <label class="detail-label">创建时间：</label>
                    <div id="viewDate" class="detail-value"></div>
                </div>
                <div class="detail-group">
                    <label class="detail-label">交底内容：</label>
                    <div id="viewContent" class="detail-value content-text"></div>
                </div>
                <div class="detail-group">
                    <label class="detail-label">附件：</label>
                    <div id="viewFiles" class="detail-value"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeViewModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 交底数据管理
        class DeliveryManager {
            constructor() {
                this.deliveries = [
                    {
                        id: 1,
                        title: 'App开发技术交底规范',
                        category: '技术交底',
                        content: '1. 开发环境配置\n2. 代码规范要求\n3. 测试流程\n4. 部署注意事项',
                        date: '2024-01-15',
                        files: []
                    },
                    {
                        id: 2,
                        title: '智能家居系统安装交底',
                        category: '安装交底',
                        content: '1. 设备清单检查\n2. 安装位置确认\n3. 线路连接要求\n4. 调试步骤',
                        date: '2024-01-14',
                        files: []
                    },
                    {
                        id: 3,
                        title: '电气工程安全交底',
                        category: '安全交底',
                        content: '1. 安全防护措施\n2. 操作注意事项\n3. 应急处理流程\n4. 验收标准',
                        date: '2024-01-13',
                        files: []
                    }
                ];
                this.init();
            }

            init() {
                this.updateContentList();
                this.bindEvents();
            }

            bindEvents() {
                // 分类切换功能
                document.querySelectorAll('.category-item').forEach(item => {
                    item.addEventListener('click', () => {
                        document.querySelectorAll('.category-item').forEach(i => i.classList.remove('active'));
                        item.classList.add('active');
                        this.filterByCategory(item.textContent);
                    });
                });

                // 搜索功能
                const searchInput = document.querySelector('.search-input');
                const searchBtn = document.querySelector('.search-box .btn-primary');
                
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchDeliveries(searchInput.value);
                    }
                });

                searchBtn.addEventListener('click', () => {
                    this.searchDeliveries(searchInput.value);
                });

                // 表单提交
                document.getElementById('deliveryForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleFormSubmit();
                });

                // 文件上传
                document.getElementById('deliveryFiles').addEventListener('change', (e) => {
                    this.handleFileUpload(e.target.files);
                });
            }

            updateContentList() {
                const contentList = document.querySelector('.content-list');
                if (!contentList) return;

                contentList.innerHTML = this.deliveries.map(delivery => `
                    <div class="content-item" data-id="${delivery.id}">
                        <div>
                            <div class="content-title">${delivery.title}</div>
                            <div class="content-meta">${delivery.date} | ${delivery.category}</div>
                        </div>
                        <div class="content-actions">
                            <button class="action-btn" onclick="viewDelivery(${delivery.id})">查看</button>
                            <button class="action-btn" onclick="editDelivery(${delivery.id})">编辑</button>
                        </div>
                    </div>
                `).join('');
            }

            searchDeliveries(term) {
                const items = document.querySelectorAll('.content-item');
                items.forEach(item => {
                    const title = item.querySelector('.content-title').textContent.toLowerCase();
                    if (title.includes(term.toLowerCase()) || term === '') {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }

            filterByCategory(category) {
                if (category === '全部') {
                    this.updateContentList();
                    return;
                }

                const filtered = this.deliveries.filter(d => d.category === category);
                const contentList = document.querySelector('.content-list');
                
                contentList.innerHTML = filtered.map(delivery => `
                    <div class="content-item" data-id="${delivery.id}">
                        <div>
                            <div class="content-title">${delivery.title}</div>
                            <div class="content-meta">${delivery.date} | ${delivery.category}</div>
                        </div>
                        <div class="content-actions">
                            <button class="action-btn" onclick="viewDelivery(${delivery.id})">查看</button>
                            <button class="action-btn" onclick="editDelivery(${delivery.id})">编辑</button>
                        </div>
                    </div>
                `).join('');
            }

            handleFormSubmit() {
                const form = document.getElementById('deliveryForm');
                const formData = new FormData(form);
                
                const deliveryData = {
                    title: formData.get('title'),
                    category: formData.get('category'),
                    content: formData.get('content'),
                    files: this.currentFiles || []
                };

                if (!deliveryData.title || !deliveryData.category || !deliveryData.content) {
                    this.showToast('请填写完整信息', 'error');
                    return;
                }

                const editId = form.dataset.editId;
                if (editId) {
                    // 编辑模式
                    const index = this.deliveries.findIndex(d => d.id == editId);
                    if (index !== -1) {
                        this.deliveries[index] = {
                            ...this.deliveries[index],
                            ...deliveryData
                        };
                        this.showToast('交底更新成功', 'success');
                    }
                } else {
                    // 新增模式
                    const newDelivery = {
                        id: Date.now(),
                        ...deliveryData,
                        date: new Date().toLocaleDateString('zh-CN')
                    };
                    this.deliveries.unshift(newDelivery);
                    this.showToast('交底添加成功', 'success');
                }

                this.updateContentList();
                closeDeliveryModal();
            }

            handleFileUpload(files) {
                const fileList = document.getElementById('fileList');
                this.currentFiles = this.currentFiles || [];

                Array.from(files).forEach(file => {
                    this.currentFiles.push(file);
                    
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span>${file.name}</span>
                        <button type="button" class="btn-remove" onclick="removeFile(this, '${file.name}')">×</button>
                    `;
                    fileList.appendChild(fileItem);
                });
            }

            showToast(message, type = 'info') {
                // 创建toast容器（如果不存在）
                let toastContainer = document.getElementById('toastContainer');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toastContainer';
                    toastContainer.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                    `;
                    document.body.appendChild(toastContainer);
                }

                // 创建toast元素
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.style.cssText = `
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    margin-bottom: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;
                toast.textContent = message;

                toastContainer.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 全局函数
        let deliveryManager;

        function showAddDeliveryModal() {
            const modal = document.getElementById('deliveryModal');
            const form = document.getElementById('deliveryForm');
            const title = document.getElementById('deliveryModalTitle');
            
            form.reset();
            delete form.dataset.editId;
            title.textContent = '新增交底';
            document.getElementById('fileList').innerHTML = '';
            deliveryManager.currentFiles = [];
            
            modal.style.display = 'block';
        }

        function editDelivery(id) {
            const delivery = deliveryManager.deliveries.find(d => d.id == id);
            if (!delivery) return;

            const modal = document.getElementById('deliveryModal');
            const form = document.getElementById('deliveryForm');
            const title = document.getElementById('deliveryModalTitle');
            
            // 填充表单数据
            document.getElementById('deliveryTitle').value = delivery.title;
            document.getElementById('deliveryCategory').value = delivery.category;
            document.getElementById('deliveryContent').value = delivery.content;
            
            // 设置编辑模式
            form.dataset.editId = id;
            title.textContent = '编辑交底';
            
            modal.style.display = 'block';
        }

        function viewDelivery(id) {
            const delivery = deliveryManager.deliveries.find(d => d.id == id);
            if (!delivery) return;

            document.getElementById('viewTitle').textContent = delivery.title;
            document.getElementById('viewCategory').textContent = delivery.category;
            document.getElementById('viewDate').textContent = delivery.date;
            document.getElementById('viewContent').textContent = delivery.content;
            document.getElementById('viewFiles').innerHTML = delivery.files && delivery.files.length > 0 
                ? delivery.files.map(file => `<div>${file.name}</div>`).join('') 
                : '无附件';

            document.getElementById('viewDeliveryModal').style.display = 'block';
        }

        function closeDeliveryModal() {
            document.getElementById('deliveryModal').style.display = 'none';
        }

        function closeViewModal() {
            document.getElementById('viewDeliveryModal').style.display = 'none';
        }

        function removeFile(button, filename) {
            if (deliveryManager.currentFiles) {
                deliveryManager.currentFiles = deliveryManager.currentFiles.filter(f => f.name !== filename);
            }
            button.parentElement.remove();
        }

        // 移动端菜单切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            deliveryManager = new DeliveryManager();
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                const deliveryModal = document.getElementById('deliveryModal');
                const viewModal = document.getElementById('viewDeliveryModal');
                
                if (event.target === deliveryModal) {
                    closeDeliveryModal();
                }
                if (event.target === viewModal) {
                    closeViewModal();
                }
            });
        });
    </script>
</body>
</html>

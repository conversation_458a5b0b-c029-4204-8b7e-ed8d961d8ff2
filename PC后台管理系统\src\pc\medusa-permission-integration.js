/**
 * MedusaJS与智能家居权限系统集成模块
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

class MedusaPermissionIntegration {
    constructor(config = {}) {
        this.medusaApiUrl = config.medusaApiUrl || 'http://localhost:9000'
        this.apiKey = config.apiKey || ''
        this.permissions = new Map()
        this.userRoles = new Map()
        
        // 权限模块映射 (MedusaJS → 智能家居)
        this.moduleMapping = {
            // MedusaJS原生模块
            'products': 'mall',
            'orders': 'contract', 
            'customers': 'customer',
            'analytics': 'analysis',
            
            // 智能家居专有模块
            'smart-home-design': 'design',
            'smart-home-construction': 'construction',
            'smart-home-cost': 'cost',
            'smart-home-quote': 'quote',
            'smart-home-pricing': 'pricing',
            'smart-home-tracking': 'tracking',
            'smart-home-service': 'service',
            'smart-home-resource': 'resource',
            'smart-home-marketing': 'marketing',
            'smart-home-sharing': 'sharing'
        }
        
        // 角色权限模板 (与权限管理系统保持一致)
        this.roleTemplates = {
            OWNER: {
                name: '业主',
                medusaRole: 'customer',
                permissions: {
                    'contract': ['VIEW'],
                    'construction': ['VIEW', 'APPROVE'],
                    'design': ['VIEW', 'APPROVE'],
                    'quote': ['VIEW', 'APPROVE'],
                    'customer': ['VIEW', 'EDIT'],
                    'marketing': ['VIEW'],
                    'mall': ['VIEW', 'CREATE']
                }
            },
            PROJECT_MANAGER: {
                name: '项目经理',
                medusaRole: 'admin',
                permissions: {
                    'contract': ['VIEW', 'CREATE', 'EDIT', 'APPROVE'],
                    'construction': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'INSPECT'],
                    'cost': ['VIEW', 'CREATE', 'EDIT', 'APPROVE', 'ANALYZE'],
                    'design': ['VIEW', 'APPROVE'],
                    'quote': ['VIEW', 'APPROVE'],
                    'customer': ['VIEW', 'EDIT'],
                    'tracking': ['VIEW', 'EDIT', 'ANALYZE', 'TRACK'],
                    'resource': ['VIEW', 'MANAGE'],
                    'mall': ['VIEW', 'EDIT', 'MANAGE']
                }
            },
            DESIGNER: {
                name: '设计师',
                medusaRole: 'admin',
                permissions: {
                    'design': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
                    'analysis': ['VIEW', 'ANALYZE', 'EXPORT'],
                    'quote': ['CREATE', 'EDIT'],
                    'customer': ['VIEW'],
                    'tracking': ['VIEW', 'TRACK'],
                    'marketing': ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                    'mall': ['VIEW']
                }
            },
            SALES: {
                name: '销售人员',
                medusaRole: 'admin',
                permissions: {
                    'customer': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'IMPORT', 'EXPORT'],
                    'tracking': ['VIEW', 'CREATE', 'EDIT', 'ANALYZE', 'TRACK'],
                    'contract': ['CREATE', 'EDIT'],
                    'quote': ['CREATE', 'EDIT'],
                    'resource': ['VIEW'],
                    'marketing': ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
                    'service': ['VIEW', 'CREATE', 'EDIT', 'QUERY'],
                    'mall': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'MANAGE']
                }
            },
            ADMIN: {
                name: '系统管理员',
                medusaRole: 'admin',
                permissions: {
                    'contract': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
                    'construction': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'INSPECT'],
                    'cost': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'ANALYZE'],
                    'design': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
                    'analysis': ['VIEW', 'ANALYZE', 'EXPORT'],
                    'quote': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
                    'pricing': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'MANAGE'],
                    'mall': ['VIEW', 'EDIT', 'CONFIGURE'],
                    'customer': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'IMPORT', 'EXPORT'],
                    'tracking': ['VIEW', 'CREATE', 'EDIT', 'ANALYZE', 'TRACK'],
                    'service': ['VIEW', 'CREATE', 'EDIT', 'QUERY', 'ANALYZE'],
                    'resource': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'MANAGE'],
                    'marketing': ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'MANAGE'],
                    'sharing': ['VIEW', 'CREATE', 'EDIT', 'ANALYZE', 'MANAGE'],
                    'system': ['VIEW', 'EDIT', 'CONFIGURE', 'MANAGE']
                }
            }
        }
    }

    /**
     * 初始化权限系统
     */
    async initialize() {
        try {
            console.log('🔧 初始化MedusaJS权限集成...')
            
            // 验证MedusaJS连接
            await this.validateMedusaConnection()
            
            // 加载用户权限
            await this.loadUserPermissions()
            
            console.log('✅ MedusaJS权限集成初始化完成')
            return true
        } catch (error) {
            console.error('❌ MedusaJS权限集成初始化失败:', error)
            return false
        }
    }

    /**
     * 验证MedusaJS连接
     */
    async validateMedusaConnection() {
        try {
            const response = await fetch(`${this.medusaApiUrl}/admin/auth`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            })
            
            if (!response.ok) {
                throw new Error(`MedusaJS连接失败: ${response.status}`)
            }
            
            console.log('✅ MedusaJS连接验证成功')
            return true
        } catch (error) {
            console.error('❌ MedusaJS连接验证失败:', error)
            throw error
        }
    }

    /**
     * 加载用户权限
     */
    async loadUserPermissions() {
        try {
            // 从MedusaJS获取用户列表
            const users = await this.getMedusaUsers()
            
            // 为每个用户加载智能家居权限
            for (const user of users) {
                const smartHomeRole = await this.getUserSmartHomeRole(user.id)
                if (smartHomeRole) {
                    this.userRoles.set(user.id, smartHomeRole)
                    this.permissions.set(user.id, this.roleTemplates[smartHomeRole]?.permissions || {})
                }
            }
            
            console.log(`✅ 已加载 ${users.length} 个用户的权限配置`)
        } catch (error) {
            console.error('❌ 加载用户权限失败:', error)
            throw error
        }
    }

    /**
     * 获取MedusaJS用户列表
     */
    async getMedusaUsers() {
        try {
            const response = await fetch(`${this.medusaApiUrl}/admin/users`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            })
            
            if (!response.ok) {
                throw new Error(`获取用户列表失败: ${response.status}`)
            }
            
            const data = await response.json()
            return data.users || []
        } catch (error) {
            console.error('❌ 获取MedusaJS用户列表失败:', error)
            return []
        }
    }

    /**
     * 获取用户的智能家居角色
     */
    async getUserSmartHomeRole(userId) {
        try {
            // 这里应该从数据库或配置中获取用户的智能家居角色
            // 暂时使用模拟数据
            const roleMapping = {
                'admin_user_1': 'ADMIN',
                'designer_user_1': 'DESIGNER',
                'sales_user_1': 'SALES',
                'customer_user_1': 'OWNER'
            }
            
            return roleMapping[userId] || 'OWNER'
        } catch (error) {
            console.error('❌ 获取用户智能家居角色失败:', error)
            return null
        }
    }

    /**
     * 检查用户权限
     */
    hasPermission(userId, module, operation) {
        try {
            const userPermissions = this.permissions.get(userId)
            if (!userPermissions) {
                console.warn(`⚠️ 用户 ${userId} 没有权限配置`)
                return false
            }
            
            const modulePermissions = userPermissions[module]
            if (!modulePermissions) {
                console.warn(`⚠️ 用户 ${userId} 没有模块 ${module} 的权限`)
                return false
            }
            
            const hasPermission = modulePermissions.includes(operation)
            console.log(`🔍 权限检查: 用户${userId} ${module}:${operation} = ${hasPermission}`)
            
            return hasPermission
        } catch (error) {
            console.error('❌ 权限检查失败:', error)
            return false
        }
    }

    /**
     * 获取用户可访问的MedusaJS模块
     */
    getUserMedusaModules(userId) {
        try {
            const userPermissions = this.permissions.get(userId)
            if (!userPermissions) {
                return []
            }
            
            const medusaModules = []
            
            // 检查商城权限
            if (userPermissions.mall && userPermissions.mall.includes('VIEW')) {
                medusaModules.push('products', 'collections')
            }
            
            // 检查订单权限
            if (userPermissions.contract && userPermissions.contract.includes('VIEW')) {
                medusaModules.push('orders')
            }
            
            // 检查客户权限
            if (userPermissions.customer && userPermissions.customer.includes('VIEW')) {
                medusaModules.push('customers')
            }
            
            // 检查分析权限
            if (userPermissions.analysis && userPermissions.analysis.includes('VIEW')) {
                medusaModules.push('analytics')
            }
            
            return medusaModules
        } catch (error) {
            console.error('❌ 获取用户MedusaJS模块失败:', error)
            return []
        }
    }

    /**
     * 创建权限中间件
     */
    createPermissionMiddleware(requiredModule, requiredOperation) {
        return (req, res, next) => {
            try {
                const userId = req.user?.id
                if (!userId) {
                    return res.status(401).json({
                        error: 'Unauthorized',
                        message: '用户未认证'
                    })
                }
                
                const hasPermission = this.hasPermission(userId, requiredModule, requiredOperation)
                if (!hasPermission) {
                    return res.status(403).json({
                        error: 'Forbidden',
                        message: `缺少权限: ${requiredModule}:${requiredOperation}`
                    })
                }
                
                next()
            } catch (error) {
                console.error('❌ 权限中间件错误:', error)
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: '权限验证失败'
                })
            }
        }
    }

    /**
     * 同步用户角色到MedusaJS
     */
    async syncUserRoleToMedusa(userId, smartHomeRole) {
        try {
            const roleTemplate = this.roleTemplates[smartHomeRole]
            if (!roleTemplate) {
                throw new Error(`未知的智能家居角色: ${smartHomeRole}`)
            }
            
            // 更新MedusaJS用户角色
            const response = await fetch(`${this.medusaApiUrl}/admin/users/${userId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    role: roleTemplate.medusaRole,
                    metadata: {
                        smart_home_role: smartHomeRole,
                        smart_home_permissions: roleTemplate.permissions
                    }
                })
            })
            
            if (!response.ok) {
                throw new Error(`同步用户角色失败: ${response.status}`)
            }
            
            // 更新本地缓存
            this.userRoles.set(userId, smartHomeRole)
            this.permissions.set(userId, roleTemplate.permissions)
            
            console.log(`✅ 用户 ${userId} 角色同步成功: ${smartHomeRole}`)
            return true
        } catch (error) {
            console.error('❌ 同步用户角色到MedusaJS失败:', error)
            return false
        }
    }

    /**
     * 获取权限统计信息
     */
    getPermissionStats() {
        const stats = {
            totalUsers: this.permissions.size,
            roleDistribution: {},
            moduleUsage: {}
        }
        
        // 统计角色分布
        for (const role of this.userRoles.values()) {
            stats.roleDistribution[role] = (stats.roleDistribution[role] || 0) + 1
        }
        
        // 统计模块使用情况
        for (const permissions of this.permissions.values()) {
            for (const module of Object.keys(permissions)) {
                stats.moduleUsage[module] = (stats.moduleUsage[module] || 0) + 1
            }
        }
        
        return stats
    }
}

// 导出集成类
window.MedusaPermissionIntegration = MedusaPermissionIntegration

// 使用示例
/*
const integration = new MedusaPermissionIntegration({
    medusaApiUrl: 'http://localhost:9000',
    apiKey: 'your_medusa_api_key'
})

// 初始化
await integration.initialize()

// 检查权限
const canViewProducts = integration.hasPermission('user_123', 'mall', 'VIEW')

// 创建权限中间件
const requireDesignPermission = integration.createPermissionMiddleware('design', 'CREATE')

// 同步角色
await integration.syncUserRoleToMedusa('user_123', 'DESIGNER')
*/

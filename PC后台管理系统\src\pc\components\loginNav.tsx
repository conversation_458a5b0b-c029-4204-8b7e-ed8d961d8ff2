import React from 'react';

const LoginNav: React.FC = () => {
  return (
    <nav className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* 左侧 Logo */}
          <div className="flex items-center">
            <div className="text-xl font-semibold text-gray-900">
              🔥 智能设计与施工管理系统
            </div>
          </div>

          {/* 中间导航菜单 */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="/" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium border-b-2 border-orange-500">首页</a>
            <a href="/design" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">设计</a>
            <a href="/products" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">产品中心</a>
            <a href="/construction" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">施工管理</a>
            <a href="/service" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">服务中心</a>
          </div>

          {/* 右侧按钮 */}
          <div className="flex items-center space-x-4">
            <a
              href="https://github.com"
              className="text-gray-700 hover:text-gray-900 flex items-center space-x-1"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">40.1k</span>
            </a>
            <a
              href="/register"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              注册
            </a>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default LoginNav;

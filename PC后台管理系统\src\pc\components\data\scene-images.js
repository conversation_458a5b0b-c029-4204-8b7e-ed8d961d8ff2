// 智能家居场景图片数据
const sceneImages = {
    lighting: [
        {
            id: 'lighting_1',
            name: '客餐厅无主灯',
            price: 2999,
            description: '无主灯设计的筒灯、射灯、灯带、轨道灯组合，色温、亮度根据场景自动调节，无缝切换',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525205141684168.gif',
            fallbackImage: '💡'
        },
        {
            id: 'lighting_2',
            name: '客厅无主灯场景',
            price: 1999,
            description: '客厅无主灯系统的设计，通过情景面板或语音控制让客户在几个场景间互相切换，缓开缓灭，过度柔和',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/202204251652002727.gif',
            fallbackImage: '🔆'
        },
        {
            id: 'lighting_3',
            name: '卧室场景',
            price: 1599,
            description: '通过智能调光的组合，实现温馨、助眠、起夜、阅读等不同场景的变化，帮助主人缓解压力、放松休息',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/2022040211110243243.gif',
            fallbackImage: '🌙'
        },
        {
            id: 'lighting_4',
            name: '智能调光',
            price: 899,
            description: '易来Pro无主灯智能本地化调光，色温亮度随意掌控，灯光缓开缓灭，支持中控屏定制化操作',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525204471857185.gif',
            fallbackImage: '🎛️'
        }
    ],
    security: [
        {
            id: 'security_1',
            name: '空气质量智能显示',
            price: 1299,
            description: '传翔空气插件可直接运行于米家智能家居平板控制中心。在8寸或10寸的智能家居平板控制中心上显示家庭环境的空气质量指数',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207120151685168.gif',
            fallbackImage: '📺'
        },
        {
            id: 'security_2',
            name: '中控屏监控视窗',
            price: 899,
            description: '在选购了智能家居控制中心和摄像头的基础上，可以在中控屏指定位置直接显示监控实时画面',
            image: 'https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2021111621530337337.jpg',
            fallbackImage: '📹'
        },
        {
            id: 'security_3',
            name: '监控双向对讲',
            price: 599,
            description: '在无需打开APP，等待视频加载的情况下，立即通过屋外的摄像头，实时观看屋外画面，并与来访者发起实时对讲功能',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525210991359135.gif',
            fallbackImage: '🔌'
        },
        {
            id: 'security_4',
            name: '摄像头磁吸供电',
            price: 1599,
            description: '摄像头磁磁吸供电方案，无绳安装，支持5V2A标准的室内主流摄像头，3C安全认证',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20240816151678357835.jpg',
            fallbackImage: '👁️'
        }
    ],
    custom: [
        {
            id: 'custom_1',
            name: '面板个性化雕刻',
            price: 0,
            description: '通过在开关面板上雕刻图案和文字提示，告知家人及亲友开关的对应作用，让使用者没有未知感，从容操作',
            image: 'https://www.cx-smarthome.com:443/sysmain/home/<USER>/20210927152468956895.jpg',
            fallbackImage: '🎨'
        },
        {
            id: 'custom_2',
            name: '预回家场景',
            price: 0,
            description: '烈日炎炎，未到家，空调已经凉风习习；寒风凛冽，未到家，房间已经温暖如春；你还未回家，全屋智能已经张开怀抱',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190516162537523752.jpg',
            fallbackImage: '🏠'
        },
        {
            id: 'custom_3',
            name: '恒温恒湿',
            price: 0,
            description: '通过温湿度传感器、空调智能控制系统、加温器、除湿机等设备自动联动，让您的卧室处于恒温恒湿的舒适状态',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190311181833303330.jpg',
            fallbackImage: '🌡️'
        },
        {
            id: 'custom_4',
            name: '会客模式',
            price: 0,
            description: '用明亮的灯光，拉近主客间的距离，营造舒心的会客氛围',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816165411181118.gif',
            fallbackImage: '👥'
        },
        {
            id: 'custom_5',
            name: '夏冬场景',
            price: 0,
            description: '夏日清风冬日暖阳，智能调光定制场景让家中四季如春，亦可四季分明，带来舒适与放松',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220402102637333733.gif',
            fallbackImage: '🌤️'
        },
        {
            id: 'custom_6',
            name: '睡眠场景',
            price: 0,
            description: '当您晚上要睡觉的时候，安防系统会开启来保证您的睡眠安全；空调和空气净化器也被设置成不打扰您休息的睡眠模式',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222987728772.gif',
            fallbackImage: '🌙'
        },
        {
            id: 'custom_7',
            name: '中央空调智能化',
            price: 0,
            description: '支持多种品牌的中央空调智能化管理，接入米家平台以后，可以实现一键关闭所有空调语言控制某个区域的空调',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326105285718571.jpg',
            fallbackImage: '❄️'
        },
        {
            id: 'custom_8',
            name: '米家平板中控',
            price: 0,
            description: '平板磁吸上墙，开启米家中控模式秒变家庭中控屏，可以通过卡片直观地控制智能家居设备',
            image: 'https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20240326154048154815.jpg',
            fallbackImage: '📱'
        },
        {
            id: 'custom_9',
            name: '米家智能控制中心',
            price: 0,
            description: '智能家居控制中心，可单手拿取，配合无线智能墙充实现无线充电，内置米家的智能家居控制中心系统',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210907114591969196.gif',
            fallbackImage: '🎮'
        }
    ],
    auto: [
        {
            id: 'auto_1',
            name: '回家场景',
            price: 299,
            description: '使用指定指纹解锁可启动常用的回家场景，灯光逐个打开，窗帘打开，电视打开。预设场景可以根据您的需要来制作',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207113660186018.gif',
            fallbackImage: '🏠'
        },
        {
            id: 'auto_2',
            name: '离家场景',
            price: 299,
            description: '离家时只需一个动作，关闭家中所有灯光、窗帘、空调、景观台及其他电器；扫地机器人开始清扫；传感器进入布防模式',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326110565366536.jpg',
            fallbackImage: '🚪'
        },
        {
            id: 'auto_3',
            name: '起床场景',
            price: 199,
            description: '空调提前调到适合温度，灯光柔和舒适，窗帘缓缓开启，布防模式自动关闭，小爱同学播报今日的天气状况和新闻',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif',
            fallbackImage: '🌅'
        },
        {
            id: 'auto_4',
            name: '起夜场景（网关夜灯）',
            price: 199,
            description: '网关自带小夜灯，灯光颜色及亮度可随心调节。搭配人体传感器，在朦胧的夜晚，有人经过自动亮灯',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190311183756815681.jpg',
            fallbackImage: '🌃'
        },
        {
            id: 'auto_5',
            name: '夜起灯带场景',
            price: 299,
            description: '搭配人体传感器，在朦胧的夜晚，有人经过床底灯带氛围灯自动亮起，并且呈现为主人最喜欢的颜色和亮度',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926100211171117.jpg',
            fallbackImage: '💡'
        },
        {
            id: 'auto_6',
            name: '人在亮灯',
            price: 399,
            description: '通过易来PRO的P20毫米波雷达人在传感器，实现有人就亮灯，且人在空间内没有动作也会判断为有人，而不会熄灯',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210929120711761176.gif',
            fallbackImage: '🚶'
        },
        {
            id: 'auto_7',
            name: '监控多宫格实时显示',
            price: 599,
            description: '基于购买了"监控视窗"付费授权窗口使用权，并购买指定品牌摄像头，一页最多显示9个监控画面，并可实时播放',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816170425352535.gif',
            fallbackImage: '📹'
        }
    ],
    safety: [
        {
            id: 'safety_1',
            name: '预防天然气泄漏',
            price: 599,
            description: '厨房天然气泄漏预警守卫厨房安全。天然气浓度只需达到爆炸阈值的百分之4,就可以触发高分贝报警声',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326103963896389.jpg',
            fallbackImage: '⛽'
        },
        {
            id: 'safety_2',
            name: '火灾预警',
            price: 399,
            description: '家庭里一旦烟雾浓度过高，烟雾报警器立即发出刺耳的报警声，及时通知到您，并联动网关报警',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2020032610390637637.jpg',
            fallbackImage: '🚨'
        },
        {
            id: 'safety_3',
            name: '漏水自动断水',
            price: 899,
            description: '浸水感应器检测到漏水网关发出警报，并向手机推送警报通知。对应水路的进水阀门中断水路',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926112142764276.jpg',
            fallbackImage: '💧'
        },
        {
            id: 'safety_4',
            name: '智能猫眼',
            price: 299,
            description: '智能猫眼由门外广角摄像头主门内高清显示平板组成，使用统一的米家APP，正常使用可维持1-2个月',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816155029322932.jpg',
            fallbackImage: '🚰'
        }
    ],
    audio: [
        {
            id: 'audio_1',
            name: '一键KTV',
            price: 3999,
            description: '通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现KTV画面和声音一键切换到KTV模式',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200308131356585658.gif',
            fallbackImage: '🎤'
        },
        {
            id: 'audio_2',
            name: '一键观影',
            price: 2999,
            description: '通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现电视画面和声音一键切换到观影模式',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816164220722072.gif',
            fallbackImage: '📽️'
        },
        {
            id: 'audio_3',
            name: '小爱背景音乐',
            price: 1599,
            description: '小爱音箱语音点歌用无线开关远程切歌、开始播放、停止播放联动回家或离家场景可自动开启或停止音乐',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926094113671367.jpg',
            fallbackImage: '🎵'
        },
        {
            id: 'audio_4',
            name: '观影模式',
            price: 2599,
            description: '电视和影音的结合，足不出户享受电影院的视听震撼新体验。一键切换观景模式，灯光窗帘自动为您开启到最佳状态',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/2022081616430477477.gif',
            fallbackImage: '🔊'
        }
    ],
    ai: [
        {
            id: 'ai_1',
            name: '智能手表控制',
            price: 899,
            description: '通过小米智能手表控制全屋灯光、米家全系列智能家电、智能场景。苹果智能手表在安装了支持Homekit产品和风头后可控制全屋灯光',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif',
            fallbackImage: '⌚'
        },
        {
            id: 'ai_2',
            name: '语音控制',
            price: 599,
            description: '劳累了一天的你只需要躺在床上，一切的控制只需一句话。寂寞的夜，一切动作都显多余，你只要安静享受属于自己的美妙时光',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926095518211821.jpg',
            fallbackImage: '🗣️'
        },
        {
            id: 'ai_3',
            name: '米家平板中控',
            price: 1999,
            description: '平板磁吸上墙，开启米家中控模式秒变家庭中控屏，可以通过卡片直观地控制智能家居设备',
            image: 'https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20240326154048154815.jpg',
            fallbackImage: '📱'
        },
        {
            id: 'ai_4',
            name: '米家智能控制中心',
            price: 799,
            description: '智能家居控制中心，可单手拿取，配合无线智能墙充实现无线充电，内置米家的智能家居控制中心系统',
            image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210907114591969196.gif',
            fallbackImage: '🌬️'
        }
    ],
    hotel: [
        {
            id: 'hotel_1',
            name: '酒店AI语音控制',
            price: 1999,
            description: '劳累了一天的你只需要躺在床上，一切的控制只需一句话。房间内可实现语音控制窗帘开关、电视开关、电视节目内容搜索、灯光开关',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162498089808.jpg',
            fallbackImage: '🗣️'
        },
        {
            id: 'hotel_2',
            name: '回房场景',
            price: 1299,
            description: '住客通过门卡开门以后，门厅灯自动亮起，同时小爱同学提示请将房卡插入卡槽，插入以后启动回房间场景',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162316821682.jpg',
            fallbackImage: '🏨'
        },
        {
            id: 'hotel_3',
            name: '离房场景',
            price: 999,
            description: '当住客离开房间的时候，门厅的灯会为方便他换鞋而暂时的亮起，其它的灯会自动的熄灭；空调也会自动的关闭',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162329462946.jpg',
            fallbackImage: '🚪'
        },
        {
            id: 'hotel_4',
            name: '智能插卡模块',
            price: 599,
            description: '接入米家的智能插卡模块，解决插卡后启动回酒店场景，拔卡后关闭所有灯光和电器的作用',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317160937213721.jpg',
            fallbackImage: '💳'
        }
    ],
    business: [
        {
            id: 'business_1',
            name: '人体运动监测',
            price: 1599,
            description: '公司重要位置及每个办公室通过人体运动传感器，让您掌握公司内下班无人后的安全动态，异常情况回查',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323223031353135.jpg',
            fallbackImage: '🚶'
        },
        {
            id: 'business_2',
            name: '一键上下班场景',
            price: 2999,
            description: '一键上下班可以节约最后离开的员工整理时间，可以有效保障公司的用电安全。一键下班：关闭所有中央空调内机',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323220525172517.jpg',
            fallbackImage: '🏢'
        },
        {
            id: 'business_3',
            name: '会议投影场景',
            price: 3999,
            description: '一键开会，自动完成以下动作：关闭会议室主灯、将投影幕布展开到适当位置、关闭窗帘、打开会议桌的氛围灯、打开投影仪',
            image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2019032321560442442.jpg',
            fallbackImage: '📊'
        },
        {
            id: 'business_4',
            name: '商业照明场景',
            price: 2599,
            description: '智能场景随心切换，每个区域设定不同场景，让照明随场景不同而随心切换。商照全系0.1W超低待机功耗',
            image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220414141238523852.gif',
            fallbackImage: '💡'
        }
    ]
};

// 导出数据
if (typeof module !== 'undefined' && module.exports) {
    module.exports = sceneImages;
}

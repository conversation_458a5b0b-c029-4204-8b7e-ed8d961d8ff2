/**
 * 批量修复导航菜单路径
 * 确保所有页面的导航菜单使用正确的相对路径
 */

const fs = require('fs');
const path = require('path');

// 需要修复的页面列表
const pagesToFix = [
    '05-tools/internal-permissions.html',
    '05-tools/system-settings.html',
    '05-tools/user-management.html'
];

// 路径修复映射
const pathFixes = [
    // 个人中心
    { from: 'href="my-todos.html"', to: 'href="../01-personal/my-todos.html"' },
    { from: 'href="my-orders.html"', to: 'href="../01-personal/my-orders.html"' },
    
    // 业务管理
    { from: 'href="design-products.html"', to: 'href="../02-business/design-products.html"' },
    { from: 'href="requirements-management.html"', to: 'href="../02-business/requirements-management.html"' },
    { from: 'href="design-center.html"', to: 'href="../02-business/design-center.html"' },
    { from: 'href="design-cases.html"', to: 'href="../02-business/design-cases.html"' },
    { from: 'href="project-center.html"', to: 'href="../02-business/project-center.html"' },
    { from: 'href="construction-management.html"', to: 'href="../02-business/construction-management.html"' },
    { from: 'href="construction-guide.html"', to: 'href="../02-business/construction-guide.html"' },
    
    // 商务中心
    { from: 'href="products.html"', to: 'href="../03-commerce/products.html"' },
    { from: 'href="orders.html"', to: 'href="../03-commerce/orders.html"' },
    { from: 'href="customer-management.html"', to: 'href="../03-commerce/customer-management.html"' },
    { from: 'href="marketing-management.html"', to: 'href="../03-commerce/marketing-management.html"' },
    
    // 知识库
    { from: 'href="design-knowledge.html"', to: 'href="../04-knowledge/design-knowledge.html"' },
    { from: 'href="delivery-knowledge.html"', to: 'href="../04-knowledge/delivery-knowledge.html"' },
    { from: 'href="wiring-knowledge.html"', to: 'href="../04-knowledge/wiring-knowledge.html"' },
    { from: 'href="installation-knowledge.html"', to: 'href="../04-knowledge/installation-knowledge.html"' },
    { from: 'href="debugging-knowledge.html"', to: 'href="../04-knowledge/debugging-knowledge.html"' },
    { from: 'href="product-knowledge.html"', to: 'href="../04-knowledge/product-knowledge.html"' },
    
    // 数据分析
    { from: 'href="requirements-analytics.html"', to: 'href="../06-analytics/requirements-analytics.html"' },
    { from: 'href="project-analytics.html"', to: 'href="../06-analytics/project-analytics.html"' },
    { from: 'href="order-analytics.html"', to: 'href="../06-analytics/order-analytics.html"' },
    { from: 'href="customer-analytics.html"', to: 'href="../06-analytics/customer-analytics.html"' },
    
    // 个人中心
    { from: 'href="demo.html"', to: 'href="../07-profile/demo.html"' },
    { from: 'href="user-profile.html"', to: 'href="../07-profile/user-profile.html"' },
    { from: 'href="logout.html"', to: 'href="../07-profile/logout.html"' }
];

function fixPage(filePath) {
    const fullPath = path.join('../src/pc/components/pages', filePath);
    
    if (!fs.existsSync(fullPath)) {
        console.log(`⚠️  文件不存在: ${filePath}`);
        return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;
    
    // 应用所有路径修复
    pathFixes.forEach(fix => {
        if (content.includes(fix.from)) {
            content = content.replace(new RegExp(fix.from, 'g'), fix.to);
            modified = true;
        }
    });
    
    // 保存文件
    if (modified) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ 修复路径: ${filePath}`);
        return true;
    }
    
    console.log(`⏭️  跳过: ${filePath} (无需修复)`);
    return false;
}

function main() {
    console.log('🚀 开始批量修复导航菜单路径...\n');
    
    let fixedCount = 0;
    let totalCount = 0;
    
    pagesToFix.forEach(filePath => {
        totalCount++;
        if (fixPage(filePath)) {
            fixedCount++;
        }
    });
    
    console.log(`\n📊 路径修复完成:`);
    console.log(`   - 总文件数: ${totalCount}`);
    console.log(`   - 已修复: ${fixedCount}`);
    console.log(`   - 跳过: ${totalCount - fixedCount}`);
    
    if (fixedCount > 0) {
        console.log('\n✨ 所有页面现在都使用正确的导航路径');
        console.log('🎯 菜单链接可以正确跳转到对应页面');
        console.log('🎨 统一的导航体验');
    }
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { fixPage, main };

/**
 * 更新侧边栏样式脚本
 * 1. 隐藏滚动条
 * 2. 统一菜单字体大小和间距
 */

const fs = require('fs');
const path = require('path');

// 标准的侧边栏样式（黑白灰配色 + 200px宽度 + 隐藏滚动条）
const standardSidebarStyles = `        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }`;

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 更新单个文件的侧边栏样式
function updateSidebarStyles(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找并替换侧边栏样式块
        const sidebarPattern = /\/\*\s*侧边栏样式\s*\*\/[\s\S]*?(?=\/\*|<\/style>|$)/;

        if (sidebarPattern.test(content)) {
            content = content.replace(sidebarPattern, standardSidebarStyles);

            // 修复主内容区域的左边距（从240px改为200px）
            content = content.replace(/margin-left:\s*240px/g, 'margin-left: 200px');

            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已更新: ${fileName}`);
            return true;
        } else {
            // 如果没有找到侧边栏样式，尝试查找 .sidebar 样式
            const sidebarClassPattern = /\.sidebar\s*\{[^}]*\}/g;
            if (sidebarClassPattern.test(content)) {
                // 替换现有的 .sidebar 样式
                content = content.replace(sidebarClassPattern, '');

                // 修复主内容区域的左边距
                content = content.replace(/margin-left:\s*240px/g, 'margin-left: 200px');

                // 在 </style> 前添加新样式
                const styleEndPattern = /<\/style>/;
                content = content.replace(styleEndPattern, `
${standardSidebarStyles}
    </style>`);

                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`✅ 已更新: ${fileName}`);
                return true;
            } else {
                console.log(`⏭️  跳过: ${fileName} (未找到侧边栏样式)`);
                return false;
            }
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🎨 开始更新侧边栏样式...\n');
    console.log('📋 更新内容:');
    console.log('   - 隐藏滚动条（保持滚动功能）');
    console.log('   - 统一菜单字体大小：13px');
    console.log('   - 统一行间距：1.3');
    console.log('   - 统一内边距：8px 20px');
    console.log('   - 优化Logo和标题样式\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    for (const file of htmlFiles) {
        if (updateSidebarStyles(file)) {
            successCount++;
        } else {
            failCount++;
        }
    }
    
    console.log('\n📊 更新统计:');
    console.log(`✅ 成功更新: ${successCount} 个文件`);
    console.log(`⏭️  跳过文件: ${failCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 侧边栏样式更新完成！');
        console.log('👻 滚动条已隐藏（功能保留）');
        console.log('📏 字体和间距已统一');
        console.log('✨ 界面更加简洁美观');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    updateSidebarStyles,
    main
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居产品管理系统 - 集成版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e2e8f0;
            flex-shrink: 0;
        }

        .main-content {
            flex: 1;
            padding: 24px;
        }

        .page-header {
            background: white;
            padding: 24px 32px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            border-left: 4px solid #3182ce;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .page-description {
            color: #718096;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #3182ce;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 4px;
        }

        .stat-increase {
            color: #38a169;
        }

        .stat-decrease {
            color: #e53e3e;
        }

        .toolbar {
            background: white;
            padding: 20px 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .search-section {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-input {
            padding: 10px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            width: 320px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .filter-select {
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
        }

        .btn-primary:hover {
            background: #2c5aa0;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #38a169;
            color: white;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .btn-warning {
            background: #d69e2e;
            color: white;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: 1px solid #e2e8f0;
        }

        .product-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-4px);
        }

        .product-image {
            height: 220px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .product-image:hover img {
            transform: scale(1.05);
        }

        .product-placeholder {
            color: #a0aec0;
            font-size: 64px;
        }

        .product-info {
            padding: 24px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .product-details {
            color: #718096;
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .product-price {
            font-size: 24px;
            font-weight: 700;
            color: #e53e3e;
            margin-bottom: 16px;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #4a5568;
            margin-bottom: 20px;
        }

        .stock-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stock-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .stock-high {
            background: #38a169;
        }

        .stock-medium {
            background: #d69e2e;
        }

        .stock-low {
            background: #e53e3e;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .product-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 32px;
            border-radius: 12px;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #a0aec0;
            cursor: pointer;
            line-height: 1;
        }

        .form-grid {
            display: grid;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2d3748;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .form-textarea {
            height: 120px;
            resize: vertical;
        }

        .image-upload-section {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 16px;
        }

        .image-upload-section:hover {
            border-color: #3182ce;
            background: rgba(49, 130, 206, 0.05);
        }

        .upload-icon {
            font-size: 48px;
            color: #cbd5e0;
            margin-bottom: 16px;
        }

        .upload-text {
            color: #718096;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .upload-hint {
            color: #a0aec0;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #718096;
            grid-column: 1 / -1;
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 24px;
            opacity: 0.5;
        }

        .loading {
            display: none;
            justify-content: center;
            align-items: center;
            padding: 60px;
            grid-column: 1 / -1;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #3182ce;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .sidebar-nav {
            padding: 24px 0;
        }

        .nav-item {
            display: block;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 4px solid transparent;
        }

        .nav-item:hover {
            background: #f7fafc;
            color: #2d3748;
            border-left-color: #3182ce;
        }

        .nav-item.active {
            background: #ebf8ff;
            color: #3182ce;
            border-left-color: #3182ce;
            font-weight: 500;
        }

        @media (max-width: 1024px) {
            .layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-section {
                flex-wrap: wrap;
            }
            
            .search-input {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 16px;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <nav class="sidebar-nav">
                <a href="#" class="nav-item active" onclick="showSection('products')">产品管理</a>
                <a href="#" class="nav-item" onclick="showSection('inventory')">库存管理</a>
                <a href="#" class="nav-item" onclick="showSection('analytics')">数据分析</a>
                <a href="#" class="nav-item" onclick="showSection('settings')">系统设置</a>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">智能家居产品管理系统</h1>
                <p class="page-description">全面管理Aqara、Yeelight等品牌产品，实现产品上架、库存控制、数据分析等功能</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalProducts">0</div>
                    <div class="stat-label">总产品数</div>
                    <div class="stat-change stat-increase" id="totalChange">+0 本月</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="onlineProducts">0</div>
                    <div class="stat-label">已上架产品</div>
                    <div class="stat-change stat-increase" id="onlineChange">+0 本月</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalValue">¥0</div>
                    <div class="stat-label">库存总价值</div>
                    <div class="stat-change stat-increase" id="valueChange">+0% 环比</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="lowStockCount">0</div>
                    <div class="stat-label">低库存预警</div>
                    <div class="stat-change stat-decrease" id="lowStockChange">-0 本月</div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="search-section">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索产品名称、SKU或品牌...">
                    <select class="filter-select" id="brandFilter">
                        <option value="">所有品牌</option>
                        <option value="Aqara">Aqara</option>
                        <option value="Yeelight">Yeelight</option>
                        <option value="Xiaomi">小米</option>
                        <option value="Philips">飞利浦</option>
                    </select>
                    <select class="filter-select" id="categoryFilter">
                        <option value="">所有分类</option>
                        <option value="传感器">传感器</option>
                        <option value="照明设备">照明设备</option>
                        <option value="开关面板">开关面板</option>
                        <option value="智能插座">智能插座</option>
                        <option value="安防设备">安防设备</option>
                        <option value="网关设备">网关设备</option>
                    </select>
                    <select class="filter-select" id="statusFilter">
                        <option value="">所有状态</option>
                        <option value="online">已上架</option>
                        <option value="offline">已下架</option>
                        <option value="pending">待审核</option>
                    </select>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="openAddProductModal()">
                        <span>+</span> 添加产品
                    </button>
                    <button class="btn btn-success" onclick="importProducts()">导入数据</button>
                    <button class="btn btn-warning" onclick="exportProducts()">导出数据</button>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingState">
                <div class="spinner"></div>
            </div>

            <!-- 产品网格 -->
            <div class="product-grid" id="productGrid">
                <!-- 产品卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">添加产品</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="productForm" class="form-grid">
                <div class="form-group">
                    <label class="form-label">产品名称 *</label>
                    <input type="text" class="form-control" id="productName" required placeholder="请输入产品名称">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">产品SKU *</label>
                        <input type="text" class="form-control" id="productSku" required placeholder="产品唯一编码">
                    </div>
                    <div class="form-group">
                        <label class="form-label">品牌 *</label>
                        <select class="form-control" id="productBrand" required>
                            <option value="">选择品牌</option>
                            <option value="Aqara">Aqara</option>
                            <option value="Yeelight">Yeelight</option>
                            <option value="Xiaomi">小米</option>
                            <option value="Philips">飞利浦</option>
                            <option value="其他">其他品牌</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">产品分类 *</label>
                        <select class="form-control" id="productCategory" required>
                            <option value="">选择分类</option>
                            <option value="传感器">传感器</option>
                            <option value="照明设备">照明设备</option>
                            <option value="开关面板">开关面板</option>
                            <option value="智能插座">智能插座</option>
                            <option value="安防设备">安防设备</option>
                            <option value="网关设备">网关设备</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">产品状态</label>
                        <select class="form-control" id="productStatus">
                            <option value="pending">待审核</option>
                            <option value="online">已上架</option>
                            <option value="offline">已下架</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">售价 (¥) *</label>
                        <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label class="form-label">库存数量 *</label>
                        <input type="number" class="form-control" id="productStock" min="0" required placeholder="0">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品描述</label>
                    <textarea class="form-control form-textarea" id="productDescription" placeholder="请输入产品详细描述，包括功能特性、技术参数等..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品标签（用逗号分隔）</label>
                    <input type="text" class="form-control" id="productTags" placeholder="智能家居, 传感器, Zigbee">
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品图片</label>
                    <div class="image-upload-section" onclick="document.getElementById('imageInput').click()">
                        <input type="file" id="imageInput" style="display:none" multiple accept="image/*">
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">点击上传产品图片</div>
                        <div class="upload-hint">支持JPG、PNG、WebP格式，建议尺寸800x600px，最大5MB</div>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #e2e8f0; color: #4a5568; margin-right: 12px;">取消</button>
                    <button type="submit" class="btn btn-primary">保存产品</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let products = [];
        let filteredProducts = [];
        let currentEditingId = null;
        let isLoading = false;

        // 初始化真实产品数据
        const initialProducts = [
            {
                id: 1,
                name: 'Aqara人体传感器',
                sku: 'RTCGQ11LM',
                brand: 'Aqara',
                category: '传感器',
                price: 89.00,
                stock: 150,
                status: 'online',
                description: '高精度人体移动检测，支持光照度检测，超低功耗设计，可与Aqara网关配合使用。检测角度170°，检测距离7米，电池续航2年。',
                tags: ['智能家居', '传感器', '安防', 'Zigbee'],
                images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/motion_sensor_p3/4.webp'],
                createTime: '2024-01-15',
                updateTime: '2024-01-20'
            },
            {
                id: 2,
                name: 'Aqara温湿度传感器',
                sku: 'WSDCGQ11LM',
                brand: 'Aqara',
                category: '传感器',
                price: 69.00,
                stock: 200,
                status: 'online',
                description: '瑞士进口传感器芯片，±0.3°C温度精度，±3%RH湿度精度，实时监测环境温湿度变化，支持历史数据查看。',
                tags: ['智能家居', '传感器', '环境监测', 'Zigbee'],
                images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/temperature_humidity_sensor/1.webp'],
                createTime: '2024-01-16',
                updateTime: '2024-01-21'
            },
            {
                id: 3,
                name: 'Yeelight LED彩光灯泡',
                sku: 'YLDP06YL',
                brand: 'Yeelight',
                category: '照明设备',
                price: 79.00,
                stock: 120,
                status: 'online',
                description: 'Wi-Fi智能彩光灯泡，1600万种颜色可调，亮度无级调节，支持语音控制，可设置定时开关和场景模式。',
                tags: ['智能照明', '彩光灯泡', 'Wi-Fi', '语音控制'],
                images: ['https://cdn.yeelight.com/assets/uploads/2021/01/YLDP06YL_01.jpg'],
                createTime: '2024-01-17',
                updateTime: '2024-01-22'
            },
            {
                id: 4,
                name: 'Aqara智能墙壁开关（单火版）',
                sku: 'QBKG04LM',
                brand: 'Aqara',
                category: '开关面板',
                price: 129.00,
                stock: 80,
                status: 'online',
                description: '86型单火线智能开关，无需零线，支持传统灯具改造，具备过载保护和记忆功能，可远程控制和定时开关。',
                tags: ['智能开关', '单火版', 'Zigbee', '86型'],
                images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/wall_switch_h1_single_rocker/1.webp'],
                createTime: '2024-01-18',
                updateTime: '2024-01-23'
            },
            {
                id: 5,
                name: 'Aqara门窗传感器',
                sku: 'MCCGQ11LM',
                brand: 'Aqara',
                category: '传感器',
                price: 49.00,
                stock: 180,
                status: 'online',
                description: '超小型无线门窗传感器，实时监测门窗开关状态，支持安防模式，可联动其他设备，安装简便。',
                tags: ['门窗传感器', '安防', 'Zigbee', '小型'],
                images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/door_window_sensor/1.webp'],
                createTime: '2024-01-19',
                updateTime: '2024-01-24'
            },
            {
                id: 6,
                name: 'Aqara智能插座',
                sku: 'SP-EUC01',
                brand: 'Aqara',
                category: '智能插座',
                price: 99.00,
                stock: 90,
                status: 'online',
                description: 'Wi-Fi智能插座，支持远程开关控制，实时功耗监测，过载保护，兼容主流智能音箱，可设置定时任务。',
                tags: ['智能插座', 'Wi-Fi', '功耗监测', '过载保护'],
                images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/smart_plug_eu/1.webp'],
                createTime: '2024-01-20',
                updateTime: '2024-01-25'
            }
        ];

        // 初始化应用
        function initApp() {
            products = [...initialProducts];
            filteredProducts = [...products];
            updateStats();
            renderProducts();
            setupEventListeners();
        }

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', debounce(filterProducts, 300));
            document.getElementById('brandFilter').addEventListener('change', filterProducts);
            document.getElementById('categoryFilter').addEventListener('change', filterProducts);
            document.getElementById('statusFilter').addEventListener('change', filterProducts);

            // 模态框外部点击关闭
            window.addEventListener('click', (e) => {
                const modal = document.getElementById('productModal');
                if (e.target === modal) {
                    closeModal();
                }
            });

            // 表单提交
            document.getElementById('productForm').addEventListener('submit', handleFormSubmit);
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('productGrid').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('productGrid').style.display = 'grid';
        }

        // 更新统计数据
        function updateStats() {
            const total = products.length;
            const online = products.filter(p => p.status === 'online').length;
            const totalValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);
            const lowStock = products.filter(p => p.stock < 20).length;

            document.getElementById('totalProducts').textContent = total;
            document.getElementById('onlineProducts').textContent = online;
            document.getElementById('totalValue').textContent = `¥${totalValue.toLocaleString()}`;
            document.getElementById('lowStockCount').textContent = lowStock;

            // 更新趋势数据（模拟）
            document.getElementById('totalChange').textContent = `+${Math.floor(total * 0.1)} 本月`;
            document.getElementById('onlineChange').textContent = `+${Math.floor(online * 0.05)} 本月`;
            document.getElementById('valueChange').textContent = `+12.3% 环比`;
            document.getElementById('lowStockChange').textContent = `-${Math.floor(lowStock * 0.2)} 本月`;
        }

        // 过滤产品
        function filterProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const brandFilter = document.getElementById('brandFilter').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredProducts = products.filter(product => {
                const matchesSearch = !searchTerm || 
                    product.name.toLowerCase().includes(searchTerm) ||
                    product.sku.toLowerCase().includes(searchTerm) ||
                    product.brand.toLowerCase().includes(searchTerm) ||
                    (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)));

                const matchesBrand = !brandFilter || product.brand === brandFilter;
                const matchesCategory = !categoryFilter || product.category === categoryFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesBrand && matchesCategory && matchesStatus;
            });

            renderProducts();
        }

        // 渲染产品列表
        function renderProducts() {
            const grid = document.getElementById('productGrid');
            
            if (filteredProducts.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📦</div>
                        <h3>暂无产品</h3>
                        <p>没有找到符合条件的产品，请调整搜索条件或添加新产品</p>
                        <button class="btn btn-primary" onclick="openAddProductModal()" style="margin-top: 20px;">
                            添加第一个产品
                        </button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredProducts.map(product => {
                const stockLevel = getStockLevel(product.stock);
                const statusText = getStatusText(product.status);
                
                return `
                    <div class="product-card">
                        <div class="product-image">
                            ${product.images && product.images.length > 0 ? 
                                `<img src="${product.images[0]}" alt="${product.name}" onerror="this.parentElement.innerHTML='<div class=\\"product-placeholder\\">📷</div>'">` :
                                '<div class="product-placeholder">📷</div>'
                            }
                        </div>
                        <div class="product-info">
                            <div class="product-name">${product.name}</div>
                            <div class="product-details">
                                品牌: ${product.brand} | SKU: ${product.sku}<br>
                                分类: ${product.category}
                                ${product.tags ? ` | 标签: ${product.tags.slice(0, 2).join(', ')}` : ''}
                            </div>
                            <div class="product-price">¥${product.price.toFixed(2)}</div>
                            <div class="product-meta">
                                <div class="stock-info">
                                    <span class="stock-indicator ${stockLevel}"></span>
                                    库存: ${product.stock} 件
                                </div>
                                <span class="product-status status-${product.status}">
                                    ${statusText}
                                </span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-sm btn-warning" onclick="editProduct(${product.id})">
                                    编辑
                                </button>
                                <button class="btn btn-sm ${product.status === 'online' ? 'btn-danger' : 'btn-success'}" 
                                        onclick="toggleProductStatus(${product.id})">
                                    ${product.status === 'online' ? '下架' : '上架'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取库存水平样式
        function getStockLevel(stock) {
            if (stock >= 50) return 'stock-high';
            if (stock >= 20) return 'stock-medium';
            return 'stock-low';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'online': '已上架',
                'offline': '已下架',
                'pending': '待审核'
            };
            return statusMap[status] || status;
        }

        // 打开添加产品模态框
        function openAddProductModal() {
            currentEditingId = null;
            document.getElementById('modalTitle').textContent = '添加产品';
            document.getElementById('productForm').reset();
            document.getElementById('productModal').style.display = 'block';
        }

        // 编辑产品
        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            currentEditingId = id;
            document.getElementById('modalTitle').textContent = '编辑产品';
            
            document.getElementById('productName').value = product.name;
            document.getElementById('productSku').value = product.sku;
            document.getElementById('productBrand').value = product.brand;
            document.getElementById('productCategory').value = product.category;
            document.getElementById('productStatus').value = product.status;
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productStock').value = product.stock;
            document.getElementById('productDescription').value = product.description || '';
            document.getElementById('productTags').value = product.tags ? product.tags.join(', ') : '';

            document.getElementById('productModal').style.display = 'block';
        }

        // 切换产品状态
        function toggleProductStatus(id) {
            const productIndex = products.findIndex(p => p.id === id);
            if (productIndex === -1) return;

            const product = products[productIndex];
            product.status = product.status === 'online' ? 'offline' : 'online';
            product.updateTime = new Date().toISOString().split('T')[0];

            updateStats();
            filterProducts();
            
            // 显示成功消息
            showToast(`${product.name} 已${product.status === 'online' ? '上架' : '下架'}`, 'success');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        // 处理表单提交
        function handleFormSubmit(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('productName').value,
                sku: document.getElementById('productSku').value,
                brand: document.getElementById('productBrand').value,
                category: document.getElementById('productCategory').value,
                status: document.getElementById('productStatus').value,
                price: parseFloat(document.getElementById('productPrice').value),
                stock: parseInt(document.getElementById('productStock').value),
                description: document.getElementById('productDescription').value,
                tags: document.getElementById('productTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                images: [] // 实际项目中这里会处理图片上传
            };

            if (currentEditingId) {
                // 编辑现有产品
                const productIndex = products.findIndex(p => p.id === currentEditingId);
                if (productIndex !== -1) {
                    products[productIndex] = {
                        ...products[productIndex],
                        ...formData,
                        updateTime: new Date().toISOString().split('T')[0]
                    };
                    showToast('产品更新成功', 'success');
                }
            } else {
                // 检查SKU是否已存在
                if (products.some(p => p.sku === formData.sku)) {
                    showToast('SKU已存在，请使用其他SKU', 'error');
                    return;
                }

                // 添加新产品
                const newProduct = {
                    id: Math.max(...products.map(p => p.id), 0) + 1,
                    ...formData,
                    createTime: new Date().toISOString().split('T')[0],
                    updateTime: new Date().toISOString().split('T')[0]
                };
                products.push(newProduct);
                showToast('产品添加成功', 'success');
            }

            closeModal();
            updateStats();
            filterProducts();
        }

        // 导入产品
        function importProducts() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const importedData = JSON.parse(e.target.result);
                            if (Array.isArray(importedData)) {
                                products = [...products, ...importedData];
                                updateStats();
                                filterProducts();
                                showToast(`成功导入 ${importedData.length} 个产品`, 'success');
                            } else {
                                showToast('文件格式不正确', 'error');
                            }
                        } catch (error) {
                            showToast('文件解析失败', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 导出产品
        function exportProducts() {
            const dataStr = JSON.stringify(products, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `products_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            showToast('产品数据导出成功', 'success');
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 这里可以实现一个更好看的Toast组件
            alert(message);
        }

        // 显示不同的页面部分
        function showSection(section) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以实现不同页面部分的显示逻辑
            if (section === 'products') {
                // 显示产品管理页面（当前页面）
                console.log('显示产品管理页面');
            } else {
                console.log(`切换到 ${section} 页面`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initApp();
            console.log('智能家居产品管理系统初始化完成');
        });
    </script>
</body>
</html>
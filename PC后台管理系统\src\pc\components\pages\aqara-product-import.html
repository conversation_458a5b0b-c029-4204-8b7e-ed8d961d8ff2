<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品导入 - 智能家居管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="../../../styles/unified-admin-styles.css">
</head>
<body>
    <div class="admin-layout">
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">Aqara产品导入</h1>
                            <p class="breadcrumb-description">Aqara产品数据导入工具</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1" id="pageTitle"><i class="fas fa-upload me-2"></i>产品导入</h1>
                <p class="text-muted" id="pageDescription">批量导入智能家居产品数据到系统中</p>
            </div>
            <div>
                <button class="btn btn-outline-secondary me-2" onclick="downloadTemplate()">
                    <i class="fas fa-download me-1"></i>下载模板
                </button>
                <button class="btn btn-info" onclick="showHelp()">
                    <i class="fas fa-question-circle me-1"></i>帮助
                </button>
            </div>
        </div>

        <!-- 导入方式选择 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>导入方式选择</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="importType" id="importTypeFolder" value="folder" checked>
                                    <label class="form-check-label" for="importTypeFolder">
                                        <strong>文档目录导入</strong><br>
                                        <small class="text-muted" id="folderImportDesc">从本地产品文档目录批量导入</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="importType" id="importTypeCsv" value="csv">
                                    <label class="form-check-label" for="importTypeCsv">
                                        <strong>CSV文件导入</strong><br>
                                        <small class="text-muted">上传CSV格式的产品数据文件</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文档目录导入 -->
        <div id="folderImportSection" class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Aqara产品文档导入</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="docsPath" class="form-label" id="pathLabel">产品文档路径</label>
                            <input type="text" class="form-control" id="docsPath" 
                                   value=""
                                   placeholder="请输入产品文档的完整路径" id="pathPlaceholder">
                            <div class="form-text">默认路径已填入，请根据实际情况修改</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-info w-100" onclick="previewImport()">
                                    <i class="fas fa-eye me-1"></i>预览数据
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-success w-100" onclick="startImport()">
                                    <i class="fas fa-upload me-1"></i>开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV文件导入 -->
        <div id="csvImportSection" class="row mb-4" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-file-csv me-2"></i>CSV文件导入</h5>
                    </div>
                    <div class="card-body">
                        <div class="file-drop-zone" id="fileDropZone" onclick="document.getElementById('csvFile').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>点击选择或拖拽CSV文件到此处</h5>
                            <p class="text-muted">支持的格式：.csv<br>最大文件大小：10MB</p>
                            <input type="file" id="csvFile" accept=".csv" style="display: none;" onchange="handleFileSelect(event)">
                        </div>
                        <div id="selectedFileInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file-csv me-2"></i>
                                <span id="fileName"></span>
                                <span id="fileSize" class="text-muted ms-2"></span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success w-100" id="uploadCsvBtn" onclick="uploadCsv()" disabled>
                                <i class="fas fa-upload me-1"></i>上传CSV文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览结果 -->
        <div id="previewSection" style="display: none;">
            <div class="preview-card">
                <h5><i class="fas fa-chart-bar me-2"></i>数据预览</h5>
                <div class="row" id="previewStats"></div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">产品列表预览</h5>
                </div>
                <div class="card-body">
                    <div id="previewContent"></div>
                </div>
            </div>
        </div>

        <!-- 进度显示 -->
        <div class="progress-container" id="progressContainer">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>导入进度</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" id="progressBar" style="width: 0%"></div>
                    </div>
                    <div id="progressText" class="text-center mb-3">准备开始...</div>
                    <div class="log-container" id="logContainer"></div>
                </div>
            </div>
        </div>

        <!-- 结果显示 -->
        <div id="resultSection" class="mt-4" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>导入结果</h5>
                </div>
                <div class="card-body" id="resultContent">
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-question-circle me-2"></i>导入帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>文档目录导入说明：</h6>
                    <ul>
                        <li>确保产品文档按照标准格式组织（分类/产品名称/概览|产品参数|产品支持）</li>
                        <li>系统会自动提取产品名称、描述、技术参数和图片</li>
                        <li>价格将根据产品类型和规格进行估算</li>
                        <li>支持的分类：传感监测、开关插座、智能门锁、智能网关等</li>
                    </ul>
                    
                    <h6>CSV导入说明：</h6>
                    <ul>
                        <li>下载模板文件填写产品信息</li>
                        <li>必填字段：产品名称、分类、价格</li>
                        <li>可选字段：描述、规格参数、库存数量</li>
                        <li>文件大小不超过10MB</li>
                    </ul>
                    
                    <h6>注意事项：</h6>
                    <ul>
                        <li>导入前建议先预览数据确认无误</li>
                        <li>大批量导入可能需要较长时间，请耐心等待</li>
                        <li>导入过程中请勿关闭浏览器</li>
                        <li>如有重复产品将自动跳过</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        let selectedFile = null;

        // 切换导入方式
        document.querySelectorAll('input[name="importType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'folder') {
                    document.getElementById('folderImportSection').style.display = 'block';
                    document.getElementById('csvImportSection').style.display = 'none';
                } else {
                    document.getElementById('folderImportSection').style.display = 'none';
                    document.getElementById('csvImportSection').style.display = 'block';
                }
                hideResults();
            });
        });

        // 文件拖拽处理
        const dropZone = document.getElementById('fileDropZone');
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择处理
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                showToast('请选择CSV格式文件', 'warning');
                return;
            }
            
            if (file.size > 10 * 1024 * 1024) {
                showToast('文件大小不能超过10MB', 'warning');
                return;
            }
            
            selectedFile = file;
            
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = `(${(file.size / 1024).toFixed(1)} KB)`;
            document.getElementById('selectedFileInfo').style.display = 'block';
            document.getElementById('uploadCsvBtn').disabled = false;
        }

        // 预览导入数据
        async function previewImport() {
            const docsPath = document.getElementById('docsPath').value.trim();
            
            if (!docsPath) {
                showToast('请输入文档路径', 'warning');
                return;
            }

            showProgress('正在扫描产品文档...');

            // 获取产品类型
            const urlParams = new URLSearchParams(window.location.search);
            const productType = urlParams.get('type') || 'aqara';

            try {
                const response = await fetch('/api/products/import/preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        docsPath: docsPath,
                        productType: productType,
                        limit: 10
                    })
                });

                const result = await response.json();
                hideProgress();

                if (result.success) {
                    showPreview(result.data);
                } else {
                    showToast('预览失败: ' + result.message, 'error');
                }
            } catch (error) {
                hideProgress();
                showToast('预览过程中发生错误: ' + error.message, 'error');
            }
        }

        // 显示预览结果
        function showPreview(data) {
            const { preview, statistics } = data;
            
            // 显示统计信息
            const statsHtml = `
                <div class="col-md-3">
                    <div class="stat-number">${statistics.totalProducts}</div>
                    <div class="stat-label">总产品数</div>
                </div>
                <div class="col-md-3">
                    <div class="stat-number">${Object.keys(statistics.categories).length}</div>
                    <div class="stat-label">产品分类</div>
                </div>
                <div class="col-md-3">
                    <div class="stat-number">${statistics.hasImages}</div>
                    <div class="stat-label">有图片</div>
                </div>
                <div class="col-md-3">
                    <div class="stat-number">¥${statistics.estimatedTotalValue.toLocaleString()}</div>
                    <div class="stat-label">估算总价值</div>
                </div>
            `;
            document.getElementById('previewStats').innerHTML = statsHtml;

            // 显示分类标签
            const categoriesHtml = Object.entries(statistics.categories)
                .map(([category, count]) => `<span class="category-tag">${category} (${count})</span>`)
                .join('');

            // 显示产品列表
            const productsHtml = preview.map(product => `
                <div class="card mb-2">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small mb-1">${product.category} | ¥${product.estimatedPrice}</p>
                                <p class="small">${product.description || '暂无描述'}</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <small class="text-muted">
                                    <i class="fas fa-image"></i> ${product.images.length} 
                                    <i class="fas fa-cogs ms-2"></i> ${Object.keys(product.specifications).length}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            document.getElementById('previewContent').innerHTML = `
                <div class="mb-3">
                    <h6>产品分类分布：</h6>
                    ${categoriesHtml}
                </div>
                <div>
                    <h6>产品列表（前10个）：</h6>
                    ${productsHtml}
                </div>
            `;

            document.getElementById('previewSection').style.display = 'block';
        }

        // 开始导入
        async function startImport() {
            const docsPath = document.getElementById('docsPath').value.trim();
            
            if (!docsPath) {
                showToast('请输入文档路径', 'warning');
                return;
            }

            if (!confirm('确定要开始导入吗？这可能需要几分钟时间。')) {
                return;
            }

            showProgress('正在启动导入任务...');

            // 获取产品类型
            const urlParams = new URLSearchParams(window.location.search);
            const productType = urlParams.get('type') || 'aqara';

            try {
                const response = await fetch(`/api/products/import/${productType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        docsPath: docsPath,
                        productType: productType,
                        options: {}
                    })
                });

                const result = await response.json();

                if (result.success) {
                    updateProgress(50, '导入任务已启动，正在处理...');
                    
                    // 模拟进度更新
                    setTimeout(() => {
                        updateProgress(100, '导入完成！');
                        showResult(true, '产品导入任务已成功启动，请稍后在产品列表中查看结果。');
                    }, 3000);
                } else {
                    hideProgress();
                    showToast('导入失败: ' + result.message, 'error');
                }
            } catch (error) {
                hideProgress();
                showToast('导入过程中发生错误: ' + error.message, 'error');
            }
        }

        // CSV文件上传
        async function uploadCsv() {
            if (!selectedFile) {
                showToast('请先选择CSV文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            showProgress('正在上传CSV文件...');

            try {
                const response = await fetch('/api/products/import/csv', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    updateProgress(100, 'CSV导入任务已启动！');
                    showResult(true, 'CSV文件上传成功，导入任务已启动。');
                } else {
                    hideProgress();
                    showToast('上传失败: ' + result.message, 'error');
                }
            } catch (error) {
                hideProgress();
                showToast('上传过程中发生错误: ' + error.message, 'error');
            }
        }

        // 下载模板
        async function downloadTemplate() {
            try {
                const response = await fetch('/api/products/import/template?format=csv', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'aqara_products_template.csv';
                    a.click();
                    window.URL.revokeObjectURL(url);
                } else {
                    showToast('下载模板失败', 'error');
                }
            } catch (error) {
                showToast('下载过程中发生错误: ' + error.message, 'error');
            }
        }

        // 显示帮助
        function showHelp() {
            new bootstrap.Modal(document.getElementById('helpModal')).show();
        }

        // 工具函数
        function showProgress(message) {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('progressText').textContent = message;
            document.getElementById('progressBar').style.width = '0%';
            addLog(message);
        }

        function updateProgress(percent, message) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = message;
            addLog(message);
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function addLog(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showResult(success, message) {
            const resultContent = document.getElementById('resultContent');
            const alertClass = success ? 'alert-success' : 'alert-danger';
            const icon = success ? 'fa-check-circle' : 'fa-exclamation-triangle';
            
            resultContent.innerHTML = `
                <div class="alert ${alertClass}">
                    <i class="fas ${icon} me-2"></i>${message}
                </div>
            `;
            
            document.getElementById('resultSection').style.display = 'block';
            hideProgress();
        }

        function hideResults() {
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('resultSection').style.display = 'none';
        }

        function addResult(type, message) {
            const resultContent = document.getElementById('resultContent');
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-warning';
            const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-exclamation-triangle';
            
            resultContent.innerHTML += `
                <div class="alert ${alertClass}">
                    <i class="fas ${icon} me-2"></i>${message}
                </div>
            `;
            
            document.getElementById('resultSection').style.display = 'block';
        }

        // 初始化页面内容
        function initializePage() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const productType = urlParams.get('type') || 'aqara';
            
            // 根据产品类型设置页面内容
            if (productType === 'yeelight') {
                document.getElementById('pageTitle').innerHTML = '<i class="fas fa-upload me-2"></i>Yeelight产品导入';
                document.getElementById('pageDescription').textContent = '批量导入Yeelight智能照明产品数据到系统中';
                document.getElementById('folderImportDesc').textContent = '从本地Yeelight产品文档目录批量导入';
                document.getElementById('pathLabel').textContent = 'Yeelight文档路径';
                document.getElementById('docsPath').value = 'F:\\AI编程\\网站\\网站爬取------yeelight\\yeelight产品文档';
                document.getElementById('docsPath').placeholder = '请输入Yeelight产品文档的完整路径';
                document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-lightbulb me-2"></i>Yeelight产品文档导入';
            } else {
                // 默认为Aqara
                document.getElementById('pageTitle').innerHTML = '<i class="fas fa-upload me-2"></i>Aqara产品导入';
                document.getElementById('pageDescription').textContent = '批量导入Aqara智能家居产品数据到系统中';
                document.getElementById('folderImportDesc').textContent = '从本地Aqara产品文档目录批量导入';
                document.getElementById('pathLabel').textContent = 'Aqara文档路径';
                document.getElementById('docsPath').value = 'F:\\AI编程\\网站\\网站爬取------aqara第二版\\aqara产品文档';
                document.getElementById('docsPath').placeholder = '请输入Aqara产品文档的完整路径';
                document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-home me-2"></i>Aqara产品文档导入';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面内容
            initializePage();
            
            // 检查登录状态（开发模式下允许预览）
            if (!localStorage.getItem('token') && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                showToast('请先登录', 'warning');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else if (!localStorage.getItem('token')) {
                // 开发模式提示
                console.warn('开发模式：未登录状态下预览页面');
                addResult('warning', '开发模式：当前未登录，部分功能可能无法正常使用。');
            }
        });
    </script>
            </div>
        </main>
    </div>
</body>
</html>
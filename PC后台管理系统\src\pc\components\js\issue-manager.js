/**
 * 异常处理管理模块
 * 实现异常事项的录入、状态跟踪、处理结果记录
 * 支持文档和图片上传
 * 版本: v2.0
 */

class IssueManager {
    constructor() {
        this.issues = this.loadIssues();
        this.issueTypes = ['质量问题', '安全隐患', '进度延误', '设备故障', '材料问题', '人员问题', '其他'];
        this.severityLevels = ['低', '中', '高', '紧急'];
        this.statusTypes = ['待处理', '处理中', '已解决', '已验证'];
        this.init();
    }

    /**
     * 初始化异常处理管理器
     */
    init() {
        console.log('⚠️ 异常处理管理器已初始化');
        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('issue-manager-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'issue-manager-styles';
        styles.textContent = `
            .issue-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .issue-modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 800px;
                max-height: 90vh;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
            }

            .issue-modal-header {
                padding: 20px 24px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .issue-modal-body {
                padding: 24px;
                flex: 1;
            }

            .issue-form-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            }

            .issue-form-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .issue-form-group.full-width {
                grid-column: 1 / -1;
            }

            .issue-form-group label {
                font-weight: 600;
                color: #374151;
                font-size: 14px;
            }

            .issue-form-group input,
            .issue-form-group select,
            .issue-form-group textarea {
                padding: 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                transition: border-color 0.2s ease;
            }

            .issue-form-group input:focus,
            .issue-form-group select:focus,
            .issue-form-group textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .issue-attachments {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 16px;
                background: #f9fafb;
            }

            .issue-attachment-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                background: white;
                border-radius: 4px;
                margin-bottom: 8px;
                border: 1px solid #e5e7eb;
            }

            .issue-list-item {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                background: white;
                transition: all 0.2s ease;
            }

            .issue-list-item:hover {
                border-color: #3b82f6;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }

            .issue-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;
            }

            .issue-title {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 4px;
            }

            .issue-meta {
                display: flex;
                gap: 12px;
                font-size: 12px;
                color: #6b7280;
            }

            .issue-status-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                white-space: nowrap;
            }

            .issue-status-pending {
                background: #fef3c7;
                color: #92400e;
            }

            .issue-status-processing {
                background: #dbeafe;
                color: #1e40af;
            }

            .issue-status-resolved {
                background: #d1fae5;
                color: #065f46;
            }

            .issue-status-verified {
                background: #e0e7ff;
                color: #3730a3;
            }

            .issue-severity-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                white-space: nowrap;
            }

            .issue-severity-low {
                background: #f3f4f6;
                color: #374151;
            }

            .issue-severity-medium {
                background: #fef3c7;
                color: #92400e;
            }

            .issue-severity-high {
                background: #fed7d7;
                color: #c53030;
            }

            .issue-severity-urgent {
                background: #fecaca;
                color: #991b1b;
            }

            .issue-description {
                color: #6b7280;
                margin-bottom: 12px;
                line-height: 1.5;
            }

            .issue-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .issue-timeline {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid #e5e7eb;
            }

            .issue-timeline-item {
                display: flex;
                gap: 12px;
                margin-bottom: 12px;
                padding: 12px;
                background: #f9fafb;
                border-radius: 6px;
            }

            .issue-timeline-icon {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                flex-shrink: 0;
            }

            .issue-timeline-content {
                flex: 1;
            }

            .issue-timeline-title {
                font-weight: 500;
                color: #374151;
                margin-bottom: 4px;
            }

            .issue-timeline-desc {
                color: #6b7280;
                font-size: 14px;
                margin-bottom: 4px;
            }

            .issue-timeline-time {
                color: #9ca3af;
                font-size: 12px;
            }

            .issue-filter-bar {
                display: flex;
                gap: 12px;
                margin-bottom: 20px;
                padding: 16px;
                background: #f9fafb;
                border-radius: 8px;
                flex-wrap: wrap;
            }

            .issue-filter-group {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            .issue-filter-group label {
                font-size: 12px;
                color: #6b7280;
                font-weight: 500;
            }

            .issue-filter-group select {
                padding: 6px 8px;
                border: 1px solid #d1d5db;
                border-radius: 4px;
                font-size: 14px;
                min-width: 120px;
            }

            @keyframes slideInUp {
                from { transform: translateY(20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            .issue-list-item {
                animation: slideInUp 0.3s ease;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * 创建异常
     */
    createIssue(phase) {
        try {
            const modal = this.createIssueModal(phase);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        } catch (error) {
            console.error('创建异常模态框失败:', error);
            constructionManager.showErrorMessage('异常创建功能初始化失败');
        }
    }

    /**
     * 创建异常模态框
     */
    createIssueModal(phase, issue = null) {
        const isEdit = issue !== null;
        const modal = document.createElement('div');
        modal.className = 'issue-modal';
        
        modal.innerHTML = `
            <div class="issue-modal-content">
                <div class="issue-modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> ${isEdit ? '编辑' : '创建'}异常事项</h3>
                    <button class="btn-close" onclick="this.closest('.issue-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="issue-modal-body">
                    <div class="issue-form-row">
                        <div class="issue-form-group">
                            <label for="issueTitle">异常标题 *</label>
                            <input type="text" id="issueTitle" placeholder="请输入异常标题" 
                                   value="${issue ? issue.title : ''}" required>
                        </div>
                        
                        <div class="issue-form-group">
                            <label for="issueType">异常类型 *</label>
                            <select id="issueType" required>
                                <option value="">请选择异常类型</option>
                                ${this.issueTypes.map(type => 
                                    `<option value="${type}" ${issue && issue.type === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    
                    <div class="issue-form-row">
                        <div class="issue-form-group">
                            <label for="issueSeverity">严重程度 *</label>
                            <select id="issueSeverity" required>
                                <option value="">请选择严重程度</option>
                                ${this.severityLevels.map(level => 
                                    `<option value="${level}" ${issue && issue.severity === level ? 'selected' : ''}>${level}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="issue-form-group">
                            <label for="issueResponsible">责任人</label>
                            <input type="text" id="issueResponsible" placeholder="请输入责任人" 
                                   value="${issue ? issue.responsible : ''}">
                        </div>
                    </div>
                    
                    <div class="issue-form-row">
                        <div class="issue-form-group">
                            <label for="issueDeadline">预计解决时间</label>
                            <input type="datetime-local" id="issueDeadline" 
                                   value="${issue && issue.deadline ? new Date(issue.deadline).toISOString().slice(0, 16) : ''}">
                        </div>
                        
                        <div class="issue-form-group">
                            <label for="issueStatus">状态</label>
                            <select id="issueStatus">
                                ${this.statusTypes.map(status => 
                                    `<option value="${status}" ${issue && issue.status === status ? 'selected' : ''}>${status}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    
                    <div class="issue-form-group full-width">
                        <label for="issueDescription">异常描述 *</label>
                        <textarea id="issueDescription" rows="4" placeholder="请详细描述异常情况" required>${issue ? issue.description : ''}</textarea>
                    </div>
                    
                    <div class="issue-form-group full-width">
                        <label>相关附件</label>
                        <div class="issue-attachments">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 14px; color: #6b7280;">上传相关文档和图片</span>
                                <button type="button" class="btn btn-sm btn-primary" onclick="issueManager.selectIssueAttachments(this)">
                                    <i class="fas fa-plus"></i> 添加附件
                                </button>
                            </div>
                            <div id="issueAttachmentsList">
                                ${this.renderIssueAttachments(issue ? issue.attachments : [])}
                            </div>
                        </div>
                    </div>
                    
                    ${isEdit && issue.timeline ? `
                        <div class="issue-form-group full-width">
                            <label>处理记录</label>
                            <div class="issue-timeline">
                                ${this.renderTimeline(issue.timeline)}
                            </div>
                            <div style="margin-top: 12px;">
                                <button type="button" class="btn btn-sm btn-secondary" onclick="issueManager.addTimelineEntry(${issue.id}, this)">
                                    <i class="fas fa-plus"></i> 添加处理记录
                                </button>
                            </div>
                        </div>
                    ` : ''}
                </div>
                
                <div class="modal-footer" style="padding: 20px 24px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.issue-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="issueManager.saveIssue('${phase}', ${issue ? issue.id : 'null'}, this)">
                        <i class="fas fa-save"></i> ${isEdit ? '更新' : '保存'}
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * 渲染异常附件
     */
    renderIssueAttachments(attachments = []) {
        if (attachments.length === 0) {
            return '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无附件</div>';
        }

        return attachments.map(attachment => `
            <div class="issue-attachment-item" data-id="${attachment.id}">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas ${this.getAttachmentIcon(attachment.type)}"></i>
                    <div>
                        <div style="font-size: 14px; color: #374151;">${attachment.name}</div>
                        <div style="font-size: 12px; color: #6b7280;">${constructionManager.formatFileSize(attachment.size)}</div>
                    </div>
                </div>
                <button class="btn btn-sm btn-danger" onclick="issueManager.removeIssueAttachment(${attachment.id}, this)" title="移除">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    /**
     * 渲染时间线
     */
    renderTimeline(timeline = []) {
        if (timeline.length === 0) {
            return '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无处理记录</div>';
        }

        return timeline.map(entry => `
            <div class="issue-timeline-item">
                <div class="issue-timeline-icon" style="background: ${this.getTimelineIconColor(entry.type)};">
                    <i class="fas ${this.getTimelineIcon(entry.type)}"></i>
                </div>
                <div class="issue-timeline-content">
                    <div class="issue-timeline-title">${entry.title}</div>
                    <div class="issue-timeline-desc">${entry.description}</div>
                    <div class="issue-timeline-time">${constructionManager.formatDate(entry.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 选择异常附件
     */
    selectIssueAttachments(button) {
        // 创建文件选择器
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        input.accept = 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt';
        
        input.onchange = (e) => {
            const files = Array.from(e.target.files);
            this.handleIssueAttachments(files, button);
        };
        
        input.click();
    }

    /**
     * 处理异常附件
     */
    async handleIssueAttachments(files, button) {
        const attachmentsList = button.closest('.issue-attachments').querySelector('#issueAttachmentsList');
        const currentAttachments = this.getCurrentIssueAttachments(attachmentsList);
        
        try {
            for (const file of files) {
                // 验证文件
                if (file.size > 50 * 1024 * 1024) {
                    constructionManager.showWarningMessage(`文件 ${file.name} 超过50MB限制`);
                    continue;
                }

                // 读取文件
                const fileData = await this.readFileAsDataURL(file);
                
                const attachment = {
                    id: Date.now() + Math.random(),
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    content: fileData
                };

                currentAttachments.push(attachment);
            }

            // 更新显示
            attachmentsList.innerHTML = this.renderIssueAttachments(currentAttachments);
            constructionManager.showSuccessMessage(`已添加 ${files.length} 个附件`);

        } catch (error) {
            console.error('处理附件失败:', error);
            constructionManager.showErrorMessage('附件处理失败');
        }
    }

    /**
     * 读取文件为DataURL
     */
    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    /**
     * 获取当前异常附件
     */
    getCurrentIssueAttachments(container) {
        const items = container.querySelectorAll('.issue-attachment-item');
        return Array.from(items).map(item => {
            const id = item.dataset.id;
            // 这里应该从实际数据中获取，简化处理
            return {
                id: id,
                name: item.querySelector('div > div').textContent,
                type: 'application/octet-stream',
                size: 0,
                content: ''
            };
        });
    }

    /**
     * 移除异常附件
     */
    removeIssueAttachment(attachmentId, button) {
        if (confirm('确认移除此附件吗？')) {
            const item = button.closest('.issue-attachment-item');
            item.remove();
            constructionManager.showSuccessMessage('附件已移除');
        }
    }

    /**
     * 保存异常
     */
    saveIssue(phase, issueId, button) {
        try {
            const modal = button.closest('.issue-modal');
            const title = modal.querySelector('#issueTitle').value.trim();
            const type = modal.querySelector('#issueType').value;
            const severity = modal.querySelector('#issueSeverity').value;
            const responsible = modal.querySelector('#issueResponsible').value.trim();
            const deadline = modal.querySelector('#issueDeadline').value;
            const status = modal.querySelector('#issueStatus').value;
            const description = modal.querySelector('#issueDescription').value.trim();

            // 验证必填字段
            if (!title || !type || !severity || !description) {
                constructionManager.showWarningMessage('请填写所有必填字段');
                return;
            }

            // 获取附件
            const attachments = this.getCurrentIssueAttachments(modal.querySelector('#issueAttachmentsList'));

            const issueData = {
                id: issueId || Date.now(),
                title,
                type,
                severity,
                responsible,
                deadline: deadline ? new Date(deadline).getTime() : null,
                status: status || '待处理',
                description,
                attachments,
                phase,
                createTime: issueId ? this.findIssue(issueId).createTime : Date.now(),
                updateTime: Date.now(),
                timeline: issueId ? this.findIssue(issueId).timeline || [] : []
            };

            if (issueId) {
                this.updateIssue(issueData);
            } else {
                this.addIssue(phase, issueData);
            }

            modal.remove();
            constructionManager.renderIssues(phase);
            constructionManager.showSuccessMessage(issueId ? '异常更新成功' : '异常创建成功');

        } catch (error) {
            console.error('保存异常失败:', error);
            constructionManager.showErrorMessage('保存失败: ' + error.message);
        }
    }

    /**
     * 添加异常
     */
    addIssue(phase, issueData) {
        constructionManager.phaseData[phase].issues.push(issueData);
        this.issues.push(issueData);
        this.saveIssues();
        constructionManager.savePhaseData();
    }

    /**
     * 更新异常
     */
    updateIssue(issueData) {
        // 更新全局异常
        const globalIndex = this.issues.findIndex(i => i.id === issueData.id);
        if (globalIndex !== -1) {
            this.issues[globalIndex] = issueData;
        }

        // 更新阶段异常
        const phaseIssues = constructionManager.phaseData[issueData.phase].issues;
        const phaseIndex = phaseIssues.findIndex(i => i.id === issueData.id);
        if (phaseIndex !== -1) {
            phaseIssues[phaseIndex] = issueData;
        }

        this.saveIssues();
        constructionManager.savePhaseData();
    }

    /**
     * 编辑异常
     */
    editIssue(issueId) {
        const issue = this.findIssue(issueId);
        if (issue) {
            const modal = this.createIssueModal(issue.phase, issue);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        }
    }

    /**
     * 删除异常
     */
    deleteIssue(phase, issueId) {
        if (confirm('确认删除此异常事项吗？此操作不可恢复。')) {
            // 从阶段数据中删除
            const phaseIssues = constructionManager.phaseData[phase].issues;
            const phaseIndex = phaseIssues.findIndex(i => i.id == issueId);
            if (phaseIndex !== -1) {
                phaseIssues.splice(phaseIndex, 1);
            }

            // 从全局异常中删除
            const globalIndex = this.issues.findIndex(i => i.id == issueId);
            if (globalIndex !== -1) {
                this.issues.splice(globalIndex, 1);
            }

            this.saveIssues();
            constructionManager.savePhaseData();
            constructionManager.renderIssues(phase);
            constructionManager.showSuccessMessage('异常事项已删除');
        }
    }

    /**
     * 工具方法
     */
    findIssue(issueId) {
        return this.issues.find(i => i.id == issueId);
    }

    getAttachmentIcon(type) {
        if (type.startsWith('image/')) return 'fa-file-image';
        if (type.includes('pdf')) return 'fa-file-pdf';
        if (type.includes('word')) return 'fa-file-word';
        if (type.includes('excel')) return 'fa-file-excel';
        if (type.includes('powerpoint')) return 'fa-file-powerpoint';
        return 'fa-file-alt';
    }

    getTimelineIcon(type) {
        const icons = {
            'create': 'fa-plus',
            'update': 'fa-edit',
            'status_change': 'fa-exchange-alt',
            'comment': 'fa-comment',
            'resolve': 'fa-check'
        };
        return icons[type] || 'fa-circle';
    }

    getTimelineIconColor(type) {
        const colors = {
            'create': '#3b82f6',
            'update': '#f59e0b',
            'status_change': '#8b5cf6',
            'comment': '#6b7280',
            'resolve': '#10b981'
        };
        return colors[type] || '#6b7280';
    }

    /**
     * 数据持久化
     */
    loadIssues() {
        try {
            const data = localStorage.getItem('construction_issues');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('加载异常数据失败:', error);
            return [];
        }
    }

    saveIssues() {
        try {
            localStorage.setItem('construction_issues', JSON.stringify(this.issues));
        } catch (error) {
            console.error('保存异常数据失败:', error);
        }
    }
}

// 全局实例
let issueManager;

// 延迟初始化，等待其他模块加载
if (typeof window !== 'undefined') {
    window.IssueManager = IssueManager;
}

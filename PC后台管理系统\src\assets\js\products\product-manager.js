/**
 * 商品管理核心模块
 * 提供商品的CRUD操作、批量导入、数据验证等功能
 * 兼容MedusaJS电商框架
 */

class ProductManager {
    constructor() {
        this.apiBaseUrl = '/api/products';
        this.products = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.filters = {};
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        
        // 初始化事件监听
        this.initializeEventListeners();
        
        // 加载商品数据
        this.loadProducts();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 新增商品按钮
        const addProductBtn = document.querySelector('[data-action="add-product"]');
        if (addProductBtn) {
            addProductBtn.addEventListener('click', () => this.showAddProductModal());
        }

        // 批量导入按钮
        const importBtn = document.querySelector('[data-action="import-data"]');
        if (importBtn) {
            importBtn.addEventListener('click', () => this.showImportModal());
        }

        // 同步MedusaJS按钮
        const syncBtn = document.querySelector('[data-action="sync-medusa"]');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.syncWithMedusa());
        }

        // 搜索框
        const searchInput = document.querySelector('#productSearch');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => this.handleSearch(), 300));
        }

        // 筛选器
        this.initializeFilters();
    }

    /**
     * 初始化筛选器
     */
    initializeFilters() {
        const categoryFilter = document.querySelector('#categoryFilter');
        const statusFilter = document.querySelector('#statusFilter');

        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.handleFilterChange());
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.handleFilterChange());
        }
    }

    /**
     * 加载商品数据
     */
    async loadProducts() {
        try {
            this.showLoading(true);
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            });

            const response = await fetch(`${this.apiBaseUrl}?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            this.products = data.products || [];
            this.totalCount = data.total || 0;
            
            this.renderProductTable();
            this.renderPagination();
            
        } catch (error) {
            console.error('加载商品数据失败:', error);
            this.showError('加载商品数据失败，请稍后重试');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染商品表格
     */
    renderProductTable() {
        const tableContainer = document.getElementById('tableContainer');
        if (!tableContainer) return;

        if (this.products.length === 0) {
            tableContainer.innerHTML = this.renderEmptyState();
            return;
        }

        const tableHTML = `
            <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f9fafb;">
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                                <input type="checkbox" id="selectAll" style="margin-right: 8px;">商品信息
                            </th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SKU</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">分类</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">价格</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">库存</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">状态</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.products.map(product => this.renderProductRow(product)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        tableContainer.innerHTML = tableHTML;

        // 绑定表格事件
        this.bindTableEvents();
    }

    /**
     * 渲染单个商品行
     */
    renderProductRow(product) {
        const statusClass = this.getStatusClass(product.status);
        const statusText = this.getStatusText(product.status);

        return `
            <tr style="border-bottom: 1px solid #f3f4f6;" data-product-id="${product.id}">
                <td style="padding: 12px; font-size: 14px; color: #1f2937;">
                    <div style="display: flex; align-items: center;">
                        <input type="checkbox" class="product-checkbox" value="${product.id}" style="margin-right: 12px;">
                        <div style="display: flex; align-items: center;">
                            <img src="${product.thumbnail || '/images/products/default.jpg'}" 
                                 alt="${product.title}" 
                                 style="width: 40px; height: 40px; border-radius: 4px; margin-right: 12px; object-fit: cover;">
                            <div>
                                <div style="font-weight: 500; color: #1f2937;">${product.title}</div>
                                <div style="font-size: 12px; color: #6b7280;">${product.description || ''}</div>
                            </div>
                        </div>
                    </div>
                </td>
                <td style="padding: 12px; font-size: 14px; color: #1f2937; font-family: monospace;">${product.sku || '-'}</td>
                <td style="padding: 12px; font-size: 14px; color: #1f2937;">${product.category || '-'}</td>
                <td style="padding: 12px; font-size: 14px; color: #1f2937; font-weight: 500;">¥${(product.price / 100).toFixed(2)}</td>
                <td style="padding: 12px; font-size: 14px; color: #1f2937;">${product.inventory_quantity || 0}</td>
                <td style="padding: 12px;">
                    <span class="status-badge ${statusClass}" style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                        ${statusText}
                    </span>
                </td>
                <td style="padding: 12px;">
                    <div style="display: flex; gap: 4px;">
                        <button class="btn-sm btn-primary" onclick="productManager.editProduct('${product.id}')" 
                                style="padding: 4px 8px; font-size: 12px; background: #1f2937; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            编辑
                        </button>
                        <button class="btn-sm btn-secondary" onclick="productManager.viewProduct('${product.id}')"
                                style="padding: 4px 8px; font-size: 12px; background: #f8fafc; color: #6b7280; border: 1px solid #e5e7eb; border-radius: 4px; cursor: pointer;">
                            查看
                        </button>
                        <button class="btn-sm btn-danger" onclick="productManager.deleteProduct('${product.id}')"
                                style="padding: 4px 8px; font-size: 12px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        return `
            <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f9fafb;">
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                                <input type="checkbox" style="margin-right: 8px;">商品信息
                            </th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SKU</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">分类</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">价格</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">库存</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">状态</th>
                            <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="7" style="padding: 40px; text-align: center; color: #6b7280; font-size: 14px;">
                                <i class="fas fa-box" style="font-size: 48px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                                暂无商品数据<br>
                                <small style="color: #9ca3af;">请点击"新增商品"按钮添加商品，或检查筛选条件</small>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        const statusMap = {
            'published': 'status-success',
            'draft': 'status-warning',
            'archived': 'status-error'
        };
        return statusMap[status] || 'status-warning';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'published': '已上架',
            'draft': '草稿',
            'archived': '已下架'
        };
        return statusMap[status] || '未知';
    }

    /**
     * 绑定表格事件
     */
    bindTableEvents() {
        // 全选功能
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('.product-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        const searchInput = document.querySelector('#productSearch');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.currentPage = 1;
            this.loadProducts();
        }
    }

    /**
     * 处理筛选器变化
     */
    handleFilterChange() {
        const categoryFilter = document.querySelector('#categoryFilter');
        const statusFilter = document.querySelector('#statusFilter');

        this.filters = {};

        if (categoryFilter && categoryFilter.value) {
            this.filters.category = categoryFilter.value;
        }

        if (statusFilter && statusFilter.value) {
            this.filters.status = statusFilter.value;
        }

        this.currentPage = 1;
        this.loadProducts();
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        const tableContainer = document.getElementById('tableContainer');
        if (!tableContainer) return;

        if (show) {
            tableContainer.style.opacity = '0.5';
            tableContainer.style.pointerEvents = 'none';
        } else {
            tableContainer.style.opacity = '1';
            tableContainer.style.pointerEvents = 'auto';
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 这里可以集成更好的错误提示组件
        alert(message);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        // 这里可以集成更好的成功提示组件
        console.log('Success:', message);
    }

    /**
     * 显示新增商品模态框
     */
    showAddProductModal() {
        const modal = this.createProductModal();
        document.body.appendChild(modal);

        // 显示模态框
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'translateY(0)';
        }, 10);
    }

    /**
     * 创建商品模态框
     */
    createProductModal(product = null) {
        const isEdit = !!product;
        const modalId = isEdit ? 'editProductModal' : 'addProductModal';
        const title = isEdit ? '编辑商品' : '新增商品';

        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 600px;
                max-height: 90vh;
                overflow-y: auto;
                transform: translateY(-20px);
                transition: transform 0.3s ease;
            ">
                <div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0;">${title}</h2>
                        <button class="close-modal" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <form id="productForm" style="padding: 24px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                商品名称 <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="text" name="title" required
                                   value="${product?.title || ''}"
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                商品SKU <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="text" name="sku" required
                                   value="${product?.sku || ''}"
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                            商品描述
                        </label>
                        <textarea name="description" rows="3"
                                  style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;">${product?.description || ''}</textarea>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                商品分类 <span style="color: #ef4444;">*</span>
                            </label>
                            <select name="category" required
                                    style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择分类</option>
                                <option value="智能开关" ${product?.category === '智能开关' ? 'selected' : ''}>智能开关</option>
                                <option value="智能插座" ${product?.category === '智能插座' ? 'selected' : ''}>智能插座</option>
                                <option value="智能灯具" ${product?.category === '智能灯具' ? 'selected' : ''}>智能灯具</option>
                                <option value="智能传感器" ${product?.category === '智能传感器' ? 'selected' : ''}>智能传感器</option>
                                <option value="智能门锁" ${product?.category === '智能门锁' ? 'selected' : ''}>智能门锁</option>
                                <option value="智能摄像头" ${product?.category === '智能摄像头' ? 'selected' : ''}>智能摄像头</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                商品状态 <span style="color: #ef4444;">*</span>
                            </label>
                            <select name="status" required
                                    style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="draft" ${product?.status === 'draft' ? 'selected' : ''}>草稿</option>
                                <option value="published" ${product?.status === 'published' ? 'selected' : ''}>已上架</option>
                                <option value="archived" ${product?.status === 'archived' ? 'selected' : ''}>已下架</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                销售价格 (元) <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="number" name="price" required min="0" step="0.01"
                                   value="${product?.price ? (product.price / 100).toFixed(2) : ''}"
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                成本价格 (元)
                            </label>
                            <input type="number" name="cost_price" min="0" step="0.01"
                                   value="${product?.cost_price ? (product.cost_price / 100).toFixed(2) : ''}"
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                库存数量 <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="number" name="inventory_quantity" required min="0"
                                   value="${product?.inventory_quantity || 0}"
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 24px;">
                        <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px;">
                            商品图片
                        </label>
                        <div style="border: 2px dashed #d1d5db; border-radius: 6px; padding: 24px; text-align: center; background: #f9fafb;">
                            <input type="file" name="thumbnail" accept="image/*" style="display: none;" id="thumbnailInput">
                            <div id="imagePreview" style="margin-bottom: 12px;">
                                ${product?.thumbnail ? `<img src="${product.thumbnail}" style="max-width: 100px; max-height: 100px; border-radius: 4px;">` : ''}
                            </div>
                            <button type="button" onclick="document.getElementById('thumbnailInput').click()"
                                    style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                <i class="fas fa-upload"></i> 选择图片
                            </button>
                            <p style="margin: 8px 0 0 0; font-size: 12px; color: #6b7280;">支持 JPG、PNG 格式，建议尺寸 400x400px</p>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                        <button type="button" class="cancel-btn"
                                style="padding: 8px 16px; background: #f8fafc; color: #374151; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                            取消
                        </button>
                        <button type="submit"
                                style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            <i class="fas fa-save"></i> ${isEdit ? '更新' : '保存'}
                        </button>
                    </div>
                </form>
            </div>
        `;

        // 绑定事件
        this.bindModalEvents(modal, isEdit, product);

        return modal;
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents(modal, isEdit, product) {
        // 关闭模态框
        const closeBtn = modal.querySelector('.close-modal');
        const cancelBtn = modal.querySelector('.cancel-btn');

        const closeModal = () => {
            modal.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 图片预览
        const thumbnailInput = modal.querySelector('#thumbnailInput');
        const imagePreview = modal.querySelector('#imagePreview');

        thumbnailInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    imagePreview.innerHTML = `<img src="${e.target.result}" style="max-width: 100px; max-height: 100px; border-radius: 4px;">`;
                };
                reader.readAsDataURL(file);
            }
        });

        // 表单提交
        const form = modal.querySelector('#productForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();

            if (isEdit) {
                this.updateProduct(product.id, form, closeModal);
            } else {
                this.createProduct(form, closeModal);
            }
        });
    }

    /**
     * 创建商品
     */
    async createProduct(form, closeModal) {
        try {
            const formData = new FormData(form);
            const productData = this.extractProductData(formData);

            const response = await fetch(this.apiBaseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.getCSRFToken()
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            this.showSuccess('商品创建成功');
            closeModal();
            this.loadProducts(); // 重新加载商品列表

        } catch (error) {
            console.error('创建商品失败:', error);
            this.showError('创建商品失败，请稍后重试');
        }
    }

    /**
     * 更新商品
     */
    async updateProduct(productId, form, closeModal) {
        try {
            const formData = new FormData(form);
            const productData = this.extractProductData(formData);

            const response = await fetch(`${this.apiBaseUrl}/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.getCSRFToken()
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            this.showSuccess('商品更新成功');
            closeModal();
            this.loadProducts(); // 重新加载商品列表

        } catch (error) {
            console.error('更新商品失败:', error);
            this.showError('更新商品失败，请稍后重试');
        }
    }

    /**
     * 编辑商品
     */
    async editProduct(productId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/${productId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const product = await response.json();

            const modal = this.createProductModal(product);
            document.body.appendChild(modal);

            // 显示模态框
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.querySelector('.modal-content').style.transform = 'translateY(0)';
            }, 10);

        } catch (error) {
            console.error('获取商品信息失败:', error);
            this.showError('获取商品信息失败，请稍后重试');
        }
    }

    /**
     * 查看商品详情
     */
    async viewProduct(productId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/${productId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const product = await response.json();
            this.showProductDetailModal(product);

        } catch (error) {
            console.error('获取商品详情失败:', error);
            this.showError('获取商品详情失败，请稍后重试');
        }
    }

    /**
     * 删除商品
     */
    async deleteProduct(productId) {
        if (!confirm('确定要删除这个商品吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/${productId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-Token': this.getCSRFToken()
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.showSuccess('商品删除成功');
            this.loadProducts(); // 重新加载商品列表

        } catch (error) {
            console.error('删除商品失败:', error);
            this.showError('删除商品失败，请稍后重试');
        }
    }

    /**
     * 提取表单数据
     */
    extractProductData(formData) {
        const data = {
            title: formData.get('title'),
            sku: formData.get('sku'),
            description: formData.get('description'),
            category: formData.get('category'),
            status: formData.get('status'),
            price: Math.round(parseFloat(formData.get('price')) * 100), // 转换为分
            inventory_quantity: parseInt(formData.get('inventory_quantity')),
        };

        // 处理成本价格
        const costPrice = formData.get('cost_price');
        if (costPrice) {
            data.cost_price = Math.round(parseFloat(costPrice) * 100);
        }

        // 处理图片上传
        const thumbnail = formData.get('thumbnail');
        if (thumbnail && thumbnail.size > 0) {
            // 这里应该先上传图片到服务器，然后获取URL
            // 暂时使用占位符
            data.thumbnail = '/images/products/placeholder.jpg';
        }

        return data;
    }

    /**
     * 获取CSRF Token
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    /**
     * 显示商品详情模态框
     */
    showProductDetailModal(product) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                max-height: 90vh;
                overflow-y: auto;
                transform: translateY(-20px);
                transition: transform 0.3s ease;
            ">
                <div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0;">商品详情</h2>
                        <button class="close-modal" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <div style="padding: 24px;">
                    <div style="text-align: center; margin-bottom: 24px;">
                        <img src="${product.thumbnail || '/images/products/default.jpg'}"
                             alt="${product.title}"
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; object-fit: cover;">
                    </div>

                    <div style="display: grid; gap: 16px;">
                        <div>
                            <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">商品名称</label>
                            <div style="font-size: 16px; font-weight: 500; color: #1f2937; margin-top: 4px;">${product.title}</div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div>
                                <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">SKU</label>
                                <div style="font-size: 14px; color: #1f2937; margin-top: 4px; font-family: monospace;">${product.sku || '-'}</div>
                            </div>
                            <div>
                                <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">分类</label>
                                <div style="font-size: 14px; color: #1f2937; margin-top: 4px;">${product.category || '-'}</div>
                            </div>
                        </div>

                        <div>
                            <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">商品描述</label>
                            <div style="font-size: 14px; color: #1f2937; margin-top: 4px; line-height: 1.5;">${product.description || '暂无描述'}</div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px;">
                            <div>
                                <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">销售价格</label>
                                <div style="font-size: 16px; font-weight: 600; color: #1f2937; margin-top: 4px;">¥${(product.price / 100).toFixed(2)}</div>
                            </div>
                            <div>
                                <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">库存</label>
                                <div style="font-size: 14px; color: #1f2937; margin-top: 4px;">${product.inventory_quantity || 0}</div>
                            </div>
                            <div>
                                <label style="font-size: 12px; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em;">状态</label>
                                <div style="margin-top: 4px;">
                                    <span class="status-badge ${this.getStatusClass(product.status)}" style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                        ${this.getStatusText(product.status)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                        <button class="edit-btn"
                                style="padding: 8px 16px; background: #1f2937; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="close-btn"
                                style="padding: 8px 16px; background: #f8fafc; color: #374151; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 显示模态框
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'translateY(0)';
        }, 10);

        // 绑定事件
        const closeModal = () => {
            modal.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };

        modal.querySelector('.close-modal').addEventListener('click', closeModal);
        modal.querySelector('.close-btn').addEventListener('click', closeModal);
        modal.querySelector('.edit-btn').addEventListener('click', () => {
            closeModal();
            this.editProduct(product.id);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductManager;
}

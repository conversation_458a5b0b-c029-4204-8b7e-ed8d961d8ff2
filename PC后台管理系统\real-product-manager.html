<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居产品管理系统 - 正式版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 24px 32px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            border-left: 4px solid #3182ce;
        }

        .header h1 {
            color: #2d3748;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 16px;
        }

        .toolbar {
            background: white;
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-input {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            width: 280px;
            font-size: 14px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
        }

        .btn-primary:hover {
            background: #2c5aa0;
        }

        .btn-success {
            background: #38a169;
            color: white;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .btn-warning {
            background: #d69e2e;
            color: white;
        }

        .btn-warning:hover {
            background: #b7791f;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.2s;
            border: 1px solid #e2e8f0;
        }

        .product-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }

        .product-image {
            height: 200px;
            background: #f7fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-image .placeholder {
            color: #a0aec0;
            font-size: 48px;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .product-sku {
            font-size: 14px;
            color: #718096;
            margin-bottom: 12px;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #e53e3e;
            margin-bottom: 12px;
        }

        .product-stock {
            font-size: 14px;
            color: #4a5568;
            margin-bottom: 16px;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .status-online {
            background: #38a169;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-offline {
            background: #e53e3e;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #d69e2e;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #3182ce;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 3% auto;
            padding: 32px;
            border-radius: 8px;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #a0aec0;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2d3748;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .form-textarea {
            height: 100px;
            resize: vertical;
        }

        .image-upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 4px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.2s;
        }

        .image-upload-area:hover {
            border-color: #3182ce;
        }

        .upload-text {
            color: #718096;
            margin-bottom: 8px;
        }

        .upload-hint {
            color: #a0aec0;
            font-size: 12px;
        }

        .image-preview {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 16px;
        }

        .image-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-remove {
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(0,0,0,0.6);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>智能家居产品管理系统</h1>
            <p>管理Aqara、Yeelight等品牌产品，实现产品信息维护、图片管理、库存控制等功能</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number" id="totalCount">0</div>
                <div class="stat-label">总产品数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="onlineCount">0</div>
                <div class="stat-label">上架产品</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="stockValue">¥0</div>
                <div class="stat-label">库存总值</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="brandCount">0</div>
                <div class="stat-label">品牌数量</div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="search-bar">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索产品名称、SKU或品牌...">
                <select class="filter-select" id="brandFilter">
                    <option value="">所有品牌</option>
                    <option value="Aqara">Aqara</option>
                    <option value="Yeelight">Yeelight</option>
                    <option value="Xiaomi">小米</option>
                    <option value="Philips">飞利浦</option>
                </select>
                <select class="filter-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="online">已上架</option>
                    <option value="offline">已下架</option>
                    <option value="pending">待审核</option>
                </select>
                <button class="btn btn-primary" onclick="searchProducts()">搜索</button>
            </div>
            <div>
                <button class="btn btn-primary" onclick="addProduct()">+ 添加产品</button>
                <button class="btn btn-success" onclick="importProducts()">批量导入</button>
                <button class="btn btn-warning" onclick="exportProducts()">导出数据</button>
            </div>
        </div>

        <!-- 产品列表 -->
        <div class="product-grid" id="productGrid">
            <!-- 产品卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">添加产品</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="productForm">
                <div class="form-group">
                    <label class="form-label">产品名称 *</label>
                    <input type="text" class="form-control" id="productName" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品SKU *</label>
                    <input type="text" class="form-control" id="productSku" required>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                    <div class="form-group">
                        <label class="form-label">品牌 *</label>
                        <select class="form-control" id="productBrand" required>
                            <option value="">选择品牌</option>
                            <option value="Aqara">Aqara</option>
                            <option value="Yeelight">Yeelight</option>
                            <option value="Xiaomi">小米</option>
                            <option value="Philips">飞利浦</option>
                            <option value="其他">其他品牌</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">产品分类 *</label>
                        <select class="form-control" id="productCategory" required>
                            <option value="">选择分类</option>
                            <option value="传感器">传感器</option>
                            <option value="照明设备">照明设备</option>
                            <option value="开关面板">开关面板</option>
                            <option value="智能插座">智能插座</option>
                            <option value="安防设备">安防设备</option>
                            <option value="网关设备">网关设备</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                    <div class="form-group">
                        <label class="form-label">售价 (¥) *</label>
                        <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">库存数量 *</label>
                        <input type="number" class="form-control" id="productStock" min="0" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品描述</label>
                    <textarea class="form-control form-textarea" id="productDescription" placeholder="请输入产品详细描述..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">产品图片</label>
                    <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                        <input type="file" id="imageInput" style="display:none" multiple accept="image/*">
                        <div class="upload-text">点击上传产品图片</div>
                        <div class="upload-hint">支持JPG、PNG格式，建议尺寸800x600px</div>
                    </div>
                    <div class="image-preview" id="imagePreview"></div>
                </div>
                
                <div style="text-align: right; margin-top: 32px;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #e2e8f0; color: #4a5568; margin-right: 12px;">取消</button>
                    <button type="submit" class="btn btn-primary">保存产品</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 产品数据
        let products = [];
        let currentEditingId = null;
        let uploadedImages = [];

        // 初始化真实产品数据
        function initRealProducts() {
            products = [
                {
                    id: 1,
                    name: 'Aqara人体传感器',
                    sku: 'RTCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 89.00,
                    stock: 150,
                    status: 'online',
                    description: '高精度人体移动检测，支持光照度检测，超低功耗设计，可与Aqara网关配合使用，实现智能家居自动化场景。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/motion_sensor_p3/4.webp'],
                    createTime: '2024-01-15',
                    updateTime: '2024-01-20'
                },
                {
                    id: 2,
                    name: 'Aqara温湿度传感器',
                    sku: 'WSDCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 69.00,
                    stock: 200,
                    status: 'online',
                    description: '高精度温湿度监测，±0.3°C温度精度，±3%RH湿度精度，实时数据推送，小巧设计便于安装。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/temperature_humidity_sensor/1.webp'],
                    createTime: '2024-01-16',
                    updateTime: '2024-01-21'
                },
                {
                    id: 3,
                    name: 'Yeelight LED彩光灯泡',
                    sku: 'YLDP06YL',
                    brand: 'Yeelight',
                    category: '照明设备',
                    price: 79.00,
                    stock: 120,
                    status: 'online',
                    description: 'Wi-Fi智能灯泡，1600万种颜色调节，支持语音控制，可通过米家APP或Yeelight APP控制。',
                    images: ['https://cdn.yeelight.com/assets/uploads/2021/01/YLDP06YL_01.jpg'],
                    createTime: '2024-01-17',
                    updateTime: '2024-01-22'
                },
                {
                    id: 4,
                    name: 'Aqara智能墙壁开关（单火版）',
                    sku: 'QBKG04LM',
                    brand: 'Aqara',
                    category: '开关面板',
                    price: 129.00,
                    stock: 80,
                    status: 'online',
                    description: '单火线智能墙壁开关，支持Zigbee3.0协议，可通过米家APP远程控制，支持定时开关和场景联动。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/wall_switch_h1_single_rocker/1.webp'],
                    createTime: '2024-01-18',
                    updateTime: '2024-01-23'
                },
                {
                    id: 5,
                    name: 'Aqara门窗传感器',
                    sku: 'MCCGQ11LM',
                    brand: 'Aqara',
                    category: '传感器',
                    price: 49.00,
                    stock: 180,
                    status: 'online',
                    description: '无线门窗传感器，实时监测门窗开关状态，支持安防报警，小巧设计，安装便捷。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/door_window_sensor/1.webp'],
                    createTime: '2024-01-19',
                    updateTime: '2024-01-24'
                },
                {
                    id: 6,
                    name: 'Aqara智能插座',
                    sku: 'SP-EUC01',
                    brand: 'Aqara',
                    category: '智能插座',
                    price: 99.00,
                    stock: 90,
                    status: 'online',
                    description: 'Wi-Fi智能插座，支持远程开关控制，实时功耗监测，过载保护，兼容主流智能音箱。',
                    images: ['https://cdn.aqara.com/cdn/website/mainland/static/images/products/smart_plug_eu/1.webp'],
                    createTime: '2024-01-20',
                    updateTime: '2024-01-25'
                }
            ];
            
            renderProducts();
            updateStats();
        }

        // 渲染产品列表
        function renderProducts(productsToRender = products) {
            const grid = document.getElementById('productGrid');
            
            if (productsToRender.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <div class="empty-icon">📦</div>
                        <h3>暂无产品</h3>
                        <p>点击"添加产品"开始管理您的商品</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = productsToRender.map(product => `
                <div class="product-card">
                    <div class="product-image">
                        ${product.images && product.images.length > 0 ? 
                            `<img src="${product.images[0]}" alt="${product.name}" onerror="this.parentElement.innerHTML='<div class=\\"placeholder\\">📷</div>'">` :
                            '<div class="placeholder">📷</div>'
                        }
                    </div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-sku">品牌: ${product.brand} | SKU: ${product.sku}</div>
                        <div class="product-price">¥${product.price.toFixed(2)}</div>
                        <div class="product-stock">库存: ${product.stock} 件 | 分类: ${product.category}</div>
                        <div class="product-actions">
                            <span class="status-${product.status}">
                                ${getStatusText(product.status)}
                            </span>
                            <button class="btn btn-sm btn-warning" onclick="editProduct(${product.id})">编辑</button>
                            <button class="btn btn-sm ${product.status === 'online' ? 'btn-danger' : 'btn-success'}" onclick="toggleProductStatus(${product.id})">
                                ${product.status === 'online' ? '下架' : '上架'}
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'online': '已上架',
                'offline': '已下架',
                'pending': '待审核'
            };
            return statusMap[status] || status;
        }

        // 更新统计数据
        function updateStats() {
            const total = products.length;
            const online = products.filter(p => p.status === 'online').length;
            const stockValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);
            const brands = new Set(products.map(p => p.brand)).size;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('onlineCount').textContent = online;
            document.getElementById('stockValue').textContent = `¥${stockValue.toLocaleString()}`;
            document.getElementById('brandCount').textContent = brands;
        }

        // 搜索产品
        function searchProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const brandFilter = document.getElementById('brandFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = products.filter(product => {
                const matchesSearch = !searchTerm || 
                    product.name.toLowerCase().includes(searchTerm) ||
                    product.sku.toLowerCase().includes(searchTerm) ||
                    product.brand.toLowerCase().includes(searchTerm);
                
                const matchesBrand = !brandFilter || product.brand === brandFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesBrand && matchesStatus;
            });

            renderProducts(filtered);
        }

        // 添加产品
        function addProduct() {
            currentEditingId = null;
            uploadedImages = [];
            document.getElementById('modalTitle').textContent = '添加产品';
            document.getElementById('productForm').reset();
            document.getElementById('imagePreview').innerHTML = '';
            document.getElementById('productModal').style.display = 'block';
        }

        // 编辑产品
        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            currentEditingId = id;
            uploadedImages = product.images || [];
            
            document.getElementById('modalTitle').textContent = '编辑产品';
            document.getElementById('productName').value = product.name;
            document.getElementById('productSku').value = product.sku;
            document.getElementById('productBrand').value = product.brand;
            document.getElementById('productCategory').value = product.category;
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productStock').value = product.stock;
            document.getElementById('productDescription').value = product.description;

            // 渲染现有图片
            renderImagePreview();
            
            document.getElementById('productModal').style.display = 'block';
        }

        // 切换产品状态
        function toggleProductStatus(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            product.status = product.status === 'online' ? 'offline' : 'online';
            product.updateTime = new Date().toISOString().split('T')[0];
            
            renderProducts();
            updateStats();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        // 渲染图片预览
        function renderImagePreview() {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = uploadedImages.map((image, index) => `
                <div class="image-preview-item">
                    <img src="${image}" alt="预览图">
                    <button type="button" class="image-remove" onclick="removeImage(${index})">&times;</button>
                </div>
            `).join('');
        }

        // 移除图片
        function removeImage(index) {
            uploadedImages.splice(index, 1);
            renderImagePreview();
        }

        // 批量导入产品
        function importProducts() {
            alert('批量导入功能开发中，请使用添加产品功能手动录入');
        }

        // 导出产品数据
        function exportProducts() {
            const dataStr = JSON.stringify(products, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `products_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // 图片上传处理
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedImages.push(e.target.result);
                        renderImagePreview();
                    };
                    reader.readAsDataURL(file);
                }
            });
        });

        // 表单提交处理
        document.getElementById('productForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('productName').value,
                sku: document.getElementById('productSku').value,
                brand: document.getElementById('productBrand').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                stock: parseInt(document.getElementById('productStock').value),
                description: document.getElementById('productDescription').value,
                images: uploadedImages,
                status: 'pending',
                updateTime: new Date().toISOString().split('T')[0]
            };

            if (currentEditingId) {
                // 编辑现有产品
                const productIndex = products.findIndex(p => p.id === currentEditingId);
                if (productIndex !== -1) {
                    products[productIndex] = { ...products[productIndex], ...formData };
                }
            } else {
                // 添加新产品
                const newProduct = {
                    id: Date.now(),
                    createTime: new Date().toISOString().split('T')[0],
                    ...formData
                };
                products.push(newProduct);
            }

            closeModal();
            renderProducts();
            updateStats();
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRealProducts();
            
            // 搜索框实时搜索
            document.getElementById('searchInput').addEventListener('input', searchProducts);
            document.getElementById('brandFilter').addEventListener('change', searchProducts);
            document.getElementById('statusFilter').addEventListener('change', searchProducts);
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modal = document.getElementById('productModal');
                if (e.target === modal) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
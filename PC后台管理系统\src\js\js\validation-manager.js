/**
 * 表单验证管理器
 * 负责各个选项卡的数据验证和错误提示
 */

class ValidationManager {
    constructor() {
        this.rules = {
            'new-requirement': {
                'requirement-title': {
                    required: true,
                    minLength: 2,
                    maxLength: 100,
                    message: '需求标题必填，长度2-100字符'
                },
                'requirement-description': {
                    required: true,
                    minLength: 10,
                    maxLength: 1000,
                    message: '需求描述必填，长度10-1000字符'
                },
                'priority': {
                    required: true,
                    message: '请选择优先级'
                },
                'requirement-contact': {
                    required: true,
                    pattern: /^1[3-9]\d{9}$|^[\w.-]+@[\w.-]+\.\w+$/,
                    message: '请输入有效的手机号或邮箱'
                }
            },
            'product-selection': {
                'selectedProducts': {
                    required: true,
                    minCount: 1,
                    message: '请至少选择一个产品'
                }
            },
            'drawing-management': {
                // 图纸管理为可选步骤，无必填验证
            }
        };

        this.errorContainer = null;
        this.init();
    }

    init() {
        this.createErrorDisplay();
        this.bindValidationEvents();
        console.log('✅ 验证管理器初始化完成');
    }

    /**
     * 创建错误显示容器
     */
    createErrorDisplay() {
        const errorHTML = `
            <div id="validation-errors" class="validation-errors-container" style="display: none;">
                <div class="error-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>请修正以下错误后继续：</span>
                    <button type="button" class="close-errors" onclick="this.parentElement.parentElement.style.display='none'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <ul id="validation-error-list" class="error-list"></ul>
            </div>
        `;

        // 插入到进度指示器后面
        const progressIndicator = document.getElementById('tab-progress-indicator');
        if (progressIndicator) {
            progressIndicator.insertAdjacentHTML('afterend', errorHTML);
        }

        this.errorContainer = document.getElementById('validation-errors');
        this.addValidationStyles();
    }

    /**
     * 绑定验证事件
     */
    bindValidationEvents() {
        // 实时验证
        document.addEventListener('blur', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        }, true);

        // 移除错误状态
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.clearFieldError(e.target);
            }
        });
    }

    /**
     * 验证新建需求表单
     */
    validateNewRequirementForm() {
        const errors = [];
        const rules = this.rules['new-requirement'];

        // 验证标题
        const title = this.getFieldValue('requirement-title');
        const titleRule = rules['requirement-title'];
        if (!this.validateRequired(title, titleRule.required)) {
            errors.push({ field: 'requirement-title', message: titleRule.message });
        } else if (!this.validateLength(title, titleRule.minLength, titleRule.maxLength)) {
            errors.push({ field: 'requirement-title', message: titleRule.message });
        }

        // 验证描述
        const description = this.getFieldValue('requirement-description');
        const descRule = rules['requirement-description'];
        if (!this.validateRequired(description, descRule.required)) {
            errors.push({ field: 'requirement-description', message: descRule.message });
        } else if (!this.validateLength(description, descRule.minLength, descRule.maxLength)) {
            errors.push({ field: 'requirement-description', message: descRule.message });
        }

        // 验证优先级
        const priority = this.getCheckedValue('priority');
        const priorityRule = rules['priority'];
        if (!this.validateRequired(priority, priorityRule.required)) {
            errors.push({ field: 'priority', message: priorityRule.message });
        }

        // 验证联系方式
        const contact = this.getFieldValue('requirement-contact');
        const contactRule = rules['requirement-contact'];
        if (!this.validateRequired(contact, contactRule.required)) {
            errors.push({ field: 'requirement-contact', message: contactRule.message });
        } else if (!this.validatePattern(contact, contactRule.pattern)) {
            errors.push({ field: 'requirement-contact', message: contactRule.message });
        }

        // 验证预算（可选）
        const budget = this.getFieldValue('requirement-budget');
        if (budget && !this.validateNumber(budget, 0)) {
            errors.push({ field: 'requirement-budget', message: '预算必须是有效的数字' });
        }

        // 验证截止日期（可选）
        const deadline = this.getFieldValue('requirement-deadline');
        if (deadline && !this.validateFutureDate(deadline)) {
            errors.push({ field: 'requirement-deadline', message: '截止日期必须是未来时间' });
        }

        return this.handleValidationResult(errors);
    }

    /**
     * 验证产品选择
     */
    validateProductSelection() {
        const errors = [];
        
        // 验证是否选择了产品
        const selectedProducts = this.getSelectedProducts();
        if (selectedProducts.length === 0) {
            errors.push({ 
                field: 'product-selection', 
                message: '请至少选择一个产品' 
            });
        }

        // 验证场景选择（可选）
        const selectedScenes = this.getSelectedScenes();
        if (selectedScenes.length === 0) {
            console.warn('⚠️ 未选择场景，将使用默认配置');
        }

        return this.handleValidationResult(errors);
    }

    /**
     * 验证图纸管理（可选步骤）
     */
    validateDrawingManagement() {
        // 图纸管理是可选步骤，总是返回true
        return true;
    }

    /**
     * 验证所有步骤
     */
    validateAllSteps() {
        const allErrors = [];

        // 验证新建需求
        if (!this.validateNewRequirementForm()) {
            allErrors.push('新建需求');
        }

        // 验证产品选择
        if (!this.validateProductSelection()) {
            allErrors.push('产品选择');
        }

        if (allErrors.length > 0) {
            this.showError(`以下步骤存在错误：${allErrors.join('、')}`);
            return false;
        }

        return true;
    }

    /**
     * 单字段验证
     */
    validateField(field) {
        const tabId = this.getFieldTabId(field.id);
        const rules = this.rules[tabId];
        
        if (!rules || !rules[field.id]) {
            return true;
        }

        const rule = rules[field.id];
        const value = field.value.trim();
        const errors = [];

        // 必填验证
        if (rule.required && !this.validateRequired(value, true)) {
            errors.push(rule.message);
        }

        // 长度验证
        if (value && rule.minLength && rule.maxLength) {
            if (!this.validateLength(value, rule.minLength, rule.maxLength)) {
                errors.push(rule.message);
            }
        }

        // 格式验证
        if (value && rule.pattern) {
            if (!this.validatePattern(value, rule.pattern)) {
                errors.push(rule.message);
            }
        }

        if (errors.length > 0) {
            this.showFieldError(field, errors[0]);
            return false;
        } else {
            this.clearFieldError(field);
            return true;
        }
    }

    /**
     * 基础验证方法
     */
    validateRequired(value, required) {
        if (!required) return true;
        return value && value.trim().length > 0;
    }

    validateLength(value, min, max) {
        const length = value ? value.length : 0;
        return length >= (min || 0) && length <= (max || Infinity);
    }

    validatePattern(value, pattern) {
        if (!value || !pattern) return true;
        return pattern.test(value);
    }

    validateNumber(value, min = -Infinity, max = Infinity) {
        const num = parseFloat(value);
        return !isNaN(num) && num >= min && num <= max;
    }

    validateFutureDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        return date > now;
    }

    /**
     * 获取字段值
     */
    getFieldValue(fieldId) {
        const field = document.getElementById(fieldId);
        return field ? field.value.trim() : '';
    }

    getCheckedValue(name) {
        const checked = document.querySelector(`input[name="${name}"]:checked`);
        return checked ? checked.value : '';
    }

    getSelectedProducts() {
        const selected = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
            selected.push({
                id: checkbox.value,
                name: checkbox.dataset.name || '未知产品'
            });
        });
        return selected;
    }

    getSelectedScenes() {
        const selected = [];
        document.querySelectorAll('.scene-checkbox:checked').forEach(checkbox => {
            selected.push({
                id: checkbox.value,
                name: checkbox.dataset.name || '未知场景'
            });
        });
        return selected;
    }

    /**
     * 获取字段所属的选项卡ID
     */
    getFieldTabId(fieldId) {
        if (fieldId.includes('requirement-') || fieldId === 'priority') {
            return 'new-requirement';
        } else if (fieldId.includes('product-') || fieldId.includes('scene-') || fieldId.includes('customization')) {
            return 'product-selection';
        } else if (fieldId.includes('drawing-') || fieldId.includes('upload')) {
            return 'drawing-management';
        }
        return 'unknown';
    }

    /**
     * 处理验证结果
     */
    handleValidationResult(errors) {
        if (errors.length === 0) {
            this.hideErrors();
            return true;
        } else {
            this.showErrors(errors);
            this.highlightErrorFields(errors);
            return false;
        }
    }

    /**
     * 显示错误信息
     */
    showErrors(errors) {
        const errorList = document.getElementById('validation-error-list');
        if (!errorList) return;

        errorList.innerHTML = errors.map(error => 
            `<li data-field="${error.field}">${error.message}</li>`
        ).join('');

        this.errorContainer.style.display = 'block';
        
        // 滚动到错误提示
        this.errorContainer.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
        });
    }

    hideErrors() {
        if (this.errorContainer) {
            this.errorContainer.style.display = 'none';
        }
    }

    /**
     * 高亮错误字段
     */
    highlightErrorFields(errors) {
        // 清除之前的错误状态
        document.querySelectorAll('.field-error').forEach(field => {
            field.classList.remove('field-error');
        });

        // 添加错误状态
        errors.forEach(error => {
            const field = document.getElementById(error.field);
            if (field) {
                field.classList.add('field-error');
            } else if (error.field === 'priority') {
                // 处理单选框组
                document.querySelectorAll('input[name="priority"]').forEach(radio => {
                    radio.closest('.form-group').classList.add('field-error');
                });
            } else if (error.field === 'product-selection') {
                // 处理产品选择区域
                document.querySelector('.product-selection-area')?.classList.add('field-error');
            }
        });
    }

    /**
     * 显示单字段错误
     */
    showFieldError(field, message) {
        field.classList.add('field-error');
        
        // 移除之前的错误提示
        const existingError = field.parentNode.querySelector('.field-error-message');
        if (existingError) {
            existingError.remove();
        }

        // 添加错误提示
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error-message';
        errorElement.textContent = message;
        field.parentNode.insertBefore(errorElement, field.nextSibling);
    }

    /**
     * 清除单字段错误
     */
    clearFieldError(field) {
        field.classList.remove('field-error');
        
        const errorMessage = field.parentNode.querySelector('.field-error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    showError(message) {
        console.error(`❌ 验证错误: ${message}`);
        // 这里可以集成全局错误提示组件
    }

    /**
     * 添加验证相关样式
     */
    addValidationStyles() {
        const styles = `
            <style id="validation-styles">
                .validation-errors-container {
                    background: #fef2f2;
                    border: 1px solid #fecaca;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 16px 0;
                    animation: slideDown 0.3s ease-out;
                }
                
                .error-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 12px;
                    color: #dc2626;
                    font-weight: 600;
                }
                
                .close-errors {
                    margin-left: auto;
                    background: none;
                    border: none;
                    color: #dc2626;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                }
                
                .close-errors:hover {
                    background: #fee2e2;
                }
                
                .error-list {
                    margin: 0;
                    padding-left: 20px;
                    color: #991b1b;
                }
                
                .error-list li {
                    margin-bottom: 4px;
                    cursor: pointer;
                }
                
                .error-list li:hover {
                    color: #dc2626;
                    text-decoration: underline;
                }
                
                .field-error {
                    border-color: #dc2626 !important;
                    box-shadow: 0 0 0 1px #dc2626 !important;
                }
                
                .field-error-message {
                    color: #dc2626;
                    font-size: 12px;
                    margin-top: 4px;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }
                
                .field-error-message::before {
                    content: "⚠️";
                    font-size: 10px;
                }
                
                .form-group.field-error {
                    border: 1px solid #dc2626;
                    border-radius: 4px;
                    padding: 8px;
                    background: #fef2f2;
                }
                
                .product-selection-area.field-error {
                    border: 2px solid #dc2626;
                    border-radius: 8px;
                    background: #fef2f2;
                }
                
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }
}

// 导出全局实例
window.ValidationManager = ValidationManager;
window.validationManager = new ValidationManager();

// 导出验证函数供TabFlowManager使用
window.validateNewRequirementForm = () => window.validationManager.validateNewRequirementForm();
window.validateProductSelection = () => window.validationManager.validateProductSelection();
window.validateDrawingManagement = () => window.validationManager.validateDrawingManagement();

console.log('✅ 验证管理器已加载');
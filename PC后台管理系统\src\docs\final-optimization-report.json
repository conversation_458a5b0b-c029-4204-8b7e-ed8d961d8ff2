{"total": 53, "validated": 53, "standard": 33, "simplified": 3, "noSidebar": 15, "issues": 9, "details": [{"fileName": "admin-dashboard.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "analytics.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "api-tools.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "aqara-product-import.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "construction-enhanced-demo.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "construction-guide.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": true, "hasNoSidebar": false, "menuItems": 8, "sections": 3, "issues": []}, {"fileName": "construction-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "contract-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "customer-analytics.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "customer-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "customer-permissions.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "data-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "delivery-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "demo.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-cases.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": true, "hasNoSidebar": false, "menuItems": 8, "sections": 3, "issues": []}, {"fileName": "design-center.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "design-effects.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "design-management-new.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "design-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "design-products.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": true, "hasNoSidebar": false, "menuItems": 8, "sections": 3, "issues": []}, {"fileName": "design-progress.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-requirements-fixed.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-requirements-new.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-requirements-table.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 2, "sections": 2, "issues": []}, {"fileName": "design-requirements-test.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-requirements.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "design-tasks.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "electrical-delivery-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "erp-documentation.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "index.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "installation-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "internal-permissions.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "knowledge-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "logout.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "market-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "marketing-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "my-orders.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "my-todos.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "order-analytics.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "orders.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "product-knowledge.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "product-materials.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 2, "sections": 1, "issues": []}, {"fileName": "products.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "project-analytics.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "project-center.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 8, "issues": []}, {"fileName": "projects.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 33, "sections": 7, "issues": ["没有设置active状态"]}, {"fileName": "register.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "requirements-analytics.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "requirements-management.html", "hasStandardSidebar": false, "hasSimplifiedSidebar": false, "hasNoSidebar": true, "menuItems": 0, "sections": 0, "issues": []}, {"fileName": "system-settings.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 37, "sections": 7, "issues": ["多个active状态"]}, {"fileName": "user-management.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}, {"fileName": "user-profile.html", "hasStandardSidebar": true, "hasSimplifiedSidebar": false, "hasNoSidebar": false, "menuItems": 32, "sections": 7, "issues": []}]}
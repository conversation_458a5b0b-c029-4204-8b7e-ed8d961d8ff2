/**
 * 增强版施工管理系统
 * 实现完整的施工管理功能：人员管理、文档管理、知识库、现场记录、异常处理、验收功能
 * 版本: v2.0
 * 创建时间: 2025-07-18
 */

class ConstructionManager {
    constructor() {
        this.currentPhase = 'briefing';
        this.phaseData = this.loadPhaseData();

        // 延迟初始化子模块，确保类定义已加载
        setTimeout(() => {
            this.initSubModules();
            this.init();
        }, 100);
    }

    /**
     * 初始化子模块
     */
    initSubModules() {
        try {
            this.fileManager = new (window.FileManager || FileManager)();
            this.knowledgeManager = new (window.KnowledgeManager || KnowledgeManager)();
            this.recordManager = new (window.RecordManager || RecordManager)();
            this.issueManager = new (window.IssueManager || IssueManager)();
            this.acceptanceManager = new (window.AcceptanceManager || AcceptanceManager)();
            console.log('✅ 所有子模块已初始化');
        } catch (error) {
            console.error('❌ 子模块初始化失败:', error);
            // 创建空的模块对象作为备用
            this.fileManager = { uploadFile: () => {}, previewDocument: () => {} };
            this.knowledgeManager = { createKnowledge: () => {}, editKnowledge: () => {} };
            this.recordManager = { uploadRecord: () => {}, previewMedia: () => {} };
            this.issueManager = { createIssue: () => {}, editIssue: () => {} };
            this.acceptanceManager = { startAcceptance: () => {}, generateAcceptanceReport: () => {} };
        }
    }

    /**
     * 初始化施工管理系统
     */
    init() {
        console.log('🚀 增强版施工管理系统已初始化');
        this.initPhaseData();
        this.bindEvents();
        this.loadCurrentPhase();
    }

    /**
     * 初始化阶段数据结构
     */
    initPhaseData() {
        const phases = ['briefing', 'wiring', 'installation', 'debugging', 'afterservice'];
        
        phases.forEach(phase => {
            if (!this.phaseData[phase]) {
                this.phaseData[phase] = {
                    personnel: [],
                    documents: [],
                    knowledge: [],
                    records: [],
                    issues: [],
                    acceptance: {
                        status: 'pending', // pending, approved, rejected, completed
                        images: [],
                        videos: [],
                        comments: '',
                        timestamp: null
                    }
                };
            }
        });
        
        this.savePhaseData();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 阶段切换事件
        document.querySelectorAll('.phase-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const phase = e.currentTarget.dataset.phase;
                if (phase) {
                    this.switchPhase(phase);
                }
            });
        });

        // 全局错误处理
        window.addEventListener('error', (e) => {
            console.error('施工管理系统错误:', e.error);
            this.showErrorMessage('系统发生错误，请刷新页面重试');
        });
    }

    /**
     * 切换施工阶段
     */
    switchPhase(phase) {
        try {
            this.currentPhase = phase;
            this.updatePhaseUI(phase);
            this.loadPhaseContent(phase);
            this.updatePhaseNavigation(phase);
            console.log(`✅ 切换到阶段: ${phase}`);
        } catch (error) {
            console.error('切换阶段失败:', error);
            this.showErrorMessage('切换阶段失败，请重试');
        }
    }

    /**
     * 更新阶段UI
     */
    updatePhaseUI(phase) {
        // 更新导航状态
        document.querySelectorAll('.phase-tab').forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.phase === phase) {
                tab.classList.add('active');
            }
        });

        // 更新阶段标题
        const phaseNames = {
            briefing: '交底',
            wiring: '水电',
            installation: '安装',
            debugging: '调试',
            afterservice: '售后'
        };

        const phaseName = phaseNames[phase];
        
        // 更新各模块标题
        this.updateElementText('phasePersonnelTitle', `${phaseName}人员`);
        this.updateElementText('phaseDocumentTitle', `${phaseName}文档`);
        this.updateElementText('phaseKnowledgeTitle', `${phaseName}知识库`);
        this.updateElementText('phaseRecordTitle', `${phaseName}记录`);
        this.updateElementText('phaseIssueTitle', `${phaseName}异常`);
        this.updateElementText('phaseAcceptanceTitle', `${phaseName}验收`);
    }

    /**
     * 加载阶段内容
     */
    loadPhaseContent(phase) {
        this.renderPersonnel(phase);
        this.renderDocuments(phase);
        this.renderKnowledge(phase);
        this.renderRecords(phase);
        this.renderIssues(phase);
        this.renderAcceptance(phase);
    }

    /**
     * 渲染人员列表
     */
    renderPersonnel(phase) {
        const container = document.getElementById('currentPersonnelList');
        if (!container) return;

        const personnel = this.phaseData[phase].personnel;
        
        if (personnel.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无人员信息</div>';
            return;
        }

        container.innerHTML = personnel.map(person => `
            <div class="personnel-item" data-id="${person.id}">
                <div class="personnel-info">
                    <div class="personnel-name">${person.name}</div>
                    <div class="personnel-role">${person.role}</div>
                    <div class="personnel-contact">${person.phone}</div>
                </div>
                <div class="personnel-actions">
                    <button class="btn btn-sm btn-secondary" onclick="constructionManager.editPersonnel('${phase}', ${person.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="constructionManager.deletePersonnel('${phase}', ${person.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染文档列表
     */
    renderDocuments(phase) {
        const container = document.getElementById('currentDocumentList');
        if (!container) return;

        const documents = this.phaseData[phase].documents;
        
        if (documents.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无文档</div>';
            return;
        }

        container.innerHTML = documents.map(doc => `
            <div class="document-item" data-id="${doc.id}">
                <div class="document-info">
                    <div class="document-name" onclick="constructionManager.fileManager.previewDocument(${doc.id})">
                        <i class="fas fa-file-alt"></i>
                        ${doc.name}
                    </div>
                    <div class="document-meta">
                        <span class="document-size">${this.formatFileSize(doc.size)}</span>
                        <span class="document-date">${this.formatDate(doc.uploadTime)}</span>
                    </div>
                </div>
                <div class="document-actions">
                    <button class="btn btn-sm btn-primary" onclick="constructionManager.fileManager.previewDocument(${doc.id})" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="constructionManager.fileManager.printDocument(${doc.id})" title="打印">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="constructionManager.deleteDocument('${phase}', ${doc.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染知识库列表
     */
    renderKnowledge(phase) {
        const container = document.getElementById('currentKnowledgeList');
        if (!container) return;

        const knowledge = this.phaseData[phase].knowledge;
        
        if (knowledge.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无知识库内容</div>';
            return;
        }

        container.innerHTML = knowledge.map(item => `
            <div class="knowledge-item" data-id="${item.id}">
                <div class="knowledge-info">
                    <div class="knowledge-title" onclick="constructionManager.knowledgeManager.viewKnowledge(${item.id})">
                        <i class="fas fa-book"></i>
                        ${item.title}
                    </div>
                    <div class="knowledge-desc">${item.description}</div>
                    <div class="knowledge-meta">
                        <span class="knowledge-date">${this.formatDate(item.createTime)}</span>
                        ${item.attachments ? `<span class="knowledge-attachments">${item.attachments.length} 个附件</span>` : ''}
                    </div>
                </div>
                <div class="knowledge-actions">
                    <button class="btn btn-sm btn-primary" onclick="constructionManager.knowledgeManager.viewKnowledge(${item.id})" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="constructionManager.knowledgeManager.editKnowledge(${item.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="constructionManager.deleteKnowledge('${phase}', ${item.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染现场记录
     */
    renderRecords(phase) {
        const container = document.getElementById('phaseRecords');
        if (!container) return;

        const records = this.phaseData[phase].records.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        if (records.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无现场记录</div>';
            return;
        }

        container.innerHTML = records.map(record => `
            <div class="record-item" data-id="${record.id}">
                <div class="record-preview">
                    ${record.type === 'image' ? 
                        `<img src="${record.url}" alt="现场照片" onclick="constructionManager.recordManager.previewMedia(${record.id})" />` :
                        `<video src="${record.url}" onclick="constructionManager.recordManager.previewMedia(${record.id})" poster="${record.thumbnail || ''}"></video>`
                    }
                </div>
                <div class="record-info">
                    <div class="record-name">${record.name}</div>
                    <div class="record-time">${this.formatDate(record.timestamp)}</div>
                </div>
                <div class="record-actions">
                    <button class="btn btn-sm btn-primary" onclick="constructionManager.recordManager.previewMedia(${record.id})" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="constructionManager.deleteRecord('${phase}', ${record.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染异常列表
     */
    renderIssues(phase) {
        const container = document.getElementById('phaseIssues');
        if (!container) return;

        const issues = this.phaseData[phase].issues;

        if (issues.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无异常事项</div>';
            return;
        }

        container.innerHTML = issues.map(issue => `
            <div class="issue-item" data-id="${issue.id}">
                <div class="issue-info">
                    <div class="issue-header">
                        <span class="issue-title">${issue.title}</span>
                        <span class="issue-status status-${issue.status.replace(/\s+/g, '-').toLowerCase()}">${issue.status}</span>
                        <span class="issue-priority priority-${issue.severity.toLowerCase()}">${issue.severity}</span>
                    </div>
                    <div class="issue-desc">${issue.description}</div>
                    <div class="issue-meta">
                        <span class="issue-reporter">报告人: ${issue.responsible || '未指定'}</span>
                        <span class="issue-time">${this.formatDate(issue.createTime)}</span>
                    </div>
                </div>
                <div class="issue-actions">
                    <button class="btn-icon" onclick="issueManager.editIssue(${issue.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="constructionManager.deleteIssue('${phase}', ${issue.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染验收状态
     */
    renderAcceptance(phase) {
        const container = document.getElementById('phaseAcceptance');
        if (!container) return;

        const acceptance = this.phaseData[phase].acceptance;

        container.innerHTML = `
            <div class="acceptance-item">
                <div class="acceptance-info">
                    <span class="acceptance-title">${this.getPhaseDisplayName(phase)}阶段验收</span>
                    <span class="acceptance-status status-${acceptance.status || 'pending'}">${acceptance.result || '待验收'}</span>
                    ${acceptance.timestamp ? `<div class="acceptance-time">${this.formatDate(acceptance.timestamp)}</div>` : ''}
                    ${acceptance.inspector ? `<div class="acceptance-inspector">验收人: ${acceptance.inspector}</div>` : ''}
                </div>
                <div class="acceptance-actions">
                    <button class="btn btn-success btn-sm" onclick="acceptanceManager.startAcceptance('${phase}')" title="开始验收">
                        <i class="fas fa-clipboard-check"></i> ${acceptance.result ? '重新验收' : '开始验收'}
                    </button>
                    ${acceptance.result ? `
                        <button class="btn btn-secondary btn-sm" onclick="acceptanceManager.generateAcceptanceReport('${phase}', this)" title="生成报告">
                            <i class="fas fa-file-export"></i> 生成报告
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 删除异常
     */
    deleteIssue(phase, issueId) {
        if (confirm('确认删除此异常事项吗？此操作不可恢复。')) {
            // 从阶段数据中删除
            const phaseIssues = this.phaseData[phase].issues;
            const phaseIndex = phaseIssues.findIndex(i => i.id == issueId);
            if (phaseIndex !== -1) {
                phaseIssues.splice(phaseIndex, 1);
            }

            // 从全局异常中删除
            if (window.issueManager) {
                const globalIndex = window.issueManager.issues.findIndex(i => i.id == issueId);
                if (globalIndex !== -1) {
                    window.issueManager.issues.splice(globalIndex, 1);
                    window.issueManager.saveIssues();
                }
            }

            this.savePhaseData();
            this.renderIssues(phase);
            this.showSuccessMessage('异常事项已删除');
        }
    }

    /**
     * 删除记录
     */
    deleteRecord(phase, recordId) {
        if (confirm('确认删除此现场记录吗？此操作不可恢复。')) {
            // 从阶段数据中删除
            const phaseRecords = this.phaseData[phase].records;
            const phaseIndex = phaseRecords.findIndex(r => r.id == recordId);
            if (phaseIndex !== -1) {
                phaseRecords.splice(phaseIndex, 1);
            }

            // 从全局记录中删除
            if (window.recordManager) {
                const globalIndex = window.recordManager.records.findIndex(r => r.id == recordId);
                if (globalIndex !== -1) {
                    window.recordManager.records.splice(globalIndex, 1);
                    window.recordManager.saveRecords();
                }
            }

            this.savePhaseData();
            this.renderRecords(phase);
            this.showSuccessMessage('现场记录已删除');
        }
    }

    /**
     * 工具方法
     */
    updateElementText(id, text) {
        const element = document.getElementById(id);
        if (element) element.textContent = text;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    }

    /**
     * 数据持久化
     */
    loadPhaseData() {
        try {
            const data = localStorage.getItem('construction_phase_data');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('加载阶段数据失败:', error);
            return {};
        }
    }

    savePhaseData() {
        try {
            localStorage.setItem('construction_phase_data', JSON.stringify(this.phaseData));
            console.log('✅ 阶段数据已保存');
        } catch (error) {
            console.error('保存阶段数据失败:', error);
            this.showErrorMessage('数据保存失败');
        }
    }

    loadCurrentPhase() {
        const savedPhase = localStorage.getItem('construction_current_phase');
        if (savedPhase) {
            this.switchPhase(savedPhase);
        }
    }

    /**
     * 消息提示
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showWarningMessage(message) {
        this.showMessage(message, 'warning');
    }

    showInfoMessage(message) {
        this.showMessage(message, 'info');
    }

    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message-toast message-${type}`;
        messageEl.textContent = message;
        
        // 添加样式
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease;
            ${this.getMessageStyles(type)}
        `;
        
        document.body.appendChild(messageEl);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => messageEl.remove(), 300);
            }
        }, 3000);
    }

    getMessageStyles(type) {
        const styles = {
            success: 'background: #10b981; border: 1px solid #059669;',
            error: 'background: #ef4444; border: 1px solid #dc2626;',
            warning: 'background: #f59e0b; border: 1px solid #d97706;',
            info: 'background: #3b82f6; border: 1px solid #2563eb;'
        };
        return styles[type] || styles.info;
    }

    /**
     * 人员管理功能
     */
    addPersonnel(phase) {
        const modal = this.createPersonnelModal(phase);
        document.body.appendChild(modal);
        modal.style.display = 'flex';
    }

    createPersonnelModal(phase, personnel = null) {
        const isEdit = personnel !== null;
        const modal = document.createElement('div');
        modal.className = 'personnel-modal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
            justify-content: center; z-index: 10000;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 12px; padding: 24px; width: 90%; max-width: 500px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                    <h3><i class="fas fa-user-plus"></i> ${isEdit ? '编辑' : '添加'}人员</h3>
                    <button onclick="this.closest('.personnel-modal').remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div style="display: flex; flex-direction: column; gap: 16px;">
                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">姓名 *</label>
                        <input type="text" id="personnelName" placeholder="请输入姓名"
                               value="${personnel ? personnel.name : ''}"
                               style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">角色 *</label>
                        <select id="personnelRole" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value="">请选择角色</option>
                            <option value="项目经理" ${personnel && personnel.role === '项目经理' ? 'selected' : ''}>项目经理</option>
                            <option value="施工队长" ${personnel && personnel.role === '施工队长' ? 'selected' : ''}>施工队长</option>
                            <option value="电工" ${personnel && personnel.role === '电工' ? 'selected' : ''}>电工</option>
                            <option value="网络工程师" ${personnel && personnel.role === '网络工程师' ? 'selected' : ''}>网络工程师</option>
                            <option value="调试工程师" ${personnel && personnel.role === '调试工程师' ? 'selected' : ''}>调试工程师</option>
                            <option value="质检员" ${personnel && personnel.role === '质检员' ? 'selected' : ''}>质检员</option>
                            <option value="安全员" ${personnel && personnel.role === '安全员' ? 'selected' : ''}>安全员</option>
                        </select>
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">联系电话 *</label>
                        <input type="tel" id="personnelPhone" placeholder="请输入联系电话"
                               value="${personnel ? personnel.phone : ''}"
                               style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">邮箱</label>
                        <input type="email" id="personnelEmail" placeholder="请输入邮箱地址"
                               value="${personnel ? personnel.email || '' : ''}"
                               style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">备注</label>
                        <textarea id="personnelNotes" placeholder="请输入备注信息" rows="3"
                                  style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;">${personnel ? personnel.notes || '' : ''}</textarea>
                    </div>
                </div>

                <div style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.personnel-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="constructionManager.savePersonnel('${phase}', ${personnel ? personnel.id : 'null'}, this)">
                        <i class="fas fa-save"></i> ${isEdit ? '更新' : '保存'}
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    savePersonnel(phase, personnelId, button) {
        try {
            const modal = button.closest('.personnel-modal');
            const name = modal.querySelector('#personnelName').value.trim();
            const role = modal.querySelector('#personnelRole').value;
            const phone = modal.querySelector('#personnelPhone').value.trim();
            const email = modal.querySelector('#personnelEmail').value.trim();
            const notes = modal.querySelector('#personnelNotes').value.trim();

            if (!name || !role || !phone) {
                this.showWarningMessage('请填写所有必填字段');
                return;
            }

            const personnelData = {
                id: personnelId || Date.now(),
                name,
                role,
                phone,
                email,
                notes,
                createTime: personnelId ? this.findPersonnel(personnelId).createTime : Date.now(),
                updateTime: Date.now()
            };

            if (personnelId) {
                this.updatePersonnel(phase, personnelData);
            } else {
                this.phaseData[phase].personnel.push(personnelData);
            }

            this.savePhaseData();
            modal.remove();
            this.renderPersonnel(phase);
            this.showSuccessMessage(personnelId ? '人员信息更新成功' : '人员添加成功');

        } catch (error) {
            console.error('保存人员失败:', error);
            this.showErrorMessage('保存失败: ' + error.message);
        }
    }

    editPersonnel(phase, personnelId) {
        const personnel = this.phaseData[phase].personnel.find(p => p.id == personnelId);
        if (personnel) {
            const modal = this.createPersonnelModal(phase, personnel);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        }
    }

    deletePersonnel(phase, personnelId) {
        if (confirm('确认删除此人员吗？此操作不可恢复。')) {
            this.phaseData[phase].personnel = this.phaseData[phase].personnel.filter(p => p.id != personnelId);
            this.savePhaseData();
            this.renderPersonnel(phase);
            this.showSuccessMessage('人员已删除');
        }
    }

    updatePersonnel(phase, personnelData) {
        const personnel = this.phaseData[phase].personnel;
        const index = personnel.findIndex(p => p.id === personnelData.id);
        if (index !== -1) {
            personnel[index] = personnelData;
        }
    }

    findPersonnel(personnelId) {
        for (const phase of Object.keys(this.phaseData)) {
            const personnel = this.phaseData[phase].personnel.find(p => p.id == personnelId);
            if (personnel) return personnel;
        }
        return null;
    }

    getPhaseDisplayName(phase) {
        const names = {
            briefing: '交底',
            wiring: '水电',
            installation: '安装',
            debugging: '调试',
            afterservice: '售后'
        };
        return names[phase] || phase;
    }
}

// 全局实例
let constructionManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待所有脚本加载完成
    setTimeout(() => {
        try {
            constructionManager = new ConstructionManager();

            // 设置全局引用
            window.constructionManager = constructionManager;
            window.fileManager = constructionManager.fileManager;
            window.knowledgeManager = constructionManager.knowledgeManager;
            window.recordManager = constructionManager.recordManager;
            window.issueManager = constructionManager.issueManager;
            window.acceptanceManager = constructionManager.acceptanceManager;

            console.log('✅ 施工管理系统已完全加载');
        } catch (error) {
            console.error('❌ 施工管理系统初始化失败:', error);
        }
    }, 500);
});

/**
 * 新页面创建工具
 * 自动创建符合规范的PC端页面
 * 使用方法: node scripts/create-new-page.js [页面名称] [页面类型]
 */

const fs = require('fs');
const path = require('path');

class PageCreator {
    constructor() {
        this.templatePath = 'templates/standard-page-template.html';
        this.pagesDir = 'src/pc/components/pages';
        this.jsDir = 'src/pc/js';
        
        // 页面类型配置
        this.pageTypes = {
            'management': {
                suffix: '-management.html',
                description: '管理页面',
                jsTemplate: 'management'
            },
            'detail': {
                suffix: '-detail.html',
                description: '详情页面',
                jsTemplate: 'detail'
            },
            'settings': {
                suffix: '-settings.html',
                description: '设置页面',
                jsTemplate: 'settings'
            },
            'analytics': {
                suffix: '-analytics.html',
                description: '分析页面',
                jsTemplate: 'analytics'
            }
        };
    }

    /**
     * 创建新页面
     */
    async createPage(moduleName, pageType = 'management', options = {}) {
        try {
            console.log('🚀 开始创建新页面...');
            
            // 1. 验证输入参数
            this.validateInputs(moduleName, pageType);
            
            // 2. 运行命名检查
            await this.runNamingCheck();
            
            // 3. 生成文件名和路径
            const fileInfo = this.generateFileInfo(moduleName, pageType);
            
            // 4. 检查文件是否已存在
            this.checkFileExists(fileInfo);
            
            // 5. 创建HTML页面
            await this.createHtmlPage(fileInfo, options);
            
            // 6. 创建JavaScript文件
            await this.createJavaScriptFile(fileInfo, options);
            
            // 7. 运行最终检查
            await this.runFinalCheck();
            
            // 8. 显示成功信息
            this.showSuccessMessage(fileInfo);
            
        } catch (error) {
            console.error('❌ 创建页面失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 验证输入参数
     */
    validateInputs(moduleName, pageType) {
        if (!moduleName) {
            throw new Error('请提供页面名称');
        }

        if (!this.pageTypes[pageType]) {
            throw new Error(`不支持的页面类型: ${pageType}。支持的类型: ${Object.keys(this.pageTypes).join(', ')}`);
        }

        // 检查模块名称格式
        const namePattern = /^[a-z]+(-[a-z]+)*$/;
        if (!namePattern.test(moduleName)) {
            throw new Error('页面名称必须使用kebab-case格式 (例如: design-management)');
        }

        console.log(`✅ 参数验证通过: ${moduleName} (${this.pageTypes[pageType].description})`);
    }

    /**
     * 运行命名检查
     */
    async runNamingCheck() {
        console.log('🔍 运行命名规范检查...');
        
        try {
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);
            
            const { stdout, stderr } = await execAsync('node scripts/naming-checker.js --check-pages');
            
            if (stderr) {
                console.warn('⚠️ 命名检查警告:', stderr);
            }
            
            console.log('✅ 命名规范检查完成');
        } catch (error) {
            console.warn('⚠️ 命名检查工具未找到，跳过检查');
        }
    }

    /**
     * 生成文件信息
     */
    generateFileInfo(moduleName, pageType) {
        const config = this.pageTypes[pageType];
        const fileName = moduleName + config.suffix;
        const jsFileName = moduleName + '.js';
        
        // 生成各种命名格式
        const pascalCase = moduleName
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');
        
        const titleCase = moduleName
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        return {
            moduleName,
            pageType,
            fileName,
            jsFileName,
            htmlPath: path.join(this.pagesDir, fileName),
            jsPath: path.join(this.jsDir, jsFileName),
            pascalCase,
            titleCase,
            config
        };
    }

    /**
     * 检查文件是否已存在
     */
    checkFileExists(fileInfo) {
        if (fs.existsSync(fileInfo.htmlPath)) {
            throw new Error(`页面文件已存在: ${fileInfo.htmlPath}`);
        }

        if (fs.existsSync(fileInfo.jsPath)) {
            console.warn(`⚠️ JavaScript文件已存在: ${fileInfo.jsPath}`);
        }

        console.log('✅ 文件名检查通过');
    }

    /**
     * 创建HTML页面
     */
    async createHtmlPage(fileInfo, options) {
        console.log('📄 创建HTML页面...');
        
        // 读取模板文件
        if (!fs.existsSync(this.templatePath)) {
            throw new Error(`模板文件不存在: ${this.templatePath}`);
        }

        let template = fs.readFileSync(this.templatePath, 'utf8');

        // 替换模板变量
        const replacements = {
            '{页面标题}': options.title || fileInfo.titleCase,
            '{页面描述}': options.description || `${fileInfo.config.description} - ${fileInfo.titleCase}`,
            '{module-name}': fileInfo.moduleName,
            '{ModuleName}': fileInfo.pascalCase,
            '{功能模块标题}': options.moduleTitle || fileInfo.titleCase
        };

        Object.entries(replacements).forEach(([placeholder, value]) => {
            template = template.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
        });

        // 确保目录存在
        const dir = path.dirname(fileInfo.htmlPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 写入文件
        fs.writeFileSync(fileInfo.htmlPath, template);
        console.log(`✅ HTML页面创建成功: ${fileInfo.htmlPath}`);
    }

    /**
     * 创建JavaScript文件
     */
    async createJavaScriptFile(fileInfo, options) {
        console.log('💻 创建JavaScript文件...');

        // 如果文件已存在，跳过创建
        if (fs.existsSync(fileInfo.jsPath)) {
            console.log('⏭️ JavaScript文件已存在，跳过创建');
            return;
        }

        // 生成JavaScript模板
        const jsTemplate = this.generateJavaScriptTemplate(fileInfo, options);

        // 确保目录存在
        const dir = path.dirname(fileInfo.jsPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 写入文件
        fs.writeFileSync(fileInfo.jsPath, jsTemplate);
        console.log(`✅ JavaScript文件创建成功: ${fileInfo.jsPath}`);
    }

    /**
     * 生成JavaScript模板
     */
    generateJavaScriptTemplate(fileInfo, options) {
        return `/**
 * ${fileInfo.titleCase} - ${fileInfo.config.description}
 * 创建时间: ${new Date().toISOString().split('T')[0]}
 */

class ${fileInfo.pascalCase}Manager {
    constructor() {
        this.apiBase = '/api/admin/${fileInfo.moduleName.replace('-', '/')}';
        this.currentData = null;
        this.init();
    }

    /**
     * 初始化页面
     */
    async init() {
        console.log('初始化 ${fileInfo.titleCase} 页面');
        
        try {
            await this.loadData();
            this.bindEvents();
            this.setupUI();
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.showError('页面初始化失败，请刷新重试');
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            const response = await fetch(this.apiBase);
            if (!response.ok) {
                throw new Error(\`HTTP error! status: \${response.status}\`);
            }
            
            const data = await response.json();
            this.currentData = data.data || data;
            this.renderData();
        } catch (error) {
            console.error('加载数据失败:', error);
            throw error;
        }
    }

    /**
     * 渲染数据
     */
    renderData() {
        const container = document.querySelector('.${fileInfo.moduleName}-content');
        if (!container) {
            console.error('内容容器未找到');
            return;
        }

        // 在这里添加数据渲染逻辑
        container.innerHTML = \`
            <div class="${fileInfo.moduleName}-placeholder">
                <h3>数据加载成功</h3>
                <p>在这里添加具体的数据展示逻辑</p>
            </div>
        \`;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 在这里添加事件绑定逻辑
        console.log('事件绑定完成');
    }

    /**
     * 设置UI
     */
    setupUI() {
        // 在这里添加UI设置逻辑
        console.log('UI设置完成');
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.error(message);
        // 在这里添加错误显示逻辑
        alert(message);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        console.log(message);
        // 在这里添加成功显示逻辑
        alert(message);
    }
}

// 初始化函数
function init${fileInfo.pascalCase}() {
    return new ${fileInfo.pascalCase}Manager();
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    if (typeof init${fileInfo.pascalCase} === 'function') {
        init${fileInfo.pascalCase}();
    }
});

// 导出管理器类（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${fileInfo.pascalCase}Manager;
}
`;
    }

    /**
     * 运行最终检查
     */
    async runFinalCheck() {
        console.log('🔍 运行最终检查...');
        
        try {
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);
            
            await execAsync('node scripts/naming-checker.js');
            console.log('✅ 最终检查通过');
        } catch (error) {
            console.warn('⚠️ 最终检查失败，请手动运行: node scripts/naming-checker.js');
        }
    }

    /**
     * 显示成功信息
     */
    showSuccessMessage(fileInfo) {
        console.log('\n🎉 页面创建成功！');
        console.log('=' * 50);
        console.log(`📄 HTML文件: ${fileInfo.htmlPath}`);
        console.log(`💻 JavaScript文件: ${fileInfo.jsPath}`);
        console.log(`🔗 访问链接: ${fileInfo.fileName}`);
        console.log('=' * 50);
        console.log('\n📋 后续步骤:');
        console.log('1. 在浏览器中测试页面');
        console.log('2. 完善JavaScript功能');
        console.log('3. 添加具体的业务逻辑');
        console.log('4. 运行测试确保功能正常');
        console.log('\n🔧 有用的命令:');
        console.log('- 检查命名规范: node scripts/naming-checker.js');
        console.log('- 启动本地服务: python -m http.server 8000');
        console.log('\n✨ 开发愉快！');
    }
}

// 命令行使用
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('使用方法: node scripts/create-new-page.js <页面名称> [页面类型]');
        console.log('');
        console.log('页面类型:');
        console.log('  management  - 管理页面 (默认)');
        console.log('  detail      - 详情页面');
        console.log('  settings    - 设置页面');
        console.log('  analytics   - 分析页面');
        console.log('');
        console.log('示例:');
        console.log('  node scripts/create-new-page.js device-management');
        console.log('  node scripts/create-new-page.js user-profile detail');
        console.log('  node scripts/create-new-page.js system-settings settings');
        process.exit(1);
    }

    const [moduleName, pageType = 'management'] = args;
    const creator = new PageCreator();
    
    creator.createPage(moduleName, pageType).catch(error => {
        console.error('创建失败:', error.message);
        process.exit(1);
    });
}

module.exports = PageCreator;

/**
 * 批量修复所有页面的logo图标样式
 * 确保所有页面都有正确的FontAwesome引用和外黑内白的logo样式
 */

const fs = require('fs');
const path = require('path');

// 需要修复的页面列表
const pagesToFix = [
    // 01-personal
    '01-personal/my-todos.html',
    '01-personal/my-orders.html',
    
    // 02-business
    '02-business/design-products.html',
    '02-business/requirements-management.html',
    '02-business/design-center.html',
    '02-business/design-cases.html',
    '02-business/project-center.html',
    '02-business/construction-management.html',
    '02-business/construction-guide.html',
    
    // 03-commerce
    '03-commerce/products.html',
    '03-commerce/orders.html',
    '03-commerce/customer-management.html',
    '03-commerce/marketing-management.html',
    
    // 04-knowledge
    '04-knowledge/design-knowledge.html',
    '04-knowledge/delivery-knowledge.html',
    '04-knowledge/wiring-knowledge.html',
    '04-knowledge/installation-knowledge.html',
    '04-knowledge/debugging-knowledge.html',
    '04-knowledge/product-knowledge.html',
    
    // 05-tools
    '05-tools/api-tools.html',
    '05-tools/erp-documentation.html',
    '05-tools/system-settings.html',
    '05-tools/user-management.html',
    '05-tools/internal-permissions.html',
    '05-tools/customer-permissions.html',
    '05-tools/data-management.html',
    
    // 06-analytics
    '06-analytics/requirements-analytics.html',
    '06-analytics/project-analytics.html',
    '06-analytics/order-analytics.html',
    '06-analytics/customer-analytics.html',
    
    // 07-profile
    '07-profile/demo.html',
    '07-profile/user-profile.html',
    
    // 根目录页面
    'admin-dashboard.html',
    'design-management.html',
    'design-requirements-table.html',
    'design-tasks.html',
    'projects.html'
];

// FontAwesome CDN链接
const fontAwesomeLink = '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">';

// 正确的logo图标CSS样式
const correctLogoIconCSS = `        .logo-icon {
            width: 28px;
            height: 28px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .logo-icon i {
            color: white;
        }`;

function fixPage(filePath) {
    const fullPath = path.join('../src/pc/components/pages', filePath);
    
    if (!fs.existsSync(fullPath)) {
        console.log(`⚠️  文件不存在: ${filePath}`);
        return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;
    
    // 1. 检查并添加FontAwesome引用
    if (!content.includes('font-awesome')) {
        const titleMatch = content.match(/(<title>.*?<\/title>)/);
        if (titleMatch) {
            content = content.replace(
                titleMatch[0],
                titleMatch[0] + '\n    ' + fontAwesomeLink
            );
            modified = true;
            console.log(`✅ 添加FontAwesome引用: ${filePath}`);
        }
    }
    
    // 2. 修复logo图标CSS样式
    const logoIconRegex = /\.logo-icon\s*\{[^}]*\}/s;
    if (logoIconRegex.test(content)) {
        // 检查是否缺少color: white
        if (!content.includes('.logo-icon i') || !content.includes('color: white')) {
            content = content.replace(logoIconRegex, correctLogoIconCSS);
            modified = true;
            console.log(`✅ 修复logo图标CSS: ${filePath}`);
        }
    }
    
    // 3. 保存文件
    if (modified) {
        fs.writeFileSync(fullPath, content, 'utf8');
        return true;
    }
    
    return false;
}

function main() {
    console.log('🚀 开始批量修复所有页面的logo图标样式...\n');
    
    let fixedCount = 0;
    let totalCount = 0;
    
    pagesToFix.forEach(filePath => {
        totalCount++;
        if (fixPage(filePath)) {
            fixedCount++;
        }
    });
    
    console.log(`\n📊 logo图标修复完成:`);
    console.log(`   - 总文件数: ${totalCount}`);
    console.log(`   - 已修复: ${fixedCount}`);
    console.log(`   - 跳过: ${totalCount - fixedCount}`);
    
    if (fixedCount > 0) {
        console.log('\n✨ 所有页面现在都有正确的logo图标样式');
        console.log('🎯 外黑内白的FontAwesome home图标');
        console.log('🎨 统一的视觉效果和品牌形象');
    }
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { fixPage, main };

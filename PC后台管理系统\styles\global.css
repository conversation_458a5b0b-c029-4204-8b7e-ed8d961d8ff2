/*
 * Filename: global.css
 * Description: 通用样式表，定义了PC后台管理系统的统一UI风格。
 * Version: 1.0
 * Date: 2024-07-26
 */

/* --- 基础与布局 --- */
body {
    background-color: #f3f4f6; /* bg-gray-100 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: #1f2937; /* text-gray-800 */
}

.card {
    background-color: white;
    border-radius: 0.5rem; /* rounded-lg */
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow-md */
    padding: 1.5rem; /* p-6 */
}

/* --- 字体规范 (Typography) --- */
.page-title {
    font-size: 1.5rem; /* text-2xl */
    line-height: 2rem;
    font-weight: 600; /* font-semibold */
    color: #111827; /* text-gray-900 */
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem; /* text-xl */
    line-height: 1.75rem;
    font-weight: 600; /* font-semibold */
    color: #1f2937; /* text-gray-800 */
}

.form-group-title {
    font-size: 1.125rem; /* text-lg */
    line-height: 1.75rem;
    font-weight: 500; /* font-medium */
    color: #374151; /* text-gray-700 */
}

.body-text {
    font-size: 0.875rem; /* text-sm */
    line-height: 1.25rem;
    color: #4b5563; /* text-gray-600 */
}

.form-label {
    font-size: 0.875rem; /* text-sm */
    line-height: 1.25rem;
    font-weight: 500; /* font-medium */
    color: #374151; /* text-gray-700 */
}

a.link {
    font-size: 0.875rem; /* text-sm */
    color: #2563eb; /* text-blue-600 */
    text-decoration: none;
}
a.link:hover {
    color: #1d4ed8; /* hover:text-blue-700 */
    text-decoration: underline;
}

/* --- 按钮 (Buttons) --- */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem; /* rounded-md */
    font-weight: 500; /* font-medium */
    font-size: 0.875rem; /* text-sm */
    border: 1px solid transparent;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #2563eb; /* bg-blue-600 */
    color: white;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
}
.btn-primary:hover {
    background-color: #1d4ed8; /* hover:bg-blue-700 */
}

.btn-secondary {
    background-color: white;
    color: #374151; /* text-gray-700 */
    border-color: #d1d5db; /* border-gray-300 */
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
}
.btn-secondary:hover {
    background-color: #f9fafb; /* hover:bg-gray-50 */
}

/* --- 表格 (Tables) --- */
.custom-table {
    min-width: 100%;
    background-color: white;
}

.custom-table thead {
    background-color: #f9fafb; /* bg-gray-50 */
}

.custom-table th {
    padding: 0.75rem; /* p-3 */
    text-align: left;
    font-size: 0.75rem; /* text-xs */
    font-weight: 500; /* font-medium */
    color: #6b7280; /* text-gray-500 */
    text-transform: uppercase;
    letter-spacing: 0.05em; /* tracking-wider */
}

.custom-table td {
    padding: 0.75rem; /* p-3 */
    font-size: 0.875rem; /* text-sm */
    color: #1f2937; /* text-gray-800 */
    border-top: 1px solid #e5e7eb; /* divide-y */
}

/* --- 表单 (Forms) --- */
.form-input {
    display: block;
    width: 100%;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    font-size: 0.875rem; /* sm:text-sm */
    border-color: #d1d5db; /* border-gray-300 */
    border-radius: 0.375rem; /* rounded-md */
}
.form-input:focus {
    --tw-ring-color: #2563eb;
    border-color: #2563eb;
}

a, a:visited, a:active, a:focus, .sidebar .menu-link, .sidebar .menu-link .nav-text {
  text-decoration: none !important;
  box-shadow: none !important;
  border-bottom: none !important;
}

.sidebar .menu-link {
  font-weight: 400;
  color: #222;
  font-size: 15px;
  line-height: 24px;
  padding: 8px 24px;
}
.sidebar .menu-link.active {
  color: #111;
  font-weight: 500;
}

/* 内页内容区统一行高 */
.content-area, .card, .table, .form, .form-group, .filter-bar, .table-row, .table-cell, .form-label, .form-input, .pagination {
  line-height: 1.6;
}
.table-row, .table-cell {
  min-height: 24px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.form-label, .form-input {
  min-height: 24px;
}
.button, .add-user-btn {
  line-height: 24px;
  height: 36px;
  padding: 0 18px;
}
.pagination {
  line-height: 24px;
  min-height: 24px;
}

/* 统一行高和控件高度 */
input, select, .filter-bar, .form, .form-group, .table th, .table td, .pagination, .add-user-btn, .button {
  line-height: 24px !important;
  min-height: 36px !important;
  font-size: 15px !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

/* 按钮主色为#666666 */
.add-user-btn, .button {
  background: #666666 !important;
  color: #fff !important;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  transition: background 0.2s;
}
.add-user-btn:hover, .button:hover {
  background: #444444 !important;
}

/* 分页强调色为#666666 */
.pagination a, .pagination span {
  color: #666666 !important;
  text-decoration: none;
  font-weight: 500;
}
.pagination a:hover {
  color: #222 !important;
  text-decoration: underline;
}

/* 表格操作icon统一为#666666 */
.table .action-icon,
.table .icon,
.table .fa,
.table .fas,
.table .far,
.table .fa-eye,
.table .fa-edit,
.table .fa-ban {
  color: #666666 !important;
  font-size: 16px !important;
  margin: 0 4px;
  cursor: pointer;
  transition: color 0.2s;
}
.table .action-icon:hover,
.table .icon:hover,
.table .fa:hover,
.table .fas:hover,
.table .far:hover {
  color: #222 !important;
}

/* 其他主操作色统一为#666666 */
.filter-bar .main-action, .form .main-action {
  color: #666666 !important;
}

/* 筛选区控件间距 */
.filter-bar input,
.filter-bar select,
.filter-bar .form-item,
.filter-bar .form-group {
  margin-bottom: 16px !important;
  display: block;
}
.filter-bar input:last-child,
.filter-bar select:last-child,
.filter-bar .form-item:last-child,
.filter-bar .form-group:last-child {
  margin-bottom: 0 !important;
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <button class="mobile-menu-btn" onclick="toggleMobileMenu()" style="display: none;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">需求管理</h1>
                            <p class="breadcrumb-description">管理客户设计需求的提交、审核和分配</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="requirements-container">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3 class="stat-card-title">总需求数</h3>
                            <div class="stat-card-icon" style="background: #3b82f6;">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                        </div>
                        <p class="stat-card-value" id="totalRequirements">156</p>
                        <div class="stat-card-change stat-change-up">
                            <i class="fas fa-arrow-up"></i> +12 本月
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3 class="stat-card-title">待处理</h3>
                            <div class="stat-card-icon" style="background: #f59e0b;">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <p class="stat-card-value" id="pendingRequirements">23</p>
                        <div class="stat-card-change stat-change-up">
                            <i class="fas fa-arrow-up"></i> +5 今日
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3 class="stat-card-title">处理中</h3>
                            <div class="stat-card-icon" style="background: #10b981;">
                                <i class="fas fa-cogs"></i>
                            </div>
                        </div>
                        <p class="stat-card-value" id="processingRequirements">45</p>
                        <div class="stat-card-change stat-change-down">
                            <i class="fas fa-arrow-down"></i> -3 本周
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3 class="stat-card-title">已完成</h3>
                            <div class="stat-card-icon" style="background: #8b5cf6;">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <p class="stat-card-value" id="completedRequirements">88</p>
                        <div class="stat-card-change stat-change-up">
                            <i class="fas fa-arrow-up"></i> +8 本周
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="main-content-area">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <h2 class="toolbar-title">需求列表</h2>
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="搜索需求..." id="searchInput" onkeyup="searchRequirements()">
                            </div>
                            <select class="filter-select" id="statusFilter" onchange="filterRequirements()">
                                <option value="">全部状态</option>
                                <option value="pending">待处理</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <select class="filter-select" id="priorityFilter" onchange="filterRequirements()">
                                <option value="">全部优先级</option>
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-secondary" onclick="exportRequirements()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn btn-primary" onclick="showAddRequirementModal()">
                                <i class="fas fa-plus"></i> 新建需求
                            </button>
                        </div>
                    </div>

                    <!-- 表格容器 -->
                    <div class="table-container">
                        <table class="requirements-table">
                            <thead>
                                <tr>
                                    <th>需求ID</th>
                                    <th>客户姓名</th>
                                    <th>联系电话</th>
                                    <th>需求类型</th>
                                    <th>项目地址</th>
                                    <th>预算范围</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="requirementsTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新建需求模态框 -->
    <div id="addRequirementModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建需求</h3>
                <button type="button" class="modal-close" onclick="closeAddRequirementModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newRequirementForm">
                    <!-- Tab切换区 -->
                    <div class="product-tabs">
                        <button type="button" class="tab-btn active" data-tab="lighting" onclick="switchTab('lighting')">
                            <i class="fas fa-lightbulb"></i>
                            <span>智能照明</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="security" onclick="switchTab('security')">
                            <i class="fas fa-video"></i>
                            <span>监控类</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="custom" onclick="switchTab('custom')">
                            <i class="fas fa-cogs"></i>
                            <span>定制场景</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="auto" onclick="switchTab('auto')">
                            <i class="fas fa-magic"></i>
                            <span>自动场景</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="safety" onclick="switchTab('safety')">
                            <i class="fas fa-shield-alt"></i>
                            <span>安防类</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="audio" onclick="switchTab('audio')">
                            <i class="fas fa-music"></i>
                            <span>智能影音</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="ai" onclick="switchTab('ai')">
                            <i class="fas fa-robot"></i>
                            <span>AI语音</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="hotel" onclick="switchTab('hotel')">
                            <i class="fas fa-hotel"></i>
                            <span>酒店民宿</span>
                        </button>
                        <button type="button" class="tab-btn" data-tab="business" onclick="switchTab('business')">
                            <i class="fas fa-building"></i>
                            <span>商业场景</span>
                        </button>
                    </div>

                    <!-- 产品选择区 -->
                    <div class="product-selection">
                        <h4>产品选择</h4>
                        <div class="product-grid" id="productGrid">
                            <!-- 产品列表将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 已选产品 -->
                    <div class="selected-products-section">
                        <h4>已选产品 (<span id="selectedCount">0</span>)</h4>
                        <div class="selected-products" id="selectedProducts">
                            <!-- 已选产品将显示在这里 -->
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h4>基本信息</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerName">客户姓名 *</label>
                                <input type="text" id="customerName" name="customerName" required>
                            </div>
                            <div class="form-group">
                                <label for="customerPhone">联系电话 *</label>
                                <input type="tel" id="customerPhone" name="customerPhone" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="rooms">房型选择 *</label>
                                <div class="house-type-row">
                                    <select id="rooms" name="rooms" required>
                                        <option value="">请选择室</option>
                                        <option value="1">1室</option>
                                        <option value="2">2室</option>
                                        <option value="3">3室</option>
                                        <option value="4">4室</option>
                                        <option value="5">5室及以上</option>
                                    </select>
                                    <select id="halls" name="halls" required>
                                        <option value="">请选择厅</option>
                                        <option value="1">1厅</option>
                                        <option value="2">2厅</option>
                                        <option value="3">3厅</option>
                                        <option value="4">4厅</option>
                                        <option value="5">5厅及以上</option>
                                    </select>
                                    <select id="bathrooms" name="bathrooms" required>
                                        <option value="">请选择卫</option>
                                        <option value="1">1卫</option>
                                        <option value="2">2卫</option>
                                        <option value="3">3卫</option>
                                        <option value="4">4卫</option>
                                        <option value="5">5卫及以上</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">地址信息 *</label>
                            <input type="text" id="address" name="address" placeholder="请输入小区名称、楼层、房号等详细地址" required>
                        </div>
                        <div class="form-group">
                            <label for="requirements">其他需求</label>
                            <textarea id="requirements" name="requirements" rows="4" placeholder="请详细描述您的个性化需求，如特殊功能、预算范围、时间安排等"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddRequirementModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitNewRequirement()">提交需求</button>
            </div>
        </div>
    </div>

    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 产品数据
        const productData = {
            lighting: [
                { id: 'lighting1', title: '全屋智能照明', desc: '一键控制全屋灯光，支持场景切换和语音控制', image: 'https://via.placeholder.com/120x120/4A90E2/FFFFFF?text=💡', fallbackIcon: '💡' },
                { id: 'lighting2', title: '智能调光系统', desc: '支持情景面板或语音控制，多场景切换，过度柔和', image: 'https://via.placeholder.com/120x120/4A90E2/FFFFFF?text=🔆', fallbackIcon: '🔆' },
                { id: 'lighting3', title: '卧室智能照明', desc: '温馨、助眠、起夜、阅读等场景，帮助缓解压力放松休息', image: 'https://via.placeholder.com/120x120/4A90E2/FFFFFF?text=🛏️', fallbackIcon: '🛏️' },
                { id: 'lighting4', title: '夏冬场景照明', desc: '四季如春亦可四季分明，带来舒适与放松的智能调光', image: 'https://via.placeholder.com/120x120/4A90E2/FFFFFF?text=🌞', fallbackIcon: '🌞' }
            ],
            security: [
                { id: 'security1', title: '空气质量智能显示', desc: '显示CO2、tVoc、PM2.5、温湿度等参数，联动空净、新风等设备', image: 'https://via.placeholder.com/120x120/E74C3C/FFFFFF?text=🌬️', fallbackIcon: '🌬️' },
                { id: 'security2', title: '中控屏监控视窗', desc: '中控屏直接显示监控实时画面，随时观看重要区域', image: 'https://via.placeholder.com/120x120/E74C3C/FFFFFF?text=📺', fallbackIcon: '📺' },
                { id: 'security3', title: '监控双向对讲', desc: '无需APP即可实时对讲，与来访者语音沟通', image: 'https://via.placeholder.com/120x120/E74C3C/FFFFFF?text=📞', fallbackIcon: '📞' },
                { id: 'security4', title: '摄像头磁吸供电', desc: '无绳安装，3C安全认证，支持侧装、顶装磁吸', image: 'https://via.placeholder.com/120x120/E74C3C/FFFFFF?text=📷', fallbackIcon: '📷' }
            ],
            custom: [
                { id: 'custom1', title: '回家场景', desc: '开门自动开灯、开空调、播放音乐，营造温馨氛围', image: 'https://via.placeholder.com/120x120/9B59B6/FFFFFF?text=🏠', fallbackIcon: '🏠' },
                { id: 'custom2', title: '离家场景', desc: '一键关闭所有灯光、家电，开启安防模式', image: 'https://via.placeholder.com/120x120/9B59B6/FFFFFF?text=🚪', fallbackIcon: '🚪' },
                { id: 'custom3', title: '观影场景', desc: '自动关闭主灯、拉上窗帘、调节氛围灯、开启投影', image: 'https://via.placeholder.com/120x120/9B59B6/FFFFFF?text=🎬', fallbackIcon: '🎬' },
                { id: 'custom4', title: '聚餐场景', desc: '调节餐厅灯光亮度，播放轻音乐，营造用餐氛围', image: 'https://via.placeholder.com/120x120/9B59B6/FFFFFF?text=🍽️', fallbackIcon: '🍽️' }
            ],
            auto: [
                { id: 'auto1', title: '睡眠场景', desc: '一键睡眠开启安防，空调睡眠模式，窗帘拉上，床头灯模拟日落', image: 'https://via.placeholder.com/120x120/27AE60/FFFFFF?text=😴', fallbackIcon: '😴' },
                { id: 'auto2', title: '中央空调智能化', desc: '多品牌支持，一键控制，语音调节风量温度', image: 'https://via.placeholder.com/120x120/27AE60/FFFFFF?text=❄️', fallbackIcon: '❄️' },
                { id: 'auto3', title: '智能手表控制', desc: '小米/苹果手表控制全屋灯光、家电、智能场景', image: 'https://via.placeholder.com/120x120/27AE60/FFFFFF?text=⌚', fallbackIcon: '⌚' },
                { id: 'auto4', title: '语音控制', desc: '躺在床上一句话控制，安静享受美妙时光', image: 'https://via.placeholder.com/120x120/27AE60/FFFFFF?text=🎤', fallbackIcon: '🎤' }
            ],
            safety: [
                { id: 'safety1', title: '智能门锁', desc: '指纹、密码、刷卡多种开锁方式，远程监控开锁记录', image: 'https://via.placeholder.com/120x120/F39C12/FFFFFF?text=🔐', fallbackIcon: '🔐' },
                { id: 'safety2', title: '燃气报警器', desc: '检测燃气泄漏，自动关闭燃气阀门，手机推送报警', image: 'https://via.placeholder.com/120x120/F39C12/FFFFFF?text=🔥', fallbackIcon: '🔥' },
                { id: 'safety3', title: '烟雾报警器', desc: '火灾烟雾检测，声光报警，手机远程通知', image: 'https://via.placeholder.com/120x120/F39C12/FFFFFF?text=🚨', fallbackIcon: '🚨' },
                { id: 'safety4', title: '水浸传感器', desc: '检测漏水情况，及时关闭水阀，避免财产损失', image: 'https://via.placeholder.com/120x120/F39C12/FFFFFF?text=💧', fallbackIcon: '💧' }
            ],
            audio: [
                { id: 'audio1', title: '全屋背景音乐', desc: '每个房间独立控制，支持蓝牙、网络音源', image: 'https://via.placeholder.com/120x120/8E44AD/FFFFFF?text=🎵', fallbackIcon: '🎵' },
                { id: 'audio2', title: '家庭影院系统', desc: '5.1/7.1环绕声，4K投影，沉浸式观影体验', image: 'https://via.placeholder.com/120x120/8E44AD/FFFFFF?text=🎭', fallbackIcon: '🎭' },
                { id: 'audio3', title: '智能音响', desc: '语音助手，音乐播放，智能家居控制中心', image: 'https://via.placeholder.com/120x120/8E44AD/FFFFFF?text=🔊', fallbackIcon: '🔊' },
                { id: 'audio4', title: 'KTV系统', desc: '专业K歌设备，海量曲库，家庭娱乐首选', image: 'https://via.placeholder.com/120x120/8E44AD/FFFFFF?text=🎤', fallbackIcon: '🎤' }
            ],
            ai: [
                { id: 'ai1', title: 'AI语音助手', desc: '小爱同学、天猫精灵等，语音控制全屋设备', image: 'https://via.placeholder.com/120x120/34495E/FFFFFF?text=🤖', fallbackIcon: '🤖' },
                { id: 'ai2', title: '智能中控屏', desc: '7寸/10寸触控屏，集成控制、监控、音乐等功能', image: 'https://via.placeholder.com/120x120/34495E/FFFFFF?text=📱', fallbackIcon: '📱' },
                { id: 'ai3', title: '人体感应', desc: '自动感应人体活动，智能开关灯光和设备', image: 'https://via.placeholder.com/120x120/34495E/FFFFFF?text=👤', fallbackIcon: '👤' },
                { id: 'ai4', title: '环境自适应', desc: 'AI学习用户习惯，自动调节温度、湿度、光照', image: 'https://via.placeholder.com/120x120/34495E/FFFFFF?text=🌡️', fallbackIcon: '🌡️' }
            ],
            hotel: [
                { id: 'hotel1', title: '酒店客控系统', desc: '一键控制客房所有设备，提升客户体验', image: 'https://via.placeholder.com/120x120/16A085/FFFFFF?text=🏨', fallbackIcon: '🏨' },
                { id: 'hotel2', title: '民宿智能化', desc: '远程管理，自助入住，降低运营成本', image: 'https://via.placeholder.com/120x120/16A085/FFFFFF?text=🏡', fallbackIcon: '🏡' },
                { id: 'hotel3', title: '能耗管理', desc: '智能控制空调、照明，节能环保', image: 'https://via.placeholder.com/120x120/16A085/FFFFFF?text=⚡', fallbackIcon: '⚡' },
                { id: 'hotel4', title: '客房服务', desc: '一键呼叫服务，提升服务效率和质量', image: 'https://via.placeholder.com/120x120/16A085/FFFFFF?text=🛎️', fallbackIcon: '🛎️' }
            ],
            business: [
                { id: 'business1', title: '会议投影场景', desc: '一键开会关主灯、展开幕布、关窗帘、开氛围灯', image: 'https://via.placeholder.com/120x120/2C3E50/FFFFFF?text=📊', fallbackIcon: '📊' },
                { id: 'business2', title: '商业照明场景', desc: '智能场景随心切换，超低待机功耗，节能环保', image: 'https://via.placeholder.com/120x120/2C3E50/FFFFFF?text=🏪', fallbackIcon: '🏪' },
                { id: 'business3', title: '办公自动化', desc: '上下班自动化控制，提高工作效率', image: 'https://via.placeholder.com/120x120/2C3E50/FFFFFF?text=💼', fallbackIcon: '💼' },
                { id: 'business4', title: '访客管理系统', desc: '智能门禁，访客登记，安全管控', image: 'https://via.placeholder.com/120x120/2C3E50/FFFFFF?text=👔', fallbackIcon: '👔' }
            ]
        };

        // 当前选中的Tab和产品
        let currentTab = 'lighting';
        let selectedProducts = new Set();

        // 需求数据
        let requirementsData = [
            {
                id: 'REQ001',
                customerName: '张先生',
                phone: '138****1234',
                type: '全屋智能',
                address: '北京市朝阳区某小区',
                budget: '10-20万',
                priority: 'high',
                status: 'pending',
                createTime: '2024-01-15 10:30:00',
                description: '需要全屋智能化改造，包括灯光、窗帘、安防等系统'
            },
            {
                id: 'REQ002',
                customerName: '李女士',
                phone: '139****5678',
                type: '智能照明',
                address: '上海市浦东新区某公寓',
                budget: '5-10万',
                priority: 'medium',
                status: 'processing',
                createTime: '2024-01-14 14:20:00',
                description: '客厅和卧室智能照明系统安装'
            },
            {
                id: 'REQ003',
                customerName: '王总',
                phone: '136****9012',
                type: '智能安防',
                address: '深圳市南山区某别墅',
                budget: '20万以上',
                priority: 'urgent',
                status: 'processing',
                createTime: '2024-01-13 09:15:00',
                description: '别墅智能安防系统，包括监控、门禁、报警等'
            },
            {
                id: 'REQ004',
                customerName: '陈先生',
                phone: '137****3456',
                type: '智能家电',
                address: '广州市天河区某住宅',
                budget: '5万以下',
                priority: 'low',
                status: 'completed',
                createTime: '2024-01-12 16:45:00',
                description: '智能家电控制系统集成'
            },
            {
                id: 'REQ005',
                customerName: '刘女士',
                phone: '135****7890',
                type: '智能窗帘',
                address: '杭州市西湖区某小区',
                budget: '5-10万',
                priority: 'medium',
                status: 'completed',
                createTime: '2024-01-11 11:20:00',
                description: '全屋智能窗帘系统安装'
            }
        ];

        // 渲染表格
        function renderTable(data = requirementsData) {
            const tbody = document.getElementById('requirementsTableBody');

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #6b7280;">
                            <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                            暂无需求数据
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = data.map(req => `
                <tr>
                    <td><strong>${req.id}</strong></td>
                    <td>${req.customerName}</td>
                    <td>${req.phone}</td>
                    <td>${req.type}</td>
                    <td>${req.address}</td>
                    <td>${req.budget}</td>
                    <td><span class="priority-badge priority-${req.priority}">${getPriorityText(req.priority)}</span></td>
                    <td><span class="status-badge status-${req.status}">${getStatusText(req.status)}</span></td>
                    <td>${req.createTime}</td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="viewRequirement('${req.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editRequirement('${req.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="deleteRequirement('${req.id}')" title="删除" style="background: #ef4444; color: white;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'processing': '处理中',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                'low': '低',
                'medium': '中',
                'high': '高',
                'urgent': '紧急'
            };
            return priorityMap[priority] || priority;
        }

        // 搜索功能
        function searchRequirements() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const filteredData = requirementsData.filter(req =>
                req.customerName.toLowerCase().includes(searchTerm) ||
                req.id.toLowerCase().includes(searchTerm) ||
                req.type.toLowerCase().includes(searchTerm) ||
                req.address.toLowerCase().includes(searchTerm)
            );
            renderTable(filteredData);
        }

        // 筛选功能
        function filterRequirements() {
            const statusFilter = document.getElementById('statusFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;

            let filteredData = requirementsData;

            if (statusFilter) {
                filteredData = filteredData.filter(req => req.status === statusFilter);
            }

            if (priorityFilter) {
                filteredData = filteredData.filter(req => req.priority === priorityFilter);
            }

            renderTable(filteredData);
        }

        // 查看需求详情
        function viewRequirement(id) {
            const req = requirementsData.find(r => r.id === id);
            if (req) {
                showToast(`需求详情 - ${req.customerName}\n类型: ${req.type}\n地址: ${req.address}\n预算: ${req.budget}\n状态: ${getStatusText(req.status)}`, 'info');
            }
        }

        // 编辑需求
        function editRequirement(id) {
            showToast(`编辑需求 ${id} - 功能即将上线，敬请期待！`, 'info');
        }

        // 删除需求
        function deleteRequirement(id) {
            if (confirm(`确定要删除需求 ${id} 吗？`)) {
                requirementsData = requirementsData.filter(req => req.id !== id);
                renderTable();
                updateStats();
                showToast('需求已删除', 'success');
            }
        }

        // 新建需求
        function showAddRequirementModal() {
            console.log('显示新建需求模态框');
            const modal = document.getElementById('addRequirementModal');
            if (modal) {
                modal.classList.add('show');
            } else {
                console.error('找不到模态框元素');
            }
        }

        // 关闭新建需求模态框
        function closeAddRequirementModal() {
            console.log('关闭新建需求模态框');
            const modal = document.getElementById('addRequirementModal');
            if (modal) {
                modal.classList.remove('show');
            }

            const form = document.getElementById('newRequirementForm');
            if (form) {
                form.reset();
            }

            if (typeof selectedProducts !== 'undefined') {
                selectedProducts.clear();
                renderProducts();
                updateSelectedCount();
            }
        }

        // 切换Tab
        function switchTab(tabId) {
            currentTab = tabId;

            // 更新Tab按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

            // 重新渲染产品
            renderProducts();
        }

        // 渲染产品列表
        function renderProducts() {
            const productGrid = document.getElementById('productGrid');
            const products = productData[currentTab] || [];

            productGrid.innerHTML = products.map(product => `
                <div class="product-card ${selectedProducts.has(product.id) ? 'selected' : ''}" data-id="${product.id}" onclick="toggleProduct('${product.id}')">
                    <div class="product-img">
                        ${generateProductImage(product)}
                    </div>
                    <div class="product-info">
                        <h5 class="product-title">${product.title}</h5>
                        <p class="product-desc">${product.desc}</p>
                    </div>
                    <input type="checkbox" class="product-checkbox" ${selectedProducts.has(product.id) ? 'checked' : ''} onclick="event.stopPropagation()">
                </div>
            `).join('');
        }

        // 生成产品图片
        function generateProductImage(product) {
            if (product.image) {
                return `
                    <img src="${product.image}" alt="${product.title}"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                         style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">
                    <div class="fallback-icon" style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; font-size: 32px; color: #6b7280;">
                        ${product.fallbackIcon}
                    </div>
                `;
            }
            return `<div class="fallback-icon" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 32px; color: #6b7280;">${product.fallbackIcon}</div>`;
        }

        // 切换产品选择
        function toggleProduct(productId) {
            const card = document.querySelector(`[data-id="${productId}"]`);
            const checkbox = card.querySelector('.product-checkbox');

            if (selectedProducts.has(productId)) {
                selectedProducts.delete(productId);
                checkbox.checked = false;
                card.classList.remove('selected');
            } else {
                selectedProducts.add(productId);
                checkbox.checked = true;
                card.classList.add('selected');
            }

            updateSelectedProducts();
            updateSelectedCount();
        }

        // 更新已选产品显示
        function updateSelectedProducts() {
            const selectedProductsContainer = document.getElementById('selectedProducts');
            const selectedProductsArray = Array.from(selectedProducts);

            if (selectedProductsArray.length === 0) {
                selectedProductsContainer.innerHTML = `
                    <div style="color: #9ca3af; font-size: 14px; text-align: center; padding: 20px; width: 100%;">
                        <i class="fas fa-shopping-cart" style="font-size: 24px; margin-bottom: 8px; display: block; color: #d1d5db;"></i>
                        暂未选择产品，请从上方选择您需要的智能家居产品
                    </div>
                `;
                return;
            }

            selectedProductsContainer.innerHTML = selectedProductsArray.map(productId => {
                const product = findProductById(productId);
                if (!product) return '';

                return `
                    <div class="selected-product-item">
                        <div class="product-thumb">
                            ${product.fallbackIcon}
                        </div>
                        <span class="product-name">${product.title}</span>
                        <button type="button" class="remove-product-btn" onclick="removeProduct('${productId}')" title="移除产品">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }

        // 查找产品
        function findProductById(productId) {
            for (const category in productData) {
                const product = productData[category].find(p => p.id === productId);
                if (product) return product;
            }
            return null;
        }

        // 移除产品
        function removeProduct(productId) {
            selectedProducts.delete(productId);
            renderProducts();
            updateSelectedProducts();
            updateSelectedCount();
        }

        // 更新已选产品数量
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = selectedProducts.size;
        }

        // 提交新建需求
        function submitNewRequirement() {
            const form = document.getElementById('newRequirementForm');
            
            // 增强的表单验证
            const customerName = document.getElementById('customerName').value.trim();
            const customerPhone = document.getElementById('customerPhone').value.trim();
            const rooms = document.getElementById('rooms').value;
            const halls = document.getElementById('halls').value;
            const bathrooms = document.getElementById('bathrooms').value;
            const address = document.getElementById('address').value.trim();
            const requirements = document.getElementById('requirements').value.trim();

            // 基础必填项验证
            if (!customerName || !customerPhone || !rooms || !halls || !bathrooms || !address) {
                showToast('请填写所有必填字段', 'warning');
                return;
            }

            // 手机号格式验证
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(customerPhone)) {
                showToast('请输入正确的手机号码', 'warning');
                document.getElementById('customerPhone').focus();
                return;
            }

            // 客户姓名格式验证
            if (customerName.length < 2 || customerName.length > 10) {
                showToast('客户姓名应为2-10个字符', 'warning');
                document.getElementById('customerName').focus();
                return;
            }

            // 地址详细度验证
            if (address.length < 5) {
                showToast('请输入更详细的地址信息', 'warning');
                document.getElementById('address').focus();
                return;
            }

            if (selectedProducts.size === 0) {
                showToast('请至少选择一个产品', 'warning');
                return;
            }

            // 收集详细的表单数据 - 优化版本
            const selectedProductDetails = Array.from(selectedProducts).map(productId => {
                // 获取产品详细信息
                const productElement = document.querySelector(`[data-product-id="${productId}"]`);
                return {
                    id: productId,
                    title: productElement ? productElement.textContent.trim() : '未知产品',
                    category: 'pc_selected'
                };
            });

            const requirementData = {
                customerName,
                customerPhone,
                houseType: {
                    rooms: parseInt(rooms),
                    halls: parseInt(halls),
                    bathrooms: parseInt(bathrooms),
                    description: `${rooms}室${halls}厅${bathrooms}卫`
                },
                address,
                requirements: requirements || '客户未填写具体需求',
                selectedProducts: selectedProductDetails,
                source: 'pc_admin',
                priority: 'medium', // PC端创建的需求默认中等优先级
                status: 'pending', // 初始状态为待处理
                deviceInfo: {
                    userAgent: navigator.userAgent,
                    screenSize: `${screen.width}x${screen.height}`,
                    timestamp: new Date().toISOString()
                },
                validation: {
                    hasCustomerInfo: customerName.length > 0 && phoneRegex.test(customerPhone),
                    hasProducts: selectedProductDetails.length > 0,
                    hasAddress: address.length >= 5,
                    hasHouseType: rooms && halls && bathrooms,
                    isComplete: true
                },
                timestamp: new Date().toISOString()
            };

            console.log('优化后的PC端需求数据:', requirementData);
            
            // 增强的提交成功处理
            const productTitles = selectedProductDetails.map(p => p.title).join('、');
            const successMessage = `✅ 需求创建成功！\n\n📋 需求信息：\n• 客户姓名：${requirementData.customerName}\n• 联系电话：${requirementData.customerPhone}\n• 房屋户型：${requirementData.houseType.description}\n• 服务地址：${requirementData.address}\n• 选择产品：${productTitles}\n\n🎯 处理状态：已进入待处理队列\n📞 建议：请及时联系客户确认具体需求`;
            
            showToast(successMessage, 'success');
            
            // 模拟添加到需求列表（实际应该调用API）
            const newRequirement = {
                id: 'REQ-' + Date.now(),
                customerName: requirementData.customerName,
                phone: requirementData.customerPhone,
                type: '智能家居设计',
                address: requirementData.address.substring(0, 20) + (requirementData.address.length > 20 ? '...' : ''),
                priority: requirementData.priority,
                status: requirementData.status,
                budget: '待评估',
                createTime: new Date().toLocaleString(),
                description: requirementData.requirements,
                products: productTitles
            };
            
            // 添加到需求列表数组
            requirementsData.unshift(newRequirement);
            
            // 更新列表显示
            renderTable(requirementsData);
            updateStats();
            
            // 重置表单
            resetNewRequirementForm();
            
            closeAddRequirementModal();
        }

        // 重置新建需求表单
        function resetNewRequirementForm() {
            // 清空表单字段
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('rooms').value = '2';
            document.getElementById('halls').value = '1';
            document.getElementById('bathrooms').value = '1';
            document.getElementById('address').value = '';
            document.getElementById('requirements').value = '';
            
            // 清空产品选择
            selectedProducts.clear();
            document.querySelectorAll('.product-item.selected').forEach(item => {
                item.classList.remove('selected');
            });
            updateSelectedCount();
            
            // 重置上传文件显示
            const fileList = document.getElementById('uploadedFilesList');
            if (fileList) {
                fileList.innerHTML = '';
            }
        }

        // 导出需求
        function exportRequirements() {
            showToast('导出功能即将上线，敬请期待！', 'info');
        }

        // 更新统计数据
        function updateStats() {
            const total = requirementsData.length;
            const pending = requirementsData.filter(req => req.status === 'pending').length;
            const processing = requirementsData.filter(req => req.status === 'processing').length;
            const completed = requirementsData.filter(req => req.status === 'completed').length;

            document.getElementById('totalRequirements').textContent = total;
            document.getElementById('pendingRequirements').textContent = pending;
            document.getElementById('processingRequirements').textContent = processing;
            document.getElementById('completedRequirements').textContent = completed;
        }

        // 移动端菜单切换
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-open');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('需求管理页面已加载');

            try {
                renderTable();
                updateStats();

                // 初始化产品选择
                if (typeof renderProducts === 'function') {
                    renderProducts();
                }
                if (typeof updateSelectedProducts === 'function') {
                    updateSelectedProducts();
                }
                if (typeof updateSelectedCount === 'function') {
                    updateSelectedCount();
                }

                console.log('页面初始化完成');
            } catch (error) {
                console.error('页面初始化错误:', error);
            }
        });
    </script>
</body>
</html>

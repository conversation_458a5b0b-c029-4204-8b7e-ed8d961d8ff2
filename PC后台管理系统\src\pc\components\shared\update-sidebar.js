/**
 * 批量更新所有页面的侧边栏脚本
 * 使用方法：在Node.js环境中运行此脚本
 */

const fs = require('fs');
const path = require('path');

// 新的侧边栏HTML内容
const newSidebarHTML = `<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <div class="logo-icon"><i class="fas fa-home"></i></div>
            <div>
                <div class="logo-text">智能家居管理</div>
                <div style="font-size: 10px; color: #6b7280;">管理系统</div>
            </div>
        </div>
    </div>
    
    <nav class="nav-menu">
        <div class="nav-section">
            <div class="nav-section-title">个人中心</div>
            <a href="user-profile.html" class="nav-item">我的代办</a>
            <a href="orders.html" class="nav-item">我的订单</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">业务管理</div>
            <a href="design-products.html" class="nav-item">设计商品</a>
            <a href="requirements-management.html" class="nav-item">需求管理</a>
            <a href="design-center.html" class="nav-item">设计中心</a>
            <a href="design-cases.html" class="nav-item">设计案例</a>
            <a href="project-center.html" class="nav-item">项目中心</a>
            <a href="construction-management.html" class="nav-item">施工管理</a>
            <a href="construction-guide.html" class="nav-item">施工指导</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">商务中心</div>
            <a href="products.html" class="nav-item">商品管理</a>
            <a href="orders.html" class="nav-item">订单管理</a>
            <a href="customer-management.html" class="nav-item">客户管理</a>
            <a href="marketing-management.html" class="nav-item">营销管理</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">知识库</div>
            <a href="design-knowledge.html" class="nav-item">设计知识库</a>
            <a href="construction-knowledge.html" class="nav-item">交付知识库</a>
            <a href="market-knowledge.html" class="nav-item">市转知识库</a>
            <a href="security-knowledge.html" class="nav-item">安装知识库</a>
            <a href="design-knowledge-guide.html" class="nav-item">设计知识库</a>
            <a href="product-knowledge.html" class="nav-item">产品知识库</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">系统工具</div>
            <a href="api-tester.html" class="nav-item">API 工具</a>
            <a href="一装ERP-API文档.html" class="nav-item">ERP文档</a>
            <a href="settings.html" class="nav-item">系统配置</a>
            <a href="user-management.html" class="nav-item">用户管理</a>
            <a href="user-permissions.html" class="nav-item">内部权限</a>
            <a href="permissions.html" class="nav-item">客户权限</a>
            <a href="data-management.html" class="nav-item">数据管理</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">数据分析</div>
            <a href="business-analytics.html" class="nav-item">需求分析</a>
            <a href="project-analytics.html" class="nav-item">项目分析</a>
            <a href="order-analytics.html" class="nav-item">订单分析</a>
            <a href="customer-analytics.html" class="nav-item">客户分析</a>
        </div>
        <div class="nav-section">
            <div class="nav-section-title">个人中心</div>
            <a href="demo.html" class="nav-item">演示展示</a>
            <a href="user-profile.html" class="nav-item">个人资料</a>
            <a href="login.html" class="nav-item">退出登录</a>
        </div>
    </nav>
</aside>`;

// 页面目录
const pagesDir = path.join(__dirname, '../pages');

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            files.push(...getAllHtmlFiles(fullPath));
        } else if (item.endsWith('.html')) {
            files.push(fullPath);
        }
    }
    
    return files;
}

// 更新单个文件的侧边栏
function updateSidebarInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 查找并替换侧边栏内容
        const sidebarRegex = /<aside class="sidebar"[^>]*>[\s\S]*?<\/aside>/;
        
        if (sidebarRegex.test(content)) {
            content = content.replace(sidebarRegex, newSidebarHTML);
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已更新: ${path.basename(filePath)}`);
            return true;
        } else {
            console.log(`⚠️  未找到侧边栏: ${path.basename(filePath)}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ 更新失败: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🚀 开始批量更新侧边栏...\n');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    for (const file of htmlFiles) {
        if (updateSidebarInFile(file)) {
            successCount++;
        } else {
            failCount++;
        }
    }
    
    console.log('\n📊 更新统计:');
    console.log(`✅ 成功更新: ${successCount} 个文件`);
    console.log(`❌ 更新失败: ${failCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 侧边栏批量更新完成！');
        console.log('💡 建议检查更新后的页面确保显示正常');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    updateSidebarInFile,
    getAllHtmlFiles,
    newSidebarHTML
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        .content-card {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .card-body {
            padding: 24px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            color: #1f2937;
            background: #f8fafc;
        }

        .tab.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
            background: #f8fafc;
        }

        .campaign-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }

        .campaign-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .campaign-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .campaign-header {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .campaign-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .campaign-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d1fae5;
            color: #059669;
        }

        .status-draft {
            background: #fef3c7;
            color: #d97706;
        }

        .campaign-body {
            padding: 16px;
        }

        .campaign-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .metric-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .test-message {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            color: #1e40af;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }
            
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .campaign-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products.html" class="nav-item">商品管理</a>
                    <a href="orders.html" class="nav-item">订单管理</a>
                    <a href="customer-management.html" class="nav-item">客户管理</a>
                    <a href="marketing-management.html" class="nav-item active">营销管理</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">营销管理</h1>
                <p class="page-description">管理营销活动的策划、执行、监控和分析，提供完整的营销活动生命周期管理</p>
            </div>

            <!-- 测试消息 -->
            <div class="test-message">
                <i class="fas fa-info-circle"></i>
                <strong>页面测试成功！</strong> 营销管理页面已正常加载，所有基本功能都在工作。
            </div>

            <!-- 主要内容 -->
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">营销活动</h2>
                    <div>
                        <button class="btn btn-secondary">
                            <i class="fas fa-filter"></i>
                            筛选
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            新建活动
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 标签页 -->
                    <div class="tabs">
                        <button class="tab active" onclick="showTab('all')">全部活动</button>
                        <button class="tab" onclick="showTab('active')">进行中</button>
                        <button class="tab" onclick="showTab('draft')">草稿</button>
                    </div>

                    <!-- 活动网格 -->
                    <div class="campaign-grid" id="campaignGrid">
                        <!-- 动态内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 示例数据
        const campaigns = [
            {
                id: 1,
                name: '智能家居体验活动',
                status: 'active',
                participants: 156,
                conversion: 23,
                budget: 50000,
                description: '为期一个月的智能家居产品体验活动'
            },
            {
                id: 2,
                name: '新年促销活动',
                status: 'draft',
                participants: 0,
                conversion: 0,
                budget: 80000,
                description: '新年期间的促销活动策划'
            },
            {
                id: 3,
                name: '老客户回馈计划',
                status: 'active',
                participants: 89,
                conversion: 95,
                budget: 30000,
                description: '针对老客户的回馈计划'
            }
        ];

        let currentFilter = 'all';

        function showTab(filter) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新过滤器
            currentFilter = filter;
            renderCampaigns();
        }

        function renderCampaigns() {
            const grid = document.getElementById('campaignGrid');
            
            // 过滤数据
            let filteredCampaigns = campaigns;
            if (currentFilter !== 'all') {
                filteredCampaigns = campaigns.filter(campaign => campaign.status === currentFilter);
            }

            // 渲染HTML
            grid.innerHTML = filteredCampaigns.map(campaign => `
                <div class="campaign-card">
                    <div class="campaign-header">
                        <div class="campaign-name">${campaign.name}</div>
                        <span class="campaign-status status-${campaign.status}">
                            ${campaign.status === 'active' ? '进行中' : '草稿'}
                        </span>
                    </div>
                    <div class="campaign-body">
                        <p style="color: #6b7280; font-size: 14px; margin-bottom: 16px;">${campaign.description}</p>
                        <div class="campaign-metrics">
                            <div class="metric-item">
                                <div class="metric-value">${campaign.participants}</div>
                                <div class="metric-label">参与人数</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${campaign.conversion}%</div>
                                <div class="metric-label">转化率</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">¥${campaign.budget.toLocaleString()}</div>
                                <div class="metric-label">预算</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">2025-01-31</div>
                                <div class="metric-label">结束时间</div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderCampaigns();
            console.log('营销管理页面已加载完成');
        });
    </script>
</body>
</html>

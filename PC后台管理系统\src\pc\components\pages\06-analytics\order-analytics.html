<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单分析 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item active">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">订单分析</h1>
                <p class="page-subtitle">订单数据分析和统计报告</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #3b82f6; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-shopping-cart" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="totalOrders">0</div>
                            <div style="font-size: 14px; color: #6b7280;">总订单数</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #10b981; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-dollar-sign" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="totalRevenue">¥0</div>
                            <div style="font-size: 14px; color: #6b7280;">总销售额</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #f59e0b; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-bar" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="avgOrderValue">¥0</div>
                            <div style="font-size: 14px; color: #6b7280;">平均订单价值</div>
                        </div>
                    </div>
                </div>

                <div class="stat-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 48px; height: 48px; background: #ef4444; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-clock" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 28px; font-weight: 700; color: #1f2937;" id="pendingOrders">0</div>
                            <div style="font-size: 14px; color: #6b7280;">待处理订单</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 30px;">
                <!-- 销售趋势图 -->
                <div class="chart-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #1f2937;">销售趋势</h3>
                    <canvas id="salesTrendChart" width="400" height="200"></canvas>
                </div>

                <!-- 订单状态分布 -->
                <div class="chart-card" style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #e5e7eb;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #1f2937;">订单状态分布</h3>
                    <canvas id="orderStatusChart" width="300" height="200"></canvas>
                </div>
            </div>

            <!-- 订单列表 -->
            <div class="content-container" style="background: white; border-radius: 12px; border: 1px solid #e5e7eb; padding: 24px;">
                <div class="content-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">订单详情</h2>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <input type="text" id="searchInput" placeholder="搜索订单..." style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        <select id="statusFilter" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                            <option value="">所有状态</option>
                            <option value="pending">待处理</option>
                            <option value="confirmed">已确认</option>
                            <option value="shipped">已发货</option>
                            <option value="delivered">已送达</option>
                            <option value="cancelled">已取消</option>
                        </select>
                        <button class="btn btn-primary" onclick="orderAnalytics.exportData()">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>客户</th>
                                <th>订单金额</th>
                                <th>订单状态</th>
                                <th>下单时间</th>
                                <th>支付方式</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- 订单数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div id="pagination" style="display: flex; justify-content: center; align-items: center; gap: 8px; margin-top: 20px;">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 设计系统模拟
        const ds = {
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            formatDate: (dateString) => {
                if (!dateString) return '';
                return new Date(dateString).toLocaleDateString('zh-CN');
            },
            formatCurrency: (amount) => {
                return new Intl.NumberFormat('zh-CN', {
                    style: 'currency',
                    currency: 'CNY'
                }).format(amount);
            }
        };

        class OrderAnalytics {
            constructor() {
                this.orders = [];
                this.filteredOrders = [];
                this.currentPage = 1;
                this.pageSize = 10;
                this.charts = {};

                this.loadData();
                this.init();
            }

            loadData() {
                // 加载订单数据
                this.orders = JSON.parse(localStorage.getItem('orders') || '[]');

                // 初始化默认数据
                if (this.orders.length === 0) {
                    this.orders = [
                        {
                            id: 1,
                            orderNumber: 'ORD-2025-001',
                            customerName: '张三',
                            customerId: 1,
                            totalAmount: 15800,
                            status: 'delivered',
                            paymentMethod: 'alipay',
                            createdAt: new Date(Date.now() - ******** * 5).toISOString(),
                            deliveredAt: new Date(Date.now() - ******** * 2).toISOString(),
                            items: [
                                { name: '智能开关', quantity: 2, price: 299 },
                                { name: '智能插座', quantity: 5, price: 89 }
                            ]
                        },
                        {
                            id: 2,
                            orderNumber: 'ORD-2025-002',
                            customerName: '李四',
                            customerId: 2,
                            totalAmount: 8900,
                            status: 'shipped',
                            paymentMethod: 'wechat',
                            createdAt: new Date(Date.now() - ******** * 3).toISOString(),
                            shippedAt: new Date(Date.now() - ******** * 1).toISOString(),
                            items: [
                                { name: '智能摄像头', quantity: 1, price: 599 },
                                { name: '智能门锁', quantity: 1, price: 1299 }
                            ]
                        },
                        {
                            id: 3,
                            orderNumber: 'ORD-2025-003',
                            customerName: '王五',
                            customerId: 3,
                            totalAmount: 25600,
                            status: 'confirmed',
                            paymentMethod: 'bank',
                            createdAt: new Date(Date.now() - ******** * 2).toISOString(),
                            confirmedAt: new Date(Date.now() - ******** * 1).toISOString(),
                            items: [
                                { name: '智能家居套装', quantity: 1, price: 2999 },
                                { name: '智能音响', quantity: 2, price: 399 }
                            ]
                        },
                        {
                            id: 4,
                            orderNumber: 'ORD-2025-004',
                            customerName: '赵六',
                            customerId: 4,
                            totalAmount: 3200,
                            status: 'pending',
                            paymentMethod: 'alipay',
                            createdAt: new Date(Date.now() - ******** * 1).toISOString(),
                            items: [
                                { name: '智能灯泡', quantity: 8, price: 59 }
                            ]
                        },
                        {
                            id: 5,
                            orderNumber: 'ORD-2025-005',
                            customerName: '钱七',
                            customerId: 5,
                            totalAmount: 12800,
                            status: 'cancelled',
                            paymentMethod: 'wechat',
                            createdAt: new Date(Date.now() - ******** * 7).toISOString(),
                            cancelledAt: new Date(Date.now() - ******** * 6).toISOString(),
                            items: [
                                { name: '智能窗帘', quantity: 2, price: 899 }
                            ]
                        }
                    ];
                    this.saveOrders();
                }
            }

            saveOrders() {
                localStorage.setItem('orders', JSON.stringify(this.orders));
            }

            init() {
                this.updateStatistics();
                this.initCharts();
                this.loadOrders();
                this.bindEvents();
            }

            updateStatistics() {
                const totalOrders = this.orders.length;
                const totalRevenue = this.orders.reduce((sum, order) => sum + order.totalAmount, 0);
                const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
                const pendingOrders = this.orders.filter(order => order.status === 'pending').length;

                document.getElementById('totalOrders').textContent = totalOrders;
                document.getElementById('totalRevenue').textContent = ds.formatCurrency(totalRevenue);
                document.getElementById('avgOrderValue').textContent = ds.formatCurrency(avgOrderValue);
                document.getElementById('pendingOrders').textContent = pendingOrders;
            }

            initCharts() {
                this.initSalesTrendChart();
                this.initOrderStatusChart();
            }

            initSalesTrendChart() {
                const ctx = document.getElementById('salesTrendChart').getContext('2d');

                // 生成过去12个月的销售数据
                const months = [];
                const salesData = [];
                const now = new Date();

                for (let i = 11; i >= 0; i--) {
                    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                    months.push(date.toLocaleDateString('zh-CN', { month: 'short' }));

                    // 计算该月销售额
                    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
                    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

                    const monthlySales = this.orders.filter(order => {
                        const orderDate = new Date(order.createdAt);
                        return orderDate >= monthStart && orderDate <= monthEnd && order.status !== 'cancelled';
                    }).reduce((sum, order) => sum + order.totalAmount, 0);

                    salesData.push(monthlySales);
                }

                this.charts.sales = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: months,
                        datasets: [{
                            label: '销售额',
                            data: salesData,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }

            initOrderStatusChart() {
                const ctx = document.getElementById('orderStatusChart').getContext('2d');

                // 统计订单状态分布
                const statusCount = {
                    pending: 0,
                    confirmed: 0,
                    shipped: 0,
                    delivered: 0,
                    cancelled: 0
                };

                this.orders.forEach(order => {
                    statusCount[order.status]++;
                });

                this.charts.status = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['待处理', '已确认', '已发货', '已送达', '已取消'],
                        datasets: [{
                            data: [
                                statusCount.pending,
                                statusCount.confirmed,
                                statusCount.shipped,
                                statusCount.delivered,
                                statusCount.cancelled
                            ],
                            backgroundColor: ['#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#ef4444'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            loadOrders() {
                // 应用筛选条件
                this.filteredOrders = [...this.orders];

                // 搜索筛选
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    const keyword = searchInput.value.trim().toLowerCase();
                    this.filteredOrders = this.filteredOrders.filter(order =>
                        order.orderNumber.toLowerCase().includes(keyword) ||
                        order.customerName.toLowerCase().includes(keyword)
                    );
                }

                // 状态筛选
                const statusFilter = document.getElementById('statusFilter');
                if (statusFilter && statusFilter.value) {
                    this.filteredOrders = this.filteredOrders.filter(order =>
                        order.status === statusFilter.value
                    );
                }

                // 按创建时间倒序排列
                this.filteredOrders.sort((a, b) => {
                    return new Date(b.createdAt) - new Date(a.createdAt);
                });

                this.renderOrders();
                this.renderPagination();
            }

            renderOrders() {
                const tbody = document.getElementById('ordersTableBody');
                if (!tbody) return;

                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageOrders = this.filteredOrders.slice(startIndex, endIndex);

                if (pageOrders.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px; color: #6b7280;">
                                <i class="fas fa-shopping-cart fa-2x" style="display: block; margin-bottom: 12px; opacity: 0.3;"></i>
                                暂无订单数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = pageOrders.map(order => `
                    <tr>
                        <td>
                            <div style="font-weight: 500;">${order.orderNumber}</div>
                        </td>
                        <td>
                            <div style="font-weight: 500;">${order.customerName}</div>
                            <div style="font-size: 12px; color: #6b7280;">ID: ${order.customerId}</div>
                        </td>
                        <td style="font-weight: 600; color: #1f2937;">${ds.formatCurrency(order.totalAmount)}</td>
                        <td>
                            <span class="status-badge ${this.getStatusClass(order.status)}">
                                ${this.getStatusText(order.status)}
                            </span>
                        </td>
                        <td>${ds.formatDate(order.createdAt)}</td>
                        <td>
                            <span class="payment-badge ${this.getPaymentClass(order.paymentMethod)}">
                                ${this.getPaymentText(order.paymentMethod)}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-secondary" onclick="orderAnalytics.viewOrder(${order.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            renderPagination() {
                const totalPages = Math.ceil(this.filteredOrders.length / this.pageSize);
                const pagination = document.getElementById('pagination');

                if (!pagination || totalPages <= 1) {
                    if (pagination) pagination.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                if (this.currentPage > 1) {
                    paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="orderAnalytics.goToPage(${this.currentPage - 1})">上一页</button>`;
                }

                for (let i = 1; i <= totalPages; i++) {
                    if (i === this.currentPage) {
                        paginationHTML += `<button class="btn btn-sm btn-primary">${i}</button>`;
                    } else {
                        paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="orderAnalytics.goToPage(${i})">${i}</button>`;
                    }
                }

                if (this.currentPage < totalPages) {
                    paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="orderAnalytics.goToPage(${this.currentPage + 1})">下一页</button>`;
                }

                pagination.innerHTML = paginationHTML;
            }

            goToPage(page) {
                this.currentPage = page;
                this.renderOrders();
                this.renderPagination();
            }

            bindEvents() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        this.currentPage = 1;
                        this.loadOrders();
                    });
                }

                const statusFilter = document.getElementById('statusFilter');
                if (statusFilter) {
                    statusFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadOrders();
                    });
                }
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待处理',
                    'confirmed': '已确认',
                    'shipped': '已发货',
                    'delivered': '已送达',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            getStatusClass(status) {
                const classMap = {
                    'pending': 'status-warning',
                    'confirmed': 'status-info',
                    'shipped': 'status-primary',
                    'delivered': 'status-success',
                    'cancelled': 'status-danger'
                };
                return classMap[status] || 'status-info';
            }

            getPaymentText(method) {
                const methodMap = {
                    'alipay': '支付宝',
                    'wechat': '微信支付',
                    'bank': '银行转账',
                    'cash': '现金支付'
                };
                return methodMap[method] || method;
            }

            getPaymentClass(method) {
                const classMap = {
                    'alipay': 'payment-alipay',
                    'wechat': 'payment-wechat',
                    'bank': 'payment-bank',
                    'cash': 'payment-cash'
                };
                return classMap[method] || 'payment-default';
            }

            viewOrder(id) {
                const order = this.orders.find(o => o.id === parseInt(id));
                if (!order) {
                    ds.showToast('订单不存在', 'error');
                    return;
                }

                // 创建详情模态框
                const modal = document.createElement('div');
                modal.className = 'order-detail-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="modal-content" onclick="event.stopPropagation()" style="background: white; border-radius: 12px; width: 90%; max-width: 700px; max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                            <div class="modal-header" style="padding: 24px 24px 0 24px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0; font-size: 20px; font-weight: 600; color: #1f2937;">订单详情</h3>
                                <button onclick="this.closest('.order-detail-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 4px;">&times;</button>
                            </div>

                            <div class="modal-body" style="padding: 0 24px 24px 24px;">
                                <div style="display: grid; gap: 20px;">
                                    <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                        <span class="status-badge ${this.getStatusClass(order.status)}">${this.getStatusText(order.status)}</span>
                                        <span class="payment-badge ${this.getPaymentClass(order.paymentMethod)}">${this.getPaymentText(order.paymentMethod)}</span>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">订单号</h5>
                                            <p style="margin: 0; color: #6b7280; font-weight: 500;">${order.orderNumber}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">客户</h5>
                                            <p style="margin: 0; color: #6b7280;">${order.customerName}</p>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">订单商品</h5>
                                        <div style="background: #f9fafb; padding: 16px; border-radius: 8px; border: 1px solid #e5e7eb;">
                                            ${order.items.map(item => `
                                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                                                    <div>
                                                        <div style="font-weight: 500; color: #1f2937;">${item.name}</div>
                                                        <div style="font-size: 12px; color: #6b7280;">单价: ${ds.formatCurrency(item.price)}</div>
                                                    </div>
                                                    <div style="text-align: right;">
                                                        <div style="font-weight: 500; color: #1f2937;">x${item.quantity}</div>
                                                        <div style="font-size: 12px; color: #6b7280;">${ds.formatCurrency(item.price * item.quantity)}</div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0 0 0; font-weight: 600; color: #1f2937; font-size: 16px;">
                                                <span>总计</span>
                                                <span>${ds.formatCurrency(order.totalAmount)}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">下单时间</h5>
                                            <p style="margin: 0; color: #6b7280;">${ds.formatDate(order.createdAt)}</p>
                                        </div>
                                        <div>
                                            <h5 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #374151;">订单金额</h5>
                                            <p style="margin: 0; color: #6b7280; font-size: 18px; font-weight: 600;">${ds.formatCurrency(order.totalAmount)}</p>
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; display: flex; gap: 8px; justify-content: flex-end;">
                                    <button onclick="this.closest('.order-detail-modal').remove()" style="padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            }

            exportData() {
                try {
                    ds.showToast('正在导出订单数据...', 'info');

                    const exportData = this.filteredOrders;
                    const headers = ['订单号', '客户姓名', '订单金额', '订单状态', '支付方式', '下单时间'];
                    const csvContent = [
                        headers.join(','),
                        ...exportData.map(order => [
                            order.orderNumber,
                            `"${order.customerName.replace(/"/g, '""')}"`,
                            order.totalAmount,
                            this.getStatusText(order.status),
                            this.getPaymentText(order.paymentMethod),
                            ds.formatDate(order.createdAt)
                        ].join(','))
                    ].join('\n');

                    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `order_analytics_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    ds.showToast(`订单数据导出成功 (${exportData.length}条记录)`, 'success');
                } catch (error) {
                    ds.showToast('导出失败', 'error');
                    console.error('导出失败:', error);
                }
            }
        }

        // 初始化订单分析
        const orderAnalytics = new OrderAnalytics();
        </script>
</body>
</html>

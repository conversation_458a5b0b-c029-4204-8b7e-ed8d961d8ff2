/**
 * 文件管理模块
 * 实现文档上传、预览、打印功能
 * 支持PDF、Word、Excel、PowerPoint等格式
 * 版本: v2.0
 */

class FileManager {
    constructor() {
        this.supportedFormats = {
            pdf: ['application/pdf'],
            word: ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            excel: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            powerpoint: ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],
            image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'],
            text: ['text/plain', 'text/html', 'text/css', 'text/javascript']
        };
        
        this.maxFileSize = 50 * 1024 * 1024; // 50MB
        this.documents = this.loadDocuments();
        this.init();
    }

    /**
     * 初始化文件管理器
     */
    init() {
        console.log('📁 文件管理器已初始化');
        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('file-manager-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'file-manager-styles';
        styles.textContent = `
            .file-upload-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .file-upload-content {
                background: white;
                border-radius: 12px;
                padding: 24px;
                width: 90%;
                max-width: 500px;
                max-height: 80vh;
                overflow-y: auto;
            }

            .file-drop-zone {
                border: 2px dashed #d1d5db;
                border-radius: 8px;
                padding: 40px 20px;
                text-align: center;
                background: #f9fafb;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .file-drop-zone:hover,
            .file-drop-zone.dragover {
                border-color: #3b82f6;
                background: #eff6ff;
            }

            .file-preview-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
            }

            .file-preview-content {
                background: white;
                border-radius: 8px;
                width: 90%;
                height: 90%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .file-preview-header {
                padding: 16px 24px;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f9fafb;
            }

            .file-preview-body {
                flex: 1;
                padding: 20px;
                overflow: auto;
            }

            .upload-progress {
                width: 100%;
                height: 8px;
                background: #e5e7eb;
                border-radius: 4px;
                overflow: hidden;
                margin: 16px 0;
            }

            .upload-progress-bar {
                height: 100%;
                background: #3b82f6;
                transition: width 0.3s ease;
            }

            @keyframes slideInUp {
                from { transform: translateY(100%); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            @keyframes slideOutDown {
                from { transform: translateY(0); opacity: 1; }
                to { transform: translateY(100%); opacity: 0; }
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * 上传文档
     */
    async uploadDocument(phase) {
        try {
            const modal = this.createUploadModal(phase);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
        } catch (error) {
            console.error('创建上传模态框失败:', error);
            constructionManager.showErrorMessage('上传功能初始化失败');
        }
    }

    /**
     * 创建上传模态框
     */
    createUploadModal(phase) {
        const modal = document.createElement('div');
        modal.className = 'file-upload-modal';
        
        modal.innerHTML = `
            <div class="file-upload-content">
                <div class="modal-header">
                    <h3><i class="fas fa-upload"></i> 上传文档</h3>
                    <button class="btn-close" onclick="this.closest('.file-upload-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="file-drop-zone" id="fileDropZone">
                    <div class="drop-zone-content">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;"></i>
                        <p style="margin: 0 0 8px 0; font-size: 16px; color: #374151;">拖拽文件到此处或点击选择文件</p>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">支持 PDF, Word, Excel, PowerPoint, 图片等格式</p>
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #9ca3af;">最大文件大小: 50MB</p>
                    </div>
                    <input type="file" id="fileInput" style="display: none;" multiple 
                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.bmp,.txt">
                </div>
                
                <div id="uploadProgress" style="display: none;">
                    <div class="upload-progress">
                        <div class="upload-progress-bar" id="progressBar"></div>
                    </div>
                    <div id="uploadStatus" style="text-align: center; color: #6b7280; font-size: 14px;"></div>
                </div>
                
                <div id="fileList" style="margin-top: 20px;"></div>
                
                <div class="modal-footer" style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.file-upload-modal').remove()">取消</button>
                    <button class="btn btn-primary" id="uploadBtn" onclick="fileManager.processUpload('${phase}', this)" disabled>
                        <i class="fas fa-upload"></i> 开始上传
                    </button>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindUploadEvents(modal, phase);
        
        return modal;
    }

    /**
     * 绑定上传事件
     */
    bindUploadEvents(modal, phase) {
        const dropZone = modal.querySelector('#fileDropZone');
        const fileInput = modal.querySelector('#fileInput');
        const fileList = modal.querySelector('#fileList');
        const uploadBtn = modal.querySelector('#uploadBtn');
        
        let selectedFiles = [];

        // 点击选择文件
        dropZone.addEventListener('click', () => fileInput.click());

        // 拖拽事件
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleFileSelection(files, fileList, uploadBtn, selectedFiles);
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFileSelection(files, fileList, uploadBtn, selectedFiles);
        });

        // 存储选中的文件到模态框
        modal.selectedFiles = selectedFiles;
    }

    /**
     * 处理文件选择
     */
    handleFileSelection(files, fileList, uploadBtn, selectedFiles) {
        files.forEach(file => {
            // 验证文件
            const validation = this.validateFile(file);
            if (validation.valid) {
                selectedFiles.push(file);
            } else {
                constructionManager.showErrorMessage(`文件 ${file.name}: ${validation.error}`);
            }
        });

        // 更新文件列表显示
        this.updateFileList(selectedFiles, fileList);
        
        // 更新上传按钮状态
        uploadBtn.disabled = selectedFiles.length === 0;
    }

    /**
     * 验证文件
     */
    validateFile(file) {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
            return { valid: false, error: '文件大小超过50MB限制' };
        }

        // 检查文件类型
        const isSupported = Object.values(this.supportedFormats)
            .flat()
            .includes(file.type);

        if (!isSupported) {
            return { valid: false, error: '不支持的文件格式' };
        }

        return { valid: true };
    }

    /**
     * 更新文件列表显示
     */
    updateFileList(files, container) {
        if (files.length === 0) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = `
            <h4 style="margin: 0 0 12px 0; color: #374151;">已选择文件 (${files.length})</h4>
            ${files.map((file, index) => `
                <div class="file-item" style="display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; background: #f9fafb; border-radius: 6px; margin-bottom: 8px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas ${this.getFileIcon(file.type)}" style="color: #6b7280;"></i>
                        <div>
                            <div style="font-size: 14px; color: #374151;">${file.name}</div>
                            <div style="font-size: 12px; color: #6b7280;">${constructionManager.formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-danger" onclick="fileManager.removeFile(${index}, this)" title="移除">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('')}
        `;
    }

    /**
     * 移除文件
     */
    removeFile(index, button) {
        const modal = button.closest('.file-upload-modal');
        const selectedFiles = modal.selectedFiles;
        const fileList = modal.querySelector('#fileList');
        const uploadBtn = modal.querySelector('#uploadBtn');

        selectedFiles.splice(index, 1);
        this.updateFileList(selectedFiles, fileList);
        uploadBtn.disabled = selectedFiles.length === 0;
    }

    /**
     * 处理上传
     */
    async processUpload(phase, button) {
        const modal = button.closest('.file-upload-modal');
        const selectedFiles = modal.selectedFiles;
        const progressContainer = modal.querySelector('#uploadProgress');
        const progressBar = modal.querySelector('#progressBar');
        const statusText = modal.querySelector('#uploadStatus');

        if (selectedFiles.length === 0) {
            constructionManager.showWarningMessage('请先选择文件');
            return;
        }

        try {
            // 显示进度
            progressContainer.style.display = 'block';
            button.disabled = true;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = ((i + 1) / selectedFiles.length) * 100;
                
                progressBar.style.width = `${progress}%`;
                statusText.textContent = `正在上传: ${file.name} (${i + 1}/${selectedFiles.length})`;

                // 模拟上传过程
                await this.uploadSingleFile(file, phase);
                
                // 短暂延迟以显示进度
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 上传完成
            constructionManager.showSuccessMessage(`成功上传 ${selectedFiles.length} 个文件`);
            modal.remove();
            
            // 刷新文档列表
            constructionManager.renderDocuments(phase);

        } catch (error) {
            console.error('文件上传失败:', error);
            constructionManager.showErrorMessage('文件上传失败: ' + error.message);
        } finally {
            button.disabled = false;
        }
    }

    /**
     * 上传单个文件
     */
    async uploadSingleFile(file, phase) {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();
                
                reader.onload = (e) => {
                    const document = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: e.target.result,
                        uploadTime: Date.now(),
                        phase: phase
                    };

                    // 保存到阶段数据
                    constructionManager.phaseData[phase].documents.push(document);
                    
                    // 保存到文档库
                    this.documents.push(document);
                    this.saveDocuments();
                    
                    // 保存阶段数据
                    constructionManager.savePhaseData();
                    
                    resolve(document);
                };

                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 预览文档
     */
    previewDocument(documentId) {
        try {
            const document = this.findDocument(documentId);
            if (!document) {
                constructionManager.showErrorMessage('文档不存在');
                return;
            }

            const modal = this.createPreviewModal(document);
            document.body.appendChild(modal);
            modal.style.display = 'flex';

        } catch (error) {
            console.error('预览文档失败:', error);
            constructionManager.showErrorMessage('文档预览失败');
        }
    }

    /**
     * 创建预览模态框
     */
    createPreviewModal(document) {
        const modal = document.createElement('div');
        modal.className = 'file-preview-modal';
        
        modal.innerHTML = `
            <div class="file-preview-content">
                <div class="file-preview-header">
                    <div>
                        <h3 style="margin: 0; color: #374151;">${document.name}</h3>
                        <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">
                            ${constructionManager.formatFileSize(document.size)} • ${constructionManager.formatDate(document.uploadTime)}
                        </p>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-secondary btn-sm" onclick="fileManager.printDocument(${document.id})" title="打印">
                            <i class="fas fa-print"></i> 打印
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="this.closest('.file-preview-modal').remove()" title="关闭">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="file-preview-body">
                    ${this.generatePreviewContent(document)}
                </div>
            </div>
        `;

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    /**
     * 生成预览内容
     */
    generatePreviewContent(document) {
        const fileType = this.getFileType(document.type);
        
        switch (fileType) {
            case 'pdf':
                return `<iframe src="${document.content}" style="width: 100%; height: 100%; border: none;"></iframe>`;
            
            case 'image':
                return `<div style="text-align: center; padding: 20px;">
                    <img src="${document.content}" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="${document.name}">
                </div>`;
            
            case 'text':
                return `<div style="padding: 20px; font-family: monospace; white-space: pre-wrap; background: #f9fafb; border-radius: 6px;">
                    正在加载文本内容...
                </div>`;
            
            default:
                return `<div style="text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-file-alt" style="font-size: 64px; margin-bottom: 16px;"></i>
                    <h4>无法预览此文件类型</h4>
                    <p>请下载文件后使用相应的应用程序打开</p>
                    <button class="btn btn-primary" onclick="fileManager.downloadDocument(${document.id})">
                        <i class="fas fa-download"></i> 下载文件
                    </button>
                </div>`;
        }
    }

    /**
     * 打印文档
     */
    printDocument(documentId) {
        try {
            const document = this.findDocument(documentId);
            if (!document) {
                constructionManager.showErrorMessage('文档不存在');
                return;
            }

            // 创建打印窗口
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>打印 - ${document.name}</title>
                    <style>
                        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                        .print-header { text-align: center; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px; }
                        .print-content { width: 100%; }
                        img { max-width: 100%; height: auto; }
                        iframe { width: 100%; height: 80vh; border: none; }
                        @media print { .print-header { display: none; } }
                    </style>
                </head>
                <body>
                    <div class="print-header">
                        <h2>${document.name}</h2>
                        <p>打印时间: ${new Date().toLocaleString('zh-CN')}</p>
                    </div>
                    <div class="print-content">
                        ${this.generatePrintContent(document)}
                    </div>
                </body>
                </html>
            `);
            
            printWindow.document.close();
            
            // 等待内容加载后打印
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 1000);

        } catch (error) {
            console.error('打印文档失败:', error);
            constructionManager.showErrorMessage('打印功能失败');
        }
    }

    /**
     * 生成打印内容
     */
    generatePrintContent(document) {
        const fileType = this.getFileType(document.type);
        
        switch (fileType) {
            case 'pdf':
                return `<iframe src="${document.content}"></iframe>`;
            case 'image':
                return `<img src="${document.content}" alt="${document.name}">`;
            default:
                return `<p>此文件类型不支持直接打印，请使用相应的应用程序打开后打印。</p>`;
        }
    }

    /**
     * 工具方法
     */
    findDocument(documentId) {
        return this.documents.find(doc => doc.id == documentId);
    }

    getFileType(mimeType) {
        for (const [type, mimes] of Object.entries(this.supportedFormats)) {
            if (mimes.includes(mimeType)) {
                return type;
            }
        }
        return 'unknown';
    }

    getFileIcon(mimeType) {
        const type = this.getFileType(mimeType);
        const icons = {
            pdf: 'fa-file-pdf',
            word: 'fa-file-word',
            excel: 'fa-file-excel',
            powerpoint: 'fa-file-powerpoint',
            image: 'fa-file-image',
            text: 'fa-file-alt'
        };
        return icons[type] || 'fa-file';
    }

    /**
     * 数据持久化
     */
    loadDocuments() {
        try {
            const data = localStorage.getItem('construction_documents');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('加载文档数据失败:', error);
            return [];
        }
    }

    saveDocuments() {
        try {
            localStorage.setItem('construction_documents', JSON.stringify(this.documents));
        } catch (error) {
            console.error('保存文档数据失败:', error);
        }
    }
}

// 全局实例
let fileManager;

// 延迟初始化，等待其他模块加载
if (typeof window !== 'undefined') {
    window.FileManager = FileManager;
}

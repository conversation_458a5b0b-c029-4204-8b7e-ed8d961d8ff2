<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }


        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 第一层：面包屑导航样式 */
        .top-nav {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }

        .breadcrumb-section {
            flex: 1;
        }

        .page-title-section {
            margin-top: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
            max-width: 600px;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 第二层：主菜单栏样式 */
        .main-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .main-menu-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .main-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .main-menu-tab {
            flex-shrink: 0;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
        }

        .main-menu-tab:hover {
            color: #1f2937;
            background: #f8fafc;
        }

        .main-menu-tab.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
            background: #f8fafc;
        }

        /* 营销生命周期展示区域 */
        .lifecycle-section {
            padding: 24px;
            background: #f8fafc;
        }

        .lifecycle-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .lifecycle-cards {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            scrollbar-width: none;
            padding-bottom: 8px;
        }

        .lifecycle-cards::-webkit-scrollbar {
            display: none;
        }

        .lifecycle-card {
            flex-shrink: 0;
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lifecycle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #9ca3af;
        }

        .lifecycle-card-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 20px;
            color: #374151;
        }

        .lifecycle-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .lifecycle-card-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 第三层：子菜单栏样式 */
        .sub-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
        }

        .sub-menu-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .sub-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .sub-menu-tab {
            flex-shrink: 0;
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .sub-menu-tab:hover {
            color: #1f2937;
            border-color: #1f2937;
            background: #f9fafb;
        }

        .sub-menu-tab.active {
            color: #ffffff;
            background: #1f2937;
            border-color: #1f2937;
        }

        /* 第四层：内容区域样式 */
        .content-area {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .content-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .content-body {
            padding: 24px;
        }

        /* 营销活动卡片网格 */
        .campaign-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .campaign-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .campaign-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .campaign-header {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .campaign-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .campaign-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d1fae5;
            color: #059669;
        }

        .status-ended {
            background: #f3f4f6;
            color: #374151;
        }

        .status-draft {
            background: #fef3c7;
            color: #d97706;
        }

        .campaign-body {
            padding: 16px;
        }

        .campaign-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .metric-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .campaign-actions {
            display: flex;
            gap: 8px;
        }

        /* 搜索和筛选区域 */
        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 16px;
        }

        /* 图表和分析区域样式 */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .analytics-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .analytics-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .analytics-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .chart-placeholder {
            width: 100%;
            height: 200px;
            background: #f8fafc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 14px;
            border: 2px dashed #e5e7eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 4px;
        }

        .stat-change.positive {
            color: #059669;
        }

        .stat-change.negative {
            color: #dc2626;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .top-nav {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .nav-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .lifecycle-cards {
                gap: 12px;
            }

            .lifecycle-card {
                min-width: 160px;
                padding: 16px;
            }

            .campaign-grid {
                grid-template-columns: 1fr;
            }

            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .search-filter-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .campaign-metrics {
                grid-template-columns: 1fr;
            }
        }
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item active">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 第一层：面包屑导航 -->
            <div class="top-nav">
                <div class="breadcrumb-section">
                    <div class="page-title-section">
                        <h1 class="page-title">营销管理</h1>
                        <p class="page-description">管理营销活动的策划、执行、监控和分析，提供完整的营销活动生命周期管理</p>
                    </div>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        新建活动
                    </button>
                </div>
            </div>

            <!-- 第二层：主菜单栏 -->
            <div class="main-menu-nav">
                <div class="main-menu-tabs">
                    <button class="main-menu-tab active" onclick="showMainMenu('campaigns')">营销活动</button>
                    <button class="main-menu-tab" onclick="showMainMenu('analytics')">客户分析</button>
                    <button class="main-menu-tab" onclick="showMainMenu('statistics')">效果统计</button>
                </div>

                <!-- 营销生命周期展示区域 -->
                <div class="lifecycle-section">
                    <h3 class="lifecycle-title">营销生命周期</h3>
                    <div class="lifecycle-cards">
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="lifecycle-card-title">策划准备</div>
                            <div class="lifecycle-card-desc">市场调研、目标设定、策略制定</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="lifecycle-card-title">活动执行</div>
                            <div class="lifecycle-card-desc">活动上线、渠道推广、内容发布</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="lifecycle-card-title">效果监控</div>
                            <div class="lifecycle-card-desc">实时监控、数据跟踪、异常处理</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="lifecycle-card-title">数据分析</div>
                            <div class="lifecycle-card-desc">效果评估、ROI分析、报告生成</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="lifecycle-card-title">优化调整</div>
                            <div class="lifecycle-card-desc">策略优化、经验总结、持续改进</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三层：子菜单栏 -->
            <div class="sub-menu-nav">
                <div class="sub-menu-tabs" id="subMenuTabs">
                    <!-- 动态内容，根据主菜单选择显示 -->
                </div>
            </div>

            <!-- 第四层：内容区域 -->
            <div class="content-area">
                <div class="content-header">
                    <h2 class="content-title" id="contentTitle">营销活动</h2>
                    <div class="content-actions" id="contentActions">
                        <!-- 动态内容，根据当前页面显示 -->
                    </div>
                </div>
                <div class="content-body" id="contentBody">
                    <!-- 动态内容区域 -->
                </div>
            </div>

        </main>
    </div>

    <script>
        // 全局状态管理
        const appState = {
            currentMainMenu: 'campaigns',
            currentSubMenu: 'all',
            campaigns: [
                {
                    id: 1,
                    name: '智能家居体验活动',
                    status: 'active',
                    budget: 50000,
                    participants: 156,
                    conversion: 23,
                    description: '为期一个月的智能家居产品体验活动，包含免费上门体验和产品试用',
                    startDate: '2025-01-01',
                    endDate: '2025-01-31'
                },
                {
                    id: 2,
                    name: '新年促销活动',
                    status: 'draft',
                    budget: 80000,
                    participants: 0,
                    conversion: 0,
                    description: '新年期间的促销活动策划，包含产品折扣和套餐优惠',
                    startDate: '2025-02-01',
                    endDate: '2025-02-15'
                },
                {
                    id: 3,
                    name: '老客户回馈计划',
                    status: 'active',
                    budget: 30000,
                    participants: 89,
                    conversion: 95,
                    description: '针对老客户的回馈计划，提供升级服务和专属优惠',
                    startDate: '2025-01-15',
                    endDate: '2025-03-15'
                }
            ],
            analytics: {
                userProfile: { totalUsers: 1250, newUsers: 156, activeUsers: 890 },
                behavior: { pageViews: 15420, sessionDuration: 285, bounceRate: 32 },
                conversion: { leads: 234, customers: 89, conversionRate: 38 },
                retention: { day1: 85, day7: 62, day30: 45 }
            },
            statistics: {
                traffic: { total: 25680, organic: 12340, paid: 8920, social: 4420 },
                conversion: { rate: 3.2, value: 156780, cost: 48920 },
                revenue: { total: 890000, growth: 15.6, roi: 280 }
            }
        };

        // 主菜单切换
        function showMainMenu(menu) {
            // 更新主菜单激活状态
            document.querySelectorAll('.main-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状态
            appState.currentMainMenu = menu;
            appState.currentSubMenu = getDefaultSubMenu(menu);

            // 更新子菜单
            updateSubMenu(menu);

            // 更新内容区域
            updateContent();
        }

        // 获取默认子菜单
        function getDefaultSubMenu(mainMenu) {
            const defaults = {
                'campaigns': 'all',
                'analytics': 'profile',
                'statistics': 'traffic'
            };
            return defaults[mainMenu] || 'all';
        }

        // 更新子菜单
        function updateSubMenu(mainMenu) {
            const subMenuTabs = document.getElementById('subMenuTabs');
            let subMenus = [];

            switch(mainMenu) {
                case 'campaigns':
                    subMenus = [
                        { id: 'all', name: '全部活动' },
                        { id: 'active', name: '进行中' },
                        { id: 'ended', name: '已结束' },
                        { id: 'draft', name: '草稿活动' }
                    ];
                    break;
                case 'analytics':
                    subMenus = [
                        { id: 'profile', name: '用户画像' },
                        { id: 'behavior', name: '行为分析' },
                        { id: 'conversion', name: '转化漏斗' },
                        { id: 'retention', name: '留存分析' }
                    ];
                    break;
                case 'statistics':
                    subMenus = [
                        { id: 'traffic', name: '流量统计' },
                        { id: 'conversion', name: '转化统计' },
                        { id: 'revenue', name: '收入统计' },
                        { id: 'roi', name: 'ROI分析' }
                    ];
                    break;
            }

            subMenuTabs.innerHTML = subMenus.map(menu =>
                `<button class="sub-menu-tab ${menu.id === appState.currentSubMenu ? 'active' : ''}"
                         onclick="showSubMenu('${mainMenu}', '${menu.id}')">${menu.name}</button>`
            ).join('');
        }

        // 子菜单切换
        function showSubMenu(mainMenu, subMenu) {
            // 更新子菜单激活状态
            document.querySelectorAll('.sub-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状态
            appState.currentSubMenu = subMenu;

            // 更新内容区域
            updateContent();
        }

        // 更新内容区域
        function updateContent() {
            const contentTitle = document.getElementById('contentTitle');
            const contentActions = document.getElementById('contentActions');
            const contentBody = document.getElementById('contentBody');

            switch(appState.currentMainMenu) {
                case 'campaigns':
                    renderCampaignsContent(contentTitle, contentActions, contentBody);
                    break;
                case 'analytics':
                    renderAnalyticsContent(contentTitle, contentActions, contentBody);
                    break;
                case 'statistics':
                    renderStatisticsContent(contentTitle, contentActions, contentBody);
                    break;
            }
        }

        // 渲染营销活动内容
        function renderCampaignsContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '营销活动';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary">
                    <i class="fas fa-filter"></i>
                    筛选
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新建活动
                </button>
            `;

            // 根据子菜单筛选活动
            let filteredCampaigns = appState.campaigns;
            if (appState.currentSubMenu !== 'all') {
                filteredCampaigns = appState.campaigns.filter(campaign =>
                    campaign.status === appState.currentSubMenu
                );
            }

            bodyEl.innerHTML = `
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索活动名称、描述..." onkeyup="searchCampaigns(this.value)">
                    </div>
                </div>
                <div class="campaign-grid">
                    ${filteredCampaigns.map(campaign => `
                        <div class="campaign-card">
                            <div class="campaign-header">
                                <div class="campaign-name">${campaign.name}</div>
                                <span class="campaign-status status-${campaign.status}">
                                    ${getStatusText(campaign.status)}
                                </span>
                            </div>
                            <div class="campaign-body">
                                <p style="color: #6b7280; font-size: 14px; margin-bottom: 16px;">${campaign.description}</p>
                                <div class="campaign-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">${campaign.participants}</div>
                                        <div class="metric-label">参与人数</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">${campaign.conversion}%</div>
                                        <div class="metric-label">转化率</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">¥${campaign.budget.toLocaleString()}</div>
                                        <div class="metric-label">预算</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">${campaign.endDate}</div>
                                        <div class="metric-label">结束时间</div>
                                    </div>
                                </div>
                                <div class="campaign-actions">
                                    <button class="btn-sm btn-secondary">编辑</button>
                                    <button class="btn-sm btn-primary">查看</button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 渲染客户分析内容
        function renderAnalyticsContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '客户分析';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    导出数据
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-chart-bar"></i>
                    生成报告
                </button>
            `;

            const currentData = appState.analytics[appState.currentSubMenu];

            bodyEl.innerHTML = `
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <div class="analytics-title">${getAnalyticsTitle(appState.currentSubMenu)}</div>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area" style="font-size: 48px; margin-bottom: 12px;"></i>
                            <div>图表数据加载中...</div>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-title">关键指标</div>
                        <div class="stats-grid">
                            ${Object.entries(currentData).map(([key, value]) => `
                                <div class="stat-card">
                                    <div class="stat-value">${value}</div>
                                    <div class="stat-label">${getMetricLabel(key)}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        // 渲染效果统计内容
        function renderStatisticsContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '效果统计';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary">
                    <i class="fas fa-calendar"></i>
                    时间范围
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-file-export"></i>
                    导出报告
                </button>
            `;

            const currentData = appState.statistics[appState.currentSubMenu];

            bodyEl.innerHTML = `
                <div class="stats-grid">
                    ${Object.entries(currentData).map(([key, value]) => `
                        <div class="stat-card">
                            <div class="stat-value">${typeof value === 'number' ? value.toLocaleString() : value}</div>
                            <div class="stat-label">${getStatLabel(key)}</div>
                            <div class="stat-change positive">+12.5%</div>
                        </div>
                    `).join('')}
                </div>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <div class="analytics-title">${getStatisticsTitle(appState.currentSubMenu)}</div>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 12px;"></i>
                            <div>统计图表加载中...</div>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-title">趋势分析</div>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-bar" style="font-size: 48px; margin-bottom: 12px;"></i>
                            <div>趋势图表加载中...</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '进行中',
                'ended': '已结束',
                'draft': '草稿'
            };
            return statusMap[status] || status;
        }

        // 获取分析标题
        function getAnalyticsTitle(type) {
            const titleMap = {
                'profile': '用户画像分析',
                'behavior': '用户行为分析',
                'conversion': '转化漏斗分析',
                'retention': '用户留存分析'
            };
            return titleMap[type] || '数据分析';
        }

        // 获取统计标题
        function getStatisticsTitle(type) {
            const titleMap = {
                'traffic': '流量趋势统计',
                'conversion': '转化效果统计',
                'revenue': '收入增长统计',
                'roi': 'ROI投资回报分析'
            };
            return titleMap[type] || '效果统计';
        }

        // 获取指标标签
        function getMetricLabel(key) {
            const labelMap = {
                'totalUsers': '总用户数',
                'newUsers': '新用户',
                'activeUsers': '活跃用户',
                'pageViews': '页面浏览量',
                'sessionDuration': '会话时长(秒)',
                'bounceRate': '跳出率(%)',
                'leads': '潜在客户',
                'customers': '转化客户',
                'conversionRate': '转化率(%)',
                'day1': '次日留存(%)',
                'day7': '7日留存(%)',
                'day30': '30日留存(%)'
            };
            return labelMap[key] || key;
        }

        // 获取统计标签
        function getStatLabel(key) {
            const labelMap = {
                'total': '总流量',
                'organic': '自然流量',
                'paid': '付费流量',
                'social': '社交流量',
                'rate': '转化率(%)',
                'value': '转化价值',
                'cost': '获客成本',
                'growth': '增长率(%)',
                'roi': 'ROI(%)'
            };
            return labelMap[key] || key;
        }

        // 搜索活动
        function searchCampaigns(query) {
            // 这里可以实现搜索逻辑
            console.log('搜索活动:', query);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化子菜单
            updateSubMenu(appState.currentMainMenu);

            // 初始化内容区域
            updateContent();
        });

        // Toast 提示功能
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1f2937'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>

/**
 * 智能家居设计与施工管理平台 - 后台管理系统
 * 通用JavaScript功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 侧边栏折叠/展开功能
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const sidebar = document.getElementById('sidebar');
    const header = document.getElementById('header');
    const mainContent = document.getElementById('main-content');
    
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('sidebar-collapsed');
            header.classList.toggle('header-collapsed');
            mainContent.classList.toggle('main-content-collapsed');
            
            // 保存用户偏好
            const isSidebarCollapsed = sidebar.classList.contains('sidebar-collapsed');
            localStorage.setItem('sidebarCollapsed', isSidebarCollapsed);
        });
        
        // 恢复用户偏好
        const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isSidebarCollapsed) {
            sidebar.classList.add('sidebar-collapsed');
            header.classList.add('header-collapsed');
            mainContent.classList.add('main-content-collapsed');
        }
    }
    
    // 移动端侧边栏切换
    const mobileMenuBtn = document.getElementById('mobile-menu');
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
        });
        
        // 点击遮罩关闭侧边栏
        document.addEventListener('click', function(event) {
            if (sidebar.classList.contains('mobile-open') && 
                !sidebar.contains(event.target) && 
                event.target !== mobileMenuBtn) {
                sidebar.classList.remove('mobile-open');
            }
        });
    }
    
    // 下拉菜单功能
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const trigger = dropdown.querySelector('.dropdown-trigger');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (trigger && menu) {
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                menu.classList.toggle('show');
            });
            
            // 点击外部关闭下拉菜单
            document.addEventListener('click', function() {
                menu.classList.remove('show');
            });
        }
    });
    
    // 标签页切换功能
    const tabGroups = document.querySelectorAll('.tabs');
    tabGroups.forEach(tabGroup => {
        const tabItems = tabGroup.querySelectorAll('.tab-item');
        const tabContents = tabGroup.querySelectorAll('.tab-content > div');
        
        tabItems.forEach((tab, index) => {
            tab.addEventListener('click', function() {
                // 移除所有活动状态
                tabItems.forEach(item => item.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // 设置当前活动状态
                tab.classList.add('active');
                tabContents[index].classList.add('active');
            });
        });
    });
    
    // 模态框功能
    const modalTriggers = document.querySelectorAll('[data-modal]');
    modalTriggers.forEach(trigger => {
        const modalId = trigger.getAttribute('data-modal');
        const modal = document.getElementById(modalId);
        
        if (modal) {
            const closeBtn = modal.querySelector('.modal-close');
            
            trigger.addEventListener('click', function() {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
            });
            
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                });
            }
            
            // 点击遮罩关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            });
        }
    });
    
    // 抽屉功能
    const drawerTriggers = document.querySelectorAll('[data-drawer]');
    drawerTriggers.forEach(trigger => {
        const drawerId = trigger.getAttribute('data-drawer');
        const drawer = document.getElementById(drawerId);
        
        if (drawer) {
            const closeBtn = drawer.querySelector('.drawer-close');
            const backdrop = document.createElement('div');
            backdrop.className = 'drawer-backdrop';
            
            trigger.addEventListener('click', function() {
                document.body.appendChild(backdrop);
                drawer.classList.add('open');
                document.body.style.overflow = 'hidden';
            });
            
            if (closeBtn) {
                closeBtn.addEventListener('click', closeDrawer);
            }
            
            // 点击遮罩关闭抽屉
            backdrop.addEventListener('click', closeDrawer);
            
            function closeDrawer() {
                drawer.classList.remove('open');
                document.body.style.overflow = '';
                if (document.body.contains(backdrop)) {
                    document.body.removeChild(backdrop);
                }
            }
        }
    });
    
    // 表格排序功能
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const table = header.closest('table');
            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const isAsc = header.classList.contains('asc');
            
            // 移除所有排序状态
            sortableHeaders.forEach(h => {
                h.classList.remove('asc', 'desc');
            });
            
            // 设置当前排序状态
            header.classList.add(isAsc ? 'desc' : 'asc');
            
            // 排序表格行
            rows.sort((a, b) => {
                const aValue = a.children[columnIndex].textContent.trim();
                const bValue = b.children[columnIndex].textContent.trim();
                
                // 尝试数字排序
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return isAsc ? aNum - bNum : bNum - aNum;
                }
                
                // 字符串排序
                return isAsc 
                    ? aValue.localeCompare(bValue) 
                    : bValue.localeCompare(aValue);
            });
            
            // 重新插入排序后的行
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));
        });
    });
    
    // 表格筛选功能
    const filterInputs = document.querySelectorAll('.table-filter');
    filterInputs.forEach(input => {
        input.addEventListener('input', function() {
            const table = document.querySelector(input.getAttribute('data-table'));
            const searchText = input.value.toLowerCase();
            
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchText) ? '' : 'none';
                });
            }
        });
    });
    
    // 通知提醒功能
    window.showNotification = function(type, title, message, duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        let iconClass = '';
        switch (type) {
            case 'success': iconClass = 'fas fa-check-circle'; break;
            case 'error': iconClass = 'fas fa-exclamation-circle'; break;
            case 'warning': iconClass = 'fas fa-exclamation-triangle'; break;
            case 'info': iconClass = 'fas fa-info-circle'; break;
        }
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconClass}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // 关闭按钮功能
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', function() {
            notification.remove();
        });
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }
    };
    
    // 表单验证功能
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                    
                    // 显示错误信息
                    const errorMsg = field.getAttribute('data-error') || '此字段不能为空';
                    let errorElement = field.nextElementSibling;
                    
                    if (!errorElement || !errorElement.classList.contains('form-error')) {
                        errorElement = document.createElement('div');
                        errorElement.className = 'form-error';
                        field.parentNode.insertBefore(errorElement, field.nextSibling);
                    }
                    
                    errorElement.textContent = errorMsg;
                } else {
                    field.classList.remove('error');
                    const errorElement = field.nextElementSibling;
                    if (errorElement && errorElement.classList.contains('form-error')) {
                        errorElement.remove();
                    }
                }
            });
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    });
});

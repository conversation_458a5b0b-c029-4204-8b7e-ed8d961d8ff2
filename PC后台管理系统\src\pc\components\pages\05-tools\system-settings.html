<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item active">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">系统配置</h1>
                            <p class="breadcrumb-description">管理系统参数、集成配置和安全设置</p>
                        </div>
                    </nav>
                    <div class="nav-actions">
                        <button class="btn btn-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo"></i> 恢复默认
                        </button>
                        <button class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save"></i> 保存配置
                        </button>
                    </div>
                </div>
            </header>

            <div class="page-content">
                <div class="settings-layout">
                    <!-- 左侧配置分类 -->
                    <div class="settings-sidebar">
                        <div class="settings-nav">
                            <div class="nav-item active" onclick="switchSettingsTab('general')">
                                <i class="fas fa-cog"></i> 基础设置
                            </div>
                            <div class="nav-item" onclick="switchSettingsTab('security')">
                                <i class="fas fa-shield-alt"></i> 安全设置
                            </div>
                            <div class="nav-item" onclick="switchSettingsTab('integrations')">
                                <i class="fas fa-plug"></i> 集成配置
                            </div>
                            <div class="nav-item" onclick="switchSettingsTab('notifications')">
                                <i class="fas fa-bell"></i> 通知设置
                            </div>
                            <div class="nav-item" onclick="switchSettingsTab('backup')">
                                <i class="fas fa-database"></i> 备份恢复
                            </div>
                            <div class="nav-item" onclick="switchSettingsTab('system')">
                                <i class="fas fa-server"></i> 系统信息
                            </div>
                        </div>
                    </div>

                    <!-- 右侧配置内容 -->
                    <div class="settings-content">
                        <!-- 基础设置 -->
                        <div class="settings-panel active" id="generalPanel">
                            <h3>基础设置</h3>
                            <div class="settings-section">
                                <h4>系统信息</h4>
                                <div class="form-group">
                                    <label for="systemName">系统名称</label>
                                    <input type="text" id="systemName" value="智能家居管理系统">
                                </div>
                                <div class="form-group">
                                    <label for="systemVersion">系统版本</label>
                                    <input type="text" id="systemVersion" value="v2.0.0" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="companyName">公司名称</label>
                                    <input type="text" id="companyName" value="智能家居科技有限公司">
                                </div>
                                <div class="form-group">
                                    <label for="contactEmail">联系邮箱</label>
                                    <input type="email" id="contactEmail" value="<EMAIL>">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>界面设置</h4>
                                <div class="form-group">
                                    <label for="theme">主题模式</label>
                                    <select id="theme">
                                        <option value="light">浅色模式</option>
                                        <option value="dark">深色模式</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="language">系统语言</label>
                                    <select id="language">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="timezone">时区设置</label>
                                    <select id="timezone">
                                        <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
                                        <option value="UTC">协调世界时 (UTC)</option>
                                        <option value="America/New_York">纽约时间 (UTC-5)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="settings-panel" id="securityPanel">
                            <h3>安全设置</h3>
                            <div class="settings-section">
                                <h4>登录安全</h4>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableMFA" checked>
                                        启用双因子认证 (MFA)
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="sessionTimeout">会话超时时间 (分钟)</label>
                                    <input type="number" id="sessionTimeout" value="120" min="30" max="480">
                                </div>
                                <div class="form-group">
                                    <label for="maxLoginAttempts">最大登录尝试次数</label>
                                    <input type="number" id="maxLoginAttempts" value="5" min="3" max="10">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableIPWhitelist">
                                        启用IP白名单
                                    </label>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>密码策略</h4>
                                <div class="form-group">
                                    <label for="minPasswordLength">最小密码长度</label>
                                    <input type="number" id="minPasswordLength" value="8" min="6" max="20">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="requireUppercase" checked>
                                        要求包含大写字母
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="requireNumbers" checked>
                                        要求包含数字
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="requireSpecialChars">
                                        要求包含特殊字符
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 集成配置 -->
                        <div class="settings-panel" id="integrationsPanel">
                            <h3>集成配置</h3>
                            <div class="settings-section">
                                <h4>第三方服务</h4>
                                <div class="integration-item">
                                    <div class="integration-info">
                                        <h5>微信小程序</h5>
                                        <p>集成微信小程序登录和支付</p>
                                    </div>
                                    <div class="integration-status">
                                        <span class="status-badge status-active">已连接</span>
                                        <button class="btn btn-sm btn-secondary" onclick="configureIntegration('wechat')">配置</button>
                                    </div>
                                </div>
                                <div class="integration-item">
                                    <div class="integration-info">
                                        <h5>支付宝</h5>
                                        <p>集成支付宝支付服务</p>
                                    </div>
                                    <div class="integration-status">
                                        <span class="status-badge status-inactive">未连接</span>
                                        <button class="btn btn-sm btn-primary" onclick="configureIntegration('alipay')">连接</button>
                                    </div>
                                </div>
                                <div class="integration-item">
                                    <div class="integration-info">
                                        <h5>短信服务</h5>
                                        <p>集成短信验证码服务</p>
                                    </div>
                                    <div class="integration-status">
                                        <span class="status-badge status-active">已连接</span>
                                        <button class="btn btn-sm btn-secondary" onclick="configureIntegration('sms')">配置</button>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>API 配置</h4>
                                <div class="form-group">
                                    <label for="apiRateLimit">API 请求频率限制 (次/分钟)</label>
                                    <input type="number" id="apiRateLimit" value="1000" min="100" max="10000">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableApiLogging" checked>
                                        启用 API 请求日志
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="apiVersion">API 版本</label>
                                    <select id="apiVersion">
                                        <option value="v1">v1.0</option>
                                        <option value="v2" selected>v2.0</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div class="settings-panel" id="notificationsPanel">
                            <h3>通知设置</h3>
                            <div class="settings-section">
                                <h4>邮件通知</h4>
                                <div class="form-group">
                                    <label for="smtpServer">SMTP 服务器</label>
                                    <input type="text" id="smtpServer" value="smtp.example.com">
                                </div>
                                <div class="form-group">
                                    <label for="smtpPort">SMTP 端口</label>
                                    <input type="number" id="smtpPort" value="587">
                                </div>
                                <div class="form-group">
                                    <label for="smtpUsername">SMTP 用户名</label>
                                    <input type="text" id="smtpUsername" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableEmailNotifications" checked>
                                        启用邮件通知
                                    </label>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>系统通知</h4>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="notifyNewOrders" checked>
                                        新订单通知
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="notifySystemErrors" checked>
                                        系统错误通知
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="notifyUserRegistration">
                                        用户注册通知
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 备份恢复 -->
                        <div class="settings-panel" id="backupPanel">
                            <h3>备份恢复</h3>
                            <div class="settings-section">
                                <h4>自动备份</h4>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableAutoBackup" checked>
                                        启用自动备份
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="backupFrequency">备份频率</label>
                                    <select id="backupFrequency">
                                        <option value="daily" selected>每日</option>
                                        <option value="weekly">每周</option>
                                        <option value="monthly">每月</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="backupRetention">备份保留天数</label>
                                    <input type="number" id="backupRetention" value="30" min="7" max="365">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>备份操作</h4>
                                <div class="backup-actions">
                                    <button class="btn btn-primary" onclick="createBackup()">
                                        <i class="fas fa-download"></i> 立即备份
                                    </button>
                                    <button class="btn btn-secondary" onclick="restoreBackup()">
                                        <i class="fas fa-upload"></i> 恢复备份
                                    </button>
                                    <button class="btn btn-secondary" onclick="downloadBackup()">
                                        <i class="fas fa-cloud-download-alt"></i> 下载备份
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="settings-panel" id="systemPanel">
                            <h3>系统信息</h3>
                            <div class="settings-section">
                                <h4>运行状态</h4>
                                <div class="system-info">
                                    <div class="info-item">
                                        <span class="info-label">系统版本:</span>
                                        <span class="info-value">v2.0.0</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">运行时间:</span>
                                        <span class="info-value" id="uptime">15天 8小时 32分钟</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">CPU 使用率:</span>
                                        <span class="info-value">25%</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">内存使用:</span>
                                        <span class="info-value">2.1GB / 8GB</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">磁盘使用:</span>
                                        <span class="info-value">45GB / 100GB</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">数据库状态:</span>
                                        <span class="info-value status-active">正常</span>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>系统操作</h4>
                                <div class="system-actions">
                                    <button class="btn btn-secondary" onclick="clearCache()">
                                        <i class="fas fa-broom"></i> 清理缓存
                                    </button>
                                    <button class="btn btn-secondary" onclick="restartSystem()">
                                        <i class="fas fa-redo"></i> 重启系统
                                    </button>
                                    <button class="btn btn-secondary" onclick="checkUpdates()">
                                        <i class="fas fa-sync"></i> 检查更新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .nav-actions { display: flex; align-items: center; gap: var(--spacing-sm); }
        
        .settings-layout { display: grid; grid-template-columns: 250px 1fr; gap: var(--spacing-lg); }
        
        .settings-sidebar { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-md); height: fit-content; }
        .settings-nav { }
        .settings-nav         

        .nav-section:first-child 
        .settings-nav 
        .settings-nav 
        
        .settings-content { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-lg); }
        .settings-panel { display: none; }
        .settings-panel.active { display: block; }
        .settings-panel h3 { margin: 0 0 var(--spacing-lg) 0; font-size: 20px; font-weight: 600; color: var(--text-primary); }
        
        .settings-section { margin-bottom: var(--spacing-xl); }
        .settings-section h4 { margin: 0 0 var(--spacing-md) 0; font-size: 16px; font-weight: 600; color: var(--text-primary); border-bottom: 1px solid var(--border-color); padding-bottom: var(--spacing-sm); }
        
        .form-group { margin-bottom: var(--spacing-md); }
        .form-group label { display: block; font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px; }
        .form-group input, .form-group select { width: 100%; max-width: 400px; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: var(--primary-black); }
        .form-group input[type="checkbox"] { width: auto; margin-right: var(--spacing-xs); }
        
        .integration-item { display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); margin-bottom: var(--spacing-sm); }
        .integration-info h5 { margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: var(--text-primary); }
        .integration-info p { margin: 0; font-size: 12px; color: var(--text-secondary); }
        .integration-status { display: flex; align-items: center; gap: var(--spacing-sm); }
        
        .status-badge { padding: 2px 8px; border-radius: var(--radius-sm); font-size: 11px; font-weight: 500; }
        .status-active { background: var(--success-green); color: var(--text-inverse); }
        .status-inactive { background: var(--gray-500); color: var(--text-inverse); }
        
        .backup-actions, .system-actions { display: flex; gap: var(--spacing-sm); flex-wrap: wrap; }
        
        .system-info { }
        .info-item { display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm) 0; border-bottom: 1px solid var(--border-color); }
        .info-item:last-child { border-bottom: none; }
        .info-label { font-size: 14px; color: var(--text-secondary); }
        .info-value { font-size: 14px; font-weight: 500; color: var(--text-primary); }
        
        .btn { padding: var(--spacing-sm) var(--spacing-md); border: 1px solid transparent; border-radius: var(--radius-sm); font-size: 12px; font-weight: 500; cursor: pointer; transition: all var(--transition-base); text-decoration: none; display: inline-flex; align-items: center; justify-content: center; gap: var(--spacing-xs); }
        .btn-primary { background: var(--primary-black); color: var(--text-inverse); }
        .btn-primary:hover { background: var(--gray-800); transform: translateY(-1px); }
        .btn-secondary { background: var(--bg-muted); color: var(--text-primary); border-color: var(--border-color); }
        .btn-secondary:hover { background: var(--bg-hover); border-color: var(--primary-black); }
        .btn-sm { padding: 4px 8px; font-size: 11px; }
        
        @media (max-width: 1200px) { .settings-layout { grid-template-columns: 200px 1fr; } }
        @media (max-width: 768px) { .settings-layout { grid-template-columns: 1fr; } .settings-sidebar { margin-bottom: var(--spacing-lg); } }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        class SettingsManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.currentPanel = 'general';
                this.settings = {};
                this.init();
            }

            async init() {
                await this.loadSettings();
                this.bindEvents();
                this.startSystemMonitoring();
            }

            async loadSettings() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/settings`, {
                        headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                    });
                    if (response.ok) {
                        this.settings = await response.json();
                        this.populateSettings();
                    } else {
                        this.loadDefaultSettings();
                    }
                } catch (error) {
                    this.loadDefaultSettings();
                }
            }

            loadDefaultSettings() {
                this.settings = {
                    systemName: '智能家居管理系统',
                    systemVersion: 'v2.0.0',
                    companyName: '智能家居科技有限公司',
                    contactEmail: '<EMAIL>',
                    theme: 'light',
                    language: 'zh-CN',
                    timezone: 'Asia/Shanghai',
                    enableMFA: true,
                    sessionTimeout: 120,
                    maxLoginAttempts: 5,
                    enableIPWhitelist: false,
                    minPasswordLength: 8,
                    requireUppercase: true,
                    requireNumbers: true,
                    requireSpecialChars: false,
                    apiRateLimit: 1000,
                    enableApiLogging: true,
                    apiVersion: 'v2',
                    enableEmailNotifications: true,
                    enableAutoBackup: true,
                    backupFrequency: 'daily',
                    backupRetention: 30
                };
                this.populateSettings();
            }

            populateSettings() {
                Object.keys(this.settings).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = this.settings[key];
                        } else {
                            element.value = this.settings[key];
                        }
                    }
                });
            }

            bindEvents() {
                // 监听所有表单元素的变化
                document.querySelectorAll('input, select').forEach(element => {
                    element.addEventListener('change', (e) => {
                        const key = e.target.id;
                        if (key && this.settings.hasOwnProperty(key)) {
                            if (e.target.type === 'checkbox') {
                                this.settings[key] = e.target.checked;
                            } else {
                                this.settings[key] = e.target.value;
                            }
                        }
                    });
                });
            }

            switchSettingsTab(panelName) {
                this.currentPanel = panelName;
                
                // 更新导航状态
                document.querySelectorAll('.settings-nav .nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.classList.add('active');
                
                // 更新面板显示
                document.querySelectorAll('.settings-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                document.getElementById(panelName + 'Panel').classList.add('active');
            }

            startSystemMonitoring() {
                // 模拟系统监控数据更新
                setInterval(() => {
                    this.updateSystemInfo();
                }, 30000); // 每30秒更新一次
            }

            updateSystemInfo() {
                // 模拟更新系统运行时间
                const uptimeElement = document.getElementById('uptime');
                if (uptimeElement) {
                    const currentTime = new Date();
                    const startTime = new Date(currentTime.getTime() - (15 * 24 * 60 * 60 * 1000)); // 15天前
                    const diff = currentTime - startTime;
                    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    uptimeElement.textContent = `${days}天 ${hours}小时 ${minutes}分钟`;
                }
            }

            async saveAllSettings() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/settings`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(this.settings)
                    });
                    
                    if (response.ok) {
                        showToast('设置保存成功！', 'success');
                    } else {
                        throw new Error('保存失败');
                    }
                } catch (error) {
                    // 模拟保存成功
                    localStorage.setItem('systemSettings', JSON.stringify(this.settings));
                    showToast('设置保存成功！', 'success');
                }
            }

            resetToDefaults() {
                if (confirm('确定要恢复所有设置到默认值吗？此操作不可撤销。')) {
                    this.loadDefaultSettings();
                    showToast('设置已恢复到默认值！', 'success');
                }
            }

            configureIntegration(service) {
                // 创建集成配置模态框
                const modal = document.createElement('div');
                modal.className = 'integration-config-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()">
                        <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px; padding: 24px;">
                            <div class="modal-header" style="margin-bottom: 16px;">
                                <h3 style="margin: 0; color: #1f2937;">配置 ${service} 集成</h3>
                                <button onclick="this.closest('.integration-config-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                            </div>
                            <div class="integration-config-content">
                                <form id="integrationConfigForm" style="display: grid; gap: 16px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">API 端点:</label>
                                        <input type="url" name="apiEndpoint" placeholder="https://api.${service.toLowerCase()}.com" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 4px;" required>
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">API 密钥:</label>
                                        <input type="password" name="apiKey" placeholder="输入API密钥" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 4px;" required>
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">超时设置 (秒):</label>
                                        <input type="number" name="timeout" value="30" min="5" max="300" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 4px;">
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <input type="checkbox" name="enabled" id="integrationEnabled" checked>
                                        <label for="integrationEnabled">启用此集成</label>
                                    </div>
                                    <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 16px;">
                                        <button type="button" onclick="this.closest('.integration-config-modal').remove()" style="padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 4px; cursor: pointer;">取消</button>
                                        <button type="button" onclick="saveIntegrationConfig('${service}')" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">保存配置</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                `;

                // 添加样式
                const overlay = modal.querySelector('.modal-overlay');
                Object.assign(overlay.style, {
                    position: 'fixed',
                    top: '0',
                    left: '0',
                    right: '0',
                    bottom: '0',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: '10000'
                });

                const content = modal.querySelector('.modal-content');
                Object.assign(content.style, {
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    maxHeight: '80vh',
                    overflow: 'auto'
                });

                const header = modal.querySelector('.modal-header');
                Object.assign(header.style, {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderBottom: '1px solid #e5e7eb',
                    paddingBottom: '12px'
                });

                document.body.appendChild(modal);
            }

            createBackup() {
                showToast('正在创建系统备份...', 'info');

                // 模拟备份过程
                setTimeout(() => {
                    const backupData = {
                        settings: this.settings,
                        users: JSON.parse(localStorage.getItem('users') || '[]'),
                        orders: JSON.parse(localStorage.getItem('orders') || '[]'),
                        customers: JSON.parse(localStorage.getItem('customers') || '[]'),
                        requirements: JSON.parse(localStorage.getItem('smart_home_requirements') || '[]'),
                        timestamp: new Date().toISOString(),
                        version: '1.0.0'
                    };

                    // 保存备份到localStorage
                    const backupId = `backup_${Date.now()}`;
                    localStorage.setItem(backupId, JSON.stringify(backupData));

                    // 更新备份列表
                    const backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
                    backups.push({
                        id: backupId,
                        name: `系统备份_${new Date().toLocaleString()}`,
                        timestamp: backupData.timestamp,
                        size: JSON.stringify(backupData).length
                    });
                    localStorage.setItem('systemBackups', JSON.stringify(backups));

                    showToast('系统备份创建成功！', 'success');
                }, 1500);
            }

            restoreBackup() {
                const backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
                
                if (backups.length === 0) {
                    showToast('没有可用的备份文件', 'warning');
                    return;
                }

                // 创建备份选择模态框
                const modal = document.createElement('div');
                modal.className = 'backup-restore-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()">
                        <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px; padding: 24px;">
                            <div class="modal-header" style="margin-bottom: 16px;">
                                <h3 style="margin: 0; color: #1f2937;">选择要恢复的备份</h3>
                                <button onclick="this.closest('.backup-restore-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                            </div>
                            <div class="backup-list" style="max-height: 400px; overflow-y: auto;">
                                ${backups.map(backup => `
                                    <div class="backup-item" style="padding: 12px; border: 1px solid #e5e7eb; border-radius: 4px; margin-bottom: 8px; cursor: pointer;" onclick="restoreFromBackup('${backup.id}')">
                                        <div style="font-weight: 500;">${backup.name}</div>
                                        <div style="font-size: 12px; color: #6b7280;">
                                            创建时间: ${new Date(backup.timestamp).toLocaleString()} | 
                                            大小: ${(backup.size / 1024).toFixed(1)} KB
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            }

            downloadBackup() {
                const backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
                
                if (backups.length === 0) {
                    showToast('没有可用的备份文件', 'warning');
                    return;
                }

                // 获取最新的备份
                const latestBackup = backups[backups.length - 1];
                const backupData = JSON.parse(localStorage.getItem(latestBackup.id) || '{}');

                // 创建下载
                const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                link.href = url;
                link.download = `${latestBackup.name.replace(/[^a-zA-Z0-9_-]/g, '_')}.json`;
                link.click();
                
                URL.revokeObjectURL(url);
                showToast('备份文件下载成功', 'success');
            }

            clearCache() {
                if (confirm('确定要清理系统缓存吗？')) {
                    // 清理各种缓存数据
                    const cacheKeys = [];
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key.includes('cache_') || key.includes('temp_') || key.includes('session_')) {
                            cacheKeys.push(key);
                        }
                    }
                    
                    cacheKeys.forEach(key => localStorage.removeItem(key));
                    
                    // 模拟清理过程
                    showToast('正在清理系统缓存...', 'info');
                    setTimeout(() => {
                        showToast(`缓存清理完成！清理了 ${cacheKeys.length} 项缓存数据`, 'success');
                    }, 1000);
                }
            }

            restartSystem() {
                if (confirm('确定要重启系统吗？这将中断所有用户的连接。')) {
                    showToast('正在准备系统重启...', 'warning');
                    
                    setTimeout(() => {
                        showToast('系统重启中，请稍候...', 'info');
                        
                        // 模拟重启过程
                        setTimeout(() => {
                            showToast('系统重启完成！页面将自动刷新', 'success');
                            
                            // 3秒后刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 3000);
                        }, 2000);
                    }, 1000);
                }
            }

            checkUpdates() {
                showToast('正在检查系统更新...', 'info');
                
                // 模拟检查更新过程
                setTimeout(() => {
                    const hasUpdate = Math.random() > 0.7; // 30%概率有更新
                    
                    if (hasUpdate) {
                        const modal = document.createElement('div');
                        modal.className = 'update-modal';
                        modal.innerHTML = `
                            <div class="modal-overlay" onclick="this.parentElement.remove()">
                                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 500px; padding: 24px;">
                                    <div class="modal-header" style="margin-bottom: 16px;">
                                        <h3 style="margin: 0; color: #1f2937;">发现新版本</h3>
                                        <button onclick="this.closest('.update-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                                    </div>
                                    <div class="update-content">
                                        <p><strong>当前版本:</strong> v1.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}</p>
                                        <p><strong>最新版本:</strong> v1.${Math.floor(Math.random() * 10) + 1}.0</p>
                                        <p><strong>更新内容:</strong></p>
                                        <ul style="margin: 12px 0; padding-left: 20px;">
                                            <li>修复了若干已知问题</li>
                                            <li>优化了系统性能</li>
                                            <li>增加了新的功能特性</li>
                                            <li>提升了安全性</li>
                                        </ul>
                                        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px;">
                                            <button onclick="this.closest('.update-modal').remove()" style="padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 4px; cursor: pointer;">稍后更新</button>
                                            <button onclick="startSystemUpdate()" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">立即更新</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);
                    } else {
                        showToast('当前版本已是最新版本！', 'success');
                    }
                }, 2000);
            }
        }

        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            toast.innerHTML = `
                <div class="toast-content ${type}">
                    <i class="fa ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // 样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: '10000',
                backgroundColor: 'white',
                border: `1px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'}`,
                borderRadius: '8px',
                padding: '12px 16px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease',
                maxWidth: '400px'
            });

            const content = toast.querySelector('.toast-content');
            Object.assign(content.style, {
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'
            });

            document.body.appendChild(toast);

            // 动画显示
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        let settingsManager;
        function switchSettingsTab(panelName) { settingsManager.switchSettingsTab(panelName); }
        function saveAllSettings() { settingsManager.saveAllSettings(); }
        function resetToDefaults() { settingsManager.resetToDefaults(); }
        function configureIntegration(service) { settingsManager.configureIntegration(service); }
        function createBackup() { settingsManager.createBackup(); }
        function restoreBackup() { settingsManager.restoreBackup(); }
        function downloadBackup() { settingsManager.downloadBackup(); }
        function clearCache() { settingsManager.clearCache(); }
        function restartSystem() { settingsManager.restartSystem(); }
        function checkUpdates() { settingsManager.checkUpdates(); }

        // 集成配置保存函数
        function saveIntegrationConfig(service) {
            const form = document.getElementById('integrationConfigForm');
            const formData = new FormData(form);
            const config = {
                service: service,
                apiEndpoint: formData.get('apiEndpoint'),
                apiKey: formData.get('apiKey'),
                timeout: parseInt(formData.get('timeout')),
                enabled: formData.get('enabled') === 'on',
                updateTime: new Date().toISOString()
            };

            // 保存配置
            let integrations = JSON.parse(localStorage.getItem('systemIntegrations') || '{}');
            integrations[service] = config;
            localStorage.setItem('systemIntegrations', JSON.stringify(integrations));

            // 关闭模态框
            document.querySelector('.integration-config-modal').remove();
            
            showToast(`${service} 集成配置保存成功`, 'success');
        }

        // 备份恢复函数
        function restoreFromBackup(backupId) {
            if (!confirm('确定要恢复此备份吗？这将覆盖当前所有数据！')) {
                return;
            }

            const backupData = JSON.parse(localStorage.getItem(backupId) || '{}');
            
            if (!backupData.settings) {
                showToast('备份数据格式错误', 'error');
                return;
            }

            // 恢复数据
            try {
                if (backupData.settings) localStorage.setItem('systemSettings', JSON.stringify(backupData.settings));
                if (backupData.users) localStorage.setItem('users', JSON.stringify(backupData.users));
                if (backupData.orders) localStorage.setItem('orders', JSON.stringify(backupData.orders));
                if (backupData.customers) localStorage.setItem('customers', JSON.stringify(backupData.customers));
                if (backupData.requirements) localStorage.setItem('smart_home_requirements', JSON.stringify(backupData.requirements));

                // 关闭模态框
                document.querySelector('.backup-restore-modal').remove();
                
                showToast('备份恢复成功！页面将自动刷新', 'success');
                
                // 3秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } catch (error) {
                showToast('备份恢复失败：' + error.message, 'error');
            }
        }

        // 系统更新函数
        function startSystemUpdate() {
            showToast('开始下载更新包...', 'info');
            
            // 关闭更新模态框
            document.querySelector('.update-modal').remove();
            
            setTimeout(() => {
                showToast('更新包下载完成，正在安装...', 'info');
                
                setTimeout(() => {
                    showToast('系统更新完成！页面将自动刷新', 'success');
                    
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                }, 3000);
            }, 2000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            settingsManager = new SettingsManager();
        });
    </script>
</body>
</html>

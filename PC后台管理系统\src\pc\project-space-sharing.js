/**
 * 项目空间分享权限管理系统
 * 核心理念：用户主导的权限分享机制
 * 版本: v2.0
 * 创建时间: 2025-07-15
 */

class ProjectSpaceSharing {
    constructor() {
        // 用户角色定义（基于真实业务需求）
        this.userRoles = {
            OWNER: {
                name: '业主/空间所有者',
                description: '拥有项目空间的完全控制权',
                canInvite: true,
                canSetPermissions: true,
                needsInvitation: false,
                hasBackendAccess: false,
                defaultPermissions: ['*'] // 所有权限
            },
            FAMILY_MEMBER: {
                name: '家庭成员',
                description: '由业主邀请的家庭成员，权限由业主设定和控制',
                canInvite: false,
                canSetPermissions: false,
                needsInvitation: true,
                hasBackendAccess: false,
                defaultPermissions: [
                    'project:VIEW,COMMENT',
                    'design:VIEW,COMMENT',
                    'construction:VIEW,UPDATE,COMMENT',
                    'files:VIEW,UPLOAD',
                    'comments:CREATE,EDIT'
                ]
            },
            HOME_DESIGNER: {
                name: '家装设计师',
                description: '被邀请进项目空间参与设计工作',
                canInvite: false,
                canSetPermissions: false,
                needsInvitation: true,
                hasBackendAccess: false,
                defaultPermissions: [
                    'project:VIEW,COMMENT',
                    'design:VIEW,CREATE,EDIT,COMMENT',
                    'construction:VIEW,UPDATE,COMMENT',
                    'files:VIEW,UPLOAD',
                    'comments:CREATE,EDIT'
                ]
            },
            SMART_HOME_DESIGNER: {
                name: '智能家居设计师+灯光设计师',
                description: '不需要被邀请，可以在后台看到用户所有的项目文件（1.0阶段智能家居设计师默认是灯光设计师）',
                canInvite: false,
                canSetPermissions: false,
                needsInvitation: false, // 关键：不需要邀请
                hasBackendAccess: true, // 关键：有后台访问权限
                defaultPermissions: [
                    'project:VIEW,EDIT,COMMENT',
                    'design:*',
                    'construction:VIEW,UPDATE,COMMENT',
                    'cost:VIEW',
                    'files:*',
                    'comments:*',
                    'system:VIEW,EDIT,CONFIGURE',
                    'backend:ACCESS'
                ]
            },
            CONSTRUCTOR: {
                name: '施工人员',
                description: '被邀请参与施工阶段，由用户决定可以看到的内容',
                canInvite: false,
                canSetPermissions: false,
                needsInvitation: true,
                hasBackendAccess: false,
                defaultPermissions: [
                    'project:VIEW,COMMENT',
                    'design:VIEW',
                    'construction:VIEW,UPDATE,COMMENT',
                    'files:VIEW,UPLOAD',
                    'comments:CREATE,EDIT'
                ]
            }
        }

        // 可分享的内容模块
        this.shareableModules = {
            DESIGN_PLANS: {
                name: '设计方案',
                permissions: ['view', 'comment', 'edit', 'approve'],
                description: '设计图纸、方案文档'
            },
            CONSTRUCTION_PROGRESS: {
                name: '施工进度',
                permissions: ['view', 'update', 'comment'],
                description: '施工进度、现场照片'
            },
            SMART_HOME_CONFIG: {
                name: '智能家居配置',
                permissions: ['view', 'edit', 'test'],
                description: '智能设备配置、场景设置'
            },
            LIGHTING_DESIGN: {
                name: '灯光设计',
                permissions: ['view', 'edit', 'preview'],
                description: '灯光布局、效果设计'
            },
            PROJECT_DOCUMENTS: {
                name: '项目文档',
                permissions: ['view', 'download', 'upload'],
                description: '合同、报价、验收单据'
            },
            BUDGET_COST: {
                name: '预算成本',
                permissions: ['view', 'edit'],
                description: '项目预算、成本明细'
            }
        }

        // 项目空间数据
        this.projectSpaces = new Map()
        this.userInvitations = new Map()
        this.sharedPermissions = new Map()
    }

    /**
     * 创建项目空间
     * 只有用户可以创建自己的项目空间
     */
    createProjectSpace(ownerId, projectData) {
        const projectId = this.generateProjectId()
        
        const projectSpace = {
            id: projectId,
            ownerId: ownerId,
            name: projectData.name,
            description: projectData.description,
            createdAt: new Date(),
            status: 'active',
            
            // 空间成员
            members: new Map([[ownerId, {
                userId: ownerId,
                role: 'OWNER',
                joinedAt: new Date(),
                permissions: ['*'], // 业主拥有所有权限
                invitedBy: null
            }]]),
            
            // 分享设置
            sharingSettings: {
                allowInvite: true,
                requireApproval: true,
                maxMembers: 50,
                publicModules: [], // 公开可见的模块
                restrictedModules: ['BUDGET_COST'] // 默认限制的模块
            }
        }
        
        this.projectSpaces.set(projectId, projectSpace)
        
        console.log(`✅ 项目空间创建成功: ${projectData.name} (${projectId})`)
        return projectSpace
    }

    /**
     * 邀请用户进入项目空间
     * 核心功能：业主决定邀请谁，给什么权限
     */
    inviteUserToProject(inviterId, projectId, inviteeId, roleType, customPermissions = null) {
        const project = this.projectSpaces.get(projectId)
        if (!project) {
            throw new Error('项目空间不存在')
        }

        // 检查邀请者权限
        const inviterMember = project.members.get(inviterId)
        if (!inviterMember) {
            throw new Error('您不是该项目成员')
        }

        const inviterRole = this.userRoles[inviterMember.role]
        if (!inviterRole.canInvite && inviterMember.role !== 'OWNER') {
            throw new Error('您没有邀请权限')
        }

        // 检查角色有效性
        if (!this.userRoles[roleType]) {
            throw new Error('无效的角色类型')
        }

        // 创建邀请
        const invitationId = this.generateInvitationId()
        const invitation = {
            id: invitationId,
            projectId: projectId,
            inviterId: inviterId,
            inviteeId: inviteeId,
            roleType: roleType,
            customPermissions: customPermissions || this.userRoles[roleType].defaultPermissions,
            status: 'pending',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天有效期
            message: `邀请您以"${this.userRoles[roleType].name}"身份参与项目`
        }

        this.userInvitations.set(invitationId, invitation)

        console.log(`📧 邀请已发送: ${inviteeId} 被邀请为 ${roleType}`)
        return invitation
    }

    /**
     * 接受邀请
     */
    acceptInvitation(invitationId, userId) {
        const invitation = this.userInvitations.get(invitationId)
        if (!invitation) {
            throw new Error('邀请不存在')
        }

        if (invitation.inviteeId !== userId) {
            throw new Error('这不是您的邀请')
        }

        if (invitation.status !== 'pending') {
            throw new Error('邀请已处理')
        }

        if (new Date() > invitation.expiresAt) {
            throw new Error('邀请已过期')
        }

        // 加入项目空间
        const project = this.projectSpaces.get(invitation.projectId)
        project.members.set(userId, {
            userId: userId,
            role: invitation.roleType,
            joinedAt: new Date(),
            permissions: invitation.customPermissions,
            invitedBy: invitation.inviterId
        })

        // 更新邀请状态
        invitation.status = 'accepted'
        invitation.acceptedAt = new Date()

        console.log(`✅ 用户 ${userId} 已加入项目 ${invitation.projectId}`)
        return true
    }

    /**
     * 设置用户在项目中的权限
     * 只有业主可以修改权限
     */
    setUserPermissions(ownerId, projectId, targetUserId, modulePermissions) {
        const project = this.projectSpaces.get(projectId)
        if (!project) {
            throw new Error('项目空间不存在')
        }

        // 只有业主可以设置权限
        if (project.ownerId !== ownerId) {
            throw new Error('只有业主可以设置权限')
        }

        const targetMember = project.members.get(targetUserId)
        if (!targetMember) {
            throw new Error('用户不在项目中')
        }

        // 不能修改业主权限
        if (targetMember.role === 'OWNER') {
            throw new Error('不能修改业主权限')
        }

        // 构建权限列表
        const permissions = []
        Object.entries(modulePermissions).forEach(([module, actions]) => {
            if (this.shareableModules[module]) {
                actions.forEach(action => {
                    if (this.shareableModules[module].permissions.includes(action)) {
                        permissions.push(`${module}:${action}`)
                    }
                })
            }
        })

        // 更新权限
        targetMember.permissions = permissions
        targetMember.permissionsUpdatedAt = new Date()

        console.log(`🔧 用户 ${targetUserId} 权限已更新`)
        return permissions
    }

    /**
     * 检查用户权限
     */
    hasPermission(userId, projectId, module, action) {
        const project = this.projectSpaces.get(projectId)
        if (!project) {
            return false
        }

        const member = project.members.get(userId)
        if (!member) {
            return false
        }

        // 业主拥有所有权限
        if (member.role === 'OWNER') {
            return true
        }

        // 检查具体权限
        const permissionKey = `${module}:${action}`
        return member.permissions.includes(permissionKey) || 
               member.permissions.includes(`${module}:*`) ||
               member.permissions.includes('*')
    }

    /**
     * 获取用户可访问的项目列表
     */
    getUserProjects(userId) {
        const userProjects = []
        
        for (const [projectId, project] of this.projectSpaces) {
            if (project.members.has(userId)) {
                const member = project.members.get(userId)
                userProjects.push({
                    projectId: projectId,
                    projectName: project.name,
                    role: member.role,
                    roleName: this.userRoles[member.role].name,
                    joinedAt: member.joinedAt,
                    permissions: member.permissions,
                    isOwner: member.role === 'OWNER'
                })
            }
        }
        
        return userProjects
    }

    /**
     * 获取项目成员列表
     */
    getProjectMembers(projectId, requesterId) {
        const project = this.projectSpaces.get(projectId)
        if (!project) {
            throw new Error('项目空间不存在')
        }

        // 检查请求者是否是项目成员
        if (!project.members.has(requesterId)) {
            throw new Error('您不是该项目成员')
        }

        const members = []
        for (const [userId, member] of project.members) {
            members.push({
                userId: userId,
                role: member.role,
                roleName: this.userRoles[member.role].name,
                joinedAt: member.joinedAt,
                permissions: member.permissions,
                invitedBy: member.invitedBy
            })
        }

        return members
    }

    /**
     * 移除项目成员
     * 只有业主可以移除成员
     */
    removeMember(ownerId, projectId, targetUserId) {
        const project = this.projectSpaces.get(projectId)
        if (!project) {
            throw new Error('项目空间不存在')
        }

        if (project.ownerId !== ownerId) {
            throw new Error('只有业主可以移除成员')
        }

        if (targetUserId === ownerId) {
            throw new Error('不能移除业主')
        }

        const removed = project.members.delete(targetUserId)
        if (removed) {
            console.log(`🚫 用户 ${targetUserId} 已被移出项目 ${projectId}`)
        }

        return removed
    }

    /**
     * 生成项目ID
     */
    generateProjectId() {
        return 'proj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    }

    /**
     * 生成邀请ID
     */
    generateInvitationId() {
        return 'inv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    }

    /**
     * 获取权限统计
     */
    getPermissionStats() {
        const stats = {
            totalProjects: this.projectSpaces.size,
            totalInvitations: this.userInvitations.size,
            roleDistribution: {},
            moduleUsage: {}
        }

        // 统计角色分布
        for (const project of this.projectSpaces.values()) {
            for (const member of project.members.values()) {
                stats.roleDistribution[member.role] = (stats.roleDistribution[member.role] || 0) + 1
            }
        }

        return stats
    }
}

// 导出类
window.ProjectSpaceSharing = ProjectSpaceSharing

// 使用示例
/*
const spaceSharing = new ProjectSpaceSharing()

// 1. 业主创建项目空间
const project = spaceSharing.createProjectSpace('owner_123', {
    name: '我的智能家居项目',
    description: '三室两厅智能化改造'
})

// 2. 业主邀请设计师
const invitation = spaceSharing.inviteUserToProject(
    'owner_123', 
    project.id, 
    'designer_456', 
    'SMART_HOME_DESIGNER'
)

// 3. 设计师接受邀请
spaceSharing.acceptInvitation(invitation.id, 'designer_456')

// 4. 业主设置设计师权限
spaceSharing.setUserPermissions('owner_123', project.id, 'designer_456', {
    'DESIGN_PLANS': ['view', 'edit'],
    'SMART_HOME_CONFIG': ['view', 'edit'],
    'CONSTRUCTION_PROGRESS': ['view']
})

// 5. 检查权限
const canEdit = spaceSharing.hasPermission('designer_456', project.id, 'DESIGN_PLANS', 'edit')
*/

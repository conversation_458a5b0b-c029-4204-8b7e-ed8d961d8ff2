/**
 * 后台管理系统主框架JavaScript
 * 负责处理侧边栏交互、页面导航、用户交互等功能
 */

class AdminDashboard {
    constructor() {
        this.sidebarOpen = false;
        this.currentPage = null;
        this.searchTimeout = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.initializePageState();
        this.setupSearch();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小改变
        window.addEventListener('resize', () => this.handleResize());
        
        // iframe加载完成
        const iframe = document.getElementById('contentFrame');
        if (iframe) {
            iframe.addEventListener('load', () => this.handleIframeLoad());
        }

        // 搜索输入
        const searchInput = document.querySelector('input[placeholder*="搜索"]');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }
    }

    /**
     * 设置键盘快捷键
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + B: 切换侧边栏
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                this.toggleSidebar();
            }

            // F5: 刷新当前页面
            if (e.key === 'F5') {
                e.preventDefault();
                this.refreshCurrentPage();
            }

            // Ctrl/Cmd + K: 聚焦搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[placeholder*="搜索"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // ESC: 关闭侧边栏（移动端）
            if (e.key === 'Escape') {
                if (window.innerWidth <= 1024 && this.sidebarOpen) {
                    this.toggleSidebar();
                }
            }
        });
    }

    /**
     * 初始化页面状态
     */
    initializePageState() {
        // 检测移动端
        this.handleResize();
        
        // 设置初始活动链接
        const activeLink = document.querySelector('.nav-link.active');
        if (activeLink) {
            activeLink.scrollIntoView({ block: 'nearest' });
        }
    }

    /**
     * 设置搜索功能
     */
    setupSearch() {
        const searchInput = document.querySelector('input[placeholder*="搜索"]');
        if (!searchInput) return;

        // 添加搜索结果下拉
        const searchContainer = searchInput.parentElement;
        const resultsContainer = document.createElement('div');
        resultsContainer.className = 'absolute top-full left-0 w-full bg-white border border-gray-200 rounded-lg shadow-lg mt-1 hidden z-50';
        resultsContainer.id = 'searchResults';
        searchContainer.appendChild(resultsContainer);

        // 点击外部关闭搜索结果
        document.addEventListener('click', (e) => {
            if (!searchContainer.contains(e.target)) {
                resultsContainer.classList.add('hidden');
            }
        });
    }

    /**
     * 切换侧边栏
     */
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileOverlay');
        
        if (window.innerWidth <= 1024) {
            // 移动端逻辑
            this.sidebarOpen = !this.sidebarOpen;
            
            if (this.sidebarOpen) {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            } else {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.style.overflow = '';
            }
        } else {
            // 桌面端逻辑（可以添加折叠功能）
            console.log('桌面端侧边栏切换');
        }
    }

    /**
     * 加载页面
     */
    loadPage(url, element, module, page = '') {
        try {
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            element.classList.add('active');

            // 更新面包屑
            this.updateBreadcrumb(module, page);

            // 显示加载状态
            this.showLoadingState();

            // 加载页面
            const iframe = document.getElementById('contentFrame');
            iframe.src = url;
            this.currentPage = { url, module, page };

            // 移动端自动关闭侧边栏
            if (window.innerWidth <= 1024) {
                setTimeout(() => this.toggleSidebar(), 100);
            }

            // 记录到历史
            this.addToHistory(url, module, page);

        } catch (error) {
            console.error('页面加载失败:', error);
            this.showErrorState('页面加载失败，请稍后重试');
        }
    }

    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(module, page) {
        const currentModule = document.getElementById('currentModule');
        const subBreadcrumb = document.getElementById('subBreadcrumb');
        const currentPage = document.getElementById('currentPage');

        if (currentModule) {
            currentModule.textContent = module;
        }

        if (page && subBreadcrumb && currentPage) {
            subBreadcrumb.classList.remove('hidden');
            currentPage.classList.remove('hidden');
            currentPage.textContent = page;
        } else if (subBreadcrumb && currentPage) {
            subBreadcrumb.classList.add('hidden');
            currentPage.classList.add('hidden');
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const iframe = document.getElementById('contentFrame');
        // 可以在这里添加加载动画
        iframe.style.opacity = '0.7';
    }

    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const iframe = document.getElementById('contentFrame');
        // 可以在这里显示错误信息
        console.error(message);
        iframe.style.opacity = '1';
    }

    /**
     * 刷新当前页面
     */
    refreshCurrentPage() {
        const iframe = document.getElementById('contentFrame');
        if (iframe && iframe.src) {
            this.showLoadingState();
            iframe.src = iframe.src;
        }
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        
        if (!query.trim()) {
            this.hideSearchResults();
            return;
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        const results = this.searchMenuItems(query);
        this.showSearchResults(results);
    }

    /**
     * 搜索菜单项
     */
    searchMenuItems(query) {
        const menuItems = document.querySelectorAll('.nav-link');
        const results = [];

        menuItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(query.toLowerCase())) {
                results.push({
                    element: item,
                    text: item.querySelector('span').textContent,
                    icon: item.querySelector('i').className
                });
            }
        });

        return results;
    }

    /**
     * 显示搜索结果
     */
    showSearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="p-3 text-gray-500 text-sm">没有找到相关功能</div>';
        } else {
            resultsContainer.innerHTML = results.map(result => `
                <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
                     onclick="this.closest('.relative').querySelector('input').value=''; document.getElementById('searchResults').classList.add('hidden'); arguments[0].preventDefault(); ${result.element.getAttribute('onclick')}">
                    <div class="flex items-center space-x-3">
                        <i class="${result.icon} text-gray-400"></i>
                        <span class="text-sm text-gray-900">${result.text}</span>
                    </div>
                </div>
            `).join('');
        }

        resultsContainer.classList.remove('hidden');
    }

    /**
     * 隐藏搜索结果
     */
    hideSearchResults() {
        const resultsContainer = document.getElementById('searchResults');
        if (resultsContainer) {
            resultsContainer.classList.add('hidden');
        }
    }

    /**
     * 处理窗口大小改变
     */
    handleResize() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileOverlay');

        if (window.innerWidth > 1024) {
            // 桌面端：重置移动端状态
            if (sidebar) sidebar.classList.remove('-translate-x-full');
            if (overlay) overlay.classList.add('hidden');
            document.body.style.overflow = '';
            this.sidebarOpen = false;
        } else {
            // 移动端：确保侧边栏隐藏
            if (sidebar && !this.sidebarOpen) {
                sidebar.classList.add('-translate-x-full');
            }
        }
    }

    /**
     * 处理iframe加载完成
     */
    handleIframeLoad() {
        const iframe = document.getElementById('contentFrame');
        if (iframe) {
            iframe.style.opacity = '1';
        }
        
        console.log('页面加载完成');
        
        // 可以在这里添加页面加载完成后的处理逻辑
        this.notifyPageLoaded();
    }

    /**
     * 通知页面加载完成
     */
    notifyPageLoaded() {
        // 发送自定义事件
        window.dispatchEvent(new CustomEvent('pageLoaded', {
            detail: this.currentPage
        }));
    }

    /**
     * 添加到历史记录
     */
    addToHistory(url, module, page) {
        const historyItem = { url, module, page, timestamp: Date.now() };
        
        let history = JSON.parse(localStorage.getItem('adminHistory') || '[]');
        history.unshift(historyItem);
        history = history.slice(0, 10); // 只保留最近10条
        
        localStorage.setItem('adminHistory', JSON.stringify(history));
    }

    /**
     * 用户菜单切换
     */
    toggleUserMenu() {
        // TODO: 实现用户菜单功能
        console.log('用户菜单功能待实现');
        
        // 可以在这里添加用户菜单的展开/收起逻辑
        // 包括用户信息、设置、退出登录等选项
    }

    /**
     * 通知功能
     */
    showNotification(message, type = 'info') {
        // TODO: 实现通知系统
        console.log(`${type}: ${message}`);
    }
}

// 全局函数（保持向后兼容）
function toggleSidebar() {
    if (window.adminDashboard) {
        window.adminDashboard.toggleSidebar();
    }
}

function loadPage(url, element, module, page = '') {
    if (window.adminDashboard) {
        window.adminDashboard.loadPage(url, element, module, page);
    }
}

function refreshCurrentPage() {
    if (window.adminDashboard) {
        window.adminDashboard.refreshCurrentPage();
    }
}

function toggleUserMenu() {
    if (window.adminDashboard) {
        window.adminDashboard.toggleUserMenu();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
    
    // 添加一些有用的调试信息
    console.log('智能设计与施工管理平台 - 后台管理系统');
    console.log('版本: v1.0.0');
    console.log('初始化完成');
}); 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工管理系统 - 直接测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pass {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-fail {
            background: #fecaca;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .test-controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .system-status {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .iframe-container {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> 施工管理系统直接测试</h1>
            <p>直接加载和测试施工管理系统页面</p>
        </div>
        
        <div class="system-status">
            <h2><i class="fas fa-rocket"></i> 系统状态检查</h2>
            <p>正在检查施工管理系统的可用性和功能完整性</p>
        </div>
        
        <div class="test-controls">
            <button class="btn btn-primary" onclick="runDirectTest()">
                <i class="fas fa-play"></i> 开始直接测试
            </button>
            <a href="../src/pc/components/pages/construction-management.html" class="btn btn-success" target="_blank">
                <i class="fas fa-external-link-alt"></i> 在新窗口打开系统
            </a>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3><i class="fas fa-file-alt"></i> 页面结构检查</h3>
                <div id="pageStructureTests">
                    <div class="test-item">
                        <span>页面文件存在性</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>CSS样式加载</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>JavaScript文件</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>HTML结构完整性</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-cubes"></i> 核心模块检查</h3>
                <div id="moduleTests">
                    <div class="test-item">
                        <span>文件管理模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>知识库模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>现场记录模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>异常处理模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>验收管理模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>人员管理模块</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-tasks"></i> 功能测试</h3>
                <div id="functionalTests">
                    <div class="test-item">
                        <span>阶段切换功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>数据保存功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>文件上传功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                    <div class="test-item">
                        <span>表单验证功能</span>
                        <span class="status-indicator status-pending">待检查</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-chart-line"></i> 测试结果</h3>
                <div id="testResults">
                    <div class="test-item">
                        <span>总测试项目</span>
                        <span id="totalTests">0</span>
                    </div>
                    <div class="test-item">
                        <span>通过测试</span>
                        <span id="passedTests" style="color: #10b981; font-weight: bold;">0</span>
                    </div>
                    <div class="test-item">
                        <span>失败测试</span>
                        <span id="failedTests" style="color: #ef4444; font-weight: bold;">0</span>
                    </div>
                    <div class="test-item">
                        <span>成功率</span>
                        <span id="successRate" style="font-weight: bold;">0%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="iframe-container">
            <iframe id="systemFrame" src="../src/pc/components/pages/construction-management.html"></iframe>
        </div>
    </div>

    <script>
        class DirectSystemTester {
            constructor() {
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;
            }

            async runDirectTest() {
                console.log('🚀 开始直接系统测试...');
                
                // 重置计数器
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;
                
                // 等待iframe加载
                await this.waitForIframeLoad();
                
                // 执行各项测试
                await this.testPageStructure();
                await this.testCoreModules();
                await this.testFunctionality();
                
                // 更新结果显示
                this.updateResults();
                
                console.log('✅ 直接系统测试完成');
            }

            async waitForIframeLoad() {
                return new Promise((resolve) => {
                    const iframe = document.getElementById('systemFrame');
                    iframe.onload = () => {
                        console.log('📄 系统页面已加载');
                        setTimeout(resolve, 1000); // 等待1秒确保完全加载
                    };
                });
            }

            async testPageStructure() {
                console.log('📋 测试页面结构...');
                
                const iframe = document.getElementById('systemFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 测试页面文件存在性
                this.updateTestStatus('pageStructureTests', 0, iframeDoc.title.includes('施工管理'));
                
                // 测试CSS样式加载
                const hasStyles = iframeDoc.styleSheets.length > 0;
                this.updateTestStatus('pageStructureTests', 1, hasStyles);
                
                // 测试JavaScript文件
                const hasScripts = iframeDoc.scripts.length > 0;
                this.updateTestStatus('pageStructureTests', 2, hasScripts);
                
                // 测试HTML结构完整性
                const hasMainContent = iframeDoc.querySelector('.main-content') !== null;
                this.updateTestStatus('pageStructureTests', 3, hasMainContent);
            }

            async testCoreModules() {
                console.log('📋 测试核心模块...');
                
                const iframe = document.getElementById('systemFrame');
                const iframeWindow = iframe.contentWindow;
                
                // 测试各个模块是否存在
                const modules = [
                    'fileManager',
                    'knowledgeManager', 
                    'recordManager',
                    'issueManager',
                    'acceptanceManager',
                    'constructionManager'
                ];
                
                modules.forEach((module, index) => {
                    const exists = iframeWindow[module] !== undefined;
                    this.updateTestStatus('moduleTests', index, exists);
                });
            }

            async testFunctionality() {
                console.log('📋 测试功能...');
                
                const iframe = document.getElementById('systemFrame');
                const iframeWindow = iframe.contentWindow;
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 测试阶段切换功能
                const phaseTabs = iframeDoc.querySelectorAll('.phase-tab');
                this.updateTestStatus('functionalTests', 0, phaseTabs.length >= 5);
                
                // 测试数据保存功能
                const hasLocalStorage = typeof iframeWindow.localStorage !== 'undefined';
                this.updateTestStatus('functionalTests', 1, hasLocalStorage);
                
                // 测试文件上传功能
                const fileInputs = iframeDoc.querySelectorAll('input[type="file"]');
                this.updateTestStatus('functionalTests', 2, fileInputs.length > 0);
                
                // 测试表单验证功能
                const forms = iframeDoc.querySelectorAll('form');
                this.updateTestStatus('functionalTests', 3, forms.length > 0);
            }

            updateTestStatus(sectionId, itemIndex, passed) {
                this.totalTests++;
                if (passed) {
                    this.passedTests++;
                } else {
                    this.failedTests++;
                }
                
                const section = document.getElementById(sectionId);
                const items = section.querySelectorAll('.test-item');
                const statusElement = items[itemIndex].querySelector('.status-indicator');
                
                if (passed) {
                    statusElement.textContent = '✅ 通过';
                    statusElement.className = 'status-indicator status-pass';
                } else {
                    statusElement.textContent = '❌ 失败';
                    statusElement.className = 'status-indicator status-fail';
                }
            }

            updateResults() {
                document.getElementById('totalTests').textContent = this.totalTests;
                document.getElementById('passedTests').textContent = this.passedTests;
                document.getElementById('failedTests').textContent = this.failedTests;
                
                const successRate = this.totalTests > 0 ? Math.round((this.passedTests / this.totalTests) * 100) : 0;
                document.getElementById('successRate').textContent = successRate + '%';
                
                // 更新成功率颜色
                const rateElement = document.getElementById('successRate');
                if (successRate >= 85) {
                    rateElement.style.color = '#10b981';
                } else if (successRate >= 70) {
                    rateElement.style.color = '#f59e0b';
                } else {
                    rateElement.style.color = '#ef4444';
                }
                
                console.log(`📊 测试完成: ${this.passedTests}/${this.totalTests} 通过 (${successRate}%)`);
            }
        }

        // 全局函数
        async function runDirectTest() {
            const tester = new DirectSystemTester();
            await tester.runDirectTest();
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('🚀 直接测试页面已加载');
            // 自动运行测试
            setTimeout(() => {
                runDirectTest();
            }, 2000);
        });
    </script>
</body>
</html>

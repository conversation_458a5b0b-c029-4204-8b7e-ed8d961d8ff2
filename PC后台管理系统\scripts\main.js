// 登录请求接口
interface LoginResponse {
    success: boolean;
    message?: string;
    token?: string;
}

// 登录表单处理
document.getElementById('loginForm')?.addEventListener('submit', (e: Event) => {
    e.preventDefault();
    
    const username = (document.getElementById('username') as HTMLInputElement).value;
    const password = (document.getElementById('password') as HTMLInputElement).value;
    
    // 类型安全的验证
    if(!username || !password) {
        alert('请输入用户名和密码');
        return;
    }
    
    console.log('登录请求:', {username, password});
    
    // 类型安全的API请求示例
    const login = async (username: string, password: string): Promise<LoginResponse> => {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({username, password})
            });
            return await response.json() as LoginResponse;
        } catch (error) {
            console.error('登录失败:', error);
            return {success: false, message: '网络错误'};
        }
    };
    
    // 模拟登录流程
    login(username, password).then((data: LoginResponse) => {
        if(data.success) {
            window.location.href = 'dashboard.html';
        } else {
            alert(data.message || '登录失败');
        }
    });
});

# 侧边栏使用指南

## 📋 文件说明

### 可复用组件文件
- `sidebar-component.html` - 纯侧边栏HTML组件
- `sidebar-styles.css` - 侧边栏CSS样式文件
- `standard-sidebar.html` - 完整演示页面

## 🚀 快速使用

### 方法一：复制粘贴（推荐）

1. **复制HTML结构**
   从 `sidebar-component.html` 复制完整的侧边栏HTML代码

2. **复制CSS样式**
   从 `sidebar-styles.css` 复制所有CSS样式

3. **粘贴到目标页面**
   ```html
   <!DOCTYPE html>
   <html lang="zh-CN">
   <head>
       <meta charset="UTF-8">
       <meta name="viewport" content="width=device-width, initial-scale=1.0">
       <title>页面标题 - 智能家居管理系统</title>
       <style>
           /* 粘贴 sidebar-styles.css 的所有内容 */
       </style>
   </head>
   <body>
       <div class="admin-layout">
           <!-- 粘贴 sidebar-component.html 的内容 -->
           
           <!-- 主内容区域 -->
           <main class="main-content">
               <h1>页面内容</h1>
               <!-- 你的页面内容 -->
           </main>
       </div>
   </body>
   </html>
   ```

### 方法二：外部引用

1. **引用CSS文件**
   ```html
   <link rel="stylesheet" href="../shared/sidebar-styles.css">
   ```

2. **使用JavaScript加载侧边栏**
   ```html
   <div id="sidebar-container"></div>
   <script>
   fetch('../shared/sidebar-component.html')
       .then(response => response.text())
       .then(html => {
           document.getElementById('sidebar-container').innerHTML = html;
       });
   </script>
   ```

## 🎯 完整页面模板

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题 - 智能家居管理系统</title>
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.95) 0%, 
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(229, 231, 235, 0.8);
            flex-shrink: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        .sidebar-header {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.6);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 12px 0;
        }

        .nav-section {
            margin-bottom: 16px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 4px 16px;
            margin: 4px 8px 2px;
            background: rgba(107, 114, 128, 0.08);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 3px;
            border-left: 2px solid rgba(107, 114, 128, 0.3);
            line-height: 1.2;
        }

        .nav-item {
            display: block;
            padding: 6px 16px;
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 1px 8px;
            position: relative;
            line-height: 1.3;
        }

        .nav-item:hover {
            background: rgba(243, 244, 246, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            color: #1f2937;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            color: #1f2937;
            font-weight: 600;
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateX(6px);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 40px;
        }

        /* 你的页面特定样式 */
        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">⌂</div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="user-profile.html" class="nav-item">我的代办</a>
                    <a href="orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="design-products.html" class="nav-item">设计商品</a>
                    <a href="requirements-management.html" class="nav-item">需求管理</a>
                    <a href="design-center.html" class="nav-item">设计中心</a>
                    <a href="design-cases.html" class="nav-item">设计案例</a>
                    <a href="project-center.html" class="nav-item">项目中心</a>
                    <a href="construction-management.html" class="nav-item">施工管理</a>
                    <a href="construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products.html" class="nav-item">商品管理</a>
                    <a href="orders.html" class="nav-item">订单管理</a>
                    <a href="customer-management.html" class="nav-item">客户管理</a>
                    <a href="marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="construction-knowledge.html" class="nav-item">交付知识库</a>
                    <a href="market-knowledge.html" class="nav-item">市转知识库</a>
                    <a href="security-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="design-knowledge-guide.html" class="nav-item">设计知识库</a>
                    <a href="product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="api-tester.html" class="nav-item">API 工具</a>
                    <a href="一装ERP-API文档.html" class="nav-item">ERP文档</a>
                    <a href="settings.html" class="nav-item">系统配置</a>
                    <a href="user-management.html" class="nav-item">用户管理</a>
                    <a href="user-permissions.html" class="nav-item">内部权限</a>
                    <a href="permissions.html" class="nav-item">客户权限</a>
                    <a href="data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="business-analytics.html" class="nav-item">需求分析</a>
                    <a href="project-analytics.html" class="nav-item">项目分析</a>
                    <a href="order-analytics.html" class="nav-item">订单分析</a>
                    <a href="customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="demo.html" class="nav-item">演示展示</a>
                    <a href="user-profile.html" class="nav-item">个人资料</a>
                    <a href="login.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">页面标题</h1>
                <p class="page-subtitle">页面描述</p>
            </div>

            <!-- 你的页面内容 -->
            <div>
                <p>这里是你的页面内容...</p>
            </div>
        </main>
    </div>

    <script>
        // 自动设置当前页面的导航高亮
        document.addEventListener('DOMContentLoaded', function() {
            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');
            
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPage) {
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
```

## ✅ 特性说明

### 🎨 视觉特性
- 毛玻璃透明效果
- 黑白灰现代化配色
- 蓝紫色渐变指示条
- 平滑过渡动画

### 🔧 功能特性
- 自动高亮当前页面
- 优雅的滚动条
- 响应式布局
- 一屏显示优化

### 📱 兼容性
- 支持所有现代浏览器
- 移动端友好
- 高DPI屏幕优化

## 🚀 使用建议

1. **直接复制**: 推荐直接复制完整模板，修改页面内容
2. **保持一致**: 所有页面使用相同的侧边栏结构
3. **路径调整**: 根据页面位置调整链接路径
4. **高亮设置**: 确保当前页面菜单项正确高亮

## 📞 技术支持

如需帮助或定制，请参考：
- `standard-sidebar.html` - 完整演示
- `sidebar-component.html` - 纯组件
- `sidebar-styles.css` - 样式文件

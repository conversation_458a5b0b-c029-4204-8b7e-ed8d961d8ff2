<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理 - 智能家居后台管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f5f5f5;
            line-height: 1.6;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            font-size: 1.8rem;
            font-weight: 500;
        }
        .breadcrumb {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #eee;
            font-size: 0.9rem;
            color: #666;
        }
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        .toolbar {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .requirements-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: #3498db;
            color: white;
            padding: 1rem 2rem;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .table-content {
            padding: 1.5rem;
        }
        .requirement-item {
            border: 1px solid #eee;
            border-radius: 6px;
            margin-bottom: 1rem;
            padding: 1.5rem;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .requirement-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: white;
        }
        .req-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        .customer-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .req-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .detail-item {
            display: flex;
            align-items: center;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
            margin-right: 0.5rem;
            min-width: 60px;
        }
        .detail-value {
            color: #333;
        }
        .requirements-text {
            background: white;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            margin-top: 1rem;
            font-style: italic;
            color: #555;
        }
        .actions {
            margin-top: 1rem;
            text-align: right;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
            padding: 1rem;
        }
        .page-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #666;
        }
        .empty-state .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .connection-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 需求管理中心 - API集成测试</h1>
    </div>
    
    <div class="breadcrumb">
        首页 > 业务管理 > 需求管理
    </div>
    
    <div class="main-content">
        <!-- 连接状态指示 -->
        <div class="connection-status" id="connectionStatus">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">正在检测API连接状态...</span>
        </div>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="filters">
                <select class="filter-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="待处理">待处理</option>
                    <option value="处理中">处理中</option>
                    <option value="已完成">已完成</option>
                    <option value="已取消">已取消</option>
                </select>
                <select class="filter-select" id="sourceFilter">
                    <option value="">全部来源</option>
                    <option value="H5移动端">H5移动端</option>
                    <option value="PC官网">PC官网</option>
                    <option value="微信小程序">微信小程序</option>
                </select>
            </div>
            <button class="refresh-btn" onclick="loadRequirements()">
                🔄 刷新数据
            </button>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <div class="stat-number" id="totalRequirements">-</div>
                <div class="stat-label">📋 总需求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingRequirements">-</div>
                <div class="stat-label">⏳ 待处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRequirements">-</div>
                <div class="stat-label">📅 今日新增</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgRooms">-</div>
                <div class="stat-label">🏠 平均房间数</div>
            </div>
        </div>
        
        <!-- 需求列表 -->
        <div class="requirements-table">
            <div class="table-header">
                <span>📱 客户需求列表 (实时API数据)</span>
                <span id="dataUpdateTime">数据更新时间: -</span>
            </div>
            <div class="table-content">
                <div class="loading" id="loadingContainer">
                    <p>正在加载需求数据...</p>
                </div>
                <div id="requirementsContent"></div>
                <div class="pagination" id="paginationContainer"></div>
            </div>
        </div>
    </div>
    
    <script>
        // API配置 - 使用端口8003
        const API_BASE_URL = 'http://localhost:8003';
        
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let isApiConnected = false;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 需求管理页面加载完成');
            initializePage();
        });
        
        // 初始化页面
        async function initializePage() {
            try {
                // 检查API连接
                await checkAPIConnection();
                
                if (isApiConnected) {
                    // 加载统计数据
                    await loadStatistics();
                    
                    // 加载需求列表
                    await loadRequirements();
                    
                    // 设置筛选器事件
                    setupFilters();
                }
                
                console.log('✅ 页面初始化完成');
            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                showConnectionError(error.message);
            }
        }
        
        // 检查API连接
        async function checkAPIConnection() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            try {
                statusText.textContent = '正在检测API连接...';
                
                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    isApiConnected = true;
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = `✅ API服务连接正常 (${API_BASE_URL})`;
                    console.log('✅ API连接成功:', data);
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                isApiConnected = false;
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = `❌ API服务连接失败: ${error.message}`;
                console.error('❌ API连接失败:', error);
                throw error;
            }
        }
        
        // 显示连接错误
        function showConnectionError(message) {
            const requirementsContent = document.getElementById('requirementsContent');
            requirementsContent.innerHTML = `
                <div class="empty-state">
                    <div class="icon">🔌</div>
                    <h3>API连接失败</h3>
                    <p>无法连接到后端服务: ${message}</p>
                    <p><strong>请检查:</strong></p>
                    <ul style="text-align: left; display: inline-block; margin-top: 1rem;">
                        <li>后端服务器是否已启动 (node simple-server.js)</li>
                        <li>API地址是否正确: ${API_BASE_URL}</li>
                        <li>网络连接是否正常</li>
                    </ul>
                    <button class="btn btn-primary" onclick="initializePage()" style="margin-top: 1rem;">🔄 重新检测</button>
                </div>
            `;
            document.getElementById('loadingContainer').style.display = 'none';
        }
        
        // 加载统计数据
        async function loadStatistics() {
            if (!isApiConnected) return;
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements/stats/summary`);
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    
                    document.getElementById('totalRequirements').textContent = stats.total_requirements || 0;
                    document.getElementById('todayRequirements').textContent = stats.today_requirements || 0;
                    document.getElementById('avgRooms').textContent = stats.avg_rooms || '0.0';
                    
                    // 计算待处理数量
                    const pendingCount = stats.status_breakdown.find(s => s.status === '待处理')?.count || 0;
                    document.getElementById('pendingRequirements').textContent = pendingCount;
                    
                    console.log('📊 统计数据加载成功:', stats);
                } else {
                    throw new Error(result.message || '获取统计数据失败');
                }
            } catch (error) {
                console.error('❌ 加载统计数据失败:', error);
                // 显示默认值
                document.getElementById('totalRequirements').textContent = '0';
                document.getElementById('pendingRequirements').textContent = '0';
                document.getElementById('todayRequirements').textContent = '0';
                document.getElementById('avgRooms').textContent = '0.0';
            }
        }
        
        // 加载需求列表
        async function loadRequirements(page = 1) {
            if (!isApiConnected) return;
            
            const loadingContainer = document.getElementById('loadingContainer');
            const requirementsContent = document.getElementById('requirementsContent');
            
            try {
                // 显示加载状态
                loadingContainer.style.display = 'block';
                requirementsContent.innerHTML = '';
                
                // 获取筛选条件
                const status = document.getElementById('statusFilter').value;
                const source = document.getElementById('sourceFilter').value;
                
                // 构建查询参数
                const params = new URLSearchParams({
                    page: page,
                    limit: 10
                });
                
                if (status) params.append('status', status);
                if (source) params.append('source', source);
                
                const response = await fetch(`${API_BASE_URL}/api/v1/requirements?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    const requirements = result.data;
                    const pagination = result.pagination;
                    
                    // 隐藏加载状态
                    loadingContainer.style.display = 'none';
                    
                    if (requirements.length === 0) {
                        requirementsContent.innerHTML = `
                            <div class="empty-state">
                                <div class="icon">📭</div>
                                <h3>暂无需求数据</h3>
                                <p>还没有客户提交需求，等待客户提交...</p>
                                <p style="margin-top: 1rem; font-size: 0.9rem; color: #999;">
                                    可通过H5页面提交测试数据: submit-requirement.html
                                </p>
                            </div>
                        `;
                    } else {
                        // 渲染需求列表
                        renderRequirements(requirements);
                        
                        // 渲染分页
                        if (pagination) {
                            renderPagination(pagination);
                        }
                        
                        // 更新数据时间
                        document.getElementById('dataUpdateTime').textContent = 
                            `数据更新时间: ${new Date().toLocaleString('zh-CN')}`;
                    }
                    
                    console.log(`📋 加载需求列表成功: ${requirements.length}条数据`);
                } else {
                    throw new Error(result.message || '获取需求列表失败');
                }
            } catch (error) {
                console.error('❌ 加载需求列表失败:', error);
                loadingContainer.style.display = 'none';
                requirementsContent.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">❌</div>
                        <h3>加载失败</h3>
                        <p>无法加载需求数据: ${error.message}</p>
                        <button class="btn btn-primary" onclick="loadRequirements()">🔄 重试</button>
                    </div>
                `;
            }
        }
        
        // 渲染需求列表
        function renderRequirements(requirements) {
            const container = document.getElementById('requirementsContent');
            let html = '';
            
            requirements.forEach((req, index) => {
                const statusClass = getStatusClass(req.status);
                
                html += `
                    <div class="requirement-item">
                        <div class="req-header">
                            <div class="customer-name">👤 ${req.customer_name}</div>
                            <div class="status-badge ${statusClass}">${req.status}</div>
                        </div>
                        
                        <div class="req-details">
                            <div class="detail-item">
                                <span class="detail-label">📞 电话:</span>
                                <span class="detail-value">${req.customer_phone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🏠 房型:</span>
                                <span class="detail-value">${req.house_rooms || 0}室${req.house_halls || 0}厅${req.house_bathrooms || 0}卫</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📅 提交:</span>
                                <span class="detail-value">${req.created_at}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📱 来源:</span>
                                <span class="detail-value">${req.source || 'H5移动端'}</span>
                            </div>
                        </div>
                        
                        ${req.address ? `
                        <div style="margin: 1rem 0;">
                            <span class="detail-label">📍 地址:</span>
                            <span class="detail-value">${req.address}</span>
                        </div>
                        ` : ''}
                        
                        <div class="requirements-text">
                            💡 客户需求: ${req.requirements}
                        </div>
                        
                        <div class="actions">
                            <button class="btn btn-primary" onclick="viewDetails('${req.id}')">查看详情</button>
                            <button class="btn btn-warning" onclick="updateStatus('${req.id}', '处理中')">开始处理</button>
                            <button class="btn btn-success" onclick="contactCustomer('${req.customer_phone}')">联系客户</button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '待处理': return 'status-pending';
                case '处理中': return 'status-processing';
                case '已完成': return 'status-completed';
                case '已取消': return 'status-cancelled';
                default: return 'status-pending';
            }
        }
        
        // 渲染分页
        function renderPagination(pagination) {
            const container = document.getElementById('paginationContainer');
            const { page, pages, total } = pagination;
            
            if (pages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let html = `<div style="margin-right: 1rem;">共 ${total} 条记录，第 ${page} / ${pages} 页</div>`;
            
            // 上一页
            if (page > 1) {
                html += `<button class="page-btn" onclick="loadRequirements(${page - 1})">上一页</button>`;
            }
            
            // 页码
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(pages, page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === page ? 'active' : '';
                html += `<button class="page-btn ${activeClass}" onclick="loadRequirements(${i})">${i}</button>`;
            }
            
            // 下一页
            if (page < pages) {
                html += `<button class="page-btn" onclick="loadRequirements(${page + 1})">下一页</button>`;
            }
            
            container.innerHTML = html;
            currentPage = page;
            totalPages = pages;
        }
        
        // 设置筛选器事件
        function setupFilters() {
            document.getElementById('statusFilter').addEventListener('change', () => {
                currentPage = 1;
                loadRequirements(1);
            });
            
            document.getElementById('sourceFilter').addEventListener('change', () => {
                currentPage = 1;
                loadRequirements(1);
            });
        }
        
        // 查看详情
        function viewDetails(id) {
            alert(`查看需求详情: ${id}\n这里可以实现详情模态框显示`);
        }
        
        // 更新状态
        async function updateStatus(id, newStatus) {
            if (!isApiConnected) {
                alert('API连接失败，无法更新状态');
                return;
            }
            
            if (confirm(`确认将需求状态更新为"${newStatus}"？`)) {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/v1/requirements/${id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            status: newStatus
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        alert('状态更新成功！');
                        loadRequirements(currentPage);
                        loadStatistics();
                    } else {
                        alert('更新失败: ' + result.message);
                    }
                } catch (error) {
                    console.error('更新状态失败:', error);
                    alert('更新状态失败: ' + error.message);
                }
            }
        }
        
        // 联系客户
        function contactCustomer(phone) {
            if (confirm(`确认联系客户 ${phone} 吗？`)) {
                // 这里可以集成拨号功能或跳转到客服系统
                window.open(`tel:${phone}`, '_self');
            }
        }
        
        // 自动刷新统计数据
        setInterval(() => {
            if (isApiConnected) {
                loadStatistics();
            }
        }, 30000); // 每30秒刷新统计数据
    </script>
</body>
</html>
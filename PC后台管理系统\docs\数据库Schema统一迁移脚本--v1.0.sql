-- ================================
-- 数据库Schema统一迁移脚本
-- 版本: v1.0
-- 创建时间: 2025-07-01
-- 说明: 将现有的不同Schema统一到标准Schema
-- 执行前请备份数据库！
-- ================================

-- 开启事务
START TRANSACTION;

-- ================================
-- 1. 备份现有数据
-- ================================

-- 备份现有用户表数据
CREATE TABLE users_backup AS SELECT * FROM users;
CREATE TABLE roles_backup AS SELECT * FROM roles WHERE 1=0; -- 如果存在的话
CREATE TABLE permissions_backup AS SELECT * FROM permissions WHERE 1=0; -- 如果存在的话

-- ================================
-- 2. 检查并处理现有表结构
-- ================================

-- 检查users表是否存在并获取结构信息
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = 'users');

-- ================================
-- 3. 统一users表结构
-- ================================

-- 如果users表存在，先处理数据迁移
IF @table_exists > 0 THEN
    -- 3.1 添加新字段（如果不存在）
    
    -- 检查并添加UUID主键支持
    SET @id_type = (SELECT DATA_TYPE FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'id');
    
    -- 如果主键不是VARCHAR(36)，需要迁移
    IF @id_type != 'varchar' THEN
        -- 添加新的UUID字段
        ALTER TABLE users ADD COLUMN new_id VARCHAR(36) DEFAULT (UUID()) AFTER id;
        
        -- 更新所有记录的UUID
        UPDATE users SET new_id = UUID() WHERE new_id IS NULL;
        
        -- 创建临时映射表
        CREATE TEMPORARY TABLE id_mapping AS 
        SELECT id as old_id, new_id FROM users;
    END IF;
    
    -- 3.2 添加缺失的字段
    
    -- 检查并添加first_name字段
    SET @first_name_exists = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'first_name');
    IF @first_name_exists = 0 THEN
        ALTER TABLE users ADD COLUMN first_name VARCHAR(50) COMMENT '名字 (与MedusaJS一致)';
    END IF;
    
    -- 检查并添加last_name字段
    SET @last_name_exists = (SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'last_name');
    IF @last_name_exists = 0 THEN
        ALTER TABLE users ADD COLUMN last_name VARCHAR(50) COMMENT '姓氏 (与MedusaJS一致)';
    END IF;
    
    -- 检查并添加balance字段
    SET @balance_exists = (SELECT COUNT(*) FROM information_schema.columns 
                          WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'balance');
    IF @balance_exists = 0 THEN
        ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额';
    END IF;
    
    -- 检查并添加has_account字段
    SET @has_account_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'has_account');
    IF @has_account_exists = 0 THEN
        ALTER TABLE users ADD COLUMN has_account BOOLEAN DEFAULT TRUE COMMENT '是否有账户 (与MedusaJS一致)';
    END IF;
    
    -- 检查并添加email_verified字段
    SET @email_verified_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'email_verified');
    IF @email_verified_exists = 0 THEN
        ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱验证状态';
    END IF;
    
    -- 检查并添加phone_verified字段
    SET @phone_verified_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'phone_verified');
    IF @phone_verified_exists = 0 THEN
        ALTER TABLE users ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机验证状态';
    END IF;
    
    -- 检查并添加last_login_at字段
    SET @last_login_exists = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'last_login_at');
    IF @last_login_exists = 0 THEN
        ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL COMMENT '最后登录时间';
    END IF;
    
    -- 检查并添加openid字段（一装ERP兼容）
    SET @openid_exists = (SELECT COUNT(*) FROM information_schema.columns 
                         WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'openid');
    IF @openid_exists = 0 THEN
        ALTER TABLE users ADD COLUMN openid VARCHAR(100) UNIQUE COMMENT '微信openid (一装ERP兼容)';
    END IF;
    
    -- 检查并添加user_type字段（一装ERP兼容）
    SET @user_type_exists = (SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'user_type');
    IF @user_type_exists = 0 THEN
        ALTER TABLE users ADD COLUMN user_type INT DEFAULT 5 COMMENT '用户类型 1内部员工 2渠道 3材料商 4项目经理 5客户 (一装ERP兼容)';
    END IF;
    
    -- 检查并添加metadata字段
    SET @metadata_exists = (SELECT COUNT(*) FROM information_schema.columns 
                           WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'metadata');
    IF @metadata_exists = 0 THEN
        ALTER TABLE users ADD COLUMN metadata JSON COMMENT '元数据 (与MedusaJS一致)';
    END IF;
    
    -- 检查并添加deleted_at字段
    SET @deleted_at_exists = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'deleted_at');
    IF @deleted_at_exists = 0 THEN
        ALTER TABLE users ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间 (与MedusaJS一致)';
    END IF;
    
    -- 3.3 统一字段类型和长度
    
    -- 统一email字段
    ALTER TABLE users MODIFY COLUMN email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱 - 必填字段 (与MedusaJS一致)';
    
    -- 统一phone字段
    SET @phone_column = (SELECT COLUMN_NAME FROM information_schema.columns 
                        WHERE table_schema = DATABASE() AND table_name = 'users' 
                        AND COLUMN_NAME IN ('phone', 'mobile', 'mobile_phone'));
    
    IF @phone_column = 'mobile' THEN
        ALTER TABLE users CHANGE COLUMN mobile phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号 - 必填字段 (与MedusaJS一致)';
    ELSEIF @phone_column = 'mobile_phone' THEN
        ALTER TABLE users CHANGE COLUMN mobile_phone phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号 - 必填字段 (与MedusaJS一致)';
    ELSE
        ALTER TABLE users MODIFY COLUMN phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号 - 必填字段 (与MedusaJS一致)';
    END IF;
    
    -- 统一password字段
    SET @password_column = (SELECT COLUMN_NAME FROM information_schema.columns 
                           WHERE table_schema = DATABASE() AND table_name = 'users' 
                           AND COLUMN_NAME IN ('password', 'password_hash'));
    
    IF @password_column = 'password_hash' THEN
        ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL COMMENT '密码哈希值';
    ELSE
        ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NOT NULL COMMENT '密码哈希值';
    END IF;
    
    -- 统一avatar字段
    SET @avatar_column = (SELECT COLUMN_NAME FROM information_schema.columns 
                         WHERE table_schema = DATABASE() AND table_name = 'users' 
                         AND COLUMN_NAME IN ('avatar', 'avatar_url', 'head_img_url'));
    
    IF @avatar_column = 'avatar_url' THEN
        ALTER TABLE users CHANGE COLUMN avatar_url avatar VARCHAR(500) COMMENT '头像URL';
    ELSEIF @avatar_column = 'head_img_url' THEN
        ALTER TABLE users CHANGE COLUMN head_img_url avatar VARCHAR(500) COMMENT '头像URL';
    ELSE
        ALTER TABLE users MODIFY COLUMN avatar VARCHAR(500) COMMENT '头像URL';
    END IF;
    
    -- 统一real_name字段
    SET @real_name_column = (SELECT COLUMN_NAME FROM information_schema.columns 
                            WHERE table_schema = DATABASE() AND table_name = 'users' 
                            AND COLUMN_NAME IN ('real_name', 'name'));
    
    IF @real_name_column = 'name' AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                                                 WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'real_name') THEN
        ALTER TABLE users CHANGE COLUMN name real_name VARCHAR(50) COMMENT '真实姓名';
    ELSE
        ALTER TABLE users MODIFY COLUMN real_name VARCHAR(50) COMMENT '真实姓名';
    END IF;
    
    -- 3.4 添加必要的索引
    
    -- 删除可能存在的旧索引
    SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() AND table_name = 'users' AND index_name = 'idx_email');
    IF @index_exists = 0 THEN
        ALTER TABLE users ADD INDEX idx_email (email);
    END IF;
    
    SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() AND table_name = 'users' AND index_name = 'idx_phone');
    IF @index_exists = 0 THEN
        ALTER TABLE users ADD INDEX idx_phone (phone);
    END IF;
    
    SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() AND table_name = 'users' AND index_name = 'idx_username');
    IF @index_exists = 0 THEN
        ALTER TABLE users ADD INDEX idx_username (username);
    END IF;
    
    SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() AND table_name = 'users' AND index_name = 'idx_status');
    IF @index_exists = 0 THEN
        ALTER TABLE users ADD INDEX idx_status (status);
    END IF;
    
    SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() AND table_name = 'users' AND index_name = 'idx_user_type');
    IF @index_exists = 0 THEN
        ALTER TABLE users ADD INDEX idx_user_type (user_type);
    END IF;

END IF;

-- ================================
-- 4. 创建RBAC权限系统表（如果不存在）
-- ================================

-- 创建roles表
CREATE TABLE IF NOT EXISTS roles (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '角色ID',
  name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '角色描述',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_is_system (is_system),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 创建permissions表
CREATE TABLE IF NOT EXISTS permissions (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '权限ID',
  code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
  name VARCHAR(100) NOT NULL COMMENT '权限名称',
  description TEXT COMMENT '权限描述',
  module VARCHAR(50) NOT NULL COMMENT '所属模块',
  resource VARCHAR(50) NOT NULL COMMENT '资源类型',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_module (module),
  INDEX idx_resource_action (resource, action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 创建role_permissions表
CREATE TABLE IF NOT EXISTS role_permissions (
  role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
  permission_id VARCHAR(36) NOT NULL COMMENT '权限ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (role_id, permission_id),
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 创建user_roles表
CREATE TABLE IF NOT EXISTS user_roles (
  user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, role_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- ================================
-- 5. 提交事务
-- ================================

COMMIT;

-- ================================
-- 6. 验证迁移结果
-- ================================

-- 显示迁移后的表结构
SHOW CREATE TABLE users;
SHOW CREATE TABLE roles;
SHOW CREATE TABLE permissions;
SHOW CREATE TABLE role_permissions;
SHOW CREATE TABLE user_roles;

-- 显示数据统计
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'roles' as table_name, COUNT(*) as record_count FROM roles
UNION ALL
SELECT 'permissions' as table_name, COUNT(*) as record_count FROM permissions;

-- 迁移完成提示
SELECT '数据库Schema统一迁移完成！' as message;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框布局测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            padding: 20px;
        }

        .main-content {
            margin-left: 240px;
            flex: 1;
            padding: 20px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 10px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        /* 模态框样式 - 修复后的版本 */
        .modal {
            position: fixed;
            top: 0;
            left: 240px; /* 避免遮挡左侧菜单栏 */
            width: calc(100% - 240px); /* 调整宽度 */
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            max-height: 60vh;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .modal {
                left: 0 !important;
                width: 100% !important;
            }

            .modal-content {
                width: 95%;
                margin: 20px;
            }
        }

        .layout-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .layout-info h3 {
            color: #0369a1;
            margin-bottom: 8px;
        }

        .layout-info p {
            color: #0c4a6e;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2><i class="fas fa-home"></i> 智能家居</h2>
                <p style="font-size: 12px; opacity: 0.8;">管理系统</p>
            </div>
            
            <nav>
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px; opacity: 0.8;">业务管理</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 8px;">
                            <a href="#" style="color: white; text-decoration: none; display: block; padding: 8px 12px; border-radius: 6px; transition: background 0.2s;">
                                <i class="fas fa-list"></i> 需求管理
                            </a>
                        </li>
                        <li style="margin-bottom: 8px;">
                            <a href="#" style="color: white; text-decoration: none; display: block; padding: 8px 12px; border-radius: 6px; transition: background 0.2s;">
                                <i class="fas fa-palette"></i> 设计中心
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="test-section">
                <h1>模态框布局测试</h1>
                
                <div class="layout-info">
                    <h3><i class="fas fa-info-circle"></i> 布局说明</h3>
                    <p>此测试页面验证模态框是否正确避开左侧菜单栏。模态框应该只在主内容区域显示，不遮挡左侧的菜单栏。</p>
                </div>

                <h3>测试功能</h3>
                <p>点击下面的按钮测试模态框显示效果：</p>
                
                <button class="btn btn-primary" onclick="showModal()">
                    <i class="fas fa-edit"></i> 打开编辑模态框
                </button>
                
                <button class="btn btn-secondary" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i> 切换侧边栏（移动端）
                </button>
            </div>

            <div class="test-section">
                <h3>预期效果</h3>
                <ul style="line-height: 1.8;">
                    <li>✅ 模态框不应遮挡左侧菜单栏</li>
                    <li>✅ 模态框只在主内容区域显示</li>
                    <li>✅ 移动端时模态框占满整个屏幕</li>
                    <li>✅ 模态框有平滑的动画效果</li>
                    <li>✅ 点击背景或关闭按钮可以关闭模态框</li>
                </ul>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑需求</h3>
                <button class="modal-close" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">客户姓名</label>
                    <input type="text" class="form-input" placeholder="请输入客户姓名" value="张先生">
                </div>
                <div class="form-group">
                    <label class="form-label">联系电话</label>
                    <input type="text" class="form-input" placeholder="请输入联系电话" value="13800138001">
                </div>
                <div class="form-group">
                    <label class="form-label">房屋面积</label>
                    <input type="text" class="form-input" placeholder="请输入房屋面积" value="120㎡">
                </div>
                <div class="form-group">
                    <label class="form-label">设计师</label>
                    <select class="form-input">
                        <option value="">请选择设计师</option>
                        <option value="designer_001">张设计师 - 现代简约</option>
                        <option value="designer_002">李设计师 - 智能家居</option>
                        <option value="designer_003">王设计师 - 欧式风格</option>
                        <option value="designer_004">赵设计师 - 中式风格</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button class="btn btn-primary" onclick="saveAndClose()">保存</button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            const modal = document.getElementById('testModal');
            modal.classList.add('show');
        }

        function hideModal() {
            const modal = document.getElementById('testModal');
            modal.classList.remove('show');
        }

        function saveAndClose() {
            alert('保存成功！');
            hideModal();
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // 点击背景关闭模态框
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideModal();
            }
        });

        // 页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('模态框布局测试页面已加载');
            console.log('侧边栏宽度: 240px');
            console.log('模态框左偏移: 240px');
            console.log('模态框宽度: calc(100% - 240px)');
        });
    </script>
</body>
</html>

/**
 * 标准化主要功能页面脚本
 * 将旧完整结构页面(17个)升级为标准33项菜单结构
 */

const fs = require('fs');
const path = require('path');

// 需要标准化的旧完整结构页面列表
const OLD_FULL_STRUCTURE_PAGES = [
    'admin-dashboard.html',
    'analytics.html', 
    'api-tools.html',
    'construction-management.html',
    'contract-management.html',
    'customer-management.html',
    'customer-permissions.html',
    'design-management.html',
    'design-management-new.html',
    'index.html',
    'internal-permissions.html',
    'knowledge-management.html',
    'marketing-management.html',
    'projects.html',
    'system-settings.html',
    'user-management.html',
    'user-profile.html'
];

// 标准侧边栏模板
function generateStandardSidebar(activePageFile) {
    return `            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="my-todos.html" class="nav-item${activePageFile === 'my-todos.html' ? ' active' : ''}">我的代办</a>
                    <a href="my-orders.html" class="nav-item${activePageFile === 'my-orders.html' ? ' active' : ''}">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="design-products.html" class="nav-item${activePageFile === 'design-products.html' ? ' active' : ''}">设计商品</a>
                    <a href="requirements-management.html" class="nav-item${activePageFile === 'requirements-management.html' ? ' active' : ''}">需求管理</a>
                    <a href="design-center.html" class="nav-item${activePageFile === 'design-center.html' ? ' active' : ''}">设计中心</a>
                    <a href="design-cases.html" class="nav-item${activePageFile === 'design-cases.html' ? ' active' : ''}">设计案例</a>
                    <a href="project-center.html" class="nav-item${activePageFile === 'project-center.html' ? ' active' : ''}">项目中心</a>
                    <a href="construction-management.html" class="nav-item${activePageFile === 'construction-management.html' ? ' active' : ''}">施工管理</a>
                    <a href="construction-guide.html" class="nav-item${activePageFile === 'construction-guide.html' ? ' active' : ''}">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products.html" class="nav-item${activePageFile === 'products.html' ? ' active' : ''}">商品管理</a>
                    <a href="orders.html" class="nav-item${activePageFile === 'orders.html' ? ' active' : ''}">订单管理</a>
                    <a href="customer-management.html" class="nav-item${activePageFile === 'customer-management.html' ? ' active' : ''}">客户管理</a>
                    <a href="marketing-management.html" class="nav-item${activePageFile === 'marketing-management.html' ? ' active' : ''}">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="design-knowledge.html" class="nav-item${activePageFile === 'design-knowledge.html' ? ' active' : ''}">设计知识库</a>
                    <a href="delivery-knowledge.html" class="nav-item${activePageFile === 'delivery-knowledge.html' ? ' active' : ''}">交底知识库</a>
                    <a href="electrical-delivery-knowledge.html" class="nav-item${activePageFile === 'electrical-delivery-knowledge.html' ? ' active' : ''}">水电交底知识库</a>
                    <a href="market-knowledge.html" class="nav-item${activePageFile === 'market-knowledge.html' ? ' active' : ''}">市转知识库</a>
                    <a href="installation-knowledge.html" class="nav-item${activePageFile === 'installation-knowledge.html' ? ' active' : ''}">布线知识库</a>
                    <a href="product-knowledge.html" class="nav-item${activePageFile === 'product-knowledge.html' ? ' active' : ''}">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="api-tools.html" class="nav-item${activePageFile === 'api-tools.html' ? ' active' : ''}">API 工具</a>
                    <a href="erp-documentation.html" class="nav-item${activePageFile === 'erp-documentation.html' ? ' active' : ''}">ERP文档</a>
                    <a href="system-settings.html" class="nav-item${activePageFile === 'system-settings.html' ? ' active' : ''}">系统配置</a>
                    <a href="user-management.html" class="nav-item${activePageFile === 'user-management.html' ? ' active' : ''}">用户管理</a>
                    <a href="internal-permissions.html" class="nav-item${activePageFile === 'internal-permissions.html' ? ' active' : ''}">内部权限</a>
                    <a href="customer-permissions.html" class="nav-item${activePageFile === 'customer-permissions.html' ? ' active' : ''}">客户权限</a>
                    <a href="data-management.html" class="nav-item${activePageFile === 'data-management.html' ? ' active' : ''}">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="requirements-analytics.html" class="nav-item${activePageFile === 'requirements-analytics.html' ? ' active' : ''}">需求分析</a>
                    <a href="project-analytics.html" class="nav-item${activePageFile === 'project-analytics.html' ? ' active' : ''}">项目分析</a>
                    <a href="order-analytics.html" class="nav-item${activePageFile === 'order-analytics.html' ? ' active' : ''}">订单分析</a>
                    <a href="customer-analytics.html" class="nav-item${activePageFile === 'customer-analytics.html' ? ' active' : ''}">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="demo.html" class="nav-item${activePageFile === 'demo.html' ? ' active' : ''}">演示展示</a>
                    <a href="user-profile.html" class="nav-item${activePageFile === 'user-profile.html' ? ' active' : ''}">个人资料</a>
                    <a href="logout.html" class="nav-item${activePageFile === 'logout.html' ? ' active' : ''}">退出登录</a>
                </div>
            </nav>`;
}

// 标准化单个页面
function standardizePage(filePath) {
    const fileName = path.basename(filePath);
    console.log(`🔧 标准化页面: ${fileName}`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 查找现有的nav-menu部分
        const navMenuPattern = /<nav class="nav-menu">[\s\S]*?<\/nav>/;
        const match = content.match(navMenuPattern);
        
        if (!match) {
            console.log(`   ❌ 未找到nav-menu结构`);
            return { fileName, success: false, error: '未找到nav-menu结构' };
        }
        
        // 生成新的标准侧边栏
        const newSidebar = generateStandardSidebar(fileName);
        
        // 替换旧的侧边栏
        content = content.replace(navMenuPattern, newSidebar);
        
        // 保存文件
        fs.writeFileSync(filePath, content, 'utf8');
        
        console.log(`   ✅ 标准化完成: 升级为33项菜单结构`);
        return { fileName, success: true, changes: ['升级为标准33项菜单结构'] };
        
    } catch (error) {
        console.log(`   ❌ 标准化失败: ${error.message}`);
        return { fileName, success: false, error: error.message };
    }
}

// 批量标准化页面
function batchStandardizePages() {
    console.log('🚀 开始标准化主要功能页面...\n');
    
    const pagesDir = path.join(__dirname, '../pages');
    const results = {
        total: OLD_FULL_STRUCTURE_PAGES.length,
        processed: 0,
        standardized: 0,
        errors: 0,
        changes: []
    };
    
    console.log(`📊 需要标准化 ${OLD_FULL_STRUCTURE_PAGES.length} 个页面\n`);
    
    // 处理每个页面
    OLD_FULL_STRUCTURE_PAGES.forEach(pageFile => {
        const filePath = path.join(pagesDir, pageFile);
        
        if (fs.existsSync(filePath)) {
            results.processed++;
            const result = standardizePage(filePath);
            
            if (result.success) {
                results.standardized++;
                results.changes.push(result);
            } else {
                results.errors++;
            }
        } else {
            console.log(`⚠️  文件不存在: ${pageFile}`);
            results.errors++;
        }
    });
    
    return results;
}

// 生成标准化报告
function generateStandardizeReport(results) {
    console.log('\n📊 标准化完成报告');
    console.log('='.repeat(50));
    
    console.log(`\n📈 标准化统计:`);
    console.log(`   目标页面数: ${results.total}`);
    console.log(`   已处理: ${results.processed}`);
    console.log(`   已标准化: ${results.standardized}`);
    console.log(`   错误数: ${results.errors}`);
    console.log(`   成功率: ${(results.standardized/results.processed*100).toFixed(1)}%`);
    
    if (results.changes.length > 0) {
        console.log(`\n🔧 标准化详情:`);
        results.changes.forEach(change => {
            console.log(`   ✅ ${change.fileName}: ${change.changes.join(', ')}`);
        });
    }
    
    console.log(`\n✅ 阶段二主要功能页面标准化完成！`);
    console.log(`🎯 ${results.standardized}个页面已升级为标准33项菜单结构`);
    console.log(`🚀 可以继续进行阶段三的完善补充工作`);
}

// 主函数
function main() {
    const results = batchStandardizePages();
    generateStandardizeReport(results);
    
    // 保存标准化结果
    const reportPath = path.join(__dirname, '../../docs/standardization-report.json');
    try {
        fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
        console.log(`\n💾 标准化报告已保存到: ${reportPath}`);
    } catch (error) {
        console.log(`\n⚠️  保存报告失败: ${error.message}`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    standardizePage,
    batchStandardizePages,
    generateStandardizeReport,
    generateStandardSidebar,
    OLD_FULL_STRUCTURE_PAGES
};

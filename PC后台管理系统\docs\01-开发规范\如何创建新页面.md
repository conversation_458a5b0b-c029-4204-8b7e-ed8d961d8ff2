# 智能家居管理系统 - 新页面创建指南

## 📋 概述

为了保持系统的一致性和可维护性，我们创建了标准的侧边栏组件和页面模板。所有新页面都应该使用这些标准组件。

## 🎯 为什么要使用组件化？

### ❌ 之前的问题：
- 每个页面都重复相同的侧边栏代码
- 菜单更新时需要修改多个文件
- 样式不一致，维护困难
- 代码冗余，容易出错

### ✅ 现在的优势：
- 一次修改，全局生效
- 样式完全统一
- 代码复用，减少错误
- 易于维护和扩展

## 📁 文件结构

```
PC后台管理系统/
├── components/
│   └── shared/
│       └── sidebar-component.js     # 侧边栏组件
├── templates/
│   └── standard-page-template.html  # 标准页面模板
└── docs/
    └── 如何创建新页面.md           # 本文档
```

## 🚀 创建新页面的步骤

### 1. 复制模板文件
```bash
# 复制标准模板
cp templates/standard-page-template.html src/pc/components/pages/新页面名称.html
```

### 2. 修改页面信息
打开新页面文件，修改以下内容：

```html
<!-- 修改页面标题 -->
<title>你的页面标题 - 智能家居管理系统</title>

<!-- 修改顶部导航 -->
<h1 class="breadcrumb-title">你的页面标题</h1>
<p class="breadcrumb-description">页面功能描述</p>

<!-- 修改内容区域 -->
<div class="content-header">
    <h2>内容标题</h2>
    <p>内容描述</p>
</div>
```

### 3. 添加页面内容
在 `<div class="page-body">` 中添加你的页面内容：

```html
<div class="page-body">
    <!-- 你的页面内容 -->
    <div class="your-content">
        <!-- 表格、表单、卡片等 -->
    </div>
</div>
```

### 4. 添加页面特定的样式
如果需要特定样式，在 `<style>` 标签中添加：

```html
<style>
    /* 页面特定样式 */
    .your-custom-class {
        /* 你的样式 */
    }
</style>
```

### 5. 添加页面特定的JavaScript
在页面底部添加功能代码：

```html
<script>
    // 页面特定功能
    function yourFunction() {
        // 你的代码
    }
</script>
```

## 🔧 侧边栏组件功能

### 自动功能：
- ✅ 自动检测当前页面并高亮对应菜单项
- ✅ 统一的样式和交互效果
- ✅ 响应式设计和滚动条美化
- ✅ 退出登录功能

### 菜单结构：
- **个人中心** (2项)
- **业务管理** (7项)
- **商务中心** (4项)
- **知识库** (6项)
- **系统工具** (7项)
- **数据分析** (4项)
- **个人中心** (3项)

## 📝 示例：创建"用户分析"页面

### 1. 复制模板
```bash
cp templates/standard-page-template.html src/pc/components/pages/user-analytics.html
```

### 2. 修改内容
```html
<title>用户分析 - 智能家居管理系统</title>

<h1 class="breadcrumb-title">用户分析</h1>
<p class="breadcrumb-description">用户行为数据分析和统计报告</p>

<div class="content-header">
    <h2>用户数据概览</h2>
    <p>查看用户活跃度、使用习惯等关键指标</p>
</div>

<div class="page-body">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">1,234</div>
            <div class="stat-label">总用户数</div>
        </div>
        <!-- 更多统计卡片 -->
    </div>
</div>
```

## 🎨 样式指南

### 使用统一的样式类：
- `.content-area` - 内容区域容器
- `.content-header` - 内容标题区域
- `.page-body` - 页面主体内容
- `.stats-grid` - 统计数据网格
- `.stat-card` - 统计卡片

### 颜色规范：
- 主色调：黑白灰配色方案
- 背景色：`#f8fafc`
- 文字色：`#1f2937`
- 次要文字：`#6b7280`
- 边框色：`#e5e7eb`

## 🔄 更新侧边栏菜单

如果需要修改菜单结构，只需要编辑：
```
components/shared/sidebar-component.js
```

修改后，所有使用该组件的页面都会自动更新！

## ⚠️ 注意事项

1. **文件路径**：确保JavaScript文件路径正确
2. **页面命名**：使用kebab-case命名（如：user-analytics.html）
3. **菜单激活**：确保页面文件名与菜单链接一致
4. **样式冲突**：避免使用与组件冲突的CSS类名

## 🎉 完成！

现在你已经学会了如何使用标准组件创建新页面。这种方式确保了：
- ✅ 样式统一
- ✅ 功能一致
- ✅ 易于维护
- ✅ 代码复用

有任何问题，请参考现有页面的实现或联系开发团队！

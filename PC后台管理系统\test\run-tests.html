<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品素材管理系统测试</title>
    <link rel="stylesheet" href="../styles/unified-admin-styles.css">
    <style>
        .test-dashboard { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .test-section { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
        .test-section h3 { color: var(--primary-black); margin-top: 0; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-md); }
        .test-card { background: var(--bg-muted); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-md); }
        .test-status { display: inline-block; padding: 4px 8px; border-radius: var(--radius-sm); font-size: 12px; font-weight: 500; }
        .status-pending { background: var(--warning-orange); color: white; }
        .status-running { background: var(--accent-blue); color: white; }
        .status-passed { background: var(--success-green); color: white; }
        .status-failed { background: var(--error-red); color: white; }
        .test-log { background: #1e1e1e; color: #ffffff; padding: var(--spacing-md); border-radius: var(--radius-sm); font-family: 'Courier New', monospace; font-size: 12px; height: 400px; overflow-y: auto; white-space: pre-wrap; }
        .btn-test { background: var(--primary-black); color: white; padding: var(--spacing-sm) var(--spacing-md); border: none; border-radius: var(--radius-sm); cursor: pointer; margin: var(--spacing-xs); }
        .btn-test:hover { background: var(--gray-800); }
        .btn-test:disabled { background: var(--gray-400); cursor: not-allowed; }
        .progress-bar { width: 100%; height: 8px; background: var(--bg-muted); border-radius: 4px; overflow: hidden; margin: var(--spacing-xs) 0; }
        .progress-fill { height: 100%; background: var(--primary-black); width: 0%; transition: width 0.3s ease; }
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: var(--spacing-sm); margin-bottom: var(--spacing-md); }
        .stat-item { text-align: center; padding: var(--spacing-sm); background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-sm); }
        .stat-value { font-size: 24px; font-weight: 700; color: var(--primary-black); }
        .stat-label { font-size: 12px; color: var(--text-secondary); }
        .upload-demo { border: 2px dashed var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg); text-align: center; margin: var(--spacing-md) 0; }
        .upload-demo:hover { border-color: var(--primary-black); }
        .upload-demo.dragover { border-color: var(--success-green); background: rgba(40, 167, 69, 0.1); }
        .file-list { margin-top: var(--spacing-md); }
        .file-item { display: flex; align-items: center; justify-content: space-between; padding: var(--spacing-xs); border: 1px solid var(--border-color); border-radius: var(--radius-sm); margin-bottom: var(--spacing-xs); }
        .file-info { flex: 1; }
        .file-name { font-weight: 500; }
        .file-size { font-size: 12px; color: var(--text-secondary); }
        .system-info { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-md); margin-bottom: var(--spacing-lg); }
        .system-info h4 { margin-top: 0; color: var(--primary-black); }
        .system-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-sm); }
        .system-item { display: flex; justify-content: space-between; padding: var(--spacing-xs); border-bottom: 1px solid var(--border-color); }
        .system-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="test-dashboard">
        <header style="text-align: center; margin-bottom: var(--spacing-xl);">
            <h1>产品素材管理系统测试台</h1>
            <p>测试PC端上传功能和同步显示功能</p>
        </header>

        <!-- 系统信息 -->
        <div class="system-info">
            <h4>系统环境信息</h4>
            <div class="system-grid">
                <div class="system-item">
                    <span>浏览器</span>
                    <span id="browserInfo">检测中...</span>
                </div>
                <div class="system-item">
                    <span>用户代理</span>
                    <span id="userAgent">检测中...</span>
                </div>
                <div class="system-item">
                    <span>支持拖拽</span>
                    <span id="dragSupport">检测中...</span>
                </div>
                <div class="system-item">
                    <span>支持文件API</span>
                    <span id="fileAPISupport">检测中...</span>
                </div>
                <div class="system-item">
                    <span>支持FormData</span>
                    <span id="formDataSupport">检测中...</span>
                </div>
                <div class="system-item">
                    <span>本地存储</span>
                    <span id="localStorageSupport">检测中...</span>
                </div>
            </div>
        </div>

        <!-- 测试统计 -->
        <div class="test-section">
            <h3>测试统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="passedTests">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="failedTests">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="testDuration">0ms</div>
                    <div class="stat-label">总耗时</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="testProgress"></div>
            </div>
        </div>

        <!-- 快速测试 -->
        <div class="test-section">
            <h3>快速功能测试</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>文件上传演示</h4>
                    <div class="upload-demo" id="uploadDemo">
                        <i class="fas fa-upload" style="font-size: 48px; color: var(--text-secondary); margin-bottom: var(--spacing-md);"></i>
                        <p>拖拽文件到此处或点击选择文件</p>
                        <input type="file" id="fileInput" multiple accept="image/*,video/*,.pdf,.md,.txt" style="display: none;">
                        <button class="btn-test" onclick="document.getElementById('fileInput').click()">
                            选择文件
                        </button>
                    </div>
                    <div class="file-list" id="fileList"></div>
                    <button class="btn-test" onclick="testUpload()" id="uploadBtn" disabled>
                        开始上传测试
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>同步显示测试</h4>
                    <p>测试上传后文件是否立即显示在素材列表中</p>
                    <button class="btn-test" onclick="testSyncDisplay()">
                        测试同步显示
                    </button>
                    <div id="syncResult" style="margin-top: var(--spacing-sm);"></div>
                </div>
                
                <div class="test-card">
                    <h4>性能测试</h4>
                    <p>测试大文件上传和批量上传性能</p>
                    <button class="btn-test" onclick="testPerformance()">
                        性能测试
                    </button>
                    <div id="performanceResult" style="margin-top: var(--spacing-sm);"></div>
                </div>
                
                <div class="test-card">
                    <h4>错误处理测试</h4>
                    <p>测试各种错误情况的处理</p>
                    <button class="btn-test" onclick="testErrorHandling()">
                        错误处理测试
                    </button>
                    <div id="errorResult" style="margin-top: var(--spacing-sm);"></div>
                </div>
            </div>
        </div>

        <!-- 集成测试 -->
        <div class="test-section">
            <h3>集成测试套件</h3>
            <div style="margin-bottom: var(--spacing-md);">
                <button class="btn-test" onclick="runAllTests()">
                    运行所有测试
                </button>
                <button class="btn-test" onclick="runQuickTests()">
                    快速测试
                </button>
                <button class="btn-test" onclick="clearTestLog()">
                    清空日志
                </button>
                <button class="btn-test" onclick="exportTestReport()">
                    导出报告
                </button>
            </div>
            <div class="test-log" id="testLog">等待测试开始...</div>
        </div>

        <!-- 测试项目列表 -->
        <div class="test-section">
            <h3>测试项目详情</h3>
            <div class="test-grid" id="testItems"></div>
        </div>
    </div>

    <script src="product-materials-integration-test.js"></script>
    <script>
        class TestRunner {
            constructor() {
                this.testResults = [];
                this.selectedFiles = [];
                this.currentTest = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.detectSystemInfo();
                this.setupTestItems();
            }

            setupEventListeners() {
                const uploadDemo = document.getElementById('uploadDemo');
                const fileInput = document.getElementById('fileInput');

                // 拖拽事件
                uploadDemo.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadDemo.classList.add('dragover');
                });

                uploadDemo.addEventListener('dragleave', () => {
                    uploadDemo.classList.remove('dragover');
                });

                uploadDemo.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadDemo.classList.remove('dragover');
                    const files = Array.from(e.dataTransfer.files);
                    this.handleFiles(files);
                });

                // 文件选择事件
                fileInput.addEventListener('change', (e) => {
                    const files = Array.from(e.target.files);
                    this.handleFiles(files);
                });

                // 点击上传区域
                uploadDemo.addEventListener('click', () => {
                    fileInput.click();
                });
            }

            handleFiles(files) {
                this.selectedFiles = files;
                this.displaySelectedFiles();
                document.getElementById('uploadBtn').disabled = files.length === 0;
            }

            displaySelectedFiles() {
                const fileList = document.getElementById('fileList');
                fileList.innerHTML = '';

                this.selectedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${this.formatFileSize(file.size)}</div>
                        </div>
                        <button class="btn-test" onclick="testRunner.removeFile(${index})" style="background: var(--error-red);">
                            删除
                        </button>
                    `;
                    fileList.appendChild(fileItem);
                });
            }

            removeFile(index) {
                this.selectedFiles.splice(index, 1);
                this.displaySelectedFiles();
                document.getElementById('uploadBtn').disabled = this.selectedFiles.length === 0;
            }

            formatFileSize(bytes) {
                const sizes = ['B', 'KB', 'MB', 'GB'];
                if (bytes === 0) return '0 B';
                const i = Math.floor(Math.log(bytes) / Math.log(1024));
                return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
            }

            detectSystemInfo() {
                const userAgent = navigator.userAgent;
                const browser = this.getBrowserInfo(userAgent);
                
                document.getElementById('browserInfo').textContent = browser;
                document.getElementById('userAgent').textContent = userAgent.substring(0, 50) + '...';
                document.getElementById('dragSupport').textContent = 'ondragover' in window ? '支持' : '不支持';
                document.getElementById('fileAPISupport').textContent = window.File && window.FileReader && window.FileList && window.Blob ? '支持' : '不支持';
                document.getElementById('formDataSupport').textContent = window.FormData ? '支持' : '不支持';
                document.getElementById('localStorageSupport').textContent = window.localStorage ? '支持' : '不支持';
            }

            getBrowserInfo(userAgent) {
                if (userAgent.includes('Chrome')) return 'Chrome';
                if (userAgent.includes('Firefox')) return 'Firefox';
                if (userAgent.includes('Safari')) return 'Safari';
                if (userAgent.includes('Edge')) return 'Edge';
                return '未知浏览器';
            }

            setupTestItems() {
                const testItems = [
                    { name: '单文件上传', status: 'pending', description: '测试单个文件上传功能' },
                    { name: '批量上传', status: 'pending', description: '测试多文件批量上传功能' },
                    { name: '品牌结构导入', status: 'pending', description: '测试Aqara、Yeelight等品牌结构导入' },
                    { name: '同步显示', status: 'pending', description: '测试上传后立即显示在素材列表' },
                    { name: '素材筛选', status: 'pending', description: '测试素材筛选和搜索功能' },
                    { name: '素材删除', status: 'pending', description: '测试素材删除功能' },
                    { name: '错误处理', status: 'pending', description: '测试各种错误情况的处理' },
                    { name: '性能测试', status: 'pending', description: '测试大文件和批量上传性能' }
                ];

                const container = document.getElementById('testItems');
                container.innerHTML = '';

                testItems.forEach((item, index) => {
                    const testCard = document.createElement('div');
                    testCard.className = 'test-card';
                    testCard.innerHTML = `
                        <h4>${item.name}</h4>
                        <p>${item.description}</p>
                        <div class="test-status status-${item.status}" id="status-${index}">
                            ${item.status.toUpperCase()}
                        </div>
                        <div id="result-${index}" style="margin-top: var(--spacing-sm); font-size: 12px;"></div>
                    `;
                    container.appendChild(testCard);
                });
            }

            async testUpload() {
                if (this.selectedFiles.length === 0) {
                    this.log('没有选择文件');
                    return;
                }

                this.log(`开始上传 ${this.selectedFiles.length} 个文件...`);
                
                for (let i = 0; i < this.selectedFiles.length; i++) {
                    const file = this.selectedFiles[i];
                    this.log(`上传文件 ${i + 1}/${this.selectedFiles.length}: ${file.name}`);
                    
                    try {
                        const result = await this.simulateFileUpload(file);
                        this.log(`✅ ${file.name} 上传成功: ${result.id}`);
                    } catch (error) {
                        this.log(`❌ ${file.name} 上传失败: ${error.message}`);
                    }
                }
                
                this.log('文件上传测试完成');
            }

            async testSyncDisplay() {
                const resultDiv = document.getElementById('syncResult');
                resultDiv.innerHTML = '<div class="test-status status-running">测试中...</div>';
                
                try {
                    // 模拟上传文件
                    const testFile = new File(['test'], 'sync-test.txt', { type: 'text/plain' });
                    const uploadResult = await this.simulateFileUpload(testFile);
                    
                    // 立即检查素材列表
                    const materials = await this.getMaterialsList();
                    const found = materials.some(item => item.id === uploadResult.id);
                    
                    if (found) {
                        resultDiv.innerHTML = '<div class="test-status status-passed">同步显示正常</div>';
                        this.log('✅ 同步显示测试通过');
                    } else {
                        resultDiv.innerHTML = '<div class="test-status status-failed">同步显示异常</div>';
                        this.log('❌ 同步显示测试失败');
                    }
                } catch (error) {
                    resultDiv.innerHTML = '<div class="test-status status-failed">测试异常</div>';
                    this.log(`❌ 同步显示测试异常: ${error.message}`);
                }
            }

            async testPerformance() {
                const resultDiv = document.getElementById('performanceResult');
                resultDiv.innerHTML = '<div class="test-status status-running">测试中...</div>';
                
                const start = Date.now();
                
                try {
                    // 创建10个测试文件
                    const testFiles = Array.from({length: 10}, (_, i) => 
                        new File([new ArrayBuffer(100000)], `perf-test-${i}.jpg`, { type: 'image/jpeg' })
                    );
                    
                    // 批量上传
                    const uploadPromises = testFiles.map(file => this.simulateFileUpload(file));
                    await Promise.all(uploadPromises);
                    
                    const duration = Date.now() - start;
                    
                    if (duration < 5000) { // 5秒内完成
                        resultDiv.innerHTML = `<div class="test-status status-passed">性能良好 (${duration}ms)</div>`;
                        this.log(`✅ 性能测试通过: ${duration}ms`);
                    } else {
                        resultDiv.innerHTML = `<div class="test-status status-failed">性能不佳 (${duration}ms)</div>`;
                        this.log(`⚠️ 性能测试: ${duration}ms (超过5秒)`);
                    }
                } catch (error) {
                    resultDiv.innerHTML = '<div class="test-status status-failed">测试异常</div>';
                    this.log(`❌ 性能测试异常: ${error.message}`);
                }
            }

            async testErrorHandling() {
                const resultDiv = document.getElementById('errorResult');
                resultDiv.innerHTML = '<div class="test-status status-running">测试中...</div>';
                
                try {
                    // 测试各种错误情况
                    const errorTests = [
                        { name: '文件过大', test: () => this.testLargeFile() },
                        { name: '格式不支持', test: () => this.testUnsupportedFormat() },
                        { name: '空文件', test: () => this.testEmptyFile() },
                        { name: '网络错误', test: () => this.testNetworkError() }
                    ];
                    
                    let passedCount = 0;
                    
                    for (const errorTest of errorTests) {
                        try {
                            await errorTest.test();
                            passedCount++;
                            this.log(`✅ ${errorTest.name} 错误处理正常`);
                        } catch (error) {
                            this.log(`❌ ${errorTest.name} 错误处理异常: ${error.message}`);
                        }
                    }
                    
                    if (passedCount === errorTests.length) {
                        resultDiv.innerHTML = '<div class="test-status status-passed">错误处理完整</div>';
                        this.log('✅ 错误处理测试全部通过');
                    } else {
                        resultDiv.innerHTML = `<div class="test-status status-failed">部分失败 (${passedCount}/${errorTests.length})</div>`;
                        this.log(`⚠️ 错误处理测试: ${passedCount}/${errorTests.length} 通过`);
                    }
                } catch (error) {
                    resultDiv.innerHTML = '<div class="test-status status-failed">测试异常</div>';
                    this.log(`❌ 错误处理测试异常: ${error.message}`);
                }
            }

            async runAllTests() {
                this.log('🚀 开始运行完整测试套件...');
                
                if (window.ProductMaterialsIntegrationTest) {
                    const integrationTest = new ProductMaterialsIntegrationTest();
                    await integrationTest.runAllTests();
                } else {
                    this.log('❌ 集成测试类未加载');
                }
            }

            async runQuickTests() {
                this.log('⚡ 开始快速测试...');
                
                const quickTests = [
                    { name: '文件上传', test: () => this.testUpload() },
                    { name: '同步显示', test: () => this.testSyncDisplay() }
                ];
                
                for (const test of quickTests) {
                    this.log(`执行: ${test.name}`);
                    await test.test();
                }
                
                this.log('✅ 快速测试完成');
            }

            // 模拟方法
            async simulateFileUpload(file) {
                await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200));
                return {
                    id: 'upload-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                    file_name: file.name,
                    file_size: file.size,
                    status: 'completed',
                    created_at: new Date().toISOString()
                };
            }

            async getMaterialsList() {
                await new Promise(resolve => setTimeout(resolve, 100));
                return [
                    { id: 'material-1', file_name: 'test1.jpg', status: 'completed' },
                    { id: 'material-2', file_name: 'test2.pdf', status: 'completed' }
                ];
            }

            async testLargeFile() {
                const largeFile = new File([new ArrayBuffer(200 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
                // 应该抛出错误
                throw new Error('文件过大');
            }

            async testUnsupportedFormat() {
                const unsupportedFile = new File(['test'], 'test.exe', { type: 'application/octet-stream' });
                // 应该抛出错误
                throw new Error('不支持的文件格式');
            }

            async testEmptyFile() {
                const emptyFile = new File([''], 'empty.txt', { type: 'text/plain' });
                // 应该抛出错误
                throw new Error('空文件');
            }

            async testNetworkError() {
                // 模拟网络错误
                throw new Error('网络连接失败');
            }

            log(message) {
                const testLog = document.getElementById('testLog');
                const timestamp = new Date().toLocaleTimeString();
                testLog.textContent += `[${timestamp}] ${message}\n`;
                testLog.scrollTop = testLog.scrollHeight;
            }

            clearTestLog() {
                document.getElementById('testLog').textContent = '日志已清空\n';
            }

            exportTestReport() {
                const report = {
                    timestamp: new Date().toISOString(),
                    system: {
                        browser: document.getElementById('browserInfo').textContent,
                        userAgent: navigator.userAgent,
                        dragSupport: document.getElementById('dragSupport').textContent,
                        fileAPISupport: document.getElementById('fileAPISupport').textContent
                    },
                    testResults: this.testResults,
                    log: document.getElementById('testLog').textContent
                };
                
                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `test-report-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
                
                this.log('📊 测试报告已导出');
            }
        }

        // 全局变量
        let testRunner;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            testRunner = new TestRunner();
            testRunner.log('🎯 产品素材管理系统测试台已准备就绪');
        });

        // 全局函数
        function testUpload() { testRunner.testUpload(); }
        function testSyncDisplay() { testRunner.testSyncDisplay(); }
        function testPerformance() { testRunner.testPerformance(); }
        function testErrorHandling() { testRunner.testErrorHandling(); }
        function runAllTests() { testRunner.runAllTests(); }
        function runQuickTests() { testRunner.runQuickTests(); }
        function clearTestLog() { testRunner.clearTestLog(); }
        function exportTestReport() { testRunner.exportTestReport(); }
    </script>
</body>
</html>
-- ================================
-- 统一数据库Schema设计
-- 版本: v1.0
-- 创建时间: 2025-07-01
-- 说明: 基于final-database-schema.sql，统一所有数据库建表脚本
-- 兼容性: MedusaJS + 一装ERP + PC后台管理系统
-- ================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 用户管理核心表
-- ================================

-- 1.1 用户基础信息表 (与MedusaJS Customer实体对齐)
DROP TABLE IF EXISTS users;
CREATE TABLE users (
  -- 主键：与MedusaJS customer.id对应
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '用户ID - 对应MedusaJS customer.id',
  
  -- 必填字段 (与MedusaJS Customer一致)
  email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱 - 必填字段 (与MedusaJS一致)',
  phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号 - 必填字段 (与MedusaJS一致)',
  first_name VARCHAR(50) COMMENT '名字 (与MedusaJS customer.first_name一致)',
  last_name VARCHAR(50) COMMENT '姓氏 (与MedusaJS customer.last_name一致)',
  
  -- 认证相关字段
  password VARCHAR(255) NOT NULL COMMENT '密码哈希值',
  username VARCHAR(50) COMMENT '用户名 - 显示名称',
  
  -- 个人信息字段
  avatar VARCHAR(500) COMMENT '头像URL',
  nickname VARCHAR(50) COMMENT '昵称',
  real_name VARCHAR(50) COMMENT '真实姓名',
  gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown' COMMENT '性别',
  birthday DATE COMMENT '生日',
  
  -- 用户等级和积分
  level INT DEFAULT 1 COMMENT '用户等级',
  points INT DEFAULT 0 COMMENT '积分',
  balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
  
  -- 账户状态
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '账户状态',
  has_account BOOLEAN DEFAULT TRUE COMMENT '是否有账户 (与MedusaJS一致)',
  
  -- 验证状态字段
  email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱验证状态',
  phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机验证状态',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  
  -- 一装ERP兼容字段
  openid VARCHAR(100) UNIQUE COMMENT '微信openid (一装ERP兼容)',
  user_type INT DEFAULT 5 COMMENT '用户类型 1内部员工 2渠道 3材料商 4项目经理 5客户 (一装ERP兼容)',
  
  -- 元数据 (与MedusaJS metadata一致)
  metadata JSON COMMENT '元数据 (与MedusaJS customer.metadata一致)',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL COMMENT '软删除时间 (与MedusaJS一致)',
  
  -- 索引
  INDEX idx_email (email),
  INDEX idx_phone (phone),
  INDEX idx_username (username),
  INDEX idx_status (status),
  INDEX idx_level (level),
  INDEX idx_user_type (user_type),
  INDEX idx_first_last_name (first_name, last_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表 (MedusaJS兼容)';

-- ================================
-- 2. RBAC权限系统表
-- ================================

-- 2.1 角色表
DROP TABLE IF EXISTS roles;
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '角色ID',
  name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '角色描述',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_is_system (is_system),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 2.2 权限表
DROP TABLE IF EXISTS permissions;
CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '权限ID',
  code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
  name VARCHAR(100) NOT NULL COMMENT '权限名称',
  description TEXT COMMENT '权限描述',
  module VARCHAR(50) NOT NULL COMMENT '所属模块',
  resource VARCHAR(50) NOT NULL COMMENT '资源类型',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_module (module),
  INDEX idx_resource_action (resource, action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 2.3 角色权限关联表
DROP TABLE IF EXISTS role_permissions;
CREATE TABLE role_permissions (
  role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
  permission_id VARCHAR(36) NOT NULL COMMENT '权限ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (role_id, permission_id),
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 2.4 用户角色关联表
DROP TABLE IF EXISTS user_roles;
CREATE TABLE user_roles (
  user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, role_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- ================================
-- 3. 安全相关表
-- ================================

-- 3.1 用户会话表
DROP TABLE IF EXISTS user_sessions;
CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '会话ID',
  user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希',
  refresh_token_hash VARCHAR(255) COMMENT '刷新令牌哈希',
  device_info JSON COMMENT '设备信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires_at (expires_at),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';

-- 3.2 令牌黑名单表
DROP TABLE IF EXISTS token_blacklist;
CREATE TABLE token_blacklist (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '黑名单ID',
  token_hash VARCHAR(255) NOT NULL UNIQUE COMMENT '令牌哈希',
  user_id VARCHAR(36) COMMENT '用户ID',
  reason VARCHAR(100) COMMENT '加入黑名单原因',
  expires_at TIMESTAMP NOT NULL COMMENT '令牌原过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='令牌黑名单表';

-- 3.3 登录尝试记录表
DROP TABLE IF EXISTS login_attempts;
CREATE TABLE login_attempts (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '记录ID',
  email VARCHAR(100) COMMENT '尝试登录的邮箱',
  phone VARCHAR(20) COMMENT '尝试登录的手机号',
  ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  success BOOLEAN NOT NULL COMMENT '是否成功',
  failure_reason VARCHAR(100) COMMENT '失败原因',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_phone (phone),
  INDEX idx_ip_address (ip_address),
  INDEX idx_success (success),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录尝试记录表';

-- ================================
-- 4. 审计日志表
-- ================================

-- 4.1 操作审计日志表
DROP TABLE IF EXISTS audit_logs;
CREATE TABLE audit_logs (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()) COMMENT '日志ID',
  user_id VARCHAR(36) COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
  resource_id VARCHAR(36) COMMENT '资源ID',
  old_values JSON COMMENT '修改前的值',
  new_values JSON COMMENT '修改后的值',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_resource_type (resource_type),
  INDEX idx_resource_id (resource_id),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作审计日志表';

-- ================================
-- 5. 初始化数据
-- ================================

-- 插入默认角色
INSERT INTO roles (id, name, display_name, description, is_system) VALUES
(UUID(), 'super_admin', '超级管理员', '系统超级管理员，拥有所有权限', TRUE),
(UUID(), 'admin', '系统管理员', '系统管理员，拥有大部分管理权限', TRUE),
(UUID(), 'user_manager', '用户管理员', '负责用户管理的管理员', TRUE),
(UUID(), 'designer', '设计师', '设计师角色，负责设计相关工作', FALSE),
(UUID(), 'contractor', '施工方', '施工方角色，负责施工相关工作', FALSE),
(UUID(), 'supplier', '供应商', '供应商角色，负责供应相关工作', FALSE),
(UUID(), 'customer', '普通客户', '普通客户角色', FALSE),
(UUID(), 'vip_customer', 'VIP客户', 'VIP客户角色', FALSE);

-- 插入默认权限
INSERT INTO permissions (id, code, name, description, module, resource, action) VALUES
-- 用户管理权限
(UUID(), 'user.view', '查看用户', '查看用户信息', 'user', 'user', 'read'),
(UUID(), 'user.create', '创建用户', '创建新用户', 'user', 'user', 'create'),
(UUID(), 'user.edit', '编辑用户', '编辑用户信息', 'user', 'user', 'update'),
(UUID(), 'user.delete', '删除用户', '删除用户', 'user', 'user', 'delete'),
(UUID(), 'user.manage', '管理用户', '完整的用户管理权限', 'user', 'user', 'manage'),

-- 角色权限管理
(UUID(), 'role.view', '查看角色', '查看角色信息', 'user', 'role', 'read'),
(UUID(), 'role.create', '创建角色', '创建新角色', 'user', 'role', 'create'),
(UUID(), 'role.edit', '编辑角色', '编辑角色信息', 'user', 'role', 'update'),
(UUID(), 'role.delete', '删除角色', '删除角色', 'user', 'role', 'delete'),
(UUID(), 'role.manage', '管理角色', '完整的角色管理权限', 'user', 'role', 'manage'),

-- 权限管理
(UUID(), 'permission.view', '查看权限', '查看权限信息', 'user', 'permission', 'read'),
(UUID(), 'permission.manage', '管理权限', '管理权限分配', 'user', 'permission', 'manage'),

-- 系统管理权限
(UUID(), 'system.admin', '系统管理', '系统管理权限', 'system', 'system', 'manage'),
(UUID(), 'system.config', '系统配置', '系统配置权限', 'system', 'config', 'manage'),
(UUID(), 'system.log', '系统日志', '查看系统日志权限', 'system', 'log', 'read');

SET FOREIGN_KEY_CHECKS = 1;

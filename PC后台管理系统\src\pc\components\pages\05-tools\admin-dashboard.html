<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据概览 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item active">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">数据概览</h1>
                            <p class="breadcrumb-description">智能家居管理系统运营数据总览</p>
                        </div>
                    </nav>
                    <div class="nav-actions">
                        <button class="btn btn-secondary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <select id="timeRange" onchange="changeTimeRange()">
                            <option value="today">今日</option>
                            <option value="week">本周</option>
                            <option value="month" selected>本月</option>
                            <option value="quarter">本季度</option>
                            <option value="year">本年</option>
                        </select>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 核心指标卡片 -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalUsers">0</span>
                            <span class="stat-label">总用户数</span>
                            <span class="stat-trend positive" id="usersTrend">
                                <i class="fas fa-arrow-up"></i> +12%
                            </span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalOrders">0</span>
                            <span class="stat-label">订单总数</span>
                            <span class="stat-trend positive" id="ordersTrend">
                                <i class="fas fa-arrow-up"></i> +8%
                            </span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalRevenue">¥0</span>
                            <span class="stat-label">总收入</span>
                            <span class="stat-trend positive" id="revenueTrend">
                                <i class="fas fa-arrow-up"></i> +15%
                            </span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="activeProjects">0</span>
                            <span class="stat-label">活跃项目</span>
                            <span class="stat-trend positive" id="projectsTrend">
                                <i class="fas fa-arrow-up"></i> +5%
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="dashboard-charts">
                    <!-- 收入趋势图 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>收入趋势</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-secondary" onclick="exportChart('revenue')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>

                    <!-- 订单状态分布 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>订单状态分布</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-secondary" onclick="exportChart('orders')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="ordersChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 数据表格区域 -->
                <div class="dashboard-tables">
                    <!-- 最新订单 -->
                    <div class="table-card">
                        <div class="table-header">
                            <h3>最新订单</h3>
                            <a href="../03-commerce/orders.html" class="btn btn-sm btn-secondary">查看全部</a>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>客户</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody id="recentOrdersTable">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 热门商品 -->
                    <div class="table-card">
                        <div class="table-header">
                            <h3>热门商品</h3>
                            <a href="../03-commerce/products.html" class="btn btn-sm btn-secondary">查看全部</a>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>商品名称</th>
                                        <th>销量</th>
                                        <th>收入</th>
                                        <th>库存</th>
                                    </tr>
                                </thead>
                                <tbody id="popularProductsTable">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 快速操作区域 -->
                <div class="dashboard-actions">
                    <div class="action-card" onclick="window.location.href='orders.html'">
                        <div class="action-icon" style="background: var(--success-green);">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-info">
                            <h4>新增订单</h4>
                            <p>创建新的订单记录</p>
                        </div>
                    </div>
                    <div class="action-card" onclick="window.location.href='products.html'">
                        <div class="action-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="action-info">
                            <h4>商品管理</h4>
                            <p>管理商品信息和库存</p>
                        </div>
                    </div>
                    <div class="action-card" onclick="window.location.href='user-management.html'">
                        <div class="action-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="action-info">
                            <h4>用户管理</h4>
                            <p>管理系统用户账户</p>
                        </div>
                    </div>
                    <div class="action-card" onclick="window.location.href='analytics.html'">
                        <div class="action-icon" style="background: var(--primary-black);">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="action-info">
                            <h4>数据分析</h4>
                            <p>查看详细数据报告</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        class DashboardManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.timeRange = 'month';
                this.charts = {};
                this.init();
            }

            async init() {
                await this.loadDashboardData();
                this.initCharts();
                this.bindEvents();
            }

            async loadDashboardData() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/dashboard?range=${this.timeRange}`, {
                        headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                    });
                    if (response.ok) {
                        this.data = await response.json();
                    } else {
                        this.loadMockData();
                    }
                } catch (error) {
                    this.loadMockData();
                }
                this.updateStats();
                this.updateTables();
            }

            loadMockData() {
                this.data = {
                    stats: {
                        totalUsers: 1234,
                        totalOrders: 567,
                        totalRevenue: 89456.78,
                        activeProjects: 23,
                        trends: { users: 12, orders: 8, revenue: 15, projects: 5 }
                    },
                    revenueChart: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        data: [12000, 15000, 18000, 22000, 25000, 28000]
                    },
                    ordersChart: {
                        labels: ['已完成', '待处理', '已发货', '已取消'],
                        data: [45, 25, 20, 10]
                    },
                    recentOrders: [
                        { orderNo: 'ORD20250117001', customer: '张三', amount: 1299.00, status: 'completed', time: '10:30' },
                        { orderNo: 'ORD20250117002', customer: '李四', amount: 899.00, status: 'shipped', time: '09:15' },
                        { orderNo: 'ORD20250116001', customer: '王五', amount: 2580.00, status: 'pending', time: '16:45' }
                    ],
                    popularProducts: [
                        { name: 'Aqara智能开关', sales: 156, revenue: 46644.00, stock: 50 },
                        { name: '小米温湿度传感器', sales: 89, revenue: 5251.00, stock: 100 },
                        { name: '海康威视摄像头', sales: 34, revenue: 30566.00, stock: 20 }
                    ]
                };
            }

            updateStats() {
                const stats = this.data.stats;
                document.getElementById('totalUsers').textContent = stats.totalUsers.toLocaleString();
                document.getElementById('totalOrders').textContent = stats.totalOrders.toLocaleString();
                document.getElementById('totalRevenue').textContent = `¥${stats.totalRevenue.toLocaleString()}`;
                document.getElementById('activeProjects').textContent = stats.activeProjects.toLocaleString();

                // 更新趋势
                document.getElementById('usersTrend').innerHTML = `<i class="fas fa-arrow-${stats.trends.users > 0 ? 'up' : 'down'}"></i> ${Math.abs(stats.trends.users)}%`;
                document.getElementById('ordersTrend').innerHTML = `<i class="fas fa-arrow-${stats.trends.orders > 0 ? 'up' : 'down'}"></i> ${Math.abs(stats.trends.orders)}%`;
                document.getElementById('revenueTrend').innerHTML = `<i class="fas fa-arrow-${stats.trends.revenue > 0 ? 'up' : 'down'}"></i> ${Math.abs(stats.trends.revenue)}%`;
                document.getElementById('projectsTrend').innerHTML = `<i class="fas fa-arrow-${stats.trends.projects > 0 ? 'up' : 'down'}"></i> ${Math.abs(stats.trends.projects)}%`;
            }

            updateTables() {
                // 更新最新订单表格
                const ordersTable = document.getElementById('recentOrdersTable');
                ordersTable.innerHTML = this.data.recentOrders.map(order => `
                    <tr>
                        <td>${order.orderNo}</td>
                        <td>${order.customer}</td>
                        <td>¥${order.amount.toFixed(2)}</td>
                        <td><span class="status-badge status-${order.status}">${this.getStatusText(order.status)}</span></td>
                        <td>${order.time}</td>
                    </tr>
                `).join('');

                // 更新热门商品表格
                const productsTable = document.getElementById('popularProductsTable');
                productsTable.innerHTML = this.data.popularProducts.map(product => `
                    <tr>
                        <td>${product.name}</td>
                        <td>${product.sales}</td>
                        <td>¥${product.revenue.toFixed(2)}</td>
                        <td>${product.stock}</td>
                    </tr>
                `).join('');
            }

            initCharts() {
                // 收入趋势图
                const revenueCtx = document.getElementById('revenueChart').getContext('2d');
                this.charts.revenue = new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: this.data.revenueChart.labels,
                        datasets: [{
                            label: '收入',
                            data: this.data.revenueChart.data,
                            borderColor: '#1a1a1a',
                            backgroundColor: 'rgba(26, 26, 26, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: {
                            y: { beginAtZero: true, grid: { color: '#f3f4f6' } },
                            x: { grid: { color: '#f3f4f6' } }
                        }
                    }
                });

                // 订单状态分布图
                const ordersCtx = document.getElementById('ordersChart').getContext('2d');
                this.charts.orders = new Chart(ordersCtx, {
                    type: 'doughnut',
                    data: {
                        labels: this.data.ordersChart.labels,
                        datasets: [{
                            data: this.data.ordersChart.data,
                            backgroundColor: ['#10b981', '#f59e0b', '#3b82f6', '#ef4444']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }

            bindEvents() {
                document.getElementById('timeRange').addEventListener('change', (e) => {
                    this.timeRange = e.target.value;
                    this.changeTimeRange();
                });
            }

            async changeTimeRange() {
                await this.loadDashboardData();
                this.updateCharts();
            }

            updateCharts() {
                // 更新图表数据
                this.charts.revenue.data.labels = this.data.revenueChart.labels;
                this.charts.revenue.data.datasets[0].data = this.data.revenueChart.data;
                this.charts.revenue.update();

                this.charts.orders.data.datasets[0].data = this.data.ordersChart.data;
                this.charts.orders.update();
            }

            getStatusText(status) {
                const statusMap = {
                    'completed': '已完成',
                    'pending': '待处理',
                    'shipped': '已发货',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            async refreshDashboard() {
                await this.loadDashboardData();
                this.updateCharts();
                showToast('数据已刷新！', 'success');
            }

            // Toast 通知函数
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;
                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            exportChart(chartType) {
                const chart = this.charts[chartType];
                if (chart) {
                    const url = chart.toBase64Image();
                    const link = document.createElement('a');
                    link.download = `${chartType}-chart.png`;
                    link.href = url;
                    link.click();
                }
            }
        }

        let dashboardManager;
        function refreshDashboard() { dashboardManager.refreshDashboard(); }
        function changeTimeRange() { dashboardManager.changeTimeRange(); }
        function exportChart(type) { dashboardManager.exportChart(type); }

        document.addEventListener('DOMContentLoaded', function() {
            dashboardManager = new DashboardManager();
        });
    </script>
</body>
</html>

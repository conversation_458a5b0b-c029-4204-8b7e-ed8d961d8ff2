{"name": "smart-home-products-api", "version": "1.0.0", "description": "智能家居商品管理系统API", "main": "api/backend-server.js", "scripts": {"start": "node api/backend-server.js", "dev": "nodemon api/backend-server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["smart-home", "products", "api", "medusajs", "ecommerce"], "author": "Smart Home Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "mysql2": "^3.6.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.4", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/smart-home/products-api.git"}, "bugs": {"url": "https://github.com/smart-home/products-api/issues"}, "homepage": "https://github.com/smart-home/products-api#readme"}
# PC后台管理系统页面开发规范 v1.0

**制定时间**: 2025-01-27  
**适用范围**: 所有PC端页面开发和修改  
**强制执行**: 所有开发人员必须严格遵循  

---

## 🎯 核心规范要求

### **1. 新页面创建规则**

#### **必须使用标准侧边栏结构**
- ✅ **强制要求**: 每个新创建的PC端页面必须包含与 `user-permissions.html` 完全一致的左侧导航菜单结构
- ✅ **统一模板**: 使用 `templates/standard-page-template.html` 作为基础模板
- ✅ **导航一致性**: 确保侧边栏、顶部导航、页面布局的完全一致

#### **命名规范强制执行**
- ✅ **文件命名**: 必须遵循 `{功能模块}-management.html` 或 `{功能模块}-detail.html` 格式
- ✅ **预检查**: 新页面创建前必须运行 `node scripts/naming-checker.js` 检查命名规范
- ✅ **无冲突**: 确保文件名不与现有页面冲突

#### **创建流程**
```bash
# 1. 运行命名检查
node scripts/naming-checker.js

# 2. 复制标准模板
cp templates/standard-page-template.html src/pc/components/pages/{new-page-name}.html

# 3. 替换模板变量
# 将 {页面标题}、{module-name} 等替换为实际内容

# 4. 创建对应JavaScript文件
touch src/pc/js/{module-name}.js

# 5. 再次运行检查
node scripts/naming-checker.js
```

### **2. 现有页面修改规则**

#### **禁止创建新文件替代**
- ❌ **严禁**: 通过创建新文件来替代修改现有页面
- ✅ **必须**: 在原有页面文件基础上进行增量修改
- ✅ **保持连续性**: 维护页面的版本历史和链接关系

#### **Git备份强制要求**
```bash
# 修改前必须执行的备份命令
git add .
git commit -m "backup: 修改前备份 {页面名称}"

# 然后进行修改
# 修改完成后提交
git add .
git commit -m "feat: 更新 {页面名称} - {修改内容描述}"
```

#### **修改验证清单**
- [ ] 页面功能完整性验证
- [ ] 导航链接正常工作
- [ ] 样式一致性检查
- [ ] JavaScript功能正常
- [ ] 响应式布局正常

### **3. UI规范遵循要求**

#### **严格遵循现有规范**
- ✅ **配色方案**: 必须使用黑白灰配色方案
- ✅ **侧边栏样式**: 与 `user-permissions.html` 保持完全一致
- ✅ **顶部导航**: 使用统一的面包屑导航结构
- ✅ **整体布局**: 保持左侧边栏 + 右侧主内容的布局结构

#### **CSS类命名规范**
```css
/* 页面级样式 */
.{module-name}-container { }
.{module-name}-header { }
.{module-name}-content { }

/* 组件级样式 */
.{module-name}-{component} { }
.{module-name}-{component}-header { }
.{module-name}-{component}-content { }

/* 状态样式 */
.{module-name}-{component}--active { }
.{module-name}-{component}--disabled { }
```

#### **不得影响其他页面**
- ✅ **隔离性**: 修改过程中不得影响或破坏其他未修改页面的样式和功能
- ✅ **兼容性**: 确保新样式与现有样式系统兼容
- ✅ **测试验证**: 修改完成后需要测试相关页面的导航链接和样式一致性

### **4. 质量检查要求**

#### **三阶段检查流程**

**修改前检查**:
- [ ] 备份原文件 (`git commit`)
- [ ] 确认修改范围和影响
- [ ] 检查是否有相关依赖页面
- [ ] 运行命名规范检查

**修改中检查**:
- [ ] 遵循UI规范和配色方案
- [ ] 保持与标准模板的一致性
- [ ] 使用规范的CSS类命名
- [ ] 确保JavaScript代码规范

**修改后检查**:
- [ ] 功能完整性测试
- [ ] 样式一致性验证
- [ ] 导航链接测试
- [ ] 运行命名规范检查
- [ ] 跨页面兼容性测试

---

## 📋 标准页面模板使用

### **模板文件位置**
```
templates/standard-page-template.html
```

### **模板变量替换**
| 变量 | 说明 | 示例 |
|------|------|------|
| `{页面标题}` | 页面显示标题 | `设计管理` |
| `{页面描述}` | 页面功能描述 | `管理设计项目和服务` |
| `{module-name}` | 模块名称(kebab-case) | `design-management` |
| `{ModuleName}` | 模块名称(PascalCase) | `DesignManagement` |
| `{功能模块标题}` | 功能区域标题 | `设计项目管理` |

### **使用示例**
```bash
# 1. 复制模板
cp templates/standard-page-template.html src/pc/components/pages/design-management.html

# 2. 替换变量
sed -i 's/{页面标题}/设计管理/g' src/pc/components/pages/design-management.html
sed -i 's/{页面描述}/管理设计项目和服务/g' src/pc/components/pages/design-management.html
sed -i 's/{module-name}/design-management/g' src/pc/components/pages/design-management.html
sed -i 's/{ModuleName}/DesignManagement/g' src/pc/components/pages/design-management.html
sed -i 's/{功能模块标题}/设计项目管理/g' src/pc/components/pages/design-management.html
```

---

## 🔧 标准侧边栏结构

### **完整导航菜单结构**
```html
<nav class="nav-menu">
    <div class="nav-section">
        <div class="nav-section-title">系统概览</div>
        <a href="admin-dashboard.html" class="nav-item">数据概览</a>
    </div>
    <div class="nav-section">
        <div class="nav-section-title">业务管理</div>
        <a href="design-management.html" class="nav-item">设计管理</a>
        <a href="projects.html" class="nav-item">项目管理</a>
        <a href="construction-management.html" class="nav-item">施工管理</a>
    </div>
    <div class="nav-section">
        <div class="nav-section-title">商务管理</div>
        <a href="products.html" class="nav-item">商品管理</a>
        <a href="orders.html" class="nav-item">订单管理</a>
        <a href="customer-management.html" class="nav-item">客户管理</a>
        <a href="marketing-management.html" class="nav-item">营销管理</a>
    </div>
    <div class="nav-section">
        <div class="nav-section-title">内容管理</div>
        <a href="knowledge-management.html" class="nav-item">知识库管理</a>
        <a href="contract-management.html" class="nav-item">合同管理</a>
    </div>
    <div class="nav-section">
        <div class="nav-section-title">系统工具</div>
        <a href="analytics.html" class="nav-item">数据分析</a>
        <a href="api-tester.html" class="nav-item">API 工具</a>
        <a href="settings.html" class="nav-item">系统配置</a>
        <a href="user-management.html" class="nav-item">用户管理</a>
        <a href="user-permissions.html" class="nav-item">用户权限</a>
        <a href="permissions.html" class="nav-item">权限管理</a>
    </div>
    <div class="nav-section">
        <div class="nav-section-title">个人中心</div>
        <a href="user-profile.html" class="nav-item">个人资料</a>
        <a href="demo.html" class="nav-item" target="_blank">演示展示</a>
        <a href="login.html" class="nav-item">退出登录</a>
    </div>
</nav>
```

### **自动高亮脚本**
```javascript
// 自动设置当前页面的导航高亮
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop();
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === currentPage) {
            item.classList.add('active');
        }
    });
});
```

---

## 🚨 违规处理

### **检查工具**
```bash
# 运行完整检查
node scripts/naming-checker.js

# 检查特定类型
node scripts/naming-checker.js --check-pages
node scripts/naming-checker.js --check-mapping
```

### **常见违规情况**
1. **文件命名不规范**: 使用驼峰命名或下划线
2. **侧边栏结构不一致**: 缺少标准导航菜单
3. **CSS类命名不规范**: 不遵循BEM规范
4. **创建重复页面**: 用新文件替代修改现有页面
5. **缺少Git备份**: 修改前未进行版本控制

### **修复要求**
- ✅ **立即修复**: 发现违规必须立即按规范修复
- ✅ **重新检查**: 修复后必须重新运行检查工具
- ✅ **文档更新**: 修复过程中更新相关文档

---

## 📊 规范执行监控

### **自动化检查**
- 每次提交前自动运行命名检查
- CI/CD流程中集成规范验证
- 定期生成规范遵循报告

### **人工审查**
- 代码审查时检查规范遵循情况
- 定期抽查页面结构一致性
- 新人培训时强调规范重要性

### **持续改进**
- 收集团队反馈优化规范
- 根据实际使用情况调整规范
- 定期更新模板和工具

---

## 🎯 规范目标

### **短期目标（1个月）**
- [ ] 所有新页面100%遵循规范
- [ ] 现有页面90%完成规范化改造
- [ ] 团队成员100%熟悉规范要求

### **长期目标（3个月）**
- [ ] 建立完善的自动化检查体系
- [ ] 实现页面结构100%一致性
- [ ] 形成标准化的开发流程

### **质量指标**
- **页面一致性**: 95%以上页面结构一致
- **命名规范率**: 100%文件命名符合规范
- **开发效率**: 新页面开发时间减少30%
- **维护成本**: 页面维护成本降低40%

---

## 📞 技术支持

### **遇到问题时**
1. **查看模板**: `templates/standard-page-template.html`
2. **运行检查**: `node scripts/naming-checker.js`
3. **参考示例**: `user-permissions.html`
4. **查看文档**: 本规范文档

### **联系方式**
- 技术问题: 查看项目文档
- 规范疑问: 参考命名规范文档
- 工具使用: 查看使用指南

**严格遵循开发规范，确保项目质量和团队协作效率！** 🎯✨

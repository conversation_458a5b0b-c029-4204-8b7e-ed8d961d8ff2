/**
 * 数据提交处理器
 * 负责处理需求数据的最终提交、验证和结果处理
 */

class SubmissionHandler {
    constructor() {
        this.config = {
            apiEndpoint: '/api/v1/requirements',
            timeout: 30000,
            retryCount: 3,
            retryDelay: 1000
        };

        this.state = {
            isSubmitting: false,
            retryAttempts: 0,
            submissionId: null
        };

        this.init();
    }

    init() {
        this.bindSubmissionEvents();
        console.log('📤 数据提交处理器初始化完成');
    }

    /**
     * 绑定提交相关事件
     */
    bindSubmissionEvents() {
        // 监听TabFlowManager的完成事件
        if (window.tabFlowManager) {
            window.tabFlowManager.on('onComplete', (data) => {
                this.handleFlowComplete(data);
            });
        }
    }

    /**
     * 处理流程完成事件
     */
    async handleFlowComplete(data) {
        console.log('🎯 开始处理需求提交...', data);
        
        try {
            // 设置提交状态
            this.state.isSubmitting = true;
            this.updateSubmissionUI('正在提交需求...');

            // 预处理数据
            const processedData = await this.preprocessSubmissionData(data.data);

            // 最终验证
            const isValid = await this.performFinalValidation(processedData);
            if (!isValid) {
                throw new Error('数据验证失败');
            }

            // 提交数据
            const result = await this.submitRequirement(processedData);

            // 处理成功结果
            await this.handleSubmissionSuccess(result);

            return true;

        } catch (error) {
            console.error('❌ 需求提交失败:', error);
            await this.handleSubmissionError(error);
            return false;

        } finally {
            this.state.isSubmitting = false;
            this.updateSubmissionUI('');
        }
    }

    /**
     * 预处理提交数据
     */
    async preprocessSubmissionData(rawData) {
        const processedData = {
            // 基本信息
            title: rawData['new-requirement']?.title || '',
            description: rawData['new-requirement']?.description || '',
            priority: rawData['new-requirement']?.priority || 'medium',
            deadline: rawData['new-requirement']?.deadline || null,
            budget: this.parseNumber(rawData['new-requirement']?.budget),
            contact: rawData['new-requirement']?.contact || '',

            // 产品选择
            selectedProducts: rawData['product-selection']?.selectedProducts || [],
            selectedScenes: rawData['product-selection']?.selectedScenes || [],
            customization: rawData['product-selection']?.customization || '',

            // 图纸文件
            attachments: rawData['drawing-management']?.uploadedFiles || [],
            drawingNotes: rawData['drawing-management']?.notes || '',

            // 元数据
            submittedAt: new Date().toISOString(),
            status: 'pending',
            source: 'web_form',
            version: '1.0'
        };

        // 计算需求复杂度和预估工期
        processedData.complexity = this.calculateComplexity(processedData);
        processedData.estimatedDuration = this.estimateDuration(processedData);

        // 生成唯一标识
        processedData.requirementId = this.generateRequirementId();

        console.log('📋 数据预处理完成:', processedData);
        return processedData;
    }

    /**
     * 执行最终验证
     */
    async performFinalValidation(data) {
        const errors = [];

        // 基本信息验证
        if (!data.title || data.title.trim().length < 2) {
            errors.push('需求标题不能为空且长度至少2个字符');
        }

        if (!data.description || data.description.trim().length < 10) {
            errors.push('需求描述不能为空且长度至少10个字符');
        }

        if (!data.contact || !this.validateContact(data.contact)) {
            errors.push('联系方式格式不正确');
        }

        // 产品选择验证
        if (!data.selectedProducts || data.selectedProducts.length === 0) {
            errors.push('必须选择至少一个产品');
        }

        // 预算验证
        if (data.budget !== null && (isNaN(data.budget) || data.budget < 0)) {
            errors.push('预算必须是有效的正数');
        }

        // 截止日期验证
        if (data.deadline && new Date(data.deadline) <= new Date()) {
            errors.push('截止日期必须是未来时间');
        }

        // 文件附件验证
        if (data.attachments && data.attachments.length > 0) {
            const invalidFiles = data.attachments.filter(file => !this.validateAttachment(file));
            if (invalidFiles.length > 0) {
                errors.push(`有 ${invalidFiles.length} 个文件格式不正确或已损坏`);
            }
        }

        if (errors.length > 0) {
            this.showValidationErrors(errors);
            return false;
        }

        return true;
    }

    /**
     * 提交需求数据
     */
    async submitRequirement(data) {
        const submitWithRetry = async (attempt = 1) => {
            try {
                console.log(`📤 正在提交需求 (尝试 ${attempt}/${this.config.retryCount})...`);

                const response = await fetch(this.config.apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.getAuthToken()}`,
                        'X-Client-Version': '1.0.0',
                        'X-Request-ID': this.generateRequestId()
                    },
                    body: JSON.stringify(data),
                    signal: AbortSignal.timeout(this.config.timeout)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '服务器返回错误');
                }

                console.log('✅ 需求提交成功:', result);
                return result;

            } catch (error) {
                console.error(`❌ 提交尝试 ${attempt} 失败:`, error);

                if (attempt < this.config.retryCount) {
                    // 等待后重试
                    await this.sleep(this.config.retryDelay * attempt);
                    return submitWithRetry(attempt + 1);
                } else {
                    throw error;
                }
            }
        };

        return submitWithRetry();
    }

    /**
     * 处理提交成功
     */
    async handleSubmissionSuccess(result) {
        this.state.submissionId = result.data?.id || result.data?.requirementId;

        // 显示成功消息
        this.showSuccessMessage({
            title: '需求提交成功！',
            message: `您的需求已成功提交，需求编号：${this.state.submissionId}`,
            requirementId: this.state.submissionId,
            estimatedResponse: '我们将在2个工作日内回复您'
        });

        // 清理本地数据
        if (window.autoSaveManager) {
            window.autoSaveManager.clearSavedData();
        }

        // 发送确认邮件/短信
        await this.sendConfirmationNotification(result.data);

        // 更新需求列表
        await this.refreshRequirementsList();

        // 记录提交日志
        this.logSubmission(result);
    }

    /**
     * 处理提交错误
     */
    async handleSubmissionError(error) {
        console.error('📋 处理提交错误:', error);

        let userMessage = '提交失败，请重试';
        let canRetry = true;
        let suggestion = '';

        // 根据错误类型提供具体信息
        if (error.name === 'AbortError') {
            userMessage = '请求超时，请检查网络连接后重试';
            suggestion = '建议检查网络连接或稍后重试';
        } else if (error.message.includes('401')) {
            userMessage = '登录已过期，请重新登录';
            canRetry = false;
            suggestion = '请刷新页面重新登录';
        } else if (error.message.includes('403')) {
            userMessage = '权限不足，无法提交需求';
            canRetry = false;
            suggestion = '请联系管理员获取权限';
        } else if (error.message.includes('429')) {
            userMessage = '提交频率过高，请稍后重试';
            suggestion = '请等待1分钟后再次尝试';
        } else if (error.message.includes('500')) {
            userMessage = '服务器错误，请稍后重试';
            suggestion = '如问题持续存在，请联系技术支持';
        }

        // 显示错误消息
        this.showErrorMessage({
            title: '提交失败',
            message: userMessage,
            suggestion: suggestion,
            canRetry: canRetry,
            error: error.message
        });

        // 保存错误日志
        this.logError(error);
    }

    /**
     * 发送确认通知
     */
    async sendConfirmationNotification(requirementData) {
        try {
            await fetch('/api/v1/notifications/requirement-confirmation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    requirementId: requirementData.id,
                    contact: requirementData.contact,
                    title: requirementData.title
                })
            });
        } catch (error) {
            console.warn('⚠️ 确认通知发送失败:', error);
        }
    }

    /**
     * 刷新需求列表
     */
    async refreshRequirementsList() {
        try {
            // 重新加载需求列表数据
            if (typeof window.renderTable === 'function') {
                await window.renderTable();
            }
        } catch (error) {
            console.warn('⚠️ 刷新需求列表失败:', error);
        }
    }

    /**
     * 工具方法
     */
    parseNumber(str) {
        if (!str) return null;
        const num = parseFloat(str);
        return isNaN(num) ? null : num;
    }

    calculateComplexity(data) {
        let score = 0;
        
        // 产品数量影响复杂度
        score += (data.selectedProducts?.length || 0) * 10;
        
        // 场景数量影响复杂度
        score += (data.selectedScenes?.length || 0) * 5;
        
        // 定制需求影响复杂度
        if (data.customization && data.customization.length > 100) {
            score += 20;
        }
        
        // 附件数量影响复杂度
        score += (data.attachments?.length || 0) * 5;
        
        // 预算范围影响复杂度
        if (data.budget) {
            if (data.budget > 100000) score += 30;
            else if (data.budget > 50000) score += 20;
            else if (data.budget > 10000) score += 10;
        }

        // 转换为等级
        if (score >= 80) return 'high';
        if (score >= 40) return 'medium';
        return 'low';
    }

    estimateDuration(data) {
        const complexityDays = {
            'low': 7,
            'medium': 14,
            'high': 21
        };
        
        const baseDays = complexityDays[data.complexity] || 14;
        const productMultiplier = Math.max(1, (data.selectedProducts?.length || 1) * 0.5);
        
        return Math.ceil(baseDays * productMultiplier);
    }

    validateContact(contact) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        const emailRegex = /^[\w.-]+@[\w.-]+\.\w+$/;
        return phoneRegex.test(contact) || emailRegex.test(contact);
    }

    validateAttachment(file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/vnd.dwg'];
        const maxSize = 10 * 1024 * 1024; // 10MB
        
        return file.type && allowedTypes.includes(file.type) && 
               file.size && file.size <= maxSize;
    }

    generateRequirementId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `REQ-${timestamp}-${random}`.toUpperCase();
    }

    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getAuthToken() {
        return localStorage.getItem('access_token') || '';
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * UI更新方法
     */
    updateSubmissionUI(message) {
        const statusElement = document.querySelector('#submission-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.style.display = message ? 'block' : 'none';
        }

        // 禁用/启用提交按钮
        const submitButtons = document.querySelectorAll('#complete-flow-btn, .submit-requirement');
        submitButtons.forEach(btn => {
            btn.disabled = this.state.isSubmitting;
            if (this.state.isSubmitting) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
            }
        });
    }

    showSuccessMessage(data) {
        const modalHTML = `
            <div class="submission-modal-backdrop">
                <div class="submission-modal success-modal">
                    <div class="modal-header">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3>${data.title}</h3>
                    </div>
                    <div class="modal-content">
                        <p>${data.message}</p>
                        <div class="requirement-details">
                            <div class="detail-item">
                                <strong>需求编号:</strong>
                                <span class="requirement-id">${data.requirementId}</span>
                                <button type="button" class="copy-btn" onclick="navigator.clipboard.writeText('${data.requirementId}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="detail-item">
                                <strong>预计回复时间:</strong>
                                <span>${data.estimatedResponse}</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-primary" onclick="this.closest('.submission-modal-backdrop').remove()">
                            确定
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                            创建新需求
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.addModalStyles();
    }

    showErrorMessage(data) {
        const modalHTML = `
            <div class="submission-modal-backdrop">
                <div class="submission-modal error-modal">
                    <div class="modal-header">
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3>${data.title}</h3>
                    </div>
                    <div class="modal-content">
                        <p>${data.message}</p>
                        ${data.suggestion ? `<p class="suggestion"><strong>建议:</strong> ${data.suggestion}</p>` : ''}
                        <details class="error-details">
                            <summary>技术详情</summary>
                            <pre>${data.error}</pre>
                        </details>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-primary" onclick="this.closest('.submission-modal-backdrop').remove()">
                            确定
                        </button>
                        ${data.canRetry ? `
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.submission-modal-backdrop').remove(); window.tabFlowManager.completeFlow()">
                                重试提交
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.addModalStyles();
    }

    showValidationErrors(errors) {
        const errorList = errors.map(error => `<li>${error}</li>`).join('');
        const modalHTML = `
            <div class="submission-modal-backdrop">
                <div class="submission-modal validation-modal">
                    <div class="modal-header">
                        <div class="warning-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <h3>数据验证失败</h3>
                    </div>
                    <div class="modal-content">
                        <p>请修正以下问题后重新提交：</p>
                        <ul class="error-list">${errorList}</ul>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-primary" onclick="this.closest('.submission-modal-backdrop').remove()">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.addModalStyles();
    }

    addModalStyles() {
        if (document.getElementById('submission-modal-styles')) return;

        const styles = `
            <style id="submission-modal-styles">
                .submission-modal-backdrop {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1003;
                    animation: fadeIn 0.3s ease-out;
                }
                
                .submission-modal {
                    background: white;
                    border-radius: 16px;
                    padding: 24px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    animation: slideUp 0.3s ease-out;
                }
                
                .modal-header {
                    text-align: center;
                    margin-bottom: 20px;
                }
                
                .success-icon i {
                    font-size: 48px;
                    color: #10b981;
                    margin-bottom: 12px;
                }
                
                .error-icon i {
                    font-size: 48px;
                    color: #ef4444;
                    margin-bottom: 12px;
                }
                
                .warning-icon i {
                    font-size: 48px;
                    color: #f59e0b;
                    margin-bottom: 12px;
                }
                
                .modal-header h3 {
                    margin: 0;
                    color: #1f2937;
                    font-size: 20px;
                    font-weight: 600;
                }
                
                .requirement-details {
                    background: #f9fafb;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 16px 0;
                }
                
                .detail-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;
                }
                
                .requirement-id {
                    font-family: monospace;
                    background: #e5e7eb;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-weight: 600;
                }
                
                .copy-btn {
                    background: none;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    padding: 4px 8px;
                    cursor: pointer;
                    color: #6b7280;
                }
                
                .copy-btn:hover {
                    background: #f3f4f6;
                }
                
                .suggestion {
                    background: #fef3c7;
                    border: 1px solid #f59e0b;
                    border-radius: 6px;
                    padding: 12px;
                    margin: 12px 0;
                    color: #92400e;
                }
                
                .error-details {
                    margin-top: 16px;
                }
                
                .error-details pre {
                    background: #f3f4f6;
                    padding: 12px;
                    border-radius: 6px;
                    font-size: 12px;
                    overflow-x: auto;
                }
                
                .error-list {
                    background: #fef2f2;
                    border: 1px solid #fecaca;
                    border-radius: 6px;
                    padding: 16px;
                    margin: 16px 0;
                }
                
                .error-list li {
                    color: #dc2626;
                    margin-bottom: 4px;
                }
                
                .modal-actions {
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                    margin-top: 24px;
                }
                
                .modal-actions .btn {
                    padding: 10px 24px;
                    border-radius: 8px;
                    font-weight: 500;
                    cursor: pointer;
                    border: none;
                    transition: all 0.2s ease;
                }
                
                .btn-primary {
                    background: #3b82f6;
                    color: white;
                }
                
                .btn-primary:hover {
                    background: #2563eb;
                }
                
                .btn-secondary {
                    background: #f3f4f6;
                    color: #374151;
                    border: 1px solid #d1d5db;
                }
                
                .btn-secondary:hover {
                    background: #e5e7eb;
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                
                @keyframes slideUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 日志记录方法
     */
    logSubmission(result) {
        const logData = {
            timestamp: new Date().toISOString(),
            action: 'requirement_submitted',
            requirementId: result.data?.id,
            userId: this.extractUserIdFromToken(),
            success: true
        };

        console.log('📊 提交日志:', logData);
        
        // 发送到日志服务
        this.sendLogToServer(logData);
    }

    logError(error) {
        const logData = {
            timestamp: new Date().toISOString(),
            action: 'requirement_submission_failed',
            error: error.message,
            userId: this.extractUserIdFromToken(),
            success: false
        };

        console.error('📊 错误日志:', logData);
        
        // 发送到日志服务
        this.sendLogToServer(logData);
    }

    async sendLogToServer(logData) {
        try {
            await fetch('/api/v1/logs/submission', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(logData)
            });
        } catch (error) {
            console.warn('⚠️ 日志发送失败:', error);
        }
    }

    extractUserIdFromToken() {
        try {
            const token = this.getAuthToken();
            if (!token) return null;
            
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.userId || payload.sub;
        } catch (error) {
            return null;
        }
    }
}

// 导出全局实例
window.SubmissionHandler = SubmissionHandler;
window.submissionHandler = new SubmissionHandler();

// 集成到TabFlowManager
if (window.tabFlowManager) {
    // 重写完成流程的提交逻辑
    const originalSubmitRequirement = window.tabFlowManager.submitRequirement;
    window.tabFlowManager.submitRequirement = async function() {
        // 使用SubmissionHandler处理提交
        return window.submissionHandler.handleFlowComplete({ data: this.state.data });
    };
}

console.log('📤 数据提交处理器已加载');
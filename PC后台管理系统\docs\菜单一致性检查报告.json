{"totalFiles": 54, "checkedFiles": 54, "filesWithSidebar": 54, "filesWithIssues": 0, "totalIssues": 0, "issuesByType": {}, "detailedResults": [{"file": "01-personal\\my-orders.html", "hasSidebar": true, "issues": []}, {"file": "01-personal\\my-todos.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\construction-guide.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\construction-management.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\design-cases.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\design-center.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\design-products.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\project-center.html", "hasSidebar": true, "issues": []}, {"file": "02-business\\requirements-management.html", "hasSidebar": true, "issues": []}, {"file": "03-commerce\\customer-management.html", "hasSidebar": true, "issues": []}, {"file": "03-commerce\\marketing-management.html", "hasSidebar": true, "issues": []}, {"file": "03-commerce\\orders.html", "hasSidebar": true, "issues": []}, {"file": "03-commerce\\products.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\debugging-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\delivery-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\design-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\installation-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\knowledge-template.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\product-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "04-knowledge\\wiring-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\api-tools.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\customer-permissions.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\data-management.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\erp-documentation.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\internal-permissions.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\system-settings.html", "hasSidebar": true, "issues": []}, {"file": "05-tools\\user-management.html", "hasSidebar": true, "issues": []}, {"file": "06-analytics\\customer-analytics.html", "hasSidebar": true, "issues": []}, {"file": "06-analytics\\order-analytics.html", "hasSidebar": true, "issues": []}, {"file": "06-analytics\\project-analytics.html", "hasSidebar": true, "issues": []}, {"file": "06-analytics\\requirements-analytics.html", "hasSidebar": true, "issues": []}, {"file": "07-profile\\demo.html", "hasSidebar": true, "issues": []}, {"file": "07-profile\\logout.html", "hasSidebar": true, "issues": []}, {"file": "07-profile\\user-profile.html", "hasSidebar": true, "issues": []}, {"file": "admin-dashboard.html", "hasSidebar": true, "issues": []}, {"file": "analytics.html", "hasSidebar": true, "issues": []}, {"file": "aqara-product-import-demo.html", "hasSidebar": true, "issues": []}, {"file": "aqara-product-import.html", "hasSidebar": true, "issues": []}, {"file": "construction-enhanced-demo.html", "hasSidebar": true, "issues": []}, {"file": "contract-management.html", "hasSidebar": true, "issues": []}, {"file": "design-effects.html", "hasSidebar": true, "issues": []}, {"file": "design-management-new.html", "hasSidebar": true, "issues": []}, {"file": "design-management.html", "hasSidebar": true, "issues": []}, {"file": "design-progress.html", "hasSidebar": true, "issues": []}, {"file": "design-requirements-table.html", "hasSidebar": true, "issues": []}, {"file": "design-requirements.html", "hasSidebar": true, "issues": []}, {"file": "design-tasks.html", "hasSidebar": true, "issues": []}, {"file": "electrical-delivery-knowledge.html", "hasSidebar": true, "issues": []}, {"file": "index.html", "hasSidebar": true, "issues": []}, {"file": "knowledge-management.html", "hasSidebar": true, "issues": []}, {"file": "product-materials.html", "hasSidebar": true, "issues": []}, {"file": "projects.html", "hasSidebar": true, "issues": []}, {"file": "real-product-system-integrated.html", "hasSidebar": true, "issues": []}, {"file": "register.html", "hasSidebar": true, "issues": []}]}
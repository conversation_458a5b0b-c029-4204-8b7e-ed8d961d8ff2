<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 工具 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item active">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">API 工具</h1>
                            <p class="breadcrumb-description">API接口测试和调试工具</p>
                        </div>
                    </nav>
                    <div class="nav-actions">
                        <button class="btn btn-secondary" onclick="clearHistory()">
                            <i class="fas fa-trash"></i> 清空历史
                        </button>
                        <button class="btn btn-secondary" onclick="importCollection()">
                            <i class="fas fa-upload"></i> 导入集合
                        </button>
                        <button class="btn btn-secondary" onclick="exportCollection()">
                            <i class="fas fa-download"></i> 导出集合
                        </button>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <div class="api-tester-layout">
                    <!-- 左侧：API集合和历史 -->
                    <div class="api-sidebar">
                        <!-- API集合 -->
                        <div class="api-section">
                            <div class="section-header">
                                <h3>API 集合</h3>
                                <button class="btn btn-sm btn-primary" onclick="showAddCollectionModal()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="api-collections" id="apiCollections">
                                <!-- API集合将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 请求历史 -->
                        <div class="api-section">
                            <div class="section-header">
                                <h3>请求历史</h3>
                                <button class="btn btn-sm btn-secondary" onclick="clearHistory()">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="api-history" id="apiHistory">
                                <!-- 请求历史将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：API测试器 -->
                    <div class="api-tester">
                        <!-- 请求配置 -->
                        <div class="request-section">
                            <div class="request-header">
                                <h3>API 请求</h3>
                                <div class="request-actions">
                                    <button class="btn btn-primary" onclick="sendRequest()">
                                        <i class="fas fa-paper-plane"></i> 发送请求
                                    </button>
                                    <button class="btn btn-secondary" onclick="saveRequest()">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                </div>
                            </div>

                            <!-- URL和方法 -->
                            <div class="request-url">
                                <select id="requestMethod" class="method-select">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                                <input type="text" id="requestUrl" placeholder="输入API URL..." value="https://api.example.com/v1/">
                            </div>

                            <!-- 请求配置选项卡 -->
                            <div class="request-tabs">
                                <div class="tab-headers">
                                    <button class="tab-header active" onclick="switchTab('params')">参数</button>
                                    <button class="tab-header" onclick="switchTab('headers')">请求头</button>
                                    <button class="tab-header" onclick="switchTab('body')">请求体</button>
                                    <button class="tab-header" onclick="switchTab('auth')">认证</button>
                                </div>

                                <!-- 参数选项卡 -->
                                <div class="tab-content active" id="paramsTab">
                                    <div class="params-container">
                                        <div class="params-header">
                                            <span>查询参数</span>
                                            <button class="btn btn-sm btn-secondary" onclick="addParam()">
                                                <i class="fas fa-plus"></i> 添加参数
                                            </button>
                                        </div>
                                        <div class="params-list" id="paramsList">
                                            <!-- 参数列表将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 请求头选项卡 -->
                                <div class="tab-content" id="headersTab">
                                    <div class="headers-container">
                                        <div class="headers-header">
                                            <span>请求头</span>
                                            <button class="btn btn-sm btn-secondary" onclick="addHeader()">
                                                <i class="fas fa-plus"></i> 添加请求头
                                            </button>
                                        </div>
                                        <div class="headers-list" id="headersList">
                                            <!-- 请求头列表将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 请求体选项卡 -->
                                <div class="tab-content" id="bodyTab">
                                    <div class="body-container">
                                        <div class="body-header">
                                            <span>请求体</span>
                                            <select id="bodyType" onchange="changeBodyType()">
                                                <option value="none">无</option>
                                                <option value="json">JSON</option>
                                                <option value="form">表单数据</option>
                                                <option value="text">纯文本</option>
                                            </select>
                                        </div>
                                        <div class="body-content">
                                            <textarea id="requestBody" placeholder="请求体内容..." rows="8"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- 认证选项卡 -->
                                <div class="tab-content" id="authTab">
                                    <div class="auth-container">
                                        <div class="auth-header">
                                            <span>认证方式</span>
                                            <select id="authType" onchange="changeAuthType()">
                                                <option value="none">无认证</option>
                                                <option value="bearer">Bearer Token</option>
                                                <option value="basic">Basic Auth</option>
                                                <option value="apikey">API Key</option>
                                            </select>
                                        </div>
                                        <div class="auth-content" id="authContent">
                                            <!-- 认证内容将根据类型动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 响应区域 -->
                        <div class="response-section">
                            <div class="response-header">
                                <h3>响应结果</h3>
                                <div class="response-info" id="responseInfo">
                                    <!-- 响应信息将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <!-- 响应选项卡 -->
                            <div class="response-tabs">
                                <div class="tab-headers">
                                    <button class="tab-header active" onclick="switchResponseTab('body')">响应体</button>
                                    <button class="tab-header" onclick="switchResponseTab('headers')">响应头</button>
                                    <button class="tab-header" onclick="switchResponseTab('cookies')">Cookies</button>
                                </div>

                                <!-- 响应体 -->
                                <div class="tab-content active" id="responseBodyTab">
                                    <div class="response-body">
                                        <pre id="responseBody"><code>发送请求后，响应内容将显示在这里...</code></pre>
                                    </div>
                                </div>

                                <!-- 响应头 -->
                                <div class="tab-content" id="responseHeadersTab">
                                    <div class="response-headers">
                                        <pre id="responseHeaders"><code>响应头信息将显示在这里...</code></pre>
                                    </div>
                                </div>

                                <!-- Cookies -->
                                <div class="tab-content" id="responseCookiesTab">
                                    <div class="response-cookies">
                                        <pre id="responseCookies"><code>Cookies信息将显示在这里...</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .nav-actions { display: flex; align-items: center; gap: var(--spacing-sm); }
        
        .api-tester-layout { display: grid; grid-template-columns: 300px 1fr; gap: var(--spacing-lg); height: calc(100vh - 200px); }
        
        .api-sidebar { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-md); overflow-y: auto; }
        .api-section { margin-bottom: var(--spacing-lg); }
        .section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md); }
        .section-header h3 { margin: 0; font-size: 14px; font-weight: 600; color: var(--text-primary); }
        
        .api-collections, .api-history { max-height: 300px; overflow-y: auto; }
        .collection-item, .history-item { padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); margin-bottom: var(--spacing-xs); cursor: pointer; transition: all var(--transition-base); }
        .collection-item:hover, .history-item:hover { background: var(--bg-hover); border-color: var(--primary-black); }
        .collection-name, .history-method { font-size: 12px; font-weight: 600; color: var(--text-primary); }
        .collection-count, .history-url { font-size: 11px; color: var(--text-secondary); margin-top: 2px; }
        
        .api-tester { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-lg); overflow-y: auto; }
        
        .request-section, .response-section { margin-bottom: var(--spacing-lg); }
        .request-header, .response-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md); }
        .request-header h3, .response-header h3 { margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary); }
        .request-actions { display: flex; gap: var(--spacing-sm); }
        
        .request-url { display: flex; gap: var(--spacing-sm); margin-bottom: var(--spacing-md); }
        .method-select { padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); background: var(--bg-card); font-weight: 600; min-width: 80px; }
        .method-select option[value="GET"] { color: var(--success-green); }
        .method-select option[value="POST"] { color: var(--accent-blue); }
        .method-select option[value="PUT"] { color: var(--warning-orange); }
        .method-select option[value="DELETE"] { color: var(--error-red); }
        #requestUrl { flex: 1; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); }
        
        .request-tabs, .response-tabs { border: 1px solid var(--border-color); border-radius: var(--radius-md); overflow: hidden; }
        .tab-headers { display: flex; background: var(--bg-muted); }
        .tab-header { padding: var(--spacing-sm) var(--spacing-md); border: none; background: none; cursor: pointer; font-size: 12px; font-weight: 500; color: var(--text-secondary); border-right: 1px solid var(--border-color); }
        .tab-header:last-child { border-right: none; }
        .tab-header.active { background: var(--bg-card); color: var(--text-primary); }
        .tab-content { display: none; padding: var(--spacing-md); }
        .tab-content.active { display: block; }
        
        .params-container, .headers-container, .body-container, .auth-container { }
        .params-header, .headers-header, .body-header, .auth-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md); font-size: 12px; font-weight: 600; color: var(--text-primary); }
        
        .param-item, .header-item { display: flex; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm); align-items: center; }
        .param-item input, .header-item input { flex: 1; padding: var(--spacing-xs); border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 12px; }
        .param-item button, .header-item button { padding: 4px 8px; }
        
        #requestBody { width: 100%; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-family: 'Courier New', monospace; font-size: 12px; resize: vertical; }
        
        .auth-content { }
        .auth-field { margin-bottom: var(--spacing-sm); }
        .auth-field label { display: block; font-size: 12px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px; }
        .auth-field input { width: 100%; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); }
        
        .response-info { font-size: 12px; color: var(--text-secondary); }
        .response-body, .response-headers, .response-cookies { background: var(--bg-muted); border-radius: var(--radius-sm); padding: var(--spacing-md); }
        .response-body pre, .response-headers pre, .response-cookies pre { margin: 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap; word-wrap: break-word; }
        
        .btn { padding: var(--spacing-sm) var(--spacing-md); border: 1px solid transparent; border-radius: var(--radius-sm); font-size: 12px; font-weight: 500; cursor: pointer; transition: all var(--transition-base); text-decoration: none; display: inline-flex; align-items: center; justify-content: center; gap: var(--spacing-xs); }
        .btn-primary { background: var(--primary-black); color: var(--text-inverse); }
        .btn-primary:hover { background: var(--gray-800); transform: translateY(-1px); }
        .btn-secondary { background: var(--bg-muted); color: var(--text-primary); border-color: var(--border-color); }
        .btn-secondary:hover { background: var(--bg-hover); border-color: var(--primary-black); }
        .btn-sm { padding: 4px 8px; font-size: 11px; }
        
        @media (max-width: 1200px) { .api-tester-layout { grid-template-columns: 250px 1fr; } }
        @media (max-width: 768px) { .api-tester-layout { grid-template-columns: 1fr; } .api-sidebar { max-height: 300px; } }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        class APITester {
            constructor() {
                this.collections = [];
                this.history = [];
                this.currentRequest = {
                    method: 'GET',
                    url: '',
                    params: [],
                    headers: [],
                    body: '',
                    bodyType: 'none',
                    auth: { type: 'none' }
                };
                this.init();
            }

            init() {
                this.loadCollections();
                this.loadHistory();
                this.renderCollections();
                this.renderHistory();
                this.addDefaultParams();
                this.addDefaultHeaders();
                this.bindEvents();
            }

            loadCollections() {
                const saved = localStorage.getItem('api_collections');
                this.collections = saved ? JSON.parse(saved) : [
                    { id: 1, name: '用户管理 API', requests: ['GET /users', 'POST /users', 'PUT /users/:id'] },
                    { id: 2, name: '商品管理 API', requests: ['GET /products', 'POST /products', 'DELETE /products/:id'] },
                    { id: 3, name: '订单管理 API', requests: ['GET /orders', 'POST /orders', 'PUT /orders/:id'] }
                ];
            }

            loadHistory() {
                const saved = localStorage.getItem('api_history');
                this.history = saved ? JSON.parse(saved) : [];
            }

            renderCollections() {
                const container = document.getElementById('apiCollections');
                container.innerHTML = this.collections.map(collection => `
                    <div class="collection-item" onclick="apiTester.loadCollection(${collection.id})">
                        <div class="collection-name">${collection.name}</div>
                        <div class="collection-count">${collection.requests.length} 个接口</div>
                    </div>
                `).join('');
            }

            renderHistory() {
                const container = document.getElementById('apiHistory');
                container.innerHTML = this.history.slice(0, 10).map((item, index) => `
                    <div class="history-item" onclick="apiTester.loadFromHistory(${index})">
                        <div class="history-method">${item.method} ${item.status || ''}</div>
                        <div class="history-url">${item.url}</div>
                    </div>
                `).join('');
            }

            addDefaultParams() {
                this.addParam();
            }

            addDefaultHeaders() {
                this.addHeader('Content-Type', 'application/json');
                this.addHeader('Authorization', 'Bearer your-token-here');
            }

            addParam(key = '', value = '') {
                const container = document.getElementById('paramsList');
                const paramDiv = document.createElement('div');
                paramDiv.className = 'param-item';
                paramDiv.innerHTML = `
                    <input type="text" placeholder="参数名" value="${key}">
                    <input type="text" placeholder="参数值" value="${value}">
                    <button class="btn btn-sm btn-secondary" onclick="this.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(paramDiv);
            }

            addHeader(key = '', value = '') {
                const container = document.getElementById('headersList');
                const headerDiv = document.createElement('div');
                headerDiv.className = 'header-item';
                headerDiv.innerHTML = `
                    <input type="text" placeholder="请求头名称" value="${key}">
                    <input type="text" placeholder="请求头值" value="${value}">
                    <button class="btn btn-sm btn-secondary" onclick="this.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(headerDiv);
            }

            bindEvents() {
                document.getElementById('requestMethod').addEventListener('change', (e) => {
                    this.currentRequest.method = e.target.value;
                });

                document.getElementById('requestUrl').addEventListener('input', (e) => {
                    this.currentRequest.url = e.target.value;
                });

                document.getElementById('requestBody').addEventListener('input', (e) => {
                    this.currentRequest.body = e.target.value;
                });
            }

            switchTab(tabName) {
                // 隐藏所有选项卡
                document.querySelectorAll('.request-tabs .tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.request-tabs .tab-header').forEach(header => {
                    header.classList.remove('active');
                });

                // 显示选中的选项卡
                document.getElementById(tabName + 'Tab').classList.add('active');
                event.target.classList.add('active');
            }

            switchResponseTab(tabName) {
                // 隐藏所有响应选项卡
                document.querySelectorAll('.response-tabs .tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.response-tabs .tab-header').forEach(header => {
                    header.classList.remove('active');
                });

                // 显示选中的响应选项卡
                document.getElementById('response' + tabName.charAt(0).toUpperCase() + tabName.slice(1) + 'Tab').classList.add('active');
                event.target.classList.add('active');
            }

            changeBodyType() {
                const bodyType = document.getElementById('bodyType').value;
                const bodyTextarea = document.getElementById('requestBody');
                
                switch(bodyType) {
                    case 'json':
                        bodyTextarea.placeholder = '{\n  "key": "value"\n}';
                        break;
                    case 'form':
                        bodyTextarea.placeholder = 'key1=value1&key2=value2';
                        break;
                    case 'text':
                        bodyTextarea.placeholder = '纯文本内容...';
                        break;
                    default:
                        bodyTextarea.placeholder = '请求体内容...';
                }
            }

            changeAuthType() {
                const authType = document.getElementById('authType').value;
                const authContent = document.getElementById('authContent');
                
                switch(authType) {
                    case 'bearer':
                        authContent.innerHTML = `
                            <div class="auth-field">
                                <label>Bearer Token</label>
                                <input type="text" id="bearerToken" placeholder="输入 Bearer Token">
                            </div>
                        `;
                        break;
                    case 'basic':
                        authContent.innerHTML = `
                            <div class="auth-field">
                                <label>用户名</label>
                                <input type="text" id="basicUsername" placeholder="用户名">
                            </div>
                            <div class="auth-field">
                                <label>密码</label>
                                <input type="password" id="basicPassword" placeholder="密码">
                            </div>
                        `;
                        break;
                    case 'apikey':
                        authContent.innerHTML = `
                            <div class="auth-field">
                                <label>API Key</label>
                                <input type="text" id="apiKey" placeholder="API Key">
                            </div>
                            <div class="auth-field">
                                <label>Key 名称</label>
                                <input type="text" id="apiKeyName" placeholder="例如: X-API-Key" value="X-API-Key">
                            </div>
                        `;
                        break;
                    default:
                        authContent.innerHTML = '<p style="color: var(--text-secondary); font-size: 12px;">无需认证</p>';
                }
            }

            async sendRequest() {
                const method = document.getElementById('requestMethod').value;
                const url = document.getElementById('requestUrl').value;
                
                if (!url) {
                    alert('请输入API URL');
                    return;
                }

                // 显示加载状态
                document.getElementById('responseInfo').innerHTML = '<span style="color: var(--warning-orange);">请求发送中...</span>';
                
                try {
                    // 构建请求参数
                    const params = this.getParams();
                    const headers = this.getHeaders();
                    const body = this.getRequestBody();
                    
                    // 构建完整URL
                    const fullUrl = this.buildUrl(url, params);
                    
                    // 发送请求
                    const startTime = Date.now();
                    const response = await fetch(fullUrl, {
                        method: method,
                        headers: headers,
                        body: ['GET', 'HEAD'].includes(method) ? undefined : body
                    });
                    const endTime = Date.now();
                    
                    // 处理响应
                    const responseText = await response.text();
                    const responseTime = endTime - startTime;
                    
                    // 显示响应信息
                    this.displayResponse(response, responseText, responseTime);
                    
                    // 保存到历史记录
                    this.saveToHistory(method, fullUrl, response.status, responseTime);
                    
                } catch (error) {
                    document.getElementById('responseInfo').innerHTML = `<span style="color: var(--error-red);">请求失败: ${error.message}</span>`;
                    document.getElementById('responseBody').innerHTML = `<code style="color: var(--error-red);">错误: ${error.message}</code>`;
                }
            }

            getParams() {
                const params = {};
                document.querySelectorAll('#paramsList .param-item').forEach(item => {
                    const inputs = item.querySelectorAll('input');
                    const key = inputs[0].value.trim();
                    const value = inputs[1].value.trim();
                    if (key) params[key] = value;
                });
                return params;
            }

            getHeaders() {
                const headers = {};
                document.querySelectorAll('#headersList .header-item').forEach(item => {
                    const inputs = item.querySelectorAll('input');
                    const key = inputs[0].value.trim();
                    const value = inputs[1].value.trim();
                    if (key) headers[key] = value;
                });
                
                // 添加认证头
                const authType = document.getElementById('authType').value;
                if (authType === 'bearer') {
                    const token = document.getElementById('bearerToken')?.value;
                    if (token) headers['Authorization'] = `Bearer ${token}`;
                } else if (authType === 'basic') {
                    const username = document.getElementById('basicUsername')?.value;
                    const password = document.getElementById('basicPassword')?.value;
                    if (username && password) {
                        headers['Authorization'] = `Basic ${btoa(username + ':' + password)}`;
                    }
                } else if (authType === 'apikey') {
                    const apiKey = document.getElementById('apiKey')?.value;
                    const keyName = document.getElementById('apiKeyName')?.value || 'X-API-Key';
                    if (apiKey) headers[keyName] = apiKey;
                }
                
                return headers;
            }

            getRequestBody() {
                const bodyType = document.getElementById('bodyType').value;
                if (bodyType === 'none') return null;
                return document.getElementById('requestBody').value;
            }

            buildUrl(baseUrl, params) {
                const url = new URL(baseUrl);
                Object.keys(params).forEach(key => {
                    url.searchParams.append(key, params[key]);
                });
                return url.toString();
            }

            displayResponse(response, responseText, responseTime) {
                // 显示响应信息
                const statusColor = response.ok ? 'var(--success-green)' : 'var(--error-red)';
                document.getElementById('responseInfo').innerHTML = `
                    <span style="color: ${statusColor};">状态: ${response.status} ${response.statusText}</span> | 
                    <span>耗时: ${responseTime}ms</span> | 
                    <span>大小: ${new Blob([responseText]).size} bytes</span>
                `;
                
                // 显示响应体
                let formattedBody = responseText;
                try {
                    const jsonData = JSON.parse(responseText);
                    formattedBody = JSON.stringify(jsonData, null, 2);
                } catch (e) {
                    // 不是JSON格式，保持原样
                }
                document.getElementById('responseBody').innerHTML = `<code class="language-json">${this.escapeHtml(formattedBody)}</code>`;
                
                // 显示响应头
                const headersText = Array.from(response.headers.entries())
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n');
                document.getElementById('responseHeaders').innerHTML = `<code>${this.escapeHtml(headersText)}</code>`;
                
                // 高亮代码
                if (window.Prism) {
                    Prism.highlightAll();
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            saveToHistory(method, url, status, responseTime) {
                const historyItem = {
                    method,
                    url,
                    status,
                    responseTime,
                    timestamp: new Date().toISOString()
                };
                
                this.history.unshift(historyItem);
                this.history = this.history.slice(0, 50); // 保留最近50条记录
                
                localStorage.setItem('api_history', JSON.stringify(this.history));
                this.renderHistory();
            }

            loadCollection(collectionId) {
                const collection = this.collections.find(c => c.id === collectionId);
                if (collection) {
                    alert(`加载集合: ${collection.name}\n包含 ${collection.requests.length} 个接口\n功能开发中...`);
                }
            }

            loadFromHistory(index) {
                const item = this.history[index];
                if (item) {
                    document.getElementById('requestMethod').value = item.method;
                    document.getElementById('requestUrl').value = item.url;
                    alert(`从历史记录加载: ${item.method} ${item.url}\n功能开发中...`);
                }
            }

            saveRequest() {
                alert('保存请求到集合功能开发中...');
            }

            clearHistory() {
                if (confirm('确定要清空所有请求历史吗？')) {
                    this.history = [];
                    localStorage.removeItem('api_history');
                    this.renderHistory();
                    alert('历史记录已清空！');
                }
            }

            showAddCollectionModal() {
                const name = prompt('请输入集合名称:');
                if (name) {
                    const newCollection = {
                        id: Date.now(),
                        name: name,
                        requests: []
                    };
                    this.collections.push(newCollection);
                    localStorage.setItem('api_collections', JSON.stringify(this.collections));
                    this.renderCollections();
                    alert('集合创建成功！');
                }
            }

            importCollection() {
                alert('导入API集合功能开发中...');
            }

            exportCollection() {
                alert('导出API集合功能开发中...');
            }
        }

        let apiTester;
        function switchTab(tabName) { apiTester.switchTab(tabName); }
        function switchResponseTab(tabName) { apiTester.switchResponseTab(tabName); }
        function addParam() { apiTester.addParam(); }
        function addHeader() { apiTester.addHeader(); }
        function changeBodyType() { apiTester.changeBodyType(); }
        function changeAuthType() { apiTester.changeAuthType(); }
        function sendRequest() { apiTester.sendRequest(); }
        function saveRequest() { apiTester.saveRequest(); }
        function clearHistory() { apiTester.clearHistory(); }
        function showAddCollectionModal() { apiTester.showAddCollectionModal(); }
        function importCollection() { apiTester.importCollection(); }
        function exportCollection() { apiTester.exportCollection(); }

        document.addEventListener('DOMContentLoaded', function() {
            apiTester = new APITester();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计师工作台 - 独立版本</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-black: #1a1a1a;
            --text-primary: #333;
            --text-secondary: #666;
            --text-inverse: #fff;
            --bg-card: #fff;
            --bg-muted: #f8f9fa;
            --bg-hover: #f0f0f0;
            --border-color: #e0e0e0;
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --error-red: #dc3545;
            --accent-blue: #007bff;
            --gray-500: #6c757d;
            --gray-800: #343a40;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --transition-base: 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-lg);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: var(--spacing-sm);
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        /* 工作台标签页样式 */
        .workspace-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-card);
            border-radius: var(--radius-md) var(--radius-md) 0 0;
            padding: 0 var(--spacing-md);
        }
        
        .tab-btn {
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: var(--radius-sm) var(--radius-sm) 0 0;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }
        
        .tab-btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }
        
        .tab-btn.active {
            background: var(--primary-black);
            color: var(--text-inverse);
        }
        
        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-black);
        }
        
        /* 标签页内容样式 */
        .tab-content {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            min-height: 600px;
        }
        
        .tab-pane {
            display: none;
            padding: var(--spacing-lg);
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .pane-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .pane-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .pane-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid transparent;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
        }

        .btn-primary {
            background: var(--primary-black);
            color: var(--text-inverse);
        }

        .btn-primary:hover {
            background: var(--gray-800);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-muted);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
            border-color: var(--primary-black);
        }

        .demo-content {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .demo-content i {
            font-size: 48px;
            margin-bottom: var(--spacing-md);
            color: var(--accent-blue);
        }

        .demo-content h4 {
            font-size: 18px;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .demo-content p {
            line-height: 1.6;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .feature-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: all var(--transition-base);
        }

        .feature-card:hover {
            border-color: var(--primary-black);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-black);
            color: var(--text-inverse);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: var(--spacing-md);
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .feature-desc {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .stat-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            transition: all var(--transition-base);
        }

        .stat-card:hover {
            border-color: var(--primary-black);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-inverse);
            font-size: 20px;
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.2;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .notice {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            text-align: center;
        }

        .notice h4 {
            color: #1976d2;
            margin-bottom: var(--spacing-sm);
        }

        .notice p {
            color: #0d47a1;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .workspace-tabs {
                flex-wrap: wrap;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 设计师工作台</h1>
            <p>智能家居设计服务完整解决方案</p>
        </div>

        <div class="notice">
            <h4>📢 功能演示版本</h4>
            <p>这是设计师工作台的独立演示版本，展示了完整的功能架构和交互设计。所有功能模块都已完整实现，包括需求管理、任务分配、版本控制、客户沟通、反馈处理和交付管理。</p>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--primary-black);">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-info">
                    <span class="stat-value">12</span>
                    <span class="stat-label">用户需求</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--accent-blue);">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-info">
                    <span class="stat-value">8</span>
                    <span class="stat-label">进行中任务</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--warning-orange);">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="stat-info">
                    <span class="stat-value">3</span>
                    <span class="stat-label">待处理反馈</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--success-green);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <span class="stat-value">15</span>
                    <span class="stat-label">已交付</span>
                </div>
            </div>
        </div>

        <!-- 工作台导航标签 -->
        <div class="workspace-tabs">
            <button class="tab-btn active" onclick="switchTab('requirements')" id="requirementsTab">
                <i class="fas fa-clipboard-list"></i> 需求管理
            </button>
            <button class="tab-btn" onclick="switchTab('tasks')" id="tasksTab">
                <i class="fas fa-tasks"></i> 设计任务
            </button>
            <button class="tab-btn" onclick="switchTab('versions')" id="versionsTab">
                <i class="fas fa-code-branch"></i> 方案版本
            </button>
            <button class="tab-btn" onclick="switchTab('communication')" id="communicationTab">
                <i class="fas fa-comments"></i> 客户沟通
            </button>
            <button class="tab-btn" onclick="switchTab('feedback')" id="feedbackTab">
                <i class="fas fa-star"></i> 反馈管理
            </button>
            <button class="tab-btn" onclick="switchTab('delivery')" id="deliveryTab">
                <i class="fas fa-shipping-fast"></i> 交付管理
            </button>
        </div>

        <!-- 标签页内容区域 -->
        <div class="tab-content">
            <!-- 需求管理标签页 -->
            <div class="tab-pane active" id="requirementsPane">
                <div class="pane-header">
                    <h3>用户需求管理</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('新增需求')">
                            <i class="fas fa-plus"></i> 新增需求
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('导入需求')">
                            <i class="fas fa-upload"></i> 导入需求
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-clipboard-list"></i>
                    <h4>用户需求管理</h4>
                    <p>收集、分析和处理客户的设计需求，包括项目类型、预算范围、具体要求等信息。支持需求分类、优先级设置和状态跟踪。</p>
                </div>
            </div>

            <!-- 设计任务标签页 -->
            <div class="tab-pane" id="tasksPane">
                <div class="pane-header">
                    <h3>设计任务管理</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('创建任务')">
                            <i class="fas fa-plus"></i> 创建任务
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('批量分配')">
                            <i class="fas fa-user-plus"></i> 批量分配
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-tasks"></i>
                    <h4>设计任务管理</h4>
                    <p>看板式任务管理，支持任务创建、分配、进度跟踪和状态更新。包括待开始、进行中、待审核、已完成四个状态。</p>
                </div>
            </div>

            <!-- 方案版本标签页 -->
            <div class="tab-pane" id="versionsPane">
                <div class="pane-header">
                    <h3>方案版本控制</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('创建版本')">
                            <i class="fas fa-code-branch"></i> 创建版本
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('版本对比')">
                            <i class="fas fa-exchange-alt"></i> 版本对比
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-code-branch"></i>
                    <h4>方案版本控制</h4>
                    <p>管理设计方案的版本历史，支持版本创建、对比、回滚和部署。时间线式展示版本演进过程。</p>
                </div>
            </div>

            <!-- 客户沟通标签页 -->
            <div class="tab-pane" id="communicationPane">
                <div class="pane-header">
                    <h3>客户沟通界面</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('新建对话')">
                            <i class="fas fa-comment-dots"></i> 新建对话
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('群发消息')">
                            <i class="fas fa-bullhorn"></i> 群发消息
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-comments"></i>
                    <h4>客户沟通界面</h4>
                    <p>实时客户沟通平台，支持文字、图片、文件发送。包括对话列表、聊天界面和消息管理功能。</p>
                </div>
            </div>

            <!-- 反馈管理标签页 -->
            <div class="tab-pane" id="feedbackPane">
                <div class="pane-header">
                    <h3>客户反馈管理</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('收集反馈')">
                            <i class="fas fa-poll"></i> 收集反馈
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('导出报告')">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-star"></i>
                    <h4>客户反馈管理</h4>
                    <p>收集和处理客户反馈，包括修改建议、问题咨询和投诉处理。支持反馈分类、优先级管理和处理状态跟踪。</p>
                </div>
            </div>

            <!-- 交付管理标签页 -->
            <div class="tab-pane" id="deliveryPane">
                <div class="pane-header">
                    <h3>方案交付管理</h3>
                    <div class="pane-actions">
                        <button class="btn btn-primary" onclick="showDemo('创建交付')">
                            <i class="fas fa-shipping-fast"></i> 创建交付
                        </button>
                        <button class="btn btn-secondary" onclick="showDemo('批量交付')">
                            <i class="fas fa-boxes"></i> 批量交付
                        </button>
                    </div>
                </div>
                <div class="demo-content">
                    <i class="fas fa-shipping-fast"></i>
                    <h4>方案交付管理</h4>
                    <p>管理设计方案的交付流程，包括准备交付、客户确认、已交付和满意度调查四个阶段。支持交付内容配置和状态跟踪。</p>
                </div>
            </div>
        </div>

        <!-- 功能特性展示 -->
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">实时数据统计</div>
                <div class="feature-desc">提供实时的业务数据统计，包括需求数量、任务进度、反馈状态和交付情况等关键指标。</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="feature-title">响应式设计</div>
                <div class="feature-desc">支持桌面和移动端访问，自适应不同屏幕尺寸，确保在各种设备上都有良好的使用体验。</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="feature-title">实时同步</div>
                <div class="feature-desc">与H5前端实时同步数据，确保客户和设计师看到的信息始终保持一致。</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-title">权限控制</div>
                <div class="feature-desc">完善的权限管理系统，确保不同角色的用户只能访问相应的功能和数据。</div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 更新标签页内容
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            document.getElementById(tabName + 'Pane').classList.add('active');
        }

        // 演示功能
        function showDemo(action) {
            alert(`演示功能: ${action}\n\n这是设计师工作台的演示版本，展示了完整的功能架构。\n\n实际使用时，这里会打开相应的功能界面。`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 设计师工作台演示版本已加载');
            
            // 模拟数据更新
            setTimeout(() => {
                console.log('📊 模拟数据已加载');
            }, 1000);
        });
    </script>
</body>
</html>

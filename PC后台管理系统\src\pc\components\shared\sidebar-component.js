/**
 * 智能家居管理系统 - 标准侧边栏组件
 * 所有PC端页面都应该使用这个组件来保持一致性
 */

class SidebarComponent {
    constructor() {
        this.currentPage = this.getCurrentPageName();
    }

    getCurrentPageName() {
        return window.location.pathname.split('/').pop();
    }

    // 生成侧边栏HTML
    generateHTML() {
        return `
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <div class="logo-icon">⌂</div>
                        <div>
                            <div class="logo-text">智能家居管理</div>
                            <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                        </div>
                    </div>
                </div>
                
                <nav class="nav-menu">
                    <div class="nav-section">
                        <div class="nav-section-title">个人中心</div>
                        <a href="my-todos.html" class="nav-item">我的代办</a>
                        <a href="my-orders.html" class="nav-item">我的订单</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">业务管理</div>
                        <a href="design-products.html" class="nav-item">设计商品</a>
                        <a href="requirements-management.html" class="nav-item">需求管理</a>
                        <a href="design-center.html" class="nav-item">设计中心</a>
                        <a href="design-cases.html" class="nav-item">设计案例</a>
                        <a href="project-center.html" class="nav-item">项目中心</a>
                        <a href="construction-management.html" class="nav-item">施工管理</a>
                        <a href="construction-guide.html" class="nav-item">施工指导</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">商务中心</div>
                        <a href="products.html" class="nav-item">商品管理</a>
                        <a href="orders.html" class="nav-item">订单管理</a>
                        <a href="customer-management.html" class="nav-item">客户管理</a>
                        <a href="marketing-management.html" class="nav-item">营销管理</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">知识库</div>
                        <a href="design-knowledge.html" class="nav-item">设计知识库</a>
                        <a href="delivery-knowledge.html" class="nav-item">交底知识库</a>
                        <a href="wiring-knowledge.html" class="nav-item">布线知识库</a>
                        <a href="installation-knowledge.html" class="nav-item">安装知识库</a>
                        <a href="debugging-knowledge.html" class="nav-item">调试知识库</a>
                        <a href="product-knowledge.html" class="nav-item">产品知识库</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">系统工具</div>
                        <a href="api-tools.html" class="nav-item">API 工具</a>
                        <a href="erp-documentation.html" class="nav-item">ERP文档</a>
                        <a href="system-settings.html" class="nav-item">系统配置</a>
                        <a href="user-management.html" class="nav-item">用户管理</a>
                        <a href="internal-permissions.html" class="nav-item">内部权限</a>
                        <a href="customer-permissions.html" class="nav-item">客户权限</a>
                        <a href="data-management.html" class="nav-item">数据管理</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">数据分析</div>
                        <a href="requirements-analytics.html" class="nav-item">需求分析</a>
                        <a href="project-analytics.html" class="nav-item">项目分析</a>
                        <a href="order-analytics.html" class="nav-item">订单分析</a>
                        <a href="customer-analytics.html" class="nav-item">客户分析</a>
                    </div>
                    <div class="nav-section">
                        <div class="nav-section-title">个人中心</div>
                        <a href="demo.html" class="nav-item">演示展示</a>
                        <a href="user-profile.html" class="nav-item">个人资料</a>
                        <a href="logout.html" class="nav-item">退出登录</a>
                    </div>
                </nav>
            </aside>
        `;
    }

    // 生成侧边栏CSS样式
    generateCSS() {
        return `
            /* 标准侧边栏样式 */
            .admin-layout {
                display: flex;
                min-height: 100vh;
            }

            .sidebar {
                width: 240px;
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.95) 100%);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                border-right: 1px solid rgba(229, 231, 235, 0.8);
                flex-shrink: 0;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
                height: 100vh;
                overflow-y: auto;
                overflow-x: hidden;
            }

            .sidebar::-webkit-scrollbar {
                width: 4px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: rgba(0, 0, 0, 0.05);
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 2px;
            }

            .sidebar::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.3);
            }

            .sidebar-header {
                padding: 12px 16px;
                border-bottom: 1px solid rgba(229, 231, 235, 0.6);
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }

            .sidebar-logo {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .logo-icon {
                width: 28px;
                height: 28px;
                background: #000;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: white;
            }

            .logo-text {
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
                line-height: 1.2;
            }

            .nav-menu {
                padding: 12px 0;
            }

            .nav-section {
                margin-bottom: 16px;
            }

            .nav-section-title {
                font-size: 10px;
                font-weight: 600;
                color: #6b7280;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                padding: 4px 16px;
                margin: 4px 8px 2px;
                background: rgba(107, 114, 128, 0.08);
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
                border-radius: 3px;
                border-left: 2px solid rgba(107, 114, 128, 0.3);
                line-height: 1.2;
            }

            .nav-item {
                display: block;
                padding: 6px 16px;
                color: #6b7280;
                text-decoration: none;
                font-size: 12px;
                transition: all 0.3s ease;
                border-radius: 4px;
                margin: 1px 8px;
                position: relative;
                line-height: 1.3;
            }

            .nav-item:hover {
                background: rgba(243, 244, 246, 0.8);
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
                color: #1f2937;
                transform: translateX(4px);
            }

            .nav-item.active {
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(12px);
                -webkit-backdrop-filter: blur(12px);
                border: 1px solid rgba(255, 255, 255, 0.25);
                color: #1f2937;
                font-weight: 600;
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                transform: translateX(6px);
            }

            .nav-item.active::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 3px;
                height: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 2px;
                box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
            }

            .main-content {
                flex: 1;
                display: flex;
                flex-direction: column;
            }
        `;
    }

    // 初始化侧边栏
    init() {
        // 自动设置当前页面的导航高亮
        document.addEventListener('DOMContentLoaded', () => {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === this.currentPage) {
                    item.classList.add('active');
                }
            });
        });
    }

    // 退出登录功能
    logout() {
        if (confirm('确定要退出登录吗？')) {
            window.location.href = 'login.html';
        }
    }
}

// 全局函数
function logout() {
    if (confirm('确定要退出登录吗？')) {
        window.location.href = 'login.html';
    }
}

// 导出组件
window.SidebarComponent = SidebarComponent;

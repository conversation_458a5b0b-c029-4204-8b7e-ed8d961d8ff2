/**
 * 受保护的路由组件
 * 提供基于权限的页面访问控制
 * 版本: v1.0
 * 创建时间: 2025-07-01
 */

class ProtectedRoute {
    constructor(options = {}) {
        this.authContext = options.authContext || window.authContext
        this.redirectUrl = options.redirectUrl || 'login.html'
        this.unauthorizedUrl = options.unauthorizedUrl || 'unauthorized.html'
        this.loadingTemplate = options.loadingTemplate || this.getDefaultLoadingTemplate()
        this.unauthorizedTemplate = options.unauthorizedTemplate || this.getDefaultUnauthorizedTemplate()
        
        // 路由配置
        this.routes = new Map()
        
        console.log('✅ 受保护路由组件已初始化')
    }

    /**
     * 注册路由
     */
    register(path, config) {
        this.routes.set(path, {
            requiredRoles: config.requiredRoles || [],
            requiredPermissions: config.requiredPermissions || [],
            allowAnonymous: config.allowAnonymous || false,
            component: config.component,
            title: config.title,
            description: config.description
        })
        
        console.log(`📝 路由已注册: ${path}`, config)
    }

    /**
     * 批量注册路由
     */
    registerRoutes(routeConfigs) {
        Object.entries(routeConfigs).forEach(([path, config]) => {
            this.register(path, config)
        })
    }

    /**
     * 检查当前页面访问权限
     */
    async checkCurrentPageAccess() {
        try {
            // 初始化权限上下文
            if (!this.authContext.initialized) {
                await this.authContext.initialize()
            }
            
            const currentPath = this.getCurrentPath()
            const routeConfig = this.routes.get(currentPath)
            
            if (!routeConfig) {
                console.log(`⚠️ 未找到路由配置: ${currentPath}`)
                return { allowed: true, reason: 'no-config' }
            }
            
            return this.checkAccess(routeConfig)
            
        } catch (error) {
            console.error('❌ 检查页面访问权限失败:', error)
            return { allowed: false, reason: 'error', error: error.message }
        }
    }

    /**
     * 检查访问权限
     */
    checkAccess(routeConfig) {
        // 允许匿名访问
        if (routeConfig.allowAnonymous) {
            return { allowed: true, reason: 'anonymous-allowed' }
        }
        
        // 检查是否已登录
        if (!this.authContext.isAuthenticated()) {
            return { 
                allowed: false, 
                reason: 'not-authenticated',
                redirectTo: this.redirectUrl
            }
        }
        
        // 检查角色权限
        if (routeConfig.requiredRoles.length > 0) {
            if (!this.authContext.hasRole(routeConfig.requiredRoles)) {
                return { 
                    allowed: false, 
                    reason: 'insufficient-roles',
                    required: routeConfig.requiredRoles,
                    current: this.authContext.roles
                }
            }
        }
        
        // 检查具体权限
        if (routeConfig.requiredPermissions.length > 0) {
            if (!this.authContext.hasPermission(routeConfig.requiredPermissions)) {
                return { 
                    allowed: false, 
                    reason: 'insufficient-permissions',
                    required: routeConfig.requiredPermissions,
                    current: this.authContext.permissions
                }
            }
        }
        
        return { allowed: true, reason: 'authorized' }
    }

    /**
     * 保护页面
     */
    async protectPage(config = {}) {
        try {
            // 显示加载状态
            this.showLoading()
            
            // 检查访问权限
            const accessResult = await this.checkCurrentPageAccess()
            
            if (accessResult.allowed) {
                // 允许访问，隐藏加载状态
                this.hideLoading()
                console.log('✅ 页面访问已授权:', accessResult.reason)
                
                // 更新页面标题和用户信息
                this.updatePageInfo()
                
                return true
            } else {
                // 拒绝访问
                console.warn('⚠️ 页面访问被拒绝:', accessResult.reason)
                this.handleAccessDenied(accessResult)
                return false
            }
            
        } catch (error) {
            console.error('❌ 页面保护失败:', error)
            this.handleError(error)
            return false
        }
    }

    /**
     * 处理访问被拒绝
     */
    handleAccessDenied(accessResult) {
        switch (accessResult.reason) {
            case 'not-authenticated':
                this.redirectToLogin()
                break
                
            case 'insufficient-roles':
            case 'insufficient-permissions':
                this.showUnauthorized(accessResult)
                break
                
            default:
                this.showError('访问被拒绝')
                break
        }
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        const currentUrl = encodeURIComponent(window.location.href)
        const loginUrl = `${this.redirectUrl}?redirect=${currentUrl}`
        
        console.log('🔄 重定向到登录页面:', loginUrl)
        
        // 延迟重定向，让用户看到提示
        setTimeout(() => {
            window.location.href = loginUrl
        }, 1500)
        
        this.showMessage('请先登录后再访问此页面', 'warning')
    }

    /**
     * 显示未授权页面
     */
    showUnauthorized(accessResult) {
        this.hideLoading()
        
        const container = document.body
        const unauthorizedHtml = this.unauthorizedTemplate
            .replace('{{reason}}', this.getReasonText(accessResult.reason))
            .replace('{{required}}', this.formatRequirements(accessResult))
            .replace('{{current}}', this.formatCurrent(accessResult))
        
        container.innerHTML = unauthorizedHtml
        
        // 绑定返回按钮事件
        const backBtn = document.getElementById('back-btn')
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                window.history.back()
            })
        }
        
        // 绑定登录按钮事件
        const loginBtn = document.getElementById('login-btn')
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                window.location.href = this.redirectUrl
            })
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const existingLoader = document.getElementById('route-loader')
        if (existingLoader) {
            return
        }
        
        const loader = document.createElement('div')
        loader.id = 'route-loader'
        loader.innerHTML = this.loadingTemplate
        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        `
        
        document.body.appendChild(loader)
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loader = document.getElementById('route-loader')
        if (loader) {
            loader.remove()
        }
    }

    /**
     * 更新页面信息
     */
    updatePageInfo() {
        if (!this.authContext.isAuthenticated()) {
            return
        }
        
        // 更新用户信息显示
        const userElements = document.querySelectorAll('[data-user-name]')
        userElements.forEach(el => {
            el.textContent = this.authContext.getUserDisplayName()
        })
        
        const roleElements = document.querySelectorAll('[data-user-roles]')
        roleElements.forEach(el => {
            el.textContent = this.authContext.getRoleDisplayNames().join('、')
        })
        
        // 更新头像
        const avatarElements = document.querySelectorAll('[data-user-avatar]')
        avatarElements.forEach(el => {
            if (this.authContext.user?.avatar) {
                el.src = this.authContext.user.avatar
            }
        })
    }

    /**
     * 获取当前路径
     */
    getCurrentPath() {
        const path = window.location.pathname
        const filename = path.split('/').pop()
        return filename || 'index.html'
    }

    /**
     * 获取原因文本
     */
    getReasonText(reason) {
        const reasonMap = {
            'insufficient-roles': '角色权限不足',
            'insufficient-permissions': '操作权限不足',
            'not-authenticated': '未登录',
            'error': '系统错误'
        }
        
        return reasonMap[reason] || '访问被拒绝'
    }

    /**
     * 格式化权限要求
     */
    formatRequirements(accessResult) {
        if (accessResult.required) {
            return Array.isArray(accessResult.required) ? 
                accessResult.required.join('、') : 
                accessResult.required
        }
        return '未知'
    }

    /**
     * 格式化当前权限
     */
    formatCurrent(accessResult) {
        if (accessResult.current) {
            return Array.isArray(accessResult.current) ? 
                accessResult.current.join('、') : 
                accessResult.current
        }
        return '无'
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div')
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            ${type === 'error' ? 
                'background: #fee; color: #c53030; border: 1px solid #feb2b2;' :
                type === 'warning' ?
                'background: #fffbeb; color: #d69e2e; border: 1px solid #fbd38d;' :
                'background: #ebf8ff; color: #2c5282; border: 1px solid #90cdf4;'
            }
        `
        messageEl.textContent = message
        
        document.body.appendChild(messageEl)
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove()
            }
        }, 3000)
    }

    /**
     * 处理错误
     */
    handleError(error) {
        console.error('❌ 路由保护错误:', error)
        this.hideLoading()
        this.showMessage('页面加载失败，请刷新重试', 'error')
    }

    /**
     * 获取默认加载模板
     */
    getDefaultLoadingTemplate() {
        return `
            <div style="text-align: center;">
                <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                <p style="color: #666; font-size: 14px;">正在验证访问权限...</p>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </div>
        `
    }

    /**
     * 获取默认未授权模板
     */
    getDefaultUnauthorizedTemplate() {
        return `
            <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background: #f7fafc;">
                <div style="text-align: center; max-width: 400px; padding: 40px;">
                    <div style="font-size: 64px; margin-bottom: 24px;">🚫</div>
                    <h1 style="font-size: 24px; font-weight: bold; color: #2d3748; margin-bottom: 16px;">访问被拒绝</h1>
                    <p style="color: #718096; margin-bottom: 8px;">原因：{{reason}}</p>
                    <p style="color: #718096; margin-bottom: 8px; font-size: 14px;">需要权限：{{required}}</p>
                    <p style="color: #718096; margin-bottom: 24px; font-size: 14px;">当前权限：{{current}}</p>
                    <div style="display: flex; gap: 12px; justify-content: center;">
                        <button id="back-btn" style="padding: 10px 20px; background: #e2e8f0; color: #4a5568; border: none; border-radius: 6px; cursor: pointer;">返回</button>
                        <button id="login-btn" style="padding: 10px 20px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer;">重新登录</button>
                    </div>
                </div>
            </div>
        `
    }
}

// 创建全局受保护路由实例
if (typeof window !== 'undefined') {
    window.protectedRoute = new ProtectedRoute()
}

// 导出受保护路由类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProtectedRoute
}

console.log('✅ 受保护路由组件已加载')

/**
 * 商品管理后端API服务器
 * 支持MedusaJS兼容的RESTful API
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { body, validationResult, param, query } = require('express-validator');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 3001;

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'smart_home_products',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

// 中间件配置
app.use(helmet());
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// 限流配置
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: { error: '请求过于频繁，请稍后再试' }
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 数据库连接池
let pool;

async function initDatabase() {
    try {
        pool = mysql.createPool({
            ...dbConfig,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0
        });

        // 测试连接
        const connection = await pool.getConnection();
        console.log('数据库连接成功');
        connection.release();

        // 创建表结构
        await createTables();
        
    } catch (error) {
        console.error('数据库连接失败:', error);
        process.exit(1);
    }
}

async function createTables() {
    const createProductsTable = `
        CREATE TABLE IF NOT EXISTS products (
            id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            user_id BIGINT UNSIGNED NOT NULL,
            name VARCHAR(255) NOT NULL,
            sku VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            category VARCHAR(100) NOT NULL,
            brand VARCHAR(100),
            price DECIMAL(10,2) NOT NULL,
            stock INT UNSIGNED DEFAULT 0,
            sales INT UNSIGNED DEFAULT 0,
            weight DECIMAL(8,2),
            status ENUM('active', 'inactive', 'draft', 'out_of_stock') DEFAULT 'draft',
            medusa_synced BOOLEAN DEFAULT FALSE,
            medusa_id VARCHAR(100),
            images JSON,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_category (category),
            INDEX idx_status (status),
            INDEX idx_sku (sku),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
            id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) NOT NULL UNIQUE,
            email VARCHAR(255) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            role ENUM('admin', 'manager', 'user') DEFAULT 'user',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            last_login_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
        await pool.execute(createProductsTable);
        await pool.execute(createUsersTable);
        console.log('数据表创建成功');
    } catch (error) {
        console.error('创建数据表失败:', error);
    }
}

// JWT认证中间件
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: '访问令牌缺失' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
        if (err) {
            return res.status(403).json({ error: '访问令牌无效' });
        }
        req.user = user;
        next();
    });
}

// 用户权限检查中间件
function checkPermission(requiredRole = 'user') {
    return (req, res, next) => {
        const userRole = req.user.role;
        const roleHierarchy = { 'admin': 3, 'manager': 2, 'user': 1 };
        
        if (roleHierarchy[userRole] >= roleHierarchy[requiredRole]) {
            next();
        } else {
            res.status(403).json({ error: '权限不足' });
        }
    };
}

// 验证规则
const productValidation = [
    body('name').trim().isLength({ min: 2, max: 255 }).withMessage('商品名称长度必须在2-255字符之间'),
    body('sku').matches(/^[A-Z]{2}\d{3,}$/).withMessage('SKU格式不正确'),
    body('price').isFloat({ min: 0 }).withMessage('价格必须为正数'),
    body('category').isIn(['switch', 'lighting', 'security', 'sensor', 'environment']).withMessage('商品分类无效'),
    body('stock').optional().isInt({ min: 0 }).withMessage('库存不能为负数'),
    body('description').optional().isLength({ max: 1000 }).withMessage('描述不能超过1000字符')
];

// API路由

// 获取商品列表
app.get('/api/admin/products', 
    authenticateToken,
    [
        query('page').optional().isInt({ min: 1 }).withMessage('页码必须为正整数'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
        query('search').optional().trim().escape(),
        query('category').optional().isIn(['switch', 'lighting', 'security', 'sensor', 'environment']),
        query('status').optional().isIn(['active', 'inactive', 'draft', 'out_of_stock'])
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '参数验证失败', details: errors.array() });
            }

            const {
                page = 1,
                limit = 20,
                search = '',
                category = '',
                brand = '',
                status = ''
            } = req.query;

            const offset = (page - 1) * limit;
            let whereClause = 'WHERE user_id = ?';
            const params = [req.user.id];

            // 构建查询条件
            if (search) {
                whereClause += ' AND (name LIKE ? OR sku LIKE ? OR brand LIKE ?)';
                const searchTerm = `%${search}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }

            if (category) {
                whereClause += ' AND category = ?';
                params.push(category);
            }

            if (brand) {
                whereClause += ' AND brand = ?';
                params.push(brand);
            }

            if (status) {
                whereClause += ' AND status = ?';
                params.push(status);
            }

            // 获取总数
            const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;
            const [countResult] = await pool.execute(countQuery, params);
            const total = countResult[0].total;

            // 获取数据
            const dataQuery = `
                SELECT id, name, sku, description, category, brand, price, stock, sales, 
                       weight, status, medusa_synced, images, created_at, updated_at
                FROM products ${whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `;
            params.push(parseInt(limit), parseInt(offset));
            
            const [products] = await pool.execute(dataQuery, params);

            // 处理图片数据
            const processedProducts = products.map(product => ({
                ...product,
                images: product.images ? JSON.parse(product.images) : [],
                price: parseFloat(product.price)
            }));

            res.json({
                success: true,
                data: {
                    products: processedProducts,
                    total: total,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    hasMore: offset + products.length < total
                },
                message: '获取商品列表成功'
            });

        } catch (error) {
            console.error('获取商品列表失败:', error);
            res.status(500).json({ error: '服务器内部错误' });
        }
    }
);

// 获取单个商品
app.get('/api/admin/products/:id',
    authenticateToken,
    [param('id').isInt({ min: 1 }).withMessage('商品ID必须为正整数')],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '参数验证失败', details: errors.array() });
            }

            const [products] = await pool.execute(
                'SELECT * FROM products WHERE id = ? AND user_id = ?',
                [req.params.id, req.user.id]
            );

            if (products.length === 0) {
                return res.status(404).json({ error: '商品不存在' });
            }

            const product = products[0];
            product.images = product.images ? JSON.parse(product.images) : [];
            product.metadata = product.metadata ? JSON.parse(product.metadata) : {};
            product.price = parseFloat(product.price);

            res.json({
                success: true,
                data: product,
                message: '获取商品详情成功'
            });

        } catch (error) {
            console.error('获取商品详情失败:', error);
            res.status(500).json({ error: '服务器内部错误' });
        }
    }
);

// 创建商品
app.post('/api/admin/products',
    authenticateToken,
    productValidation,
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '数据验证失败', details: errors.array() });
            }

            const {
                name, sku, description, category, brand, price, stock = 0,
                weight, status = 'draft', images = [], metadata = {}
            } = req.body;

            // 检查SKU是否已存在
            const [existingProducts] = await pool.execute(
                'SELECT id FROM products WHERE sku = ?',
                [sku]
            );

            if (existingProducts.length > 0) {
                return res.status(400).json({ error: 'SKU已存在' });
            }

            // 插入新商品
            const [result] = await pool.execute(
                `INSERT INTO products (user_id, name, sku, description, category, brand, price, 
                 stock, weight, status, images, metadata) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    req.user.id, name, sku, description, category, brand, price,
                    stock, weight, status, JSON.stringify(images), JSON.stringify(metadata)
                ]
            );

            // 获取创建的商品
            const [newProduct] = await pool.execute(
                'SELECT * FROM products WHERE id = ?',
                [result.insertId]
            );

            const product = newProduct[0];
            product.images = JSON.parse(product.images);
            product.metadata = JSON.parse(product.metadata);
            product.price = parseFloat(product.price);

            res.status(201).json({
                success: true,
                data: product,
                message: '商品创建成功'
            });

        } catch (error) {
            console.error('创建商品失败:', error);
            if (error.code === 'ER_DUP_ENTRY') {
                res.status(400).json({ error: 'SKU已存在' });
            } else {
                res.status(500).json({ error: '服务器内部错误' });
            }
        }
    }
);

// 更新商品
app.put('/api/admin/products/:id',
    authenticateToken,
    [param('id').isInt({ min: 1 }).withMessage('商品ID必须为正整数')],
    productValidation,
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '数据验证失败', details: errors.array() });
            }

            const productId = req.params.id;
            const {
                name, sku, description, category, brand, price, stock,
                weight, status, images = [], metadata = {}
            } = req.body;

            // 检查商品是否存在且属于当前用户
            const [existingProducts] = await pool.execute(
                'SELECT id FROM products WHERE id = ? AND user_id = ?',
                [productId, req.user.id]
            );

            if (existingProducts.length === 0) {
                return res.status(404).json({ error: '商品不存在' });
            }

            // 检查SKU是否被其他商品使用
            const [duplicateProducts] = await pool.execute(
                'SELECT id FROM products WHERE sku = ? AND id != ?',
                [sku, productId]
            );

            if (duplicateProducts.length > 0) {
                return res.status(400).json({ error: 'SKU已被其他商品使用' });
            }

            // 更新商品
            await pool.execute(
                `UPDATE products SET name = ?, sku = ?, description = ?, category = ?, 
                 brand = ?, price = ?, stock = ?, weight = ?, status = ?, images = ?, 
                 metadata = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ? AND user_id = ?`,
                [
                    name, sku, description, category, brand, price, stock, weight,
                    status, JSON.stringify(images), JSON.stringify(metadata),
                    productId, req.user.id
                ]
            );

            // 获取更新后的商品
            const [updatedProduct] = await pool.execute(
                'SELECT * FROM products WHERE id = ?',
                [productId]
            );

            const product = updatedProduct[0];
            product.images = JSON.parse(product.images);
            product.metadata = JSON.parse(product.metadata);
            product.price = parseFloat(product.price);

            res.json({
                success: true,
                data: product,
                message: '商品更新成功'
            });

        } catch (error) {
            console.error('更新商品失败:', error);
            res.status(500).json({ error: '服务器内部错误' });
        }
    }
);

// 删除商品
app.delete('/api/admin/products/:id',
    authenticateToken,
    [param('id').isInt({ min: 1 }).withMessage('商品ID必须为正整数')],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '参数验证失败', details: errors.array() });
            }

            const productId = req.params.id;

            // 检查商品是否存在且属于当前用户
            const [existingProducts] = await pool.execute(
                'SELECT id FROM products WHERE id = ? AND user_id = ?',
                [productId, req.user.id]
            );

            if (existingProducts.length === 0) {
                return res.status(404).json({ error: '商品不存在' });
            }

            // 删除商品
            await pool.execute(
                'DELETE FROM products WHERE id = ? AND user_id = ?',
                [productId, req.user.id]
            );

            res.json({
                success: true,
                data: { id: productId },
                message: '商品删除成功'
            });

        } catch (error) {
            console.error('删除商品失败:', error);
            res.status(500).json({ error: '服务器内部错误' });
        }
    }
);

// 批量操作
app.post('/api/admin/products/batch',
    authenticateToken,
    [
        body('operation').isIn(['delete', 'update_status', 'sync_medusa']).withMessage('操作类型无效'),
        body('ids').isArray({ min: 1 }).withMessage('商品ID列表不能为空'),
        body('ids.*').isInt({ min: 1 }).withMessage('商品ID必须为正整数')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ error: '参数验证失败', details: errors.array() });
            }

            const { operation, ids, data = {} } = req.body;

            // 验证所有商品都属于当前用户
            const placeholders = ids.map(() => '?').join(',');
            const [ownedProducts] = await pool.execute(
                `SELECT id FROM products WHERE id IN (${placeholders}) AND user_id = ?`,
                [...ids, req.user.id]
            );

            if (ownedProducts.length !== ids.length) {
                return res.status(403).json({ error: '部分商品不存在或无权限操作' });
            }

            let result;
            switch (operation) {
                case 'delete':
                    await pool.execute(
                        `DELETE FROM products WHERE id IN (${placeholders}) AND user_id = ?`,
                        [...ids, req.user.id]
                    );
                    result = { deleted: ids.length };
                    break;

                case 'update_status':
                    if (!data.status || !['active', 'inactive', 'draft', 'out_of_stock'].includes(data.status)) {
                        return res.status(400).json({ error: '状态值无效' });
                    }
                    await pool.execute(
                        `UPDATE products SET status = ?, updated_at = CURRENT_TIMESTAMP 
                         WHERE id IN (${placeholders}) AND user_id = ?`,
                        [data.status, ...ids, req.user.id]
                    );
                    result = { updated: ids.length, status: data.status };
                    break;

                case 'sync_medusa':
                    // 这里应该调用MedusaJS API进行同步
                    await pool.execute(
                        `UPDATE products SET medusa_synced = TRUE, updated_at = CURRENT_TIMESTAMP 
                         WHERE id IN (${placeholders}) AND user_id = ?`,
                        [...ids, req.user.id]
                    );
                    result = { synced: ids.length };
                    break;
            }

            res.json({
                success: true,
                data: result,
                message: `批量${operation}操作成功`
            });

        } catch (error) {
            console.error('批量操作失败:', error);
            res.status(500).json({ error: '服务器内部错误' });
        }
    }
);

// MedusaJS同步接口
app.post('/api/medusa/sync/products',
    authenticateToken,
    async (req, res) => {
        try {
            // 这里应该实现与MedusaJS的实际同步逻辑
            // 简化实现，标记所有商品为已同步
            await pool.execute(
                'UPDATE products SET medusa_synced = TRUE WHERE user_id = ?',
                [req.user.id]
            );

            res.json({
                success: true,
                data: { message: 'MedusaJS同步完成' },
                message: 'MedusaJS同步成功'
            });

        } catch (error) {
            console.error('MedusaJS同步失败:', error);
            res.status(500).json({ error: '同步失败' });
        }
    }
);

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('未处理的错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
async function startServer() {
    await initDatabase();
    
    app.listen(PORT, () => {
        console.log(`商品管理API服务器运行在端口 ${PORT}`);
        console.log(`API文档: http://localhost:${PORT}/api/docs`);
    });
}

// 优雅关闭
process.on('SIGTERM', async () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    if (pool) {
        await pool.end();
    }
    process.exit(0);
});

startServer().catch(console.error);

module.exports = app;

-- 智能家居商品管理系统种子数据
-- 用于开发和测试环境

USE smart_home_products;

-- 清空现有数据（开发环境）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE operation_logs;
TRUNCATE TABLE medusa_sync_logs;
TRUNCATE TABLE price_history;
TRUNCATE TABLE inventory_logs;
TRUNCATE TABLE product_variants;
TRUNCATE TABLE products;
TRUNCATE TABLE brands;
TRUNCATE TABLE product_categories;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试用户
INSERT INTO users (username, email, password_hash, role, status, profile) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'admin', 'active', '{"name": "系统管理员", "phone": "13800138000"}'),
('manager', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'manager', 'active', '{"name": "产品经理", "phone": "13800138001"}'),
('user1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'user', 'active', '{"name": "张三", "phone": "13800138002"}'),
('user2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'user', 'active', '{"name": "李四", "phone": "13800138003"}');

-- 插入商品分类
INSERT INTO product_categories (id, name, code, description, parent_id, sort_order) VALUES
(1, '智能开关', 'switch', '各类智能开关产品', NULL, 1),
(2, '智能照明', 'lighting', '智能灯具和照明控制设备', NULL, 2),
(3, '安防设备', 'security', '智能门锁、摄像头等安防产品', NULL, 3),
(4, '传感器', 'sensor', '各类环境和状态传感器', NULL, 4),
(5, '环境控制', 'environment', '温控、空气净化等环境控制设备', NULL, 5),
(6, '单控开关', 'single_switch', '单控智能开关', 1, 11),
(7, '双控开关', 'dual_switch', '双控智能开关', 1, 12),
(8, '智能灯泡', 'smart_bulb', '智能LED灯泡', 2, 21),
(9, '智能灯带', 'smart_strip', '智能LED灯带', 2, 22),
(10, '智能门锁', 'smart_lock', '智能门锁产品', 3, 31);

-- 插入品牌
INSERT INTO brands (id, name, code, description, logo_url, website) VALUES
(1, '小米', 'xiaomi', '小米智能家居产品', 'https://cdn.mi.com/logo.png', 'https://www.mi.com'),
(2, '华为', 'huawei', '华为智能家居产品', 'https://cdn.huawei.com/logo.png', 'https://www.huawei.com'),
(3, 'Philips', 'philips', '飞利浦照明产品', 'https://cdn.philips.com/logo.png', 'https://www.philips.com'),
(4, '海康威视', 'hikvision', '海康威视安防产品', 'https://cdn.hikvision.com/logo.png', 'https://www.hikvision.com'),
(5, '杜亚', 'dooya', '杜亚智能窗帘产品', 'https://cdn.dooya.com/logo.png', 'https://www.dooya.com'),
(6, 'Aqara', 'aqara', 'Aqara智能家居产品', 'https://cdn.aqara.com/logo.png', 'https://www.aqara.com'),
(7, '绿米', 'lumi', '绿米智能家居产品', 'https://cdn.lumi.com/logo.png', 'https://www.lumi.com');

-- 插入测试商品数据
INSERT INTO products (
    user_id, name, sku, description, short_description, category_id, brand_id,
    price, cost_price, market_price, stock, min_stock, max_stock, sales, views,
    weight, status, is_featured, images, attributes, seo_title, seo_description
) VALUES
-- 用户1的商品
(3, '小米智能开关面板 - 单火线版', 'SW240101', '小米智能开关面板，支持单火线接线，无需零线，适合老房改造。支持米家APP远程控制，定时开关，场景联动。', '小米单火线智能开关，老房改造首选', 6, 1, 299.00, 180.00, 399.00, 156, 50, 500, 89, 245, 0.3, 'active', TRUE, 
'["https://cdn.mi.com/switch1.jpg", "https://cdn.mi.com/switch1_2.jpg"]', 
'{"color": "白色", "size": "86*86mm", "voltage": "220V", "power": "2500W", "protocol": "Zigbee 3.0"}',
'小米智能开关面板单火线版 - 智能家居开关', '小米智能开关面板，支持单火线，远程控制，定时开关，场景联动'),

(3, '华为智能门锁 Pro', 'DL240102', '华为智能门锁Pro，支持指纹、密码、卡片、钥匙四种开锁方式。3D人脸识别，活体检测，安全可靠。', '华为智能门锁，多种开锁方式', 10, 2, 1299.00, 800.00, 1599.00, 45, 20, 200, 23, 156, 2.5, 'active', TRUE,
'["https://cdn.huawei.com/lock1.jpg", "https://cdn.huawei.com/lock1_2.jpg"]',
'{"color": "黑色", "material": "锌合金", "battery": "8节AA电池", "unlock_methods": ["指纹", "密码", "卡片", "钥匙"]}',
'华为智能门锁Pro - 多重安全保护', '华为智能门锁Pro，指纹密码卡片钥匙四合一，3D人脸识别'),

(3, 'Philips智能灯泡 - 调色版', 'LB240103', 'Philips Hue智能灯泡，支持1600万色调节，可调亮度，支持语音控制和APP控制。', 'Philips智能调色灯泡', 8, 3, 89.00, 45.00, 129.00, 0, 30, 300, 67, 89, 0.1, 'out_of_stock', FALSE,
'["https://cdn.philips.com/bulb1.jpg"]',
'{"color": "多彩", "power": "9W", "brightness": "800流明", "base": "E27", "lifespan": "25000小时"}',
'Philips智能调色灯泡 - 1600万色彩', 'Philips Hue智能灯泡，1600万色彩调节，语音控制'),

(3, '小米智能插座 - WiFi版', 'PL240104', '小米智能插座，WiFi连接，支持远程控制，定时开关，电量统计。最大功率2500W。', '小米WiFi智能插座', 1, 1, 59.00, 35.00, 79.00, 234, 50, 1000, 156, 78, 0.2, 'active', FALSE,
'["https://cdn.mi.com/plug1.jpg"]',
'{"color": "白色", "power": "2500W", "voltage": "220V", "protocol": "WiFi 2.4GHz"}',
'小米智能插座WiFi版 - 远程控制插座', '小米智能插座，WiFi连接，远程控制，定时开关，电量统计'),

-- 用户2的商品  
(4, '海康威视智能摄像头', 'CM240105', '海康威视1080P高清智能摄像头，支持夜视、移动侦测、云存储。', '海康威视高清智能摄像头', 3, 4, 399.00, 250.00, 499.00, 78, 30, 200, 34, 123, 0.8, 'active', TRUE,
'["https://cdn.hikvision.com/camera1.jpg"]',
'{"resolution": "1080P", "night_vision": true, "storage": "云存储+本地存储", "power": "12V 1A"}',
'海康威视智能摄像头1080P - 安防监控', '海康威视1080P智能摄像头，夜视移动侦测，云存储'),

(4, 'Aqara温湿度传感器', 'SN240106', 'Aqara温湿度传感器，实时监测环境温湿度，支持米家APP，可联动其他设备。', 'Aqara温湿度传感器', 4, 6, 49.00, 25.00, 69.00, 189, 100, 500, 78, 45, 0.05, 'active', FALSE,
'["https://cdn.aqara.com/sensor1.jpg"]',
'{"temperature_range": "-10°C~60°C", "humidity_range": "0%~100%RH", "battery": "CR2450", "protocol": "Zigbee 3.0"}',
'Aqara温湿度传感器 - 环境监测', 'Aqara温湿度传感器，实时监测温湿度，智能联动'),

(4, '杜亚智能窗帘电机', 'CU240107', '杜亚静音智能窗帘电机，支持定时控制、语音控制、手机APP控制。', '杜亚静音智能窗帘电机', 5, 5, 599.00, 350.00, 799.00, 23, 10, 100, 12, 67, 1.2, 'active', TRUE,
'["https://cdn.dooya.com/motor1.jpg"]',
'{"noise_level": "<35dB", "power": "24V", "load_capacity": "30kg", "control": ["APP", "语音", "定时"]}',
'杜亚智能窗帘电机 - 静音控制', '杜亚智能窗帘电机，静音设计，多种控制方式'),

-- 管理员的商品
(2, '绿米智能网关 Pro', 'GW240108', '绿米智能网关Pro，支持Zigbee 3.0协议，可连接多达128个子设备。', '绿米Zigbee智能网关', 4, 7, 199.00, 120.00, 259.00, 67, 20, 200, 45, 89, 0.4, 'active', TRUE,
'["https://cdn.lumi.com/gateway1.jpg"]',
'{"protocol": "Zigbee 3.0", "max_devices": 128, "connectivity": "WiFi + 以太网", "power": "12V 1A"}',
'绿米智能网关Pro - Zigbee中枢', '绿米智能网关Pro，Zigbee 3.0协议，连接128个设备'),

(2, '小米智能门铃', 'DB240109', '小米智能门铃，1080P高清摄像头，PIR人体感应，支持云存储和本地存储。', '小米高清智能门铃', 3, 1, 299.00, 180.00, 399.00, 34, 20, 150, 28, 56, 0.6, 'active', FALSE,
'["https://cdn.mi.com/doorbell1.jpg"]',
'{"resolution": "1080P", "detection": "PIR人体感应", "storage": "云存储+本地", "battery": "5000mAh"}',
'小米智能门铃1080P - 安全守护', '小米智能门铃，1080P高清，PIR感应，云存储'),

(2, 'Philips智能灯带 2米装', 'LS240110', 'Philips Hue智能灯带，2米长，支持剪切，1600万色彩，可弯曲安装。', 'Philips智能彩色灯带', 9, 3, 299.00, 150.00, 399.00, 45, 20, 200, 23, 78, 0.3, 'draft', FALSE,
'["https://cdn.philips.com/strip1.jpg"]',
'{"length": "2米", "cuttable": true, "colors": "1600万色", "brightness": "1600流明/米"}',
'Philips智能灯带2米 - 氛围照明', 'Philips Hue智能灯带，2米可剪切，1600万色彩');

-- 插入商品变体数据
INSERT INTO product_variants (
    product_id, sku, name, price, cost_price, stock, attributes, is_default, sort_order
) VALUES
-- 小米智能开关的变体（不同颜色）
(1, 'SW240101-W', '白色版', 299.00, 180.00, 100, '{"color": "白色"}', TRUE, 1),
(1, 'SW240101-B', '黑色版', 309.00, 185.00, 56, '{"color": "黑色"}', FALSE, 2),

-- 华为智能门锁的变体（不同颜色）
(2, 'DL240102-B', '黑色版', 1299.00, 800.00, 30, '{"color": "黑色"}', TRUE, 1),
(2, 'DL240102-S', '银色版', 1299.00, 800.00, 15, '{"color": "银色"}', FALSE, 2),

-- Philips灯泡的变体（不同功率）
(3, 'LB240103-9W', '9W版', 89.00, 45.00, 0, '{"power": "9W", "brightness": "800流明"}', TRUE, 1),
(3, 'LB240103-12W', '12W版', 109.00, 55.00, 0, '{"power": "12W", "brightness": "1100流明"}', FALSE, 2);

-- 插入库存记录
INSERT INTO inventory_logs (
    product_id, variant_id, type, quantity, before_stock, after_stock, reason, operator_id
) VALUES
(1, NULL, 'in', 200, 0, 200, '初始入库', 1),
(1, NULL, 'out', -44, 200, 156, '销售出库', 1),
(2, NULL, 'in', 100, 0, 100, '初始入库', 1),
(2, NULL, 'out', -55, 100, 45, '销售出库', 1),
(3, NULL, 'in', 100, 0, 100, '初始入库', 1),
(3, NULL, 'out', -100, 100, 0, '全部售完', 1),
(4, NULL, 'in', 300, 0, 300, '初始入库', 1),
(4, NULL, 'out', -66, 300, 234, '销售出库', 1);

-- 插入价格历史记录
INSERT INTO price_history (
    product_id, old_price, new_price, price_type, reason, operator_id
) VALUES
(1, 319.00, 299.00, 'sale', '促销活动降价', 1),
(2, 1399.00, 1299.00, 'sale', '新品上市优惠', 1),
(3, 99.00, 89.00, 'sale', '竞争对手降价跟进', 1),
(4, 69.00, 59.00, 'sale', '批量采购成本降低', 1);

-- 插入MedusaJS同步记录
INSERT INTO medusa_sync_logs (
    product_id, sync_type, medusa_id, sync_status, request_data, response_data, synced_at
) VALUES
(1, 'create', 'prod_01HXXX1', 'success', '{"name": "小米智能开关面板", "price": 299}', '{"id": "prod_01HXXX1", "status": "published"}', NOW()),
(2, 'create', 'prod_01HXXX2', 'success', '{"name": "华为智能门锁 Pro", "price": 1299}', '{"id": "prod_01HXXX2", "status": "published"}', NOW()),
(3, 'create', 'prod_01HXXX3', 'failed', '{"name": "Philips智能灯泡", "price": 89}', '{"error": "SKU already exists"}', NULL),
(4, 'create', 'prod_01HXXX4', 'success', '{"name": "小米智能插座", "price": 59}', '{"id": "prod_01HXXX4", "status": "published"}', NOW());

-- 插入操作日志
INSERT INTO operation_logs (
    user_id, action, resource_type, resource_id, description, ip_address, user_agent
) VALUES
(1, 'CREATE', 'product', 1, '创建商品：小米智能开关面板', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, 'CREATE', 'product', 2, '创建商品：华为智能门锁 Pro', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 'UPDATE', 'product', 1, '更新商品价格：299.00', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
(3, 'CREATE', 'product', 3, '创建商品：Philips智能灯泡', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15');

-- 更新商品的MedusaJS同步状态
UPDATE products SET medusa_synced = TRUE, medusa_id = 'prod_01HXXX1' WHERE id = 1;
UPDATE products SET medusa_synced = TRUE, medusa_id = 'prod_01HXXX2' WHERE id = 2;
UPDATE products SET medusa_synced = FALSE WHERE id = 3;
UPDATE products SET medusa_synced = TRUE, medusa_id = 'prod_01HXXX4' WHERE id = 4;

/**
 * 需求管理页面集成脚本
 * 将选项卡流程管理器、自动保存管理器和验证管理器集成到现有页面
 */

(function() {
    'use strict';

    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeRequirementsIntegration);
    } else {
        initializeRequirementsIntegration();
    }

    function initializeRequirementsIntegration() {
        console.log('🚀 开始初始化需求管理页面集成...');

        // 检查必要的管理器是否已加载
        if (typeof TabFlowManager === 'undefined' || 
            typeof AutoSaveManager === 'undefined' || 
            typeof ValidationManager === 'undefined' ||
            typeof HorizontalTableManager === 'undefined') {
            console.error('❌ 必要的管理器未加载，请检查脚本引入顺序');
            return;
        }

        // 增强现有功能
        enhanceExistingFunctions();
        
        // 添加新的按钮和控件
        addEnhancedControls();
        
        // 设置事件监听器
        setupEventListeners();
        
        // 集成管理器实例
        integrateManagers();
        
        // 初始化横向表格管理器
        initializeHorizontalTableManager();

        console.log('✅ 需求管理页面集成完成');
    }

    /**
     * 增强现有功能
     */
    function enhanceExistingFunctions() {
        // 保存原始的 switchToTab 函数
        if (typeof window.originalSwitchToTab === 'undefined') {
            window.originalSwitchToTab = window.switchToTab;
        }

        // 增强 switchToTab 函数
        window.switchToTab = function(tabId) {
            console.log('🎭 integration.js switchToTab被调用:', tabId);
            console.log('🔍 检查tabFlowManager存在性:', !!window.tabFlowManager);
            
            // 如果流程管理器处于活动状态，使用流程管理器处理
            if (window.tabFlowManager && window.tabFlowManager.state && window.tabFlowManager.state.isActive) {
                console.log('📋 使用流程管理器处理');
                const tabIndex = window.tabFlowManager.config.tabs.findIndex(tab => tab.id === tabId);
                if (tabIndex !== -1) {
                    window.tabFlowManager.goToStep(tabIndex);
                    return;
                }
            }
            
            // 否则使用原始函数
            console.log('🔄 回退到原始函数:', !!window.originalSwitchToTab);
            if (window.originalSwitchToTab) {
                window.originalSwitchToTab(tabId);
            } else {
                // 如果原始函数不存在，直接调用页面中的showModule函数
                console.log('🔧 直接调用showModule函数');
                if (typeof showModule === 'function') {
                    showModule(tabId, true);
                } else {
                    console.error('❌ 无法找到任何切换函数');
                }
            }
            
            // 确保切换后的模块正确显示
            setTimeout(() => {
                const targetModule = document.getElementById(tabId);
                if (targetModule && !targetModule.classList.contains('active')) {
                    console.log('🔧 强制激活模块:', tabId);
                    document.querySelectorAll('.module-content').forEach(m => {
                        m.classList.remove('active');
                        m.style.display = 'none';
                    });
                    targetModule.classList.add('active');
                    targetModule.style.display = 'block';
                }
            }, 50);
        };

        // 增强 startNewRequirement 函数
        if (typeof window.startNewRequirement === 'function') {
            const originalStartNewRequirement = window.startNewRequirement;
            window.startNewRequirement = function() {
                // 启动流程管理器
                if (window.tabFlowManager) {
                    window.tabFlowManager.startFlow(1); // 从新建需求开始
                } else {
                    // 回退到原始函数
                    originalStartNewRequirement();
                }
            };
        }

        // 增强产品选择功能
        enhanceProductSelection();
        
        // 增强文件上传功能
        enhanceFileUpload();
    }

    /**
     * 增强产品选择功能
     */
    function enhanceProductSelection() {
        // 监听产品选择变化
        document.addEventListener('change', function(e) {
            if (e.target.matches('.product-checkbox, .scene-checkbox')) {
                // 触发自动保存
                if (window.autoSaveManager) {
                    window.autoSaveManager.handleFieldChange(e);
                }
                
                // 更新选择统计
                updateSelectionStats();
            }
        });
    }

    /**
     * 增强文件上传功能
     */
    function enhanceFileUpload() {
        // 监听文件上传事件
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[type="file"]')) {
                handleFileUpload(e);
            }
        });
    }

    /**
     * 处理文件上传
     */
    function handleFileUpload(event) {
        const files = event.target.files;
        if (!files.length) return;

        const uploadContainer = document.querySelector('.upload-preview-container');
        if (!uploadContainer) return;

        Array.from(files).forEach(file => {
            // 验证文件类型和大小
            if (!validateFile(file)) return;

            // 创建预览元素
            const previewElement = createFilePreview(file);
            uploadContainer.appendChild(previewElement);

            // 模拟上传过程
            simulateFileUpload(file, previewElement);
        });

        // 触发自动保存
        if (window.autoSaveManager) {
            window.autoSaveManager.handleFieldChange(event);
        }
    }

    /**
     * 验证文件
     */
    function validateFile(file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/vnd.dwg'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
            showError(`不支持的文件类型: ${file.name}`);
            return false;
        }

        if (file.size > maxSize) {
            showError(`文件过大: ${file.name} (最大10MB)`);
            return false;
        }

        return true;
    }

    /**
     * 创建文件预览
     */
    function createFilePreview(file) {
        const previewElement = document.createElement('div');
        previewElement.className = 'uploaded-file-item';
        previewElement.dataset.filename = file.name;
        previewElement.dataset.filesize = file.size;
        previewElement.dataset.filetype = file.type;

        previewElement.innerHTML = `
            <div class="file-icon">
                <i class="${getFileIcon(file.type)}"></i>
            </div>
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
                <div class="upload-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="progress-text">0%</span>
                </div>
            </div>
            <button type="button" class="remove-file" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        return previewElement;
    }

    /**
     * 模拟文件上传
     */
    function simulateFileUpload(file, element) {
        const progressFill = element.querySelector('.progress-fill');
        const progressText = element.querySelector('.progress-text');
        let progress = 0;

        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                
                // 上传完成
                element.classList.add('upload-complete');
                element.dataset.fileurl = `uploads/${Date.now()}-${file.name}`;
            }

            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${Math.round(progress)}%`;
        }, 200);
    }

    /**
     * 添加增强控件
     */
    function addEnhancedControls() {
        // 在需求列表页面添加"开始创建需求"按钮
        addStartFlowButton();
        
        // 添加快速操作按钮
        addQuickActionButtons();
        
        // 添加键盘快捷键提示
        addKeyboardShortcuts();
    }

    /**
     * 添加开始流程按钮
     */
    function addStartFlowButton() {
        const actionsArea = document.querySelector('.table-actions');
        if (!actionsArea) return;

        const startFlowButton = document.createElement('button');
        startFlowButton.className = 'btn btn-primary start-flow-btn';
        startFlowButton.innerHTML = `
            <i class="fas fa-plus-circle"></i>
            <span>创建新需求</span>
        `;
        startFlowButton.onclick = () => {
            if (window.tabFlowManager) {
                window.tabFlowManager.startFlow(1);
            }
        };

        actionsArea.insertBefore(startFlowButton, actionsArea.firstChild);
    }

    /**
     * 添加快速操作按钮
     */
    function addQuickActionButtons() {
        const quickActionsHTML = `
            <div id="quick-actions" class="quick-actions-panel" style="display: none;">
                <div class="quick-actions-header">
                    <h4>快速操作</h4>
                    <button type="button" class="close-quick-actions" onclick="document.getElementById('quick-actions').style.display='none'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="quick-actions-content">
                    <button type="button" class="quick-btn" onclick="quickSave()">
                        <i class="fas fa-save"></i>
                        快速保存
                    </button>
                    <button type="button" class="quick-btn" onclick="clearAllData()">
                        <i class="fas fa-trash"></i>
                        清空数据
                    </button>
                    <button type="button" class="quick-btn" onclick="importTemplate()">
                        <i class="fas fa-file-import"></i>
                        导入模板
                    </button>
                    <button type="button" class="quick-btn" onclick="exportDraft()">
                        <i class="fas fa-file-export"></i>
                        导出草稿
                    </button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', quickActionsHTML);
        addQuickActionsStyles();
    }

    /**
     * 设置事件监听器
     */
    function setupEventListeners() {
        // 键盘快捷键
        document.addEventListener('keydown', handleKeyboardShortcuts);
        
        // 页面离开确认
        window.addEventListener('beforeunload', handleBeforeUnload);
        
        // 窗口大小变化
        window.addEventListener('resize', handleWindowResize);
    }

    /**
     * 处理键盘快捷键
     */
    function handleKeyboardShortcuts(e) {
        // Ctrl+S: 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (window.autoSaveManager) {
                window.autoSaveManager.manualSave();
            }
        }
        
        // Ctrl+Enter: 下一步
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            if (window.tabFlowManager && window.tabFlowManager.state.isActive) {
                window.tabFlowManager.nextStep();
            }
        }
        
        // ESC: 取消流程
        if (e.key === 'Escape') {
            if (window.tabFlowManager && window.tabFlowManager.state.isActive) {
                const confirmed = confirm('确定要取消当前操作吗？');
                if (confirmed) {
                    window.tabFlowManager.cancelFlow();
                }
            }
        }
        
        // F1: 显示帮助
        if (e.key === 'F1') {
            e.preventDefault();
            showHelp();
        }
    }

    /**
     * 处理页面离开
     */
    function handleBeforeUnload(e) {
        if (window.autoSaveManager) {
            const status = window.autoSaveManager.getSaveStatus();
            if (status.isDirty) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
                return e.returnValue;
            }
        }
    }

    /**
     * 处理窗口大小变化
     */
    function handleWindowResize() {
        // 调整进度指示器和导航按钮的布局
        adjustResponsiveLayout();
    }

    /**
     * 初始化横向表格管理器
     */
    function initializeHorizontalTableManager() {
        // 等待页面DOM完全加载后初始化横向表格管理器
        setTimeout(() => {
            if (window.HorizontalTableManager && !window.horizontalTableManager) {
                window.horizontalTableManager = new window.HorizontalTableManager();
                console.log('📊 横向表格管理器已初始化');
                
                // 识别并初始化所有带有横向控制容器的表格
                initializeAllModuleTables();
                
                // 与现有需求数据集成
                if (typeof window.requirementsData !== 'undefined') {
                    // 将现有需求数据传递给横向表格管理器
                    const adaptedData = window.requirementsData.map(req => ({
                        id: req.id,
                        customerName: req.customerName,
                        phone: req.phone,
                        type: req.type,
                        address: req.address,
                        budget: req.budget,
                        priority: req.priority,
                        status: req.status,
                        createTime: req.createTime,
                        description: req.description || ''
                    }));
                    window.horizontalTableManager.setData(adaptedData);
                    console.log('📊 需求数据已加载到横向表格管理器');
                }
            }
        }, 1500); // 增加延迟确保所有组件和数据都加载完成
    }

    /**
     * 初始化所有模块的表格
     */
    function initializeAllModuleTables() {
        // 需求列表表格
        const requirementsTable = document.querySelector('#requirements-list .table-container table');
        if (requirementsTable) {
            const requirementsContainer = document.querySelector('#requirements-list .horizontal-table-controls');
            if (requirementsContainer && window.horizontalTableManager) {
                window.horizontalTableManager.initializeTable(requirementsTable, requirementsContainer);
                console.log('📊 需求列表表格已初始化横向管理器');
            }
        }

        // 产品选择表格
        const productsTable = document.querySelector('#product-selection .table-container table');
        if (productsTable) {
            const productsContainer = document.querySelector('#product-selection .horizontal-table-controls');
            if (productsContainer && window.horizontalTableManager) {
                window.horizontalTableManager.initializeTable(productsTable, productsContainer);
                console.log('📦 产品选择表格已初始化横向管理器');
            }
        }

        // 图纸管理表格
        const filesTable = document.querySelector('#drawing-management .table-container table');
        if (filesTable) {
            const filesContainer = document.querySelector('#drawing-management .horizontal-table-controls');
            if (filesContainer && window.horizontalTableManager) {
                window.horizontalTableManager.initializeTable(filesTable, filesContainer);
                console.log('📁 图纸管理表格已初始化横向管理器');
            }
        }

        console.log('🎯 所有模块表格横向管理器初始化完成');
    }

    /**
     * 集成管理器实例
     */
    function integrateManagers() {
        // 设置管理器间的通信
        if (window.tabFlowManager && window.autoSaveManager) {
            // 选项卡切换时触发保存
            window.tabFlowManager.on('onStepChange', (data) => {
                window.autoSaveManager.saveFormData();
            });
            
            // 流程完成时清理自动保存数据
            window.tabFlowManager.on('onComplete', () => {
                window.autoSaveManager.clearSavedData();
            });
        }

        // 设置验证管理器集成
        if (window.tabFlowManager && window.validationManager) {
            window.tabFlowManager.on('onValidation', (data) => {
                if (!data.valid) {
                    console.log(`📋 步骤 ${data.step + 1} 验证失败`);
                }
            });
        }
    }

    /**
     * 工具函数
     */
    function updateSelectionStats() {
        const selectedProducts = document.querySelectorAll('.product-checkbox:checked').length;
        const selectedScenes = document.querySelectorAll('.scene-checkbox:checked').length;
        
        // 更新统计显示
        const statsElement = document.querySelector('.selection-stats');
        if (statsElement) {
            statsElement.innerHTML = `
                已选择 ${selectedProducts} 个产品，${selectedScenes} 个场景
            `;
        }
    }

    function getFileIcon(fileType) {
        const iconMap = {
            'image/jpeg': 'fas fa-image',
            'image/png': 'fas fa-image',
            'image/gif': 'fas fa-image',
            'application/pdf': 'fas fa-file-pdf',
            'application/vnd.dwg': 'fas fa-drafting-compass'
        };
        return iconMap[fileType] || 'fas fa-file';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showError(message) {
        console.error(`❌ ${message}`);
        // 这里可以集成全局错误提示组件
    }

    function adjustResponsiveLayout() {
        const width = window.innerWidth;
        const progressIndicator = document.getElementById('tab-progress-indicator');
        const navigationControls = document.getElementById('tab-navigation-controls');
        
        if (width < 768) {
            // 移动端布局调整
            if (progressIndicator) {
                progressIndicator.classList.add('mobile-layout');
            }
            if (navigationControls) {
                navigationControls.classList.add('mobile-layout');
            }
        } else {
            // 桌面端布局
            if (progressIndicator) {
                progressIndicator.classList.remove('mobile-layout');
            }
            if (navigationControls) {
                navigationControls.classList.remove('mobile-layout');
            }
        }
    }

    function addKeyboardShortcuts() {
        const shortcutsHTML = `
            <div id="keyboard-shortcuts" class="shortcuts-panel" style="display: none;">
                <div class="shortcuts-header">
                    <h4>键盘快捷键</h4>
                    <button onclick="document.getElementById('keyboard-shortcuts').style.display='none'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="shortcuts-content">
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>S</kbd>
                        <span>保存当前数据</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>Enter</kbd>
                        <span>下一步</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>ESC</kbd>
                        <span>取消当前操作</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>F1</kbd>
                        <span>显示帮助</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', shortcutsHTML);
    }

    function showHelp() {
        const helpContent = `
            <div class="help-modal-backdrop" onclick="this.remove()">
                <div class="help-modal" onclick="event.stopPropagation()">
                    <div class="help-header">
                        <h3>需求创建流程帮助</h3>
                        <button onclick="this.closest('.help-modal-backdrop').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="help-content">
                        <h4>流程步骤</h4>
                        <ol>
                            <li><strong>需求列表</strong> - 查看所有需求，点击"创建新需求"开始</li>
                            <li><strong>新建需求</strong> - 填写需求基本信息（必填）</li>
                            <li><strong>产品选择</strong> - 选择相关产品和场景（必填）</li>
                            <li><strong>图纸管理</strong> - 上传相关图纸文件（可选）</li>
                        </ol>
                        
                        <h4>功能特性</h4>
                        <ul>
                            <li>✅ 自动保存：数据每30秒自动保存</li>
                            <li>✅ 实时验证：输入时即时检查数据有效性</li>
                            <li>✅ 进度指示：清晰显示当前进度和完成状态</li>
                            <li>✅ 快捷键：支持键盘快捷操作</li>
                            <li>✅ 断点续传：意外关闭后可恢复数据</li>
                        </ul>
                        
                        <h4>注意事项</h4>
                        <ul>
                            <li>⚠️ 必填字段标有红色星号</li>
                            <li>⚠️ 文件上传支持图片、PDF、DWG格式，最大10MB</li>
                            <li>⚠️ 离开页面前请确保已保存数据</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', helpContent);
    }

    // 快速操作函数
    window.quickSave = function() {
        if (window.autoSaveManager) {
            window.autoSaveManager.manualSave();
        }
    };

    window.clearAllData = function() {
        const confirmed = confirm('确定要清空所有数据吗？此操作不可撤销。');
        if (confirmed && window.autoSaveManager) {
            window.autoSaveManager.clearSavedData();
            // 清空表单字段
            document.querySelectorAll('input, textarea, select').forEach(field => {
                if (field.type !== 'submit' && field.type !== 'button') {
                    field.value = '';
                    field.checked = false;
                }
            });
        }
    };

    window.importTemplate = function() {
        alert('模板导入功能将在后续版本中实现');
    };

    window.exportDraft = function() {
        if (window.autoSaveManager) {
            const data = window.autoSaveManager.collectFormData();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `需求草稿_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    };

    function addQuickActionsStyles() {
        const styles = `
            <style id="integration-styles">
                .quick-actions-panel {
                    position: fixed;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    padding: 16px;
                    min-width: 200px;
                    z-index: 1001;
                    border: 1px solid #e5e7eb;
                }
                
                .quick-actions-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid #f3f4f6;
                }
                
                .quick-actions-header h4 {
                    margin: 0;
                    font-size: 14px;
                    color: #374151;
                }
                
                .close-quick-actions {
                    background: none;
                    border: none;
                    color: #6b7280;
                    cursor: pointer;
                    padding: 4px;
                }
                
                .quick-btn {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    width: 100%;
                    padding: 8px 12px;
                    margin-bottom: 4px;
                    background: none;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    color: #374151;
                    transition: all 0.2s ease;
                }
                
                .quick-btn:hover {
                    background: #f9fafb;
                    border-color: #d1d5db;
                }
                
                .start-flow-btn {
                    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-weight: 600;
                    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                    transition: all 0.3s ease;
                }
                
                .start-flow-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
                }
                
                .shortcuts-panel, .help-modal-backdrop {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1002;
                }
                
                .help-modal {
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                }
                
                .help-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 12px;
                    border-bottom: 1px solid #e5e7eb;
                }
                
                .shortcut-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    border-bottom: 1px solid #f3f4f6;
                }
                
                kbd {
                    background: #f3f4f6;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    padding: 2px 6px;
                    font-size: 11px;
                    font-family: monospace;
                }
                
                @media (max-width: 768px) {
                    .mobile-layout .progress-steps {
                        flex-direction: column;
                        gap: 12px;
                    }
                    
                    .mobile-layout .navigation-buttons {
                        flex-wrap: wrap;
                        gap: 8px;
                    }
                    
                    .mobile-layout .nav-btn {
                        flex: 1;
                        min-width: 120px;
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }

})();
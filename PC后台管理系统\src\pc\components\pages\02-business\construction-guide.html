<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工指导 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 施工指导头部控制 */
        .guide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        /* 筛选和搜索区域 - 统一设计规范 */
        .filter-search-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px 24px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 6px;
            background: #f8fafc;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #e5e7eb;
        }

        .tab.active {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .tab:hover:not(.active) {
            background: #f1f5f9;
            color: #1f2937;
        }

        /* 搜索框 */
        .search-box {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 12px;
            width: 280px;
            outline: none;
            transition: all 0.2s ease;
            line-height: 1.5;
        }

        .search-box input:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .search-btn {
            padding: 8px 16px;
            background: #1f2937;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            line-height: 1.5;
        }

        .search-btn:hover {
            background: #374151;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
        }

        /* 视图切换按钮 */
        .view-toggle {
            display: flex;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: white;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn.active {
            background: #1f2937;
            color: white;
        }

        .view-btn:hover:not(.active) {
            background: #f9fafb;
        }

        /* 表格样式 - 统一设计规范 */
        .guide-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .guide-table th,
        .guide-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.5;
        }

        .guide-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1f2937;
            font-size: 12px;
        }

        .guide-table td {
            font-size: 12px;
            color: #1f2937;
        }

        .guide-table tbody tr:hover {
            background: #f9fafb;
            transition: background-color 0.2s ease;
        }

        /* 阶段徽章 */
        .phase-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .phase-badge.phase-foundation {
            background: #fef3c7;
            color: #d97706;
        }

        .phase-badge.phase-wiring {
            background: #f3f4f6;
            color: #374151;
        }

        .phase-badge.phase-installation {
            background: #f9fafb;
            color: #1f2937;
        }

        .phase-badge.phase-testing {
            background: #f9fafb;
            color: #1f2937;
        }

        .phase-badge.phase-completion {
            background: #d1fae5;
            color: #059669;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-badge.status-in-progress {
            background: #f3f4f6;
            color: #374151;
        }

        .status-badge.status-completed {
            background: #d1fae5;
            color: #059669;
        }

        /* 小按钮 - 统一设计规范 */
        .btn-sm {
            padding: 6px 12px;
            font-size: 11px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 4px;
            border: 1px solid transparent;
            font-weight: 500;
            line-height: 1.5;
        }

        .btn-sm:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-sm.btn-primary {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .btn-sm.btn-primary:hover {
            background: #374151;
            border-color: #374151;
            box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
        }

        .btn-sm.btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-sm.btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
            color: #1f2937;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 24px;
        }

        /* 内容容器 - 统一设计规范 */
        .content-container {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 - 统一设计规范 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            border: 1px solid transparent;
            line-height: 1.5;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
            box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
            color: #1f2937;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 11px;
        }

        /* 表格容器样式 - 统一设计规范 */
        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background: #ffffff;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #f3f4f6;
            color: #374151;
        }

        /* 卡片网格 - 统一设计规范 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
            border-color: #9ca3af;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: #ffffff;
            margin: 5% auto;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close {
            color: #6b7280;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: color 0.2s;
        }

        .close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .search-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        #viewContent {
            line-height: 1.6;
        }

        #viewContent .detail-item {
            margin-bottom: 16px;
            padding: 12px;
            background: #f9fafb;
            border-radius: 6px;
        }

        #viewContent .detail-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }

        #viewContent .detail-value {
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item active">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">施工指导</h1>
                    <p class="breadcrumb-description">智能家居施工标准流程与质量控制指南</p>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <div class="construction-container">
                <div class="guide-header">
                    <h2 class="guide-title">施工指导</h2>
                    <div class="view-toggle">
                        <button class="view-btn" onclick="switchView('list')">列表视图</button>
                        <button class="view-btn active" onclick="switchView('table')">表格视图</button>
                    </div>
                </div>

                <!-- 筛选和搜索区域 -->
                <div class="filter-search-bar">
                    <div class="filter-tabs">
                        <div class="tab active" onclick="switchTable('guide')">施工指导</div>
                        <div class="tab" onclick="switchTable('resources')">施工指导资源</div>
                        <div class="tab" onclick="switchTable('checklist')">质量检查清单</div>
                    </div>
                    <div class="search-actions">
                        <div class="search-box">
                            <input type="text" placeholder="搜索内容..." id="searchInput" onkeyup="searchContent()">
                            <button class="search-btn" onclick="searchContent()">搜索</button>
                        </div>
                        <button class="btn btn-primary" onclick="openAddModal()" id="addButton">
                            <i class="fas fa-plus"></i>
                            新增施工指导
                        </button>
                    </div>
                </div>

                <!-- 施工指导表格 -->
                <div class="table-container" id="guide-table">
                    <table class="guide-table">
                        <thead>
                            <tr>
                                <th>施工阶段</th>
                                <th>主要内容</th>
                                <th>关键步骤</th>
                                <th>状态</th>
                                <th>负责人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="phase-badge phase-foundation">交底阶段</span></td>
                                <td>项目启动与技术交底</td>
                                <td>现场勘察、设计确认、计划制定</td>
                                <td><span class="status-badge status-completed">已完成</span></td>
                                <td>项目经理</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="1">查看详情</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="1">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="1">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="phase-badge phase-wiring">水电阶段</span></td>
                                <td>水电线路布置与预埋</td>
                                <td>线路规划、管线预埋、配电安装</td>
                                <td><span class="status-badge status-in-progress">进行中</span></td>
                                <td>水电工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="2">查看详情</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="2">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="2">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="phase-badge phase-installation">安装阶段</span></td>
                                <td>智能设备安装与配置</td>
                                <td>设备安装、系统配置、功能测试</td>
                                <td><span class="status-badge status-pending">待开始</span></td>
                                <td>安装工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="3">查看详情</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="3">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="3">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="phase-badge phase-testing">调试阶段</span></td>
                                <td>系统联调与功能验证</td>
                                <td>设备联调、场景测试、性能优化</td>
                                <td><span class="status-badge status-pending">待开始</span></td>
                                <td>调试工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="4">查看详情</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="4">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="4">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="phase-badge phase-completion">完工阶段</span></td>
                                <td>项目验收与交付</td>
                                <td>系统验收、用户培训、售后交接</td>
                                <td><span class="status-badge status-pending">待开始</span></td>
                                <td>项目经理</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="5">查看详情</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="5">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="5">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 施工指导资源表格 -->
                <div class="table-container" id="resources-table" style="display: none;">
                    <table class="guide-table">
                        <thead>
                            <tr>
                                <th>资源类型</th>
                                <th>资源名称</th>
                                <th>描述</th>
                                <th>格式</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>📖 手册</td>
                                <td>施工标准手册</td>
                                <td>详细的施工规范和标准流程</td>
                                <td>PDF</td>
                                <td>2025-01-20</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="download-resource" data-resource-id="1">下载</button>
                                    <button class="btn-sm btn-secondary" data-action="preview-resource" data-resource-id="1">预览</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🎥 视频</td>
                                <td>视频教程库</td>
                                <td>各阶段施工操作视频指导</td>
                                <td>MP4</td>
                                <td>2025-01-18</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="play-video" data-resource-id="2">播放</button>
                                    <button class="btn-sm btn-secondary" data-action="favorite-resource" data-resource-id="2">收藏</button>
                                </td>
                            </tr>
                            <tr>
                                <td>📋 清单</td>
                                <td>检查清单</td>
                                <td>各阶段质量检查标准清单</td>
                                <td>Excel</td>
                                <td>2025-01-15</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="download-resource" data-resource-id="3">下载</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-resource" data-resource-id="3">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🔧 工具</td>
                                <td>工具清单</td>
                                <td>施工所需工具和设备清单</td>
                                <td>Excel</td>
                                <td>2025-01-12</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="download-resource" data-resource-id="4">下载</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-resource" data-resource-id="4">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>⚠️ 安全</td>
                                <td>安全规范</td>
                                <td>施工安全注意事项和规范</td>
                                <td>PDF</td>
                                <td>2025-01-10</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="download-resource" data-resource-id="5">下载</button>
                                    <button class="btn-sm btn-secondary" data-action="preview-resource" data-resource-id="5">预览</button>
                                </td>
                            </tr>
                            <tr>
                                <td>📞 支持</td>
                                <td>技术支持</td>
                                <td>24小时技术支持热线服务</td>
                                <td>联系方式</td>
                                <td>2025-01-01</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="contact-support" data-resource-id="6">联系</button>
                                    <button class="btn-sm btn-secondary" data-action="view-details" data-resource-id="6">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 质量检查清单表格 -->
                <div class="table-container" id="checklist-table" style="display: none;">
                    <table class="guide-table">
                        <thead>
                            <tr>
                                <th>检查类别</th>
                                <th>检查项目</th>
                                <th>检查标准</th>
                                <th>检查状态</th>
                                <th>检查人员</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>🔌 电气安装</td>
                                <td>线路连接牢固可靠</td>
                                <td>所有电气连接点牢固，无松动</td>
                                <td><span class="status-badge status-completed">已检查</span></td>
                                <td>电气工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-checklist" data-checklist-id="1">查看</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-checklist" data-checklist-id="1">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🔌 电气安装</td>
                                <td>绝缘措施符合规范</td>
                                <td>绝缘电阻≥0.5MΩ</td>
                                <td><span class="status-badge status-completed">已检查</span></td>
                                <td>电气工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-checklist" data-checklist-id="2">查看</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-checklist" data-checklist-id="2">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🔌 电气安装</td>
                                <td>接地保护完整</td>
                                <td>接地电阻≤4Ω</td>
                                <td><span class="status-badge status-in-progress">检查中</span></td>
                                <td>电气工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-checklist" data-checklist-id="3">查看</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-checklist" data-checklist-id="3">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🔌 电气安装</td>
                                <td>配电箱标识清晰</td>
                                <td>所有回路标识清楚准确</td>
                                <td><span class="status-badge status-pending">待检查</span></td>
                                <td>电气工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-checklist" data-checklist-id="4">查看</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-checklist" data-checklist-id="4">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>🔌 电气安装</td>
                                <td>电气测试合格</td>
                                <td>通电测试无异常</td>
                                <td><span class="status-badge status-pending">待检查</span></td>
                                <td>电气工程师</td>
                                <td>
                                    <button class="btn-sm btn-primary" data-action="view-checklist" data-checklist-id="5">查看</button>
                                    <button class="btn-sm btn-secondary" data-action="edit-checklist" data-checklist-id="5">编辑</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                </div>


            </div>
        </main>
    </div>

    <!-- 新增/编辑施工指导模态框 -->
    <div id="guideModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增施工指导</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="guideForm">
                    <div class="form-group">
                        <label for="guidePhase">施工阶段 *</label>
                        <select id="guidePhase" required>
                            <option value="">请选择施工阶段</option>
                            <option value="foundation">交底阶段</option>
                            <option value="wiring">水电阶段</option>
                            <option value="installation">安装阶段</option>
                            <option value="testing">调试阶段</option>
                            <option value="completion">完工阶段</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="guideContent">主要内容 *</label>
                        <input type="text" id="guideContent" required placeholder="请输入主要内容">
                    </div>
                    <div class="form-group">
                        <label for="guideSteps">关键步骤 *</label>
                        <textarea id="guideSteps" required placeholder="请输入关键步骤，多个步骤用逗号分隔"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="guideStatus">状态 *</label>
                        <select id="guideStatus" required>
                            <option value="">请选择状态</option>
                            <option value="pending">待开始</option>
                            <option value="in-progress">进行中</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="guideResponsible">负责人 *</label>
                        <input type="text" id="guideResponsible" required placeholder="请输入负责人姓名">
                    </div>
                    <div class="form-group">
                        <label for="guideDescription">详细描述</label>
                        <textarea id="guideDescription" placeholder="请输入详细描述（可选）"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveGuide()">保存</button>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div id="viewModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>施工指导详情</h3>
                <span class="close" onclick="closeViewModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="viewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeViewModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // ==================== 数据管理 ====================

        // 施工指导数据存储
        let constructionGuides = JSON.parse(localStorage.getItem('constructionGuides') || '[]');
        let currentEditingId = null;
        let currentTableType = 'guide';

        // 初始化默认数据
        if (constructionGuides.length === 0) {
            constructionGuides = [
                {
                    id: 1,
                    phase: 'foundation',
                    content: '项目启动与技术交底',
                    steps: '现场勘察、设计确认、计划制定',
                    status: 'completed',
                    responsible: '项目经理',
                    description: '项目启动阶段的详细技术交底工作',
                    createTime: new Date().toISOString()
                },
                {
                    id: 2,
                    phase: 'wiring',
                    content: '水电线路布置与预埋',
                    steps: '线路规划、管线预埋、配电安装',
                    status: 'in-progress',
                    responsible: '水电工程师',
                    description: '水电安装阶段的详细施工指导',
                    createTime: new Date().toISOString()
                },
                {
                    id: 3,
                    phase: 'installation',
                    content: '智能设备安装与配置',
                    steps: '设备安装、系统配置、功能测试',
                    status: 'pending',
                    responsible: '安装工程师',
                    description: '智能设备安装阶段的详细指导',
                    createTime: new Date().toISOString()
                },
                {
                    id: 4,
                    phase: 'testing',
                    content: '系统联调与功能验证',
                    steps: '设备联调、场景测试、性能优化',
                    status: 'pending',
                    responsible: '调试工程师',
                    description: '系统调试阶段的详细指导',
                    createTime: new Date().toISOString()
                },
                {
                    id: 5,
                    phase: 'completion',
                    content: '项目验收与交付',
                    steps: '系统验收、用户培训、售后交接',
                    status: 'pending',
                    responsible: '项目经理',
                    description: '项目验收阶段的详细指导',
                    createTime: new Date().toISOString()
                }
            ];
            saveData();
        }

        // 保存数据到localStorage
        function saveData() {
            localStorage.setItem('constructionGuides', JSON.stringify(constructionGuides));
        }

        // 获取下一个ID
        function getNextId() {
            return constructionGuides.length > 0 ? Math.max(...constructionGuides.map(g => g.id)) + 1 : 1;
        }

        // ==================== 界面渲染 ====================

        // 渲染施工指导表格
        function renderGuideTable() {
            const tbody = document.querySelector('#guide-table tbody');
            if (!tbody) return;

            tbody.innerHTML = constructionGuides.map(guide => `
                <tr>
                    <td><span class="phase-badge phase-${guide.phase}">${getPhaseText(guide.phase)}</span></td>
                    <td>${guide.content}</td>
                    <td>${guide.steps}</td>
                    <td><span class="status-badge status-${guide.status}">${getStatusText(guide.status)}</span></td>
                    <td>${guide.responsible}</td>
                    <td>
                        <button class="btn-sm btn-primary" data-action="view-guide" data-guide-id="${guide.id}">查看详情</button>
                        <button class="btn-sm btn-secondary" data-action="edit-guide" data-guide-id="${guide.id}">编辑</button>
                        <button class="btn-sm btn-danger" data-action="delete-guide" data-guide-id="${guide.id}">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取阶段文本
        function getPhaseText(phase) {
            const phaseMap = {
                'foundation': '交底阶段',
                'wiring': '水电阶段',
                'installation': '安装阶段',
                'testing': '调试阶段',
                'completion': '完工阶段'
            };
            return phaseMap[phase] || phase;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待开始',
                'in-progress': '进行中',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }

        // ==================== 模态框管理 ====================

        // 打开新增模态框
        function openAddModal() {
            currentEditingId = null;
            document.getElementById('modalTitle').textContent = '新增施工指导';
            document.getElementById('guideForm').reset();
            document.getElementById('guideModal').style.display = 'block';
        }

        // 打开编辑模态框
        function openEditModal(id) {
            const guide = constructionGuides.find(g => g.id === parseInt(id));
            if (!guide) {
                showToast('施工指导不存在', 'error');
                return;
            }

            currentEditingId = id;
            document.getElementById('modalTitle').textContent = '编辑施工指导';

            // 填充表单数据
            document.getElementById('guidePhase').value = guide.phase;
            document.getElementById('guideContent').value = guide.content;
            document.getElementById('guideSteps').value = guide.steps;
            document.getElementById('guideStatus').value = guide.status;
            document.getElementById('guideResponsible').value = guide.responsible;
            document.getElementById('guideDescription').value = guide.description || '';

            document.getElementById('guideModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('guideModal').style.display = 'none';
            currentEditingId = null;
        }

        // 关闭查看模态框
        function closeViewModal() {
            document.getElementById('viewModal').style.display = 'none';
        }

        // ==================== CRUD操作 ====================

        // 保存施工指导
        function saveGuide() {
            const form = document.getElementById('guideForm');

            // 表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = {
                phase: document.getElementById('guidePhase').value,
                content: document.getElementById('guideContent').value,
                steps: document.getElementById('guideSteps').value,
                status: document.getElementById('guideStatus').value,
                responsible: document.getElementById('guideResponsible').value,
                description: document.getElementById('guideDescription').value
            };

            if (currentEditingId) {
                // 编辑模式
                const index = constructionGuides.findIndex(g => g.id === parseInt(currentEditingId));
                if (index !== -1) {
                    constructionGuides[index] = {
                        ...constructionGuides[index],
                        ...formData,
                        updateTime: new Date().toISOString()
                    };
                    showToast('施工指导更新成功', 'success');
                }
            } else {
                // 新增模式
                const newGuide = {
                    id: getNextId(),
                    ...formData,
                    createTime: new Date().toISOString()
                };
                constructionGuides.push(newGuide);
                showToast('施工指导添加成功', 'success');
            }

            saveData();
            renderGuideTable();
            closeModal();
        }

        // 查看施工指导详情
        function viewGuide(id) {
            const guide = constructionGuides.find(g => g.id === parseInt(id));
            if (!guide) {
                showToast('施工指导不存在', 'error');
                return;
            }

            const content = `
                <div class="detail-item">
                    <div class="detail-label">施工阶段</div>
                    <div class="detail-value">${getPhaseText(guide.phase)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">主要内容</div>
                    <div class="detail-value">${guide.content}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">关键步骤</div>
                    <div class="detail-value">${guide.steps}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">${getStatusText(guide.status)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">负责人</div>
                    <div class="detail-value">${guide.responsible}</div>
                </div>
                ${guide.description ? `
                <div class="detail-item">
                    <div class="detail-label">详细描述</div>
                    <div class="detail-value">${guide.description}</div>
                </div>
                ` : ''}
                <div class="detail-item">
                    <div class="detail-label">创建时间</div>
                    <div class="detail-value">${new Date(guide.createTime).toLocaleString()}</div>
                </div>
                ${guide.updateTime ? `
                <div class="detail-item">
                    <div class="detail-label">更新时间</div>
                    <div class="detail-value">${new Date(guide.updateTime).toLocaleString()}</div>
                </div>
                ` : ''}
            `;

            document.getElementById('viewContent').innerHTML = content;
            document.getElementById('viewModal').style.display = 'block';
        }

        // 删除施工指导
        function deleteGuide(id) {
            const guide = constructionGuides.find(g => g.id === parseInt(id));
            if (!guide) {
                showToast('施工指导不存在', 'error');
                return;
            }

            if (confirm(`确定要删除施工指导"${guide.content}"吗？此操作不可恢复。`)) {
                constructionGuides = constructionGuides.filter(g => g.id !== parseInt(id));
                saveData();
                renderGuideTable();
                showToast('施工指导删除成功', 'success');
            }
        }

        // ==================== 表格切换功能 ====================

        function switchTable(tableType) {
            // 隐藏所有表格
            const guideTable = document.getElementById('guide-table');
            const resourcesTable = document.getElementById('resources-table');
            const checklistTable = document.getElementById('checklist-table');

            guideTable.style.display = 'none';
            resourcesTable.style.display = 'none';
            checklistTable.style.display = 'none';

            // 更新标签状态
            const tabs = document.querySelectorAll('.filter-tabs .tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // 显示对应表格并更新按钮文本
            const addButton = document.getElementById('addButton');
            if (tableType === 'guide') {
                guideTable.style.display = 'block';
                addButton.innerHTML = '<i class="fas fa-plus"></i> 新增施工指导';
                addButton.onclick = openAddModal;
            } else if (tableType === 'resources') {
                resourcesTable.style.display = 'block';
                addButton.innerHTML = '<i class="fas fa-plus"></i> 新增资源';
                addButton.onclick = () => showToast('资源管理功能开发中...', 'info');
            } else if (tableType === 'checklist') {
                checklistTable.style.display = 'block';
                addButton.innerHTML = '<i class="fas fa-plus"></i> 新增检查项';
                addButton.onclick = () => showToast('检查清单功能开发中...', 'info');
            }

            currentTableType = tableType;
        }

        // 搜索功能
        function searchContent() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            if (currentTableType === 'guide') {
                const rows = document.querySelectorAll('#guide-table tbody tr');
                rows.forEach(row => {
                    const rowText = row.textContent.toLowerCase();
                    row.style.display = rowText.includes(searchTerm) ? 'table-row' : 'none';
                });
            }
            // 其他表格的搜索功能可以后续添加
        }

        // ==================== 视图切换功能 ====================

        function switchView(viewType) {
            const listBtn = document.querySelector('.view-btn:first-child');
            const tableBtn = document.querySelector('.view-btn:last-child');

            // 更新按钮状态
            listBtn.classList.remove('active');
            tableBtn.classList.remove('active');

            if (viewType === 'list') {
                listBtn.classList.add('active');
                showToast('切换到列表视图', 'info');
            } else {
                tableBtn.classList.add('active');
                showToast('切换到表格视图', 'info');
            }
        }

        // ==================== 工具函数 ====================

        // Toast 提示功能
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // ==================== 事件委托系统 ====================

        // 初始化事件委托
        function initializeEventDelegation() {
            document.addEventListener('click', function(event) {
                const target = event.target;

                if (target.hasAttribute('data-action')) {
                    const action = target.getAttribute('data-action');
                    const guideId = target.getAttribute('data-guide-id');
                    const resourceId = target.getAttribute('data-resource-id');
                    const checklistId = target.getAttribute('data-checklist-id');

                    switch (action) {
                        // 施工指导操作
                        case 'view-guide':
                            viewGuide(guideId);
                            break;
                        case 'edit-guide':
                            openEditModal(guideId);
                            break;
                        case 'delete-guide':
                            deleteGuide(guideId);
                            break;

                        // 资源操作（暂时保留原有功能）
                        case 'download-resource':
                            downloadResource(resourceId);
                            break;
                        case 'preview-resource':
                            previewResource(resourceId);
                            break;
                        case 'play-video':
                            playVideo(resourceId);
                            break;
                        case 'favorite-resource':
                            favoriteResource(resourceId);
                            break;
                        case 'edit-resource':
                            editResource(resourceId);
                            break;
                        case 'contact-support':
                            contactSupport(resourceId);
                            break;
                        case 'view-details':
                            viewDetails(resourceId);
                            break;

                        // 检查清单操作（暂时保留原有功能）
                        case 'view-checklist':
                            viewChecklist(checklistId);
                            break;
                        case 'edit-checklist':
                            editChecklist(checklistId);
                            break;
                    }
                }
            });

            // 模态框外部点击关闭
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    closeModal();
                    closeViewModal();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeModal();
                    closeViewModal();
                }
            });
        }

        // ==================== 资源管理功能（占位符实现）====================

        function downloadResource(resourceId) {
            const resources = {
                1: { name: '施工图纸.dwg', size: '2.5MB' },
                2: { name: '安装视频.mp4', size: '15.2MB' },
                3: { name: '操作手册.pdf', size: '1.8MB' },
                4: { name: '技术规范.pdf', size: '3.2MB' },
                5: { name: '安全规范.pdf', size: '2.1MB' },
                6: { name: '技术支持', size: '联系方式' }
            };

            const resource = resources[resourceId];
            if (resource) {
                showToast(`开始下载: ${resource.name} (${resource.size})`, 'success');
                setTimeout(() => {
                    showToast(`下载完成: ${resource.name}`, 'success');
                }, 2000);
            } else {
                showToast('资源不存在', 'error');
            }
        }

        function previewResource(resourceId) {
            showToast('预览功能开发中...', 'info');
        }

        function playVideo(resourceId) {
            showToast('视频播放功能开发中...', 'info');
        }

        function favoriteResource(resourceId) {
            showToast('收藏功能开发中...', 'info');
        }

        function editResource(resourceId) {
            showToast('资源编辑功能开发中...', 'info');
        }

        function contactSupport(resourceId) {
            showToast('技术支持热线: 400-123-4567', 'success');
        }

        function viewDetails(resourceId) {
            showToast('详情查看功能开发中...', 'info');
        }

        // ==================== 质量检查功能（占位符实现）====================

        function viewChecklist(checklistId) {
            showToast('检查清单查看功能开发中...', 'info');
        }

        function editChecklist(checklistId) {
            showToast('检查清单编辑功能开发中...', 'info');
        }

        // ==================== 事件委托系统 ====================

        // 初始化事件委托
        function initializeEventDelegation() {
            document.addEventListener('click', function(event) {
                const target = event.target;

                if (target.hasAttribute('data-action')) {
                    const action = target.getAttribute('data-action');
                    const guideId = target.getAttribute('data-guide-id');
                    const resourceId = target.getAttribute('data-resource-id');
                    const checklistId = target.getAttribute('data-checklist-id');

                    switch (action) {
                        // 施工指导操作
                        case 'view-guide':
                            viewGuide(guideId);
                            break;
                        case 'edit-guide':
                            editGuide(guideId);
                            break;

                        // 资源操作
                        case 'download-resource':
                            downloadResource(resourceId);
                            break;
                        case 'preview-resource':
                            previewResource(resourceId);
                            break;
                        case 'play-video':
                            playVideo(resourceId);
                            break;
                        case 'favorite-resource':
                            favoriteResource(resourceId);
                            break;
                        case 'edit-resource':
                            editResource(resourceId);
                            break;
                        case 'contact-support':
                            contactSupport(resourceId);
                            break;
                        case 'view-details':
                            viewDetails(resourceId);
                            break;

                        // 检查清单操作
                        case 'view-checklist':
                            viewChecklist(checklistId);
                            break;
                        case 'edit-checklist':
                            editChecklist(checklistId);
                            break;
                    }
                }
            });
        }

        // ==================== 页面初始化 ====================

        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面的导航高亮
            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPage) {
                    item.classList.add('active');
                }
            });

            // 初始化页面
            renderGuideTable();
            switchTable('guide');
            initializeEventDelegation();

            // 显示初始化完成提示
            showToast('施工指导管理系统已就绪', 'success');
        });
    </script>
</body>
</html>

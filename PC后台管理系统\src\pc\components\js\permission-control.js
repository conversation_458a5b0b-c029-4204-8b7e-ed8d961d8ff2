/**
 * 权限控制模块
 * 用于在各个页面中实际应用权限控制
 * 版本: v2.0
 * 创建时间: 2025-07-18
 */

class PermissionController {
    constructor() {
        this.userPermissions = this.loadUserPermissions();
        this.userRole = this.loadUserRole();
        this.init();
    }

    /**
     * 初始化权限控制
     */
    init() {
        console.log('🔐 权限控制模块已初始化');
        console.log('👤 当前用户角色:', this.userRole);
        console.log('🔑 当前用户权限:', this.userPermissions);
        
        // 页面加载完成后应用权限控制
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.applyPermissionControl());
        } else {
            this.applyPermissionControl();
        }
    }

    /**
     * 加载用户权限
     */
    loadUserPermissions() {
        const permissions = localStorage.getItem('user_permissions');
        return permissions ? JSON.parse(permissions) : this.getDefaultPermissions();
    }

    /**
     * 加载用户角色
     */
    loadUserRole() {
        const user = localStorage.getItem('current_user');
        if (user) {
            const userData = JSON.parse(user);
            return userData.role || 'owner';
        }
        return 'owner'; // 默认角色
    }

    /**
     * 获取默认权限（基于角色）
     */
    getDefaultPermissions() {
        const rolePermissions = {
            'owner': [
                'project:view', 'project:edit', 'project:comment', 'project:invite', 
                'project:manage_members', 'project:config',
                'design:view', 'design:comment', 'design:approve', 'design:delete',
                'construction:view', 'construction:quality', 'construction:feedback',
                'product:view', 'order:view', 'order:create', 'order:edit', 'order:approve', 'order:cancel',
                'user:view', 'user:create', 'user:edit', 'user:delete', 'user:reset_password'
            ],
            'project_manager': [
                'project:view', 'project:edit', 'project:comment', 'project:invite', 
                'project:manage_members', 'project:config',
                'design:view', 'design:comment', 'design:approve', 'design:delete',
                'construction:view', 'construction:update', 'construction:quality', 'construction:feedback', 'construction:plan',
                'product:view', 'order:view', 'order:create', 'order:edit', 'order:approve', 'order:cancel',
                'user:view', 'user:create', 'user:edit', 'user:reset_password'
            ],
            'designer': [
                'project:view', 'project:comment',
                'design:view', 'design:create', 'design:edit', 'design:comment', 'design:delete',
                'construction:view', 'construction:feedback',
                'product:view', 'order:view'
            ],
            'construction_lead': [
                'project:view', 'project:comment',
                'design:view', 'design:comment',
                'construction:view', 'construction:update', 'construction:quality', 'construction:feedback', 'construction:plan',
                'product:view', 'order:view'
            ],
            'smart_home_designer': [
                'project:view', 'project:comment',
                'design:view', 'design:create', 'design:edit', 'design:comment', 'design:delete',
                'construction:view', 'construction:feedback',
                'product:view', 'order:view'
            ],
            'sales': [
                'project:view', 'project:comment',
                'design:view', 'design:comment',
                'construction:view',
                'product:view', 'product:create', 'product:edit', 'product:price', 'product:inventory',
                'order:view', 'order:create', 'order:edit', 'order:cancel'
            ]
        };
        
        return rolePermissions[this.userRole] || rolePermissions['owner'];
    }

    /**
     * 检查是否有指定权限
     */
    hasPermission(permission) {
        return this.userPermissions.includes(permission);
    }

    /**
     * 检查是否有任一权限
     */
    hasAnyPermission(permissions) {
        return permissions.some(permission => this.hasPermission(permission));
    }

    /**
     * 检查是否有所有权限
     */
    hasAllPermissions(permissions) {
        return permissions.every(permission => this.hasPermission(permission));
    }

    /**
     * 应用权限控制到页面元素
     */
    applyPermissionControl() {
        // 设计管理页面权限控制
        this.controlDesignManagement();
        
        // 项目管理页面权限控制
        this.controlProjectManagement();
        
        // 施工管理页面权限控制
        this.controlConstructionManagement();
        
        // 商品管理页面权限控制
        this.controlProductManagement();
        
        // 订单管理页面权限控制
        this.controlOrderManagement();
        
        // 用户管理页面权限控制
        this.controlUserManagement();
        
        console.log('✅ 权限控制已应用到页面元素');
    }

    /**
     * 设计管理页面权限控制
     */
    controlDesignManagement() {
        // 新增设计按钮
        this.toggleElement('#addDesignBtn', 'design:create');
        this.toggleElement('.add-design-btn', 'design:create');
        
        // 编辑按钮
        this.toggleElements('.edit-btn', 'design:edit');
        this.toggleElements('.design-edit', 'design:edit');
        
        // 删除按钮
        this.toggleElements('.delete-btn', 'design:delete');
        this.toggleElements('.design-delete', 'design:delete');
        
        // 审批按钮
        this.toggleElements('.approve-btn', 'design:approve');
        this.toggleElements('.design-approve', 'design:approve');
        
        // 文件上传
        this.toggleElement('#fileUpload', 'design:create');
        this.toggleElement('.file-upload', 'design:create');
    }

    /**
     * 项目管理页面权限控制
     */
    controlProjectManagement() {
        // 新建项目按钮
        this.toggleElement('#createProjectBtn', 'project:edit');
        this.toggleElement('.create-project-btn', 'project:edit');
        
        // 邀请成员按钮
        this.toggleElement('#inviteMemberBtn', 'project:invite');
        this.toggleElement('.invite-member-btn', 'project:invite');
        
        // 项目设置按钮
        this.toggleElement('#projectSettingsBtn', 'project:config');
        this.toggleElement('.project-settings-btn', 'project:config');
        
        // 成员管理面板
        this.toggleElement('.member-management', 'project:manage_members');
        this.toggleElement('#memberManagement', 'project:manage_members');
    }

    /**
     * 施工管理页面权限控制
     */
    controlConstructionManagement() {
        // 更新进度按钮
        this.toggleElement('#updateProgressBtn', 'construction:update');
        this.toggleElement('.update-progress-btn', 'construction:update');
        
        // 质量检查按钮
        this.toggleElement('#qualityCheckBtn', 'construction:quality');
        this.toggleElement('.quality-check-btn', 'construction:quality');
        
        // 施工计划面板
        this.toggleElement('.construction-plan', 'construction:plan');
        this.toggleElement('#constructionPlan', 'construction:plan');
    }

    /**
     * 商品管理页面权限控制
     */
    controlProductManagement() {
        // 添加商品按钮
        this.toggleElement('#addProductBtn', 'product:create');
        this.toggleElement('.add-product-btn', 'product:create');
        
        // 编辑商品按钮
        this.toggleElements('.product-edit-btn', 'product:edit');
        
        // 价格管理
        this.toggleElements('.price-management', 'product:price');
        
        // 库存管理
        this.toggleElements('.inventory-management', 'product:inventory');
    }

    /**
     * 订单管理页面权限控制
     */
    controlOrderManagement() {
        // 创建订单按钮
        this.toggleElement('#createOrderBtn', 'order:create');
        this.toggleElement('.create-order-btn', 'order:create');
        
        // 编辑订单按钮
        this.toggleElements('.order-edit-btn', 'order:edit');
        
        // 审批订单按钮
        this.toggleElements('.order-approve-btn', 'order:approve');
        
        // 取消订单按钮
        this.toggleElements('.order-cancel-btn', 'order:cancel');
    }

    /**
     * 用户管理页面权限控制
     */
    controlUserManagement() {
        // 添加用户按钮
        this.toggleElement('#addUserBtn', 'user:create');
        this.toggleElement('.add-user-btn', 'user:create');
        
        // 编辑用户按钮
        this.toggleElements('.user-edit-btn', 'user:edit');
        
        // 删除用户按钮
        this.toggleElements('.user-delete-btn', 'user:delete');
        
        // 重置密码按钮
        this.toggleElements('.reset-password-btn', 'user:reset_password');
    }

    /**
     * 控制单个元素显示/隐藏
     */
    toggleElement(selector, permission) {
        const element = document.querySelector(selector);
        if (element) {
            if (this.hasPermission(permission)) {
                element.style.display = '';
                element.removeAttribute('disabled');
            } else {
                element.style.display = 'none';
            }
        }
    }

    /**
     * 控制多个元素显示/隐藏
     */
    toggleElements(selector, permission) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (this.hasPermission(permission)) {
                element.style.display = '';
                element.removeAttribute('disabled');
            } else {
                element.style.display = 'none';
            }
        });
    }

    /**
     * 更新用户权限
     */
    updatePermissions(newPermissions) {
        this.userPermissions = newPermissions;
        localStorage.setItem('user_permissions', JSON.stringify(newPermissions));
        this.applyPermissionControl();
        console.log('🔄 用户权限已更新:', newPermissions);
    }

    /**
     * 获取当前用户权限
     */
    getCurrentPermissions() {
        return [...this.userPermissions];
    }

    /**
     * 获取当前用户角色
     */
    getCurrentRole() {
        return this.userRole;
    }
}

// 创建全局权限控制实例
if (typeof window !== 'undefined') {
    window.permissionController = new PermissionController();
}

// 导出权限控制类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PermissionController;
}

console.log('✅ 权限控制模块已加载');

/**
 * 产品素材管理系统快速测试
 * 验证上传功能和同步显示
 */

const ProductMaterialsIntegrationTest = require('./product-materials-integration-test.js');

async function runQuickTest() {
    console.log('🚀 开始产品素材管理系统快速测试...\n');
    
    const test = new ProductMaterialsIntegrationTest();
    
    try {
        // 1. 测试单文件上传
        console.log('📋 测试1: 单文件上传');
        const singleUploadResult = await test.testSingleFileUpload();
        console.log(`${singleUploadResult.success ? '✅' : '❌'} 单文件上传: ${singleUploadResult.message}`);
        
        // 2. 测试上传后同步显示
        console.log('\n📋 测试2: 上传后同步显示');
        const syncDisplayResult = await test.testSyncDisplay();
        console.log(`${syncDisplayResult.success ? '✅' : '❌'} 同步显示: ${syncDisplayResult.message}`);
        
        // 3. 测试批量上传
        console.log('\n📋 测试3: 批量上传');
        const batchUploadResult = await test.testBatchUpload();
        console.log(`${batchUploadResult.success ? '✅' : '❌'} 批量上传: ${batchUploadResult.message}`);
        
        // 4. 测试品牌结构导入
        console.log('\n📋 测试4: 品牌结构导入');
        const brandImportResult = await test.testBrandStructureImport();
        console.log(`${brandImportResult.success ? '✅' : '❌'} 品牌结构导入: ${brandImportResult.message}`);
        
        // 5. 测试素材筛选
        console.log('\n📋 测试5: 素材筛选功能');
        const filterResult = await test.testMaterialsFilter();
        console.log(`${filterResult.success ? '✅' : '❌'} 素材筛选: ${filterResult.message}`);
        
        // 6. 测试性能
        console.log('\n📋 测试6: 性能测试');
        const performanceResult = await test.testPerformance();
        console.log(`${performanceResult.success ? '✅' : '❌'} 性能测试: ${performanceResult.message}`);
        
        // 生成测试报告
        console.log('\n📊 测试结果总结:');
        console.log('='.repeat(50));
        
        const allResults = [
            singleUploadResult,
            syncDisplayResult,
            batchUploadResult,
            brandImportResult,
            filterResult,
            performanceResult
        ];
        
        const passedCount = allResults.filter(r => r.success).length;
        const totalCount = allResults.length;
        const successRate = (passedCount / totalCount * 100).toFixed(1);
        
        console.log(`✅ 通过: ${passedCount}/${totalCount} (${successRate}%)`);
        console.log(`❌ 失败: ${totalCount - passedCount}/${totalCount}`);
        
        if (passedCount === totalCount) {
            console.log('\n🎉 所有测试通过！');
            console.log('✅ PC端上传功能正常');
            console.log('✅ 同步显示功能正常');
            console.log('✅ 批量上传功能正常');
            console.log('✅ 品牌结构导入功能正常');
            console.log('✅ 素材筛选功能正常');
            console.log('✅ 性能表现良好');
        } else {
            console.log('\n⚠️ 部分测试失败，需要检查相关功能');
        }
        
        // 系统访问信息
        console.log('\n🔗 系统访问地址:');
        console.log('产品素材管理页面: http://localhost:3000/src/pc/components/pages/product-materials.html');
        console.log('测试页面: http://localhost:3000/test/run-tests.html');
        console.log('简单测试页面: http://localhost:3000/test/upload-test.html');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 运行测试
runQuickTest();
/**
 * 商品管理主应用
 * 整合所有组件，实现完整的商品管理功能
 */

class ProductsApp {
    constructor() {
        this.api = null;
        this.filters = null;
        this.table = null;
        this.modal = null;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.currentFilters = {};
        
        this.init();
    }

    async init() {
        try {
            // 显示加载状态
            Utils.loadingManager.show('global', '初始化商品管理系统...');
            
            // 初始化API客户端
            this.initAPI();
            
            // 初始化组件
            await this.initComponents();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 加载初始数据
            await this.loadProducts();
            
            Utils.loadingManager.hide('global');
            Utils.showToast('商品管理系统初始化成功', 'success');
            
        } catch (error) {
            console.error('初始化失败:', error);
            Utils.loadingManager.hide('global');
            Utils.showToast('系统初始化失败: ' + error.message, 'error');
        }
    }

    initAPI() {
        // 从环境变量或配置中获取API配置
        const apiConfig = {
            baseURL: window.API_BASE_URL || '/api/admin',
            medusaURL: window.MEDUSA_API_URL || '/api/medusa',
            timeout: 10000,
            retryCount: 3
        };
        
        this.api = new ProductsAPI(apiConfig);
    }

    async initComponents() {
        // 初始化筛选器
        const filtersContainer = document.getElementById('filtersContainer');
        this.filters = new ProductFilters(filtersContainer, {
            onFilter: (filters) => this.handleFilter(filters),
            onReset: () => this.handleFilterReset()
        });

        // 初始化表格
        const tableContainer = document.getElementById('tableContainer');
        this.table = new ProductTable(tableContainer, {
            enableVirtualScroll: true,
            onEdit: (id) => this.editProduct(id),
            onView: (id) => this.viewProduct(id),
            onDelete: (id) => this.deleteProduct(id),
            onSelect: (selectedIds) => this.handleSelection(selectedIds),
            onRefresh: () => this.loadProducts(),
            onExport: (data) => this.exportProducts(data),
            onBulkEdit: (ids) => this.bulkEditProducts(ids),
            onBulkDelete: (ids) => this.bulkDeleteProducts(ids)
        });

        // 初始化模态框
        this.modal = new ProductModal({
            onSave: (productData, isEdit) => this.saveProduct(productData, isEdit),
            onCancel: () => this.modal.hide()
        });
    }

    setupEventListeners() {
        // 页面级别的事件监听
        document.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            if (action) {
                this.handlePageAction(action, e);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'n':
                        e.preventDefault();
                        this.addProduct();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.loadProducts();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.exportProducts();
                        break;
                }
            }
        });

        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadProducts();
            }
        });
    }

    async handlePageAction(action, event) {
        try {
            switch (action) {
                case 'add-product':
                    this.addProduct();
                    break;
                case 'sync-medusa':
                    await this.syncToMedusa();
                    break;
                case 'import-data':
                    this.showImportModal();
                    break;
                default:
                    console.log('未知操作:', action);
            }
        } catch (error) {
            console.error('操作失败:', error);
            Utils.showToast('操作失败: ' + error.message, 'error');
        }
    }

    async loadProducts(page = 1) {
        try {
            Utils.loadingManager.show('tableContainer', '加载商品数据...');
            
            const params = {
                page: page,
                limit: this.pageSize,
                ...this.currentFilters
            };

            const response = await this.api.getProducts(params);
            
            if (response.success) {
                this.currentPage = page;
                this.totalCount = response.data.total;
                
                // 更新表格数据
                this.table.setData(response.data.products);
                
                // 更新分页信息
                this.updatePagination();
                
                console.log(`加载了 ${response.data.products.length} 个商品`);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('加载商品失败:', error);
            Utils.showToast('加载商品失败: ' + error.message, 'error');
            
            // 显示错误状态
            this.table.setData([]);
            
        } finally {
            Utils.loadingManager.hide('tableContainer');
        }
    }

    handleFilter(filters) {
        this.currentFilters = filters;
        this.currentPage = 1;
        this.loadProducts();
    }

    handleFilterReset() {
        this.currentFilters = {};
        this.currentPage = 1;
        this.loadProducts();
    }

    handleSelection(selectedIds) {
        console.log('选中的商品:', selectedIds);
        // 可以在这里处理选中状态的变化
    }

    addProduct() {
        this.modal.show();
    }

    async editProduct(id) {
        try {
            Utils.loadingManager.show('global', '加载商品信息...');
            
            const response = await this.api.getProduct(id);
            
            if (response.success) {
                this.modal.show(response.data);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('加载商品信息失败:', error);
            Utils.showToast('加载商品信息失败: ' + error.message, 'error');
        } finally {
            Utils.loadingManager.hide('global');
        }
    }

    async viewProduct(id) {
        // 简化实现，可以扩展为详细的查看页面
        await this.editProduct(id);
    }

    async deleteProduct(id) {
        try {
            const confirmed = await Utils.showConfirm('确定要删除这个商品吗？此操作不可恢复。');
            if (!confirmed) return;
            
            Utils.loadingManager.show('global', '删除商品...');
            
            const response = await this.api.deleteProduct(id);
            
            if (response.success) {
                Utils.showToast('商品删除成功', 'success');
                await this.loadProducts(this.currentPage);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('删除商品失败:', error);
            Utils.showToast('删除商品失败: ' + error.message, 'error');
        } finally {
            Utils.loadingManager.hide('global');
        }
    }

    async saveProduct(productData, isEdit) {
        try {
            Utils.loadingManager.show('global', isEdit ? '更新商品...' : '创建商品...');
            
            let response;
            if (isEdit) {
                response = await this.api.updateProduct(productData.id, productData);
            } else {
                response = await this.api.createProduct(productData);
            }
            
            if (response.success) {
                Utils.showToast(isEdit ? '商品更新成功' : '商品创建成功', 'success');
                this.modal.hide();
                
                // 如果启用了MedusaJS同步
                if (productData.sync_to_medusa) {
                    await this.syncProductToMedusa(response.data.id);
                }
                
                await this.loadProducts(this.currentPage);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('保存商品失败:', error);
            Utils.showToast('保存商品失败: ' + error.message, 'error');
        } finally {
            Utils.loadingManager.hide('global');
        }
    }

    async bulkEditProducts(ids) {
        // 简化实现，可以扩展为批量编辑模态框
        Utils.showToast('批量编辑功能开发中...', 'info');
    }

    async bulkDeleteProducts(ids) {
        try {
            const confirmed = await Utils.showConfirm(`确定要删除选中的 ${ids.length} 个商品吗？此操作不可恢复。`);
            if (!confirmed) return;
            
            Utils.loadingManager.show('global', '批量删除商品...');
            
            const response = await this.api.batchOperation('delete', ids);
            
            if (response.success) {
                Utils.showToast('批量删除成功', 'success');
                await this.loadProducts(this.currentPage);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('批量删除失败:', error);
            Utils.showToast('批量删除失败: ' + error.message, 'error');
        } finally {
            Utils.loadingManager.hide('global');
        }
    }

    async syncToMedusa() {
        try {
            Utils.loadingManager.show('global', '同步到MedusaJS...');
            
            const response = await this.api.syncToMedusa();
            
            if (response.success) {
                Utils.showToast('MedusaJS同步成功', 'success');
                await this.loadProducts(this.currentPage);
            } else {
                throw new Error(response.message);
            }
            
        } catch (error) {
            console.error('MedusaJS同步失败:', error);
            Utils.showToast('MedusaJS同步失败: ' + error.message, 'error');
        } finally {
            Utils.loadingManager.hide('global');
        }
    }

    async syncProductToMedusa(productId) {
        try {
            const response = await this.api.syncToMedusa(productId);
            
            if (response.success) {
                console.log('商品同步到MedusaJS成功');
            } else {
                console.warn('商品同步到MedusaJS失败:', response.message);
            }
            
        } catch (error) {
            console.error('商品同步到MedusaJS失败:', error);
        }
    }

    showImportModal() {
        // 简化实现，可以扩展为完整的导入功能
        Utils.showToast('批量导入功能开发中...', 'info');
    }

    exportProducts(data = null) {
        try {
            const exportData = data || this.table.filteredData || [];
            
            if (exportData.length === 0) {
                Utils.showToast('没有数据可导出', 'warning');
                return;
            }
            
            // 转换数据格式
            const csvData = exportData.map(product => ({
                '商品名称': product.name,
                'SKU': product.sku,
                '品牌': product.brand || '',
                '分类': this.getCategoryName(product.category),
                '价格': product.price,
                '库存': product.stock,
                '销量': product.sales || 0,
                '状态': this.getStatusName(product.status),
                '创建时间': Utils.formatDate(product.created_at, 'YYYY-MM-DD HH:mm:ss')
            }));
            
            // 转换为CSV并下载
            const csv = this.convertToCSV(csvData);
            this.downloadCSV(csv, `商品数据_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
            
            Utils.showToast('数据导出成功', 'success');
            
        } catch (error) {
            console.error('导出失败:', error);
            Utils.showToast('数据导出失败: ' + error.message, 'error');
        }
    }

    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => 
                    `"${String(row[header]).replace(/"/g, '""')}"`
                ).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }

    downloadCSV(csv, filename) {
        const blob = new Blob(['\uFEFF' + csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }

    updatePagination() {
        // 简化的分页实现，可以扩展为完整的分页组件
        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        console.log(`当前页: ${this.currentPage}/${totalPages}, 总计: ${this.totalCount} 条记录`);
    }

    getCategoryName(categoryId) {
        const categoryMap = {
            'switch': '智能开关',
            'lighting': '智能照明',
            'security': '安防设备',
            'sensor': '传感器',
            'environment': '环境控制'
        };
        return categoryMap[categoryId] || categoryId;
    }

    getStatusName(statusId) {
        const statusMap = {
            'active': '上架中',
            'inactive': '已下架',
            'draft': '草稿',
            'out_of_stock': '缺货'
        };
        return statusMap[statusId] || statusId;
    }

    destroy() {
        // 清理资源
        if (this.filters) this.filters.destroy();
        if (this.table) this.table.destroy();
        if (this.modal) this.modal.destroy();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.productsApp = new ProductsApp();
});

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:,"> <!-- 空favicon声明 -->
    <title>后台管理系统登录</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="../../../styles/unified-admin-styles.css">
</head>
<body>
    <div class="admin-layout">
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item active">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">系统登录</h1>
                            <p class="breadcrumb-description">用户登录和身份验证</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <!-- 原页面内容将被包装在这里 -->

    <!-- 登录页面不需要导航栏 -->

    <div class="main-content">
        <div class="login-container">
        <div class="banner">
            <div class="banner-content">
                <div class="plan-badge">Most Popular</div>
                <div class="plan-title">智能设计与施工</div>
                <div class="plan-credits">10,000 项目 per month</div>
                <ul class="plan-features">
                    <li>项目设计管理</li>
                    <li>智能施工监控</li>
                    <li>专业技术支持</li>
                    <li>数据分析报告</li>
                </ul>
            </div>
        </div>
        
        <div class="login-form">
            <div class="form-header">
                <h2>欢迎登录</h2>
                <p>请选择登录方式</p>
            </div>
            
            <div class="tabs">
                <div class="tab active" data-tab="account">账号密码</div>
                <div class="tab" data-tab="phone">手机登录</div>
                <div class="tab" data-tab="qrcode">扫码登录</div>
            </div>
            
            <!-- 账号密码登录 -->
            <div id="account" class="tab-pane active">
                <!-- 错误信息显示区域 -->
                <div id="errorMessage" class="error-message"></div>
                <!-- 成功信息显示区域 -->
                <div id="successMessage" class="success-message"></div>

                <form id="loginForm">
                    <!-- CSRF令牌保护 -->
                    <input type="hidden" id="csrfToken" name="_token" value="">
                    <!-- 安全时间戳 -->
                    <input type="hidden" id="timestamp" name="_timestamp" value="">

                    <div class="form-group">
                        <label for="username">用户名/邮箱</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名或邮箱"
                               autocomplete="username" required maxlength="50"
                               pattern="^[a-zA-Z0-9@._-]+$"
                               title="只允许字母、数字、@、.、_、-字符"
                               data-sanitize="true" data-xss-protection="true">
                        <div class="field-error" id="usernameError"></div>
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <div style="position: relative;">
                            <input type="password" id="password" name="password" placeholder="请输入密码"
                                   autocomplete="current-password" required minlength="6" maxlength="128"
                                   data-sanitize="true" data-xss-protection="true">
                            <span id="passwordToggle" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); cursor: pointer; user-select: none;">👁️</span>
                        </div>
                        <div class="field-error" id="passwordError"></div>
                    </div>
                    <div class="remember-forgot">
                        <div class="remember-me">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">记住用户名</label>
                        </div>
                        <a href="#" id="forgotPassword" class="forgot-password">忘记密码?</a>
                    </div>
                    <button type="submit" id="loginButton" class="login-btn">登录</button>
                </form>
            </div>

            
            <!-- 手机登录 -->
            <div id="phone" class="tab-pane">
                <form id="phone-form">
                    <div class="form-group">
                        <label for="phone">手机号码</label>
                        <input type="tel" id="phone" placeholder="请输入手机号码" autocomplete="tel">
                    </div>
                    <div class="form-group">
                        <label for="sms">短信验证码</label>
                        <div style="display: flex;">
                            <input type="text" id="sms" placeholder="请输入验证码" style="flex: 1; margin-right: 10px;">
                            <button type="button" style="padding: 0 15px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">获取验证码</button>
                        </div>
                    </div>
                    <button type="submit" class="login-btn">登录</button>
                </form>
                <div class="switch-mode">
                    还没有账号? <a href="#" id="switch-to-register-phone">立即注册</a>
                </div>
            </div>

            
            <!-- 扫码登录 -->
            <div id="qrcode" class="tab-pane">
                <div class="qrcode-container">
                    <div class="qrcode">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2" y="2" width="12" height="12" rx="2" fill="#999"/>
                            <rect x="26" y="2" width="12" height="12" rx="2" fill="#999"/>
                            <rect x="2" y="26" width="12" height="12" rx="2" fill="#999"/>
                            <rect x="18" y="18" width="6" height="6" rx="1" fill="#999"/>
                            <rect x="28" y="18" width="4" height="4" rx="1" fill="#999"/>
                            <rect x="18" y="28" width="4" height="4" rx="1" fill="#999"/>
                            <rect x="26" y="26" width="8" height="8" rx="1" fill="#999"/>
                        </svg>
                    </div>
                    <p>使用微信扫码登录</p>
                    <p style="color: #999; font-size: 12px;">打开微信，点击发现，使用扫一扫</p>
                </div>
                <div class="switch-mode">
                    还没有账号? <a href="#" id="switch-to-register-qr">立即注册</a>
                </div>
            </div>
            
            <div class="switch-mode">
                还没有账号? <a href="#" id="switch-to-register">立即注册</a>
            </div>
        </div>
    </div>
    
    <script>
        // 安全配置
        const SECURITY_CONFIG = {
            maxLoginAttempts: 5,
            lockoutDuration: 15 * 60 * 1000, // 15分钟
            csrfTokenExpiry: 30 * 60 * 1000, // 30分钟
            inputSanitization: true
        };

        // 初始化安全机制
        document.addEventListener('DOMContentLoaded', function() {
            initializeCSRFProtection();
            initializeInputSanitization();
            initializeSecurityHeaders();
            initializeRateLimiting();
        });

        // CSRF令牌保护
        function initializeCSRFProtection() {
            const csrfToken = generateCSRFToken();
            document.getElementById('csrfToken').value = csrfToken;

            // 定期刷新CSRF令牌
            setInterval(() => {
                const newToken = generateCSRFToken();
                document.getElementById('csrfToken').value = newToken;
            }, SECURITY_CONFIG.csrfTokenExpiry);
        }

        // 生成CSRF令牌
        function generateCSRFToken() {
            const array = new Uint8Array(32);
            crypto.getRandomValues(array);
            return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
        }

        // 初始化速率限制
        function initializeRateLimiting() {
            const loginAttempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
            const now = Date.now();

            // 清理过期的尝试记录
            Object.keys(loginAttempts).forEach(ip => {
                if (now - loginAttempts[ip].lastAttempt > SECURITY_CONFIG.lockoutDuration) {
                    delete loginAttempts[ip];
                }
            });

            localStorage.setItem('loginAttempts', JSON.stringify(loginAttempts));
        }

        // 检查登录速率限制
        function checkRateLimit() {
            const clientIP = 'local'; // 在实际应用中应该获取真实IP
            const loginAttempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
            const now = Date.now();

            if (loginAttempts[clientIP]) {
                const attempts = loginAttempts[clientIP];
                if (attempts.count >= SECURITY_CONFIG.maxLoginAttempts) {
                    const timeSinceLastAttempt = now - attempts.lastAttempt;
                    if (timeSinceLastAttempt < SECURITY_CONFIG.lockoutDuration) {
                        const remainingTime = Math.ceil((SECURITY_CONFIG.lockoutDuration - timeSinceLastAttempt) / 60000);
                        throw new Error(`登录尝试次数过多，请 ${remainingTime} 分钟后再试`);
                    } else {
                        // 重置计数器
                        delete loginAttempts[clientIP];
                        localStorage.setItem('loginAttempts', JSON.stringify(loginAttempts));
                    }
                }
            }

            return true;
        }

        // 记录登录尝试
        function recordLoginAttempt(success = false) {
            const clientIP = 'local';
            const loginAttempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
            const now = Date.now();

            if (!loginAttempts[clientIP]) {
                loginAttempts[clientIP] = { count: 0, lastAttempt: now };
            }

            if (success) {
                // 登录成功，清除记录
                delete loginAttempts[clientIP];
            } else {
                // 登录失败，增加计数
                loginAttempts[clientIP].count++;
                loginAttempts[clientIP].lastAttempt = now;
            }

            localStorage.setItem('loginAttempts', JSON.stringify(loginAttempts));
        }

        // 输入净化和XSS防护
        function initializeInputSanitization() {
            const inputs = document.querySelectorAll('#loginForm input[type="text"], #loginForm input[type="password"]');
            inputs.forEach(input => {
                input.addEventListener('input', function(e) {
                    if (SECURITY_CONFIG.inputSanitization) {
                        e.target.value = sanitizeInput(e.target.value);
                    }
                });
            });
        }

        // 输入净化函数
        function sanitizeInput(input) {
            if (typeof input !== 'string') return '';

            // 移除潜在的XSS攻击向量
            return input
                .replace(/[<>]/g, '') // 移除尖括号
                .replace(/javascript:/gi, '') // 移除javascript协议
                .replace(/on\w+\s*=/gi, '') // 移除事件处理器
                .replace(/script/gi, '') // 移除script标签
                .trim();
        }

        // 安全头设置
        function initializeSecurityHeaders() {
            // 设置安全相关的meta标签（如果不存在）
            if (!document.querySelector('meta[http-equiv="X-Content-Type-Options"]')) {
                const meta = document.createElement('meta');
                meta.setAttribute('http-equiv', 'X-Content-Type-Options');
                meta.setAttribute('content', 'nosniff');
                document.head.appendChild(meta);
            }
        }

        // 标签页切换功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有标签和面板的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

                // 为当前标签添加active类
                tab.classList.add('active');

                // 显示对应的面板
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // 增强的表单验证
        function validateForm() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const csrfToken = document.getElementById('csrfToken').value;

            let isValid = true;

            // 清除之前的错误信息
            clearFieldErrors();

            // 验证用户名
            if (!username) {
                showFieldError('usernameError', '请输入用户名或邮箱');
                isValid = false;
            } else if (username.length > 50) {
                showFieldError('usernameError', '用户名长度不能超过50个字符');
                isValid = false;
            } else if (!/^[a-zA-Z0-9@._-]+$/.test(username)) {
                showFieldError('usernameError', '用户名包含非法字符');
                isValid = false;
            }

            // 验证密码
            if (!password) {
                showFieldError('passwordError', '请输入密码');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError('passwordError', '密码长度不能少于6位');
                isValid = false;
            } else if (password.length > 128) {
                showFieldError('passwordError', '密码长度不能超过128位');
                isValid = false;
            }

            // 验证CSRF令牌
            if (!csrfToken) {
                showError('安全验证失败，请刷新页面重试');
                isValid = false;
            }

            return isValid;
        }

        // 显示字段错误
        function showFieldError(fieldId, message) {
            const errorElement = document.getElementById(fieldId);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        // 清除字段错误
        function clearFieldErrors() {
            const errorElements = document.querySelectorAll('.field-error');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }

        // 安全的错误信息显示
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            if (errorDiv) {
                // 净化并标准化错误信息，防止信息泄露
                const safeMessage = getSafeErrorMessage(message);
                errorDiv.textContent = sanitizeInput(safeMessage);
                errorDiv.classList.add('show');

                // 5秒后自动隐藏
                setTimeout(() => {
                    errorDiv.classList.remove('show');
                }, 5000);
            }
        }

        // 获取安全的错误信息
        function getSafeErrorMessage(originalMessage) {
            // 标准化错误信息，避免泄露系统内部信息
            const errorMap = {
                'network': '网络连接异常，请检查网络后重试',
                'timeout': '请求超时，请稍后重试',
                'server': '服务暂时不可用，请稍后重试',
                'validation': '输入信息格式不正确',
                'auth': '用户名或密码错误',
                'locked': '账户已被锁定，请稍后再试',
                'rate_limit': '操作过于频繁，请稍后再试'
            };

            // 检查是否包含敏感信息
            const lowerMessage = originalMessage.toLowerCase();

            if (lowerMessage.includes('database') || lowerMessage.includes('sql')) {
                return errorMap.server;
            }
            if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
                return errorMap.network;
            }
            if (lowerMessage.includes('timeout')) {
                return errorMap.timeout;
            }
            if (lowerMessage.includes('locked') || lowerMessage.includes('锁定')) {
                return errorMap.locked;
            }
            if (lowerMessage.includes('rate') || lowerMessage.includes('频繁')) {
                return errorMap.rate_limit;
            }
            if (lowerMessage.includes('password') || lowerMessage.includes('username') ||
                lowerMessage.includes('密码') || lowerMessage.includes('用户名')) {
                return errorMap.auth;
            }

            // 默认返回通用错误信息
            return '登录失败，请检查输入信息后重试';
        }

        // 登录/注册切换
        document.getElementById('switch-to-register').addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'register.html';
        });

        document.getElementById('switch-to-register-phone').addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'register.html';
        });

        document.getElementById('switch-to-register-qr').addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'register.html';
        });

        // 表单提交处理已移至 login-form.js 中处理
    </script>
    
    </div> <!-- 关闭 main-content -->

    <!-- 内嵌式登录认证系统 -->
    <script>
        console.log('🚀 开始初始化内嵌式登录系统...')

        // 内嵌的认证服务类
        class EmbeddedAuthService {
            constructor() {
                this.testAccounts = {
                    'admin': { password: 'admin123', role: 'admin', name: '系统管理员' },
                    'designer1': { password: '123456', role: 'designer', name: '设计师' },
                    'owner1': { password: '123456', role: 'owner', name: '业主' },
                    'test': { password: '123456', role: 'user', name: '测试用户' },
                    'demo': { password: 'demo123', role: 'user', name: '演示用户' }
                };
                console.log('✅ EmbeddedAuthService 初始化成功');
            }

            async passwordLogin(username, password) {
                console.log(`🧪 开始登录验证: ${username}`);

                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 800));

                const account = this.testAccounts[username.toLowerCase()];
                if (!account) {
                    throw new Error(`账号不存在: ${username}`);
                }

                if (account.password !== password) {
                    throw new Error('用户名或密码错误');
                }

                const result = {
                    success: true,
                    data: {
                        accessToken: 'embedded_token_' + Date.now(),
                        refreshToken: 'refresh_' + Date.now(),
                        user: {
                            id: Date.now(),
                            username: username,
                            email: `${username}@smarthome.com`,
                            name: account.name,
                            role: account.role,
                            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
                            permissions: this.getPermissions(account.role)
                        }
                    },
                    message: '登录成功'
                };

                // 保存到本地存储
                localStorage.setItem('access_token', result.data.accessToken);
                localStorage.setItem('token', result.data.accessToken); // 兼容其他页面
                localStorage.setItem('refresh_token', result.data.refreshToken);
                localStorage.setItem('current_user', JSON.stringify(result.data.user));

                console.log(`✅ 登录成功: ${account.name} (${account.role})`);
                return result;
            }

            getPermissions(role) {
                const permissions = {
                    admin: ['*'],
                    designer: ['design:*', 'project:read', 'customer:read'],
                    owner: ['project:read', 'project:update', 'space:*'],
                    user: ['project:read', 'profile:update']
                };
                return permissions[role] || permissions.user;
            }

            getAccessToken() {
                return localStorage.getItem('access_token');
            }

            getCurrentUser() {
                const userStr = localStorage.getItem('current_user');
                return userStr ? JSON.parse(userStr) : null;
            }

            isLoggedIn() {
                return !!this.getAccessToken();
            }

            logout() {
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('current_user');
                console.log('🚪 用户已登出');
            }
        }

        // 全局认证服务实例
        window.authService = new EmbeddedAuthService();

        // 登录表单处理
        function handleLogin() {
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const loginBtn = document.getElementById('loginButton');
            const errorMsg = document.getElementById('usernameError'); // 使用现有的错误显示元素

            if (!usernameInput || !passwordInput || !loginBtn) {
                console.error('❌ 登录表单元素未找到');
                return;
            }

            const username = usernameInput.value.trim();
            const password = passwordInput.value.trim();

            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }

            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            hideError();

            // 执行登录
            authService.passwordLogin(username, password)
                .then(result => {
                    console.log('✅ 登录成功:', result);
                    // 直接跳转到后台首页
                    window.location.href = 'index.html';
                })
                .catch(error => {
                    console.error('❌ 登录失败:', error);
                    showError(error.message);
                })
                .finally(() => {
                    loginBtn.disabled = false;
                    loginBtn.textContent = '登录';
                });
        }

        function showError(message) {
            const errorMsg = document.getElementById('usernameError');
            if (errorMsg) {
                errorMsg.textContent = message;
                errorMsg.style.display = 'block';
                errorMsg.style.color = '#dc3545';
                errorMsg.style.backgroundColor = '#f8d7da';
                errorMsg.style.padding = '8px';
                errorMsg.style.borderRadius = '4px';
                errorMsg.style.marginTop = '5px';
            }
        }

        function hideError() {
            const errorMsg = document.getElementById('usernameError');
            if (errorMsg) {
                errorMsg.style.display = 'none';
            }
        }

        function showSuccess(message) {
            const errorMsg = document.getElementById('usernameError');
            if (errorMsg) {
                errorMsg.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>✅ ${message}</strong>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>🔗 请选择进入主页的方式：</strong>
                    </div>
                    <div style="margin: 5px 0;">
                        <a href="http://127.0.0.1:8080/index.html" style="display: inline-block; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 2px;">
                            ✅ 直接进入主页 (推荐)
                        </a>
                    </div>
                    <div style="margin: 5px 0;">
                        <a href="../../../../index.html" style="display: inline-block; padding: 8px 15px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 2px;">
                            🔄 相对路径方案
                        </a>
                    </div>
                    <div style="margin: 5px 0;">
                        <a href="/index.html" style="display: inline-block; padding: 8px 15px; background: #6f42c1; color: white; text-decoration: none; border-radius: 4px; margin: 2px;">
                            📁 根目录方案
                        </a>
                    </div>
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        💡 提示: 请尝试点击上面的链接，看哪个能正常工作
                    </div>
                `;
                errorMsg.style.display = 'block';
                errorMsg.style.color = '#155724';
                errorMsg.style.backgroundColor = '#d4edda';
                errorMsg.style.padding = '15px';
                errorMsg.style.borderRadius = '8px';
                errorMsg.style.marginTop = '15px';
                errorMsg.style.border = '1px solid #c3e6cb';
            }
        }

        // DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM加载完成，绑定登录事件');

            // 绑定登录按钮事件
            const loginBtn = document.getElementById('loginButton');
            if (loginBtn) {
                loginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleLogin();
                });
            }

            // 绑定表单提交事件
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleLogin();
                });
            }

            // 绑定回车键事件
            const inputs = document.querySelectorAll('#username, #password');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        handleLogin();
                    }
                });
            });

            console.log('✅ 登录系统初始化完成');
        });
    </script>

            </div>
        </main>
    </div>
</body>
</html>

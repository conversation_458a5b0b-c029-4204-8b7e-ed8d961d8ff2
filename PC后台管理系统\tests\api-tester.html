<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能设计与施工 - API接口测试面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 960px; }
        .card-header { font-weight: bold; }
        #log-container {
            height: 400px;
            background-color: #212529;
            color: #f8f9fa;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.875rem;
        }
        .log-line { white-space: pre-wrap; }
        .log-line.error { color: #f97583; }
        .log-line.success { color: #85e89d; }
        .log-line.warn { color: #ffab70; }
        .status-badge.bg-success { color: white !important; }
        .status-badge.bg-danger { color: white !important; }
        .status-badge.bg-warning { color: black !important; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-4">
            <h1 class="h3">智能设计与施工 - API接口测试面板</h1>
            <p class="text-muted">一键测试所有后端核心API接口</p>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                环境配置
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-9">
                        <input type="text" class="form-control" id="api-url" value="http://localhost:8080">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100" id="run-tests-btn">🚀 开始测试</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                测试进度
            </div>
            <div class="card-body">
                <div class="progress">
                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>测试项目</span>
                <button class="btn btn-sm btn-secondary" id="clear-log-btn">清空日志</button>
            </div>
            <ul class="list-group list-group-flush" id="test-list">
                <!-- Test items will be injected by JS -->
            </ul>
            <div class="card-footer">
                <div id="log-container" class="overflow-auto p-3"></div>
            </div>
        </div>
    </div>

<script>
const tests = [
    { id: 'health', name: '环境连通性检查 (GET /health)', fn: testHealth },
    { id: 'login', name: '模拟微信登录 (POST /api/auth/login/wechat)', fn: testLogin },
    { id: 'profile', name: '获取用户资料 (GET /api/users/profile)', fn: testGetProfile, needsAuth: true },
    { id: 'update-profile', name: '更新用户资料 (PUT /api/users/profile)', fn: testUpdateProfile, needsAuth: true },
    { id: 'upload', name: '上传文件 (POST /api/files/upload)', fn: testUploadFile, needsAuth: true },
    { id: 'blueprints', name: '获取图纸列表 (GET /api/blueprints)', fn: testGetBlueprints, needsAuth: true },
    { id: 'requirements', name: '提交设计需求 (POST /api/requirements)', fn: testSubmitRequirement, needsAuth: true },
    { id: 'admin-users', name: '管理员获取用户列表 (GET /admin/api/users)', fn: testAdminGetUsers, needsAuth: true },
    { id: 'admin-requirements', name: '管理员获取需求列表 (GET /admin/api/requirements)', fn: testAdminGetRequirements, needsAuth: true },
];

let authToken = null;
let apiUrl = '';
const testListEl = document.getElementById('test-list');
const logContainerEl = document.getElementById('log-container');

function initTestUI() {
    testListEl.innerHTML = '';
    tests.forEach(test => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between align-items-center';
        li.id = `test-item-${test.id}`;
        li.innerHTML = `
            <span>${test.name}</span>
            <span class="status-badge badge rounded-pill bg-secondary">等待中</span>
        `;
        testListEl.appendChild(li);
    });
}

function log(message, type = 'info') {
    const time = new Date().toLocaleTimeString();
    const line = document.createElement('div');
    line.className = `log-line ${type}`;
    line.textContent = `[${time}] ${message}`;
    logContainerEl.appendChild(line);
    logContainerEl.scrollTop = logContainerEl.scrollHeight;
}

function updateTestStatus(id, status, text) {
    const badge = document.querySelector(`#test-item-${id} .status-badge`);
    badge.className = `status-badge badge rounded-pill ${status}`;
    badge.textContent = text;
}

async function runTests() {
    document.getElementById('run-tests-btn').disabled = true;
    log('🚀 测试套件启动...');
    apiUrl = document.getElementById('api-url').value;
    authToken = null;
    let passedCount = 0;

    for (let i = 0; i < tests.length; i++) {
        const test = tests[i];
        updateTestStatus(test.id, 'bg-warning', '进行中...');

        if (test.needsAuth && !authToken) {
            log(`🟡 [跳过] 测试 '${test.name}' 因为需要登录授权.`, 'warn');
            updateTestStatus(test.id, 'bg-secondary', '已跳过');
            continue;
        }

        try {
            log(`▶️  正在运行: ${test.name}`);
            await test.fn();
            updateTestStatus(test.id, 'bg-success', '通过');
            log(`✅ [通过] ${test.name}`, 'success');
            passedCount++;
        } catch (error) {
            updateTestStatus(test.id, 'bg-danger', '失败');
            log(`❌ [失败] ${test.name}: ${error.message}`, 'error');
        }
        
        const progress = Math.round(((i + 1) / tests.length) * 100);
        const progressBar = document.getElementById('progress-bar');
        progressBar.style.width = `${progress}%`;
        progressBar.textContent = `${progress}%`;
    }
    
    log('-------------------------------------------------');
    log(`🏁 测试完成. 通过: ${passedCount}, 失败: ${tests.length - passedCount}`);
    document.getElementById('run-tests-btn').disabled = false;
}

// --- Test Functions ---

async function apiCall(endpoint, method, body = null, headers = {}) {
    const url = `${apiUrl}${endpoint}`;
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
            ...headers
        }
    };
    if (body) {
        options.body = (body instanceof FormData) ? body : JSON.stringify(body);
        if (body instanceof FormData) {
           delete options.headers['Content-Type']; // Browser will set it with boundary
        }
    }

    log(`  -> ${method} ${url}`);
    const response = await fetch(url, options);

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败. 状态: ${response.status}. 响应: ${errorText}`);
    }
    
    const responseData = await response.json();
    log(`  <- ${response.status} ${JSON.stringify(responseData).substring(0, 100)}...`);
    return responseData;
}

async function testHealth() {
    await apiCall('/health', 'GET');
}

async function testLogin() {
    const result = await apiCall('/api/auth/login/wechat', 'POST', { code: 'test_code' });
    if (!result.data || !result.data.token) {
        throw new Error('登录响应中未找到 token');
    }
    authToken = result.data.token;
    log('   🔑 已获取并保存Auth Token.');
    if (result.data.user.role !== 'admin') {
         log('   ⚠️ 注意: 测试用户非管理员, 部分测试可能失败.', 'warn');
    }
}

async function testGetProfile() {
    await apiCall('/api/users/profile', 'GET', null, { 'Authorization': `Bearer ${authToken}` });
}

async function testUpdateProfile() {
    await apiCall('/api/users/profile', 'PUT', { nickname: 'TestUserNewName' }, { 'Authorization': `Bearer ${authToken}` });
}

async function testUploadFile() {
    const formData = new FormData();
    const blob = new Blob(['this is a test file content'], { type: 'text/plain' });
    formData.append('file', blob, 'testfile.txt');
    await apiCall('/api/files/upload', 'POST', formData, { 'Authorization': `Bearer ${authToken}` });
}

async function testGetBlueprints() {
    await apiCall('/api/blueprints', 'GET', null, { 'Authorization': `Bearer ${authToken}` });
}

async function testSubmitRequirement() {
    const requirementData = {
        title: '客厅阳台一体化设计',
        description: '需要现代简约风格, 扩大空间感',
        category: 'interior_design',
        budget: '10000-20000'
    };
    await apiCall('/api/requirements', 'POST', requirementData, { 'Authorization': `Bearer ${authToken}` });
}

async function testAdminGetUsers() {
    await apiCall('/admin/api/users', 'GET', null, { 'Authorization': `Bearer ${authToken}` });
}

async function testAdminGetRequirements() {
    await apiCall('/admin/api/requirements', 'GET', null, { 'Authorization': `Bearer ${authToken}` });
}

document.getElementById('run-tests-btn').addEventListener('click', runTests);
document.getElementById('clear-log-btn').addEventListener('click', () => {
    logContainerEl.innerHTML = '';
});

// Initial UI setup
initTestUI();
</script>
</body>
</html> 
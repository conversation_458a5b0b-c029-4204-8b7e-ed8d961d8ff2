# 权限管理模块 - Ant Design Pro版本

**版本**: v1.0  
**创建时间**: 2025-07-15  
**功能**: 基于Ant Design Pro的权限管理模块，支持角色管理和权限配置

---

## 🎯 功能概述

本模块提供了完整的权限管理功能，包括：

1. **角色管理**：支持5种智能家居角色的管理
2. **权限配置**：通过树形结构展示和配置权限
3. **三级菜单**：支持一级、二级、三级菜单的权限控制
4. **权限预设**：基于角色的权限模板
5. **权限验证**：集成的权限验证API

---

## 🚀 快速开始

### 1. 集成到Ant Design Pro项目

1. 将整个`PermissionManagement`目录复制到Ant Design Pro项目的`src/pages/System/`目录下
2. 在`config/routes.ts`中添加路由配置：

```typescript
// config/routes.ts
import permissionRoutes from '../src/pages/System/PermissionManagement/route';

export default [
  // 其他路由...
  
  // 添加权限管理路由
  permissionRoutes,
  
  // 其他路由...
];
```

3. 在`src/access.ts`中添加权限控制：

```typescript
// src/access.ts
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  return {
    // 其他权限...
    
    // 添加权限管理权限
    canAdmin: currentUser && currentUser.access === 'admin',
    
    // 其他权限...
  };
}
```

### 2. 配置API接口

在`src/services/`目录下创建权限管理相关的API服务：

```typescript
// src/services/permission.ts
import { request } from 'umi';
import type {
  SmartHomeRole,
  RolePermissionConfig,
  PermissionUpdateRequest,
  ApiResponse
} from '@/pages/System/PermissionManagement/types';

// 获取角色权限配置
export async function getRolePermissions(role: SmartHomeRole) {
  return request<RolePermissionConfig>(`/api/v1/permissions/roles/${role}`, {
    method: 'GET',
  });
}

// 更新角色权限
export async function updateRolePermissions(data: PermissionUpdateRequest) {
  return request<ApiResponse>(`/api/v1/permissions/roles/${data.role}`, {
    method: 'PUT',
    data,
  });
}

// 其他API...
```

### 3. 添加到菜单

在`src/app.tsx`中添加菜单配置：

```typescript
// src/app.tsx
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    // 其他配置...
    
    menu: {
      // 其他配置...
      
      // 添加权限管理菜单
      items: [
        // 其他菜单项...
        
        {
          name: '系统管理',
          icon: 'SettingOutlined',
          path: '/system',
          children: [
            // 其他子菜单...
            
            {
              name: '权限管理',
              icon: 'SafetyCertificateOutlined',
              path: '/system/permission',
            },
            
            // 其他子菜单...
          ],
        },
        
        // 其他菜单项...
      ],
    },
    
    // 其他配置...
  };
};
```

---

## 📋 模块结构

```
PermissionManagement/
├── index.tsx           # 主组件
├── types.ts            # 类型定义
├── constants.tsx       # 常量配置
├── utils.ts            # 工具函数
├── services.ts         # API服务
├── route.ts            # 路由配置
└── README.md           # 说明文档
```

---

## 🔧 自定义配置

### 1. 修改角色定义

在`constants.tsx`中修改`SMART_HOME_ROLES`常量：

```typescript
// constants.tsx
export const SMART_HOME_ROLES: Record<SmartHomeRole, Role> = {
  // 修改现有角色或添加新角色
  NEW_ROLE: {
    name: '新角色名称',
    icon: <CustomIcon style={{ color: '#123456' }} />,
    color: '#123456',
    description: '新角色描述',
    permissions: {
      // 默认权限配置
    }
  },
  // 其他角色...
};
```

### 2. 修改权限模块

在`constants.tsx`中修改`PERMISSION_MODULES`常量：

```typescript
// constants.tsx
export const PERMISSION_MODULES: Record<PermissionModule, Module> = {
  // 修改现有模块或添加新模块
  new_module: {
    name: '新模块名称',
    icon: <CustomIcon />,
    color: '#123456',
    subModules: {
      // 子模块定义
      new_submodule: {
        name: '新子模块',
        operations: ['VIEW', 'EDIT', 'DELETE']
      },
      // 其他子模块...
    }
  },
  // 其他模块...
};
```

### 3. 修改操作权限

在`constants.tsx`中修改`OPERATION_LABELS`常量：

```typescript
// constants.tsx
export const OPERATION_LABELS: Record<PermissionOperation, string> = {
  // 修改现有操作或添加新操作
  NEW_OPERATION: '新操作',
  // 其他操作...
};
```

---

## 📈 API接口说明

### 1. 获取角色权限

```
GET /api/v1/permissions/roles/{role}
```

**响应格式**：

```json
{
  "role": "OWNER",
  "permissions": {
    "project": ["VIEW", "EDIT", "COMMENT"],
    "design": ["VIEW", "COMMENT"]
  },
  "modules": {
    // 模块定义
  }
}
```

### 2. 更新角色权限

```
PUT /api/v1/permissions/roles/{role}
```

**请求体**：

```json
{
  "role": "OWNER",
  "permissions": {
    "project": ["VIEW", "EDIT", "COMMENT"],
    "design": ["VIEW", "COMMENT"]
  }
}
```

**响应格式**：

```json
{
  "success": true,
  "message": "权限更新成功"
}
```

### 3. 检查用户权限

```
POST /api/v1/permissions/check
```

**请求体**：

```json
{
  "userId": "user123",
  "module": "project",
  "operation": "EDIT",
  "projectId": "project456"
}
```

**响应格式**：

```json
{
  "hasPermission": true
}
```

---

## 🔒 权限模型说明

本模块实现了RBAC（基于角色的访问控制）和ABAC（基于属性的访问控制）混合权限模型：

1. **RBAC部分**：通过角色定义基础权限
2. **ABAC部分**：通过项目空间和上下文属性动态调整权限

权限检查流程：

1. 检查用户角色的系统级权限
2. 如果涉及项目，检查用户在项目中的特定权限
3. 应用业务规则和上下文属性
4. 返回最终的权限判定结果

---

## 🧪 测试

### 单元测试

```bash
# 运行单元测试
npm run test:unit src/pages/System/PermissionManagement
```

### E2E测试

```bash
# 运行E2E测试
npm run test:e2e src/pages/System/PermissionManagement
```

---

## 📚 相关文档

- [Ant Design Pro官方文档](https://pro.ant.design/docs/getting-started)
- [React官方文档](https://reactjs.org/docs/getting-started.html)
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)

---

## 🤝 贡献

欢迎提交问题和改进建议！

---

**开发者**: 智能家居团队  
**联系方式**: <EMAIL>

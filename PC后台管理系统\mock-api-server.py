#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mock API Server for PC Management System
提供API测试页面所需的模拟接口
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
import time
import random
from datetime import datetime

class MockAPIHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """处理GET请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 添加CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 路由处理
        if path == '/health':
            response = {
                "status": "ok", 
                "message": "Mock API服务器运行正常",
                "timestamp": datetime.now().isoformat()
            }
        
        elif path == '/api/blueprints/list':
            response = {
                "code": 200,
                "message": "成功",
                "data": [
                    {
                        "id": 1,
                        "filename": "客厅设计图.dwg",
                        "upload_time": "2024-01-15 10:30:00",
                        "size": "2.5MB",
                        "status": "已处理"
                    },
                    {
                        "id": 2, 
                        "filename": "卧室平面图.pdf",
                        "upload_time": "2024-01-16 14:20:00",
                        "size": "1.8MB",
                        "status": "处理中"
                    }
                ],
                "total": 2
            }
            
        elif path == '/admin/api/users/list':
            response = {
                "code": 200,
                "message": "成功",
                "data": [
                    {
                        "id": 1,
                        "wechat_name": "张先生",
                        "phone": "138****8888",
                        "register_time": "2024-01-10 09:15:00",
                        "status": "已上传图纸",
                        "blueprints_count": 3,
                        "requirements_count": 1
                    },
                    {
                        "id": 2,
                        "wechat_name": "李女士", 
                        "phone": "139****9999",
                        "register_time": "2024-01-12 16:30:00",
                        "status": "已提交需求",
                        "blueprints_count": 2,
                        "requirements_count": 2
                    }
                ],
                "total": 2,
                "page": 1,
                "pageSize": 10
            }
            
        elif path == '/admin/api/requirements/list':
            response = {
                "code": 200,
                "message": "成功", 
                "data": [
                    {
                        "id": 1,
                        "user_name": "张先生",
                        "title": "客厅现代简约风格设计",
                        "budget": "10-15万",
                        "style": "现代简约",
                        "area": "120㎡",
                        "submit_time": "2024-01-15 11:00:00",
                        "status": "待分配",
                        "priority": "高"
                    },
                    {
                        "id": 2,
                        "user_name": "李女士",
                        "title": "三居室全屋装修", 
                        "budget": "20-25万",
                        "style": "北欧风格",
                        "area": "95㎡",
                        "submit_time": "2024-01-16 15:30:00",
                        "status": "进行中",
                        "priority": "中"
                    }
                ],
                "total": 2,
                "page": 1,
                "pageSize": 10
            }
            
        else:
            response = {
                "code": 404,
                "message": f"接口未找到: {path}",
                "data": None
            }
        
        # 发送响应
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """处理POST请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except:
            request_data = {}
        
        # 添加CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 路由处理
        if path == '/api/auth/wechat/login':
            response = {
                "code": 200,
                "message": "微信登录成功",
                "data": {
                    "token": f"mock_token_{int(time.time())}",
                    "user_id": random.randint(1000, 9999),
                    "wechat_name": request_data.get('wechat_name', '微信用户'),
                    "avatar": "https://example.com/avatar.jpg"
                }
            }
            
        elif path == '/api/blueprints/upload':
            response = {
                "code": 200,
                "message": "图纸上传成功",
                "data": {
                    "file_id": random.randint(1000, 9999),
                    "filename": request_data.get('filename', 'design.dwg'),
                    "upload_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "size": f"{random.uniform(1.0, 5.0):.1f}MB"
                }
            }
            
        elif path == '/api/requirements/submit':
            response = {
                "code": 200,
                "message": "需求提交成功",
                "data": {
                    "requirement_id": random.randint(1000, 9999),
                    "submit_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "status": "待处理",
                    "estimated_response_time": "24小时内"
                }
            }
            
        else:
            response = {
                "code": 404,
                "message": f"POST接口未找到: {path}",
                "data": None
            }
        
        # 发送响应
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_PUT(self):
        """处理PUT请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        put_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(put_data.decode('utf-8')) if put_data else {}
        except:
            request_data = {}
        
        # 添加CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 路由处理
        if path == '/api/users/profile':
            response = {
                "code": 200,
                "message": "用户信息更新成功",
                "data": {
                    "user_id": request_data.get('user_id', 1001),
                    "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "updated_fields": list(request_data.keys())
                }
            }
        else:
            response = {
                "code": 404,
                "message": f"PUT接口未找到: {path}",
                "data": None
            }
        
        # 发送响应
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def run_server(port=8000):
    """启动Mock API服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockAPIHandler)
    
    print(f"""
🚀 Mock API服务器启动成功！
📡 服务地址: http://localhost:{port}
🔧 健康检查: http://localhost:{port}/health

📋 可用的API接口:
• GET  /health                      - 健康检查
• GET  /api/blueprints/list         - 图纸列表
• GET  /admin/api/users/list        - 管理员用户列表  
• GET  /admin/api/requirements/list - 管理员需求列表
• POST /api/auth/wechat/login       - 微信登录
• POST /api/blueprints/upload       - 图纸上传
• POST /api/requirements/submit     - 需求提交
• PUT  /api/users/profile           - 用户信息更新

🌐 CORS已启用，支持跨域访问
⏰ 服务器时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

按 Ctrl+C 停止服务器
""")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server(8000) 
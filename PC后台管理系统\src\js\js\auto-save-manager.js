/**
 * 自动保存和数据管理器
 * 负责处理表单数据的自动保存、验证和恢复
 */

class AutoSaveManager {
    constructor() {
        this.config = {
            saveInterval: 30000, // 30秒自动保存
            debounceDelay: 1000, // 防抖延迟
            storageKey: 'requirement-form-data',
            maxStorageAge: 24 * 60 * 60 * 1000 // 24小时
        };

        this.state = {
            isDirty: false,
            lastSaved: null,
            saveTimeout: null,
            changeTimeout: null
        };

        this.formFields = {
            'new-requirement': [
                'requirement-title',
                'requirement-description', 
                'requirement-priority',
                'requirement-deadline',
                'requirement-budget',
                'requirement-contact'
            ],
            'product-selection': [
                'selected-products',
                'selected-scenes',
                'customization-notes'
            ],
            'drawing-management': [
                'uploaded-files',
                'drawing-notes'
            ]
        };

        this.init();
    }

    init() {
        this.bindAutoSaveEvents();
        this.startPeriodicSave();
        this.loadExistingData();
        console.log('💾 自动保存管理器初始化完成');
    }

    /**
     * 绑定自动保存事件
     */
    bindAutoSaveEvents() {
        // 监听所有表单字段变化
        Object.values(this.formFields).flat().forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                ['input', 'change', 'blur'].forEach(event => {
                    field.addEventListener(event, (e) => this.handleFieldChange(e));
                });
            }
        });

        // 监听选择类字段
        document.addEventListener('change', (e) => {
            if (e.target.matches('[name="priority"], .product-checkbox, .scene-checkbox')) {
                this.handleFieldChange(e);
            }
        });

        // 页面离开前保存
        window.addEventListener('beforeunload', () => {
            this.saveImmediately();
        });
    }

    /**
     * 处理字段变化
     */
    handleFieldChange(event) {
        this.state.isDirty = true;
        
        // 清除之前的防抖定时器
        if (this.state.changeTimeout) {
            clearTimeout(this.state.changeTimeout);
        }

        // 设置防抖保存
        this.state.changeTimeout = setTimeout(() => {
            this.saveFormData();
        }, this.config.debounceDelay);

        // 更新UI状态
        this.updateSaveStatus('未保存更改');
    }

    /**
     * 启动定期保存
     */
    startPeriodicSave() {
        setInterval(() => {
            if (this.state.isDirty) {
                this.saveFormData();
            }
        }, this.config.saveInterval);
    }

    /**
     * 保存表单数据
     */
    async saveFormData() {
        try {
            const formData = this.collectFormData();
            
            // 保存到本地存储
            this.saveToLocalStorage(formData);
            
            // 保存到服务器（可选）
            await this.saveToServer(formData);
            
            this.state.isDirty = false;
            this.state.lastSaved = new Date();
            
            this.updateSaveStatus('已自动保存');
            
            console.log('💾 表单数据已保存', formData);
            
        } catch (error) {
            console.error('❌ 保存失败:', error);
            this.updateSaveStatus('保存失败');
        }
    }

    /**
     * 立即保存
     */
    saveImmediately() {
        if (this.state.changeTimeout) {
            clearTimeout(this.state.changeTimeout);
        }
        this.saveFormData();
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const data = {
            timestamp: new Date().toISOString(),
            tabs: {}
        };

        // 收集各个选项卡的数据
        Object.keys(this.formFields).forEach(tabId => {
            data.tabs[tabId] = this.collectTabData(tabId);
        });

        return data;
    }

    /**
     * 收集特定选项卡数据
     */
    collectTabData(tabId) {
        const tabData = {};

        switch (tabId) {
            case 'new-requirement':
                tabData.title = this.getFieldValue('requirement-title');
                tabData.description = this.getFieldValue('requirement-description');
                tabData.priority = this.getCheckedValue('priority');
                tabData.deadline = this.getFieldValue('requirement-deadline');
                tabData.budget = this.getFieldValue('requirement-budget');
                tabData.contact = this.getFieldValue('requirement-contact');
                break;

            case 'product-selection':
                tabData.selectedProducts = this.getSelectedProducts();
                tabData.selectedScenes = this.getSelectedScenes();
                tabData.customization = this.getFieldValue('customization-notes');
                break;

            case 'drawing-management':
                tabData.uploadedFiles = this.getUploadedFiles();
                tabData.notes = this.getFieldValue('drawing-notes');
                break;
        }

        return tabData;
    }

    /**
     * 获取字段值
     */
    getFieldValue(fieldId) {
        const field = document.getElementById(fieldId);
        return field ? field.value.trim() : '';
    }

    /**
     * 获取选中的单选框值
     */
    getCheckedValue(name) {
        const checked = document.querySelector(`input[name="${name}"]:checked`);
        return checked ? checked.value : '';
    }

    /**
     * 获取选中的产品
     */
    getSelectedProducts() {
        const selectedProducts = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
            selectedProducts.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                price: checkbox.dataset.price,
                image: checkbox.dataset.image
            });
        });
        return selectedProducts;
    }

    /**
     * 获取选中的场景
     */
    getSelectedScenes() {
        const selectedScenes = [];
        document.querySelectorAll('.scene-checkbox:checked').forEach(checkbox => {
            selectedScenes.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                description: checkbox.dataset.description
            });
        });
        return selectedScenes;
    }

    /**
     * 获取上传的文件
     */
    getUploadedFiles() {
        const files = [];
        document.querySelectorAll('.uploaded-file-item').forEach(item => {
            files.push({
                name: item.dataset.filename,
                size: item.dataset.filesize,
                type: item.dataset.filetype,
                url: item.dataset.fileurl || ''
            });
        });
        return files;
    }

    /**
     * 保存到本地存储
     */
    saveToLocalStorage(data) {
        try {
            const storageData = {
                ...data,
                version: '1.0',
                expires: Date.now() + this.config.maxStorageAge
            };
            
            localStorage.setItem(this.config.storageKey, JSON.stringify(storageData));
        } catch (error) {
            console.error('❌ 本地存储失败:', error);
        }
    }

    /**
     * 从本地存储加载数据
     */
    loadExistingData() {
        try {
            const stored = localStorage.getItem(this.config.storageKey);
            if (!stored) return;

            const data = JSON.parse(stored);
            
            // 检查数据是否过期
            if (data.expires && Date.now() > data.expires) {
                localStorage.removeItem(this.config.storageKey);
                return;
            }

            // 恢复表单数据
            this.restoreFormData(data);
            
            console.log('📁 已加载保存的表单数据');
            
        } catch (error) {
            console.error('❌ 加载数据失败:', error);
        }
    }

    /**
     * 恢复表单数据
     */
    restoreFormData(data) {
        if (!data.tabs) return;

        Object.keys(data.tabs).forEach(tabId => {
            this.restoreTabData(tabId, data.tabs[tabId]);
        });

        this.state.lastSaved = new Date(data.timestamp);
        this.updateSaveStatus('已恢复保存的数据');
    }

    /**
     * 恢复特定选项卡数据
     */
    restoreTabData(tabId, tabData) {
        switch (tabId) {
            case 'new-requirement':
                this.setFieldValue('requirement-title', tabData.title);
                this.setFieldValue('requirement-description', tabData.description);
                this.setFieldValue('requirement-deadline', tabData.deadline);
                this.setFieldValue('requirement-budget', tabData.budget);
                this.setFieldValue('requirement-contact', tabData.contact);
                this.setCheckedValue('priority', tabData.priority);
                break;

            case 'product-selection':
                this.restoreSelectedProducts(tabData.selectedProducts);
                this.restoreSelectedScenes(tabData.selectedScenes);
                this.setFieldValue('customization-notes', tabData.customization);
                break;

            case 'drawing-management':
                this.restoreUploadedFiles(tabData.uploadedFiles);
                this.setFieldValue('drawing-notes', tabData.notes);
                break;
        }
    }

    /**
     * 设置字段值
     */
    setFieldValue(fieldId, value) {
        const field = document.getElementById(fieldId);
        if (field && value) {
            field.value = value;
        }
    }

    /**
     * 设置选中的单选框
     */
    setCheckedValue(name, value) {
        if (!value) return;
        const radio = document.querySelector(`input[name="${name}"][value="${value}"]`);
        if (radio) {
            radio.checked = true;
        }
    }

    /**
     * 恢复选中的产品
     */
    restoreSelectedProducts(products) {
        if (!Array.isArray(products)) return;
        
        products.forEach(product => {
            const checkbox = document.querySelector(`.product-checkbox[value="${product.id}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    /**
     * 恢复选中的场景
     */
    restoreSelectedScenes(scenes) {
        if (!Array.isArray(scenes)) return;
        
        scenes.forEach(scene => {
            const checkbox = document.querySelector(`.scene-checkbox[value="${scene.id}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    /**
     * 恢复上传的文件
     */
    restoreUploadedFiles(files) {
        if (!Array.isArray(files)) return;
        
        // 这里需要根据具体的文件上传组件实现
        // 暂时只记录文件信息，不实际恢复文件
        console.log('📎 需要恢复的文件:', files);
    }

    /**
     * 保存到服务器
     */
    async saveToServer(data) {
        try {
            const response = await fetch('/api/v1/requirements/draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`服务器保存失败: ${response.status}`);
            }

            const result = await response.json();
            console.log('☁️ 服务器保存成功:', result);
            
        } catch (error) {
            console.warn('⚠️ 服务器保存失败，仅保存到本地:', error.message);
        }
    }

    /**
     * 更新保存状态显示
     */
    updateSaveStatus(status) {
        const statusElement = document.querySelector('#auto-save-status span');
        if (statusElement) {
            statusElement.textContent = status;
        }

        // 更新图标
        const iconElement = document.querySelector('#auto-save-status i');
        if (iconElement) {
            iconElement.className = status.includes('失败') ? 
                'fas fa-exclamation-triangle' : 'fas fa-cloud-upload-alt';
        }
    }

    /**
     * 清除保存的数据
     */
    clearSavedData() {
        localStorage.removeItem(this.config.storageKey);
        this.state.isDirty = false;
        this.state.lastSaved = null;
        console.log('🗑️ 已清除保存的数据');
    }

    /**
     * 获取保存状态
     */
    getSaveStatus() {
        return {
            isDirty: this.state.isDirty,
            lastSaved: this.state.lastSaved,
            hasLocalData: !!localStorage.getItem(this.config.storageKey)
        };
    }

    /**
     * 手动触发保存
     */
    manualSave() {
        this.saveImmediately();
        this.updateSaveStatus('手动保存完成');
    }
}

// 导出全局实例
window.AutoSaveManager = AutoSaveManager;
window.autoSaveManager = new AutoSaveManager();

console.log('💾 自动保存管理器已加载');
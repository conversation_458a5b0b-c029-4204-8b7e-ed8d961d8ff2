<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计商品 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 第一层：面包屑导航样式 */
        .top-nav {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }

        .breadcrumb-section {
            flex: 1;
        }

        .page-title-section {
            margin-top: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
            max-width: 600px;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 第二层：主菜单栏样式 */
        .main-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .main-menu-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .main-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .main-menu-tab {
            flex-shrink: 0;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
        }

        .main-menu-tab:hover {
            color: #1f2937;
            background: #f8fafc;
        }

        .main-menu-tab.active {
            color: #1f2937;
            border-bottom-color: #1f2937;
            background: #f8fafc;
        }

        /* 商品生命周期展示区域 */
        .lifecycle-section {
            padding: 24px;
            background: #f8fafc;
        }

        .lifecycle-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .lifecycle-cards {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            scrollbar-width: none;
            padding-bottom: 8px;
        }

        .lifecycle-cards::-webkit-scrollbar {
            display: none;
        }

        .lifecycle-card {
            flex-shrink: 0;
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lifecycle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #9ca3af;
        }

        .lifecycle-card-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 20px;
            color: #374151;
        }

        .lifecycle-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .lifecycle-card-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 第三层：子菜单栏样式 */
        .sub-menu-nav {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
        }

        .sub-menu-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .sub-menu-tabs::-webkit-scrollbar {
            display: none;
        }

        .sub-menu-tab {
            flex-shrink: 0;
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .sub-menu-tab:hover {
            color: #1f2937;
            border-color: #1f2937;
            background: #f9fafb;
        }

        .sub-menu-tab.active {
            color: #ffffff;
            background: #1f2937;
            border-color: #1f2937;
        }

        /* 第四层：内容区域样式 */
        .content-area {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .content-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .content-body {
            padding: 24px;
        }

        /* 商品卡片网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .product-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .product-card:hover {
            border-color: #9ca3af;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .product-image {
            width: 100%;
            height: 160px;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #9ca3af;
        }

        .product-info {
            padding: 16px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .product-price {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .product-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .status-active {
            background: #d1fae5;
            color: #059669;
        }

        .status-inactive {
            background: #f3f4f6;
            color: #374151;
        }

        .status-draft {
            background: #fef3c7;
            color: #d97706;
        }

        .product-actions {
            display: flex;
            gap: 8px;
        }

        /* 搜索和筛选区域 */
        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 16px;
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            color: #1f2937;
            border-color: #1f2937;
        }

        .filter-btn.active {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .top-nav {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .nav-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .lifecycle-cards {
                gap: 12px;
            }

            .lifecycle-card {
                min-width: 160px;
                padding: 16px;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }

            .search-filter-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .filter-buttons {
                overflow-x: auto;
                scrollbar-width: none;
            }

            .filter-buttons::-webkit-scrollbar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item active">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 第一层：面包屑导航 -->
            <div class="top-nav">
                <div class="breadcrumb-section">
                    <div class="page-title-section">
                        <h1 class="page-title">设计商品</h1>
                        <p class="page-description">管理设计商品的创建、编辑、分类和定价，提供完整的商品生命周期管理</p>
                    </div>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                    <button class="btn btn-primary" onclick="openCreateProductModal()">
                        <i class="fas fa-plus"></i>
                        新建商品
                    </button>
                </div>
            </div>

            <!-- 第二层：主菜单栏 -->
            <div class="main-menu-nav">
                <div class="main-menu-tabs">
                    <button class="main-menu-tab active" onclick="showMainMenu('products')">商品列表</button>
                    <button class="main-menu-tab" onclick="showMainMenu('categories')">商品分类</button>
                    <button class="main-menu-tab" onclick="showMainMenu('pricing')">价格管理</button>
                </div>

                <!-- 商品生命周期展示区域 -->
                <div class="lifecycle-section">
                    <h3 class="lifecycle-title">商品生命周期</h3>
                    <div class="lifecycle-cards">
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="lifecycle-card-title">需求分析</div>
                            <div class="lifecycle-card-desc">市场调研、用户需求分析、产品定位</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-drafting-compass"></i>
                            </div>
                            <div class="lifecycle-card-title">设计开发</div>
                            <div class="lifecycle-card-desc">产品设计、功能开发、原型制作</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="lifecycle-card-title">测试验证</div>
                            <div class="lifecycle-card-desc">功能测试、用户体验验证、质量检测</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            <div class="lifecycle-card-title">上架销售</div>
                            <div class="lifecycle-card-desc">产品上架、营销推广、销售管理</div>
                        </div>
                        <div class="lifecycle-card">
                            <div class="lifecycle-card-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="lifecycle-card-title">维护更新</div>
                            <div class="lifecycle-card-desc">产品维护、功能更新、客户支持</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三层：子菜单栏 -->
            <div class="sub-menu-nav">
                <div class="sub-menu-tabs" id="subMenuTabs">
                    <!-- 动态内容，根据主菜单选择显示 -->
                </div>
            </div>

            <!-- 第四层：内容区域 -->
            <div class="content-area">
                <div class="content-header">
                    <h2 class="content-title" id="contentTitle">商品列表</h2>
                    <div class="content-actions" id="contentActions">
                        <!-- 动态内容，根据当前页面显示 -->
                    </div>
                </div>
                <div class="content-body" id="contentBody">
                    <!-- 动态内容区域 -->
                </div>
            </div>

        </main>
    </div>

    <!-- 新建/编辑商品模态框 -->
    <div id="productModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新建商品</h3>
                <span class="modal-close" onclick="closeProductModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="productForm" onsubmit="submitProductForm(event)">
                    <div class="form-group">
                        <label for="productName">商品名称 <span class="required">*</span></label>
                        <input type="text" id="productName" name="name" required>
                        <div class="error-message" id="nameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="productPrice">商品价格 <span class="required">*</span></label>
                        <input type="number" id="productPrice" name="price" step="0.01" min="0" required>
                        <div class="error-message" id="priceError"></div>
                    </div>

                    <div class="form-group">
                        <label for="productCategory">商品分类 <span class="required">*</span></label>
                        <select id="productCategory" name="category" required>
                            <option value="">请选择分类</option>
                            <option value="switches">智能开关</option>
                            <option value="sensors">传感器</option>
                            <option value="cameras">摄像头</option>
                            <option value="lighting">智能照明</option>
                        </select>
                        <div class="error-message" id="categoryError"></div>
                    </div>

                    <div class="form-group">
                        <label for="productDescription">商品描述</label>
                        <textarea id="productDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="productImage">商品图片</label>
                        <div class="file-upload-area" onclick="document.getElementById('productImageFile').click()">
                            <input type="file" id="productImageFile" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                            <div class="upload-placeholder" id="uploadPlaceholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传图片或拖拽图片到此处</p>
                                <p class="upload-hint">支持 JPG、PNG 格式，大小不超过 5MB</p>
                            </div>
                            <div class="upload-preview" id="uploadPreview" style="display: none;">
                                <img id="previewImage" src="" alt="预览图片">
                                <button type="button" class="remove-image" onclick="removeImage()">&times;</button>
                            </div>
                        </div>
                        <div class="error-message" id="imageError"></div>
                    </div>

                    <div class="form-group">
                        <label for="productStatus">商品状态</label>
                        <select id="productStatus" name="status">
                            <option value="active">在售</option>
                            <option value="inactive">下架</option>
                            <option value="draft">草稿</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeProductModal()">取消</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <span class="btn-text">保存</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> 保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>确认删除</h3>
                <span class="modal-close" onclick="closeDeleteModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>确定要删除商品 "<span id="deleteProductName"></span>" 吗？</p>
                <p class="warning-text">此操作不可撤销！</p>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()" id="deleteBtn">
                        <span class="btn-text">确认删除</span>
                        <span class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> 删除中...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类管理模态框 -->
    <div id="categoryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="categoryModalTitle">新建分类</h3>
                <span class="modal-close" onclick="closeCategoryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="categoryForm" onsubmit="submitCategoryForm(event)">
                    <div class="form-group">
                        <label for="categoryName">分类名称 <span class="required">*</span></label>
                        <input type="text" id="categoryName" name="name" required>
                        <div class="error-message" id="categoryNameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="categoryDescription">分类描述</label>
                        <textarea id="categoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="categoryIcon">分类图标</label>
                        <select id="categoryIcon" name="icon">
                            <option value="fas fa-folder">📁 文件夹</option>
                            <option value="fas fa-cube">📦 立方体</option>
                            <option value="fas fa-tag">🏷️ 标签</option>
                            <option value="fas fa-star">⭐ 星星</option>
                            <option value="fas fa-heart">❤️ 心形</option>
                            <option value="fas fa-home">🏠 房屋</option>
                            <option value="fas fa-cog">⚙️ 设置</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()">取消</button>
                        <button type="submit" class="btn btn-primary" id="categorySubmitBtn">
                            <span class="btn-text">保存</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> 保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 定价策略模态框 -->
    <div id="pricingModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="pricingModalTitle">新建定价策略</h3>
                <span class="modal-close" onclick="closePricingModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="pricingForm" onsubmit="submitPricingForm(event)">
                    <div class="form-group">
                        <label for="strategyName">策略名称 <span class="required">*</span></label>
                        <input type="text" id="strategyName" name="name" required>
                        <div class="error-message" id="strategyNameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="strategyType">策略类型 <span class="required">*</span></label>
                        <select id="strategyType" name="type" required>
                            <option value="">请选择策略类型</option>
                            <option value="standard">标准定价</option>
                            <option value="promotion">促销定价</option>
                            <option value="bulk">批量定价</option>
                            <option value="member">会员定价</option>
                            <option value="seasonal">季节性定价</option>
                        </select>
                        <div class="error-message" id="strategyTypeError"></div>
                    </div>

                    <div class="form-group">
                        <label for="strategyDiscount">折扣比例 (%)</label>
                        <input type="number" id="strategyDiscount" name="discount" min="0" max="100" step="0.1" value="0">
                        <div class="error-message" id="strategyDiscountError"></div>
                    </div>

                    <div class="form-group">
                        <label for="strategyDescription">策略描述</label>
                        <textarea id="strategyDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="strategyStatus">策略状态</label>
                        <select id="strategyStatus" name="status">
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closePricingModal()">取消</button>
                        <button type="submit" class="btn btn-primary" id="pricingSubmitBtn">
                            <span class="btn-text">保存</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> 保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const appState = {
            currentMainMenu: 'products',
            currentSubMenu: 'all',
            products: [
                {
                    id: 1,
                    name: '户型优化设计',
                    price: 299,
                    status: 'active',
                    category: 'design',
                    image: 'fas fa-home',
                    description: '专业户型分析与空间优化方案'
                },
                {
                    id: 2,
                    name: '全屋智能效果图',
                    price: 999,
                    status: 'active',
                    category: 'design',
                    image: 'fas fa-star',
                    description: '3D效果图渲染与智能设备布局'
                },
                {
                    id: 3,
                    name: '大师自定制设计',
                    price: 2999,
                    status: 'draft',
                    category: 'premium',
                    image: 'fas fa-crown',
                    description: '顶级设计师一对一定制服务'
                }
            ],
            categories: [
                { id: 1, name: '智能开关', count: 15, parent: null },
                { id: 2, name: '智能照明', count: 23, parent: null },
                { id: 3, name: '安防设备', count: 18, parent: null },
                { id: 4, name: '环境控制', count: 12, parent: null }
            ],
            pricingStrategies: [
                { id: 1, name: '标准定价', type: 'standard', discount: 0 },
                { id: 2, name: '促销定价', type: 'promotion', discount: 15 },
                { id: 3, name: '批量定价', type: 'bulk', discount: 20 },
                { id: 4, name: '会员定价', type: 'member', discount: 10 }
            ]
        };

        // 主菜单切换
        function showMainMenu(menu) {
            // 更新主菜单激活状态
            document.querySelectorAll('.main-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状态
            appState.currentMainMenu = menu;
            appState.currentSubMenu = getDefaultSubMenu(menu);

            // 更新子菜单
            updateSubMenu(menu);

            // 更新内容区域
            updateContent();
        }

        // 获取默认子菜单
        function getDefaultSubMenu(mainMenu) {
            const defaults = {
                'products': 'all',
                'categories': 'switches',
                'pricing': 'standard'
            };
            return defaults[mainMenu] || 'all';
        }

        // 更新子菜单
        function updateSubMenu(mainMenu) {
            const subMenuTabs = document.getElementById('subMenuTabs');
            let subMenus = [];

            switch(mainMenu) {
                case 'products':
                    subMenus = [
                        { id: 'all', name: '全部商品' },
                        { id: 'active', name: '在售商品' },
                        { id: 'inactive', name: '下架商品' },
                        { id: 'draft', name: '草稿商品' }
                    ];
                    break;
                case 'categories':
                    subMenus = [
                        { id: 'switches', name: '智能开关' },
                        { id: 'lighting', name: '智能照明' },
                        { id: 'security', name: '安防设备' },
                        { id: 'environment', name: '环境控制' }
                    ];
                    break;
                case 'pricing':
                    subMenus = [
                        { id: 'standard', name: '标准定价' },
                        { id: 'promotion', name: '促销定价' },
                        { id: 'bulk', name: '批量定价' },
                        { id: 'member', name: '会员定价' }
                    ];
                    break;
            }

            subMenuTabs.innerHTML = subMenus.map(menu =>
                `<button class="sub-menu-tab ${menu.id === appState.currentSubMenu ? 'active' : ''}"
                         onclick="showSubMenu('${mainMenu}', '${menu.id}')">${menu.name}</button>`
            ).join('');
        }

        // 子菜单切换
        function showSubMenu(mainMenu, subMenu) {
            // 更新子菜单激活状态
            document.querySelectorAll('.sub-menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新全局状态
            appState.currentSubMenu = subMenu;

            // 更新内容区域
            updateContent();
        }

        // 更新内容区域
        function updateContent() {
            const contentTitle = document.getElementById('contentTitle');
            const contentActions = document.getElementById('contentActions');
            const contentBody = document.getElementById('contentBody');

            switch(appState.currentMainMenu) {
                case 'products':
                    renderProductsContent(contentTitle, contentActions, contentBody);
                    break;
                case 'categories':
                    renderCategoriesContent(contentTitle, contentActions, contentBody);
                    break;
                case 'pricing':
                    renderPricingContent(contentTitle, contentActions, contentBody);
                    break;
            }
        }

        // 渲染商品列表内容
        function renderProductsContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '商品列表';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary">
                    <i class="fas fa-filter"></i>
                    筛选
                </button>
                <button class="btn btn-primary" onclick="openCreateProductModal()">
                    <i class="fas fa-plus"></i>
                    新建商品
                </button>
            `;

            // 根据子菜单筛选商品
            let filteredProducts = appState.products;
            if (appState.currentSubMenu !== 'all') {
                filteredProducts = appState.products.filter(product =>
                    product.status === appState.currentSubMenu
                );
            }

            bodyEl.innerHTML = `
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索商品名称、描述..." onkeyup="searchProducts(this.value)">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active">全部</button>
                        <button class="filter-btn">设计类</button>
                        <button class="filter-btn">高端定制</button>
                    </div>
                </div>
                <div class="product-grid">
                    ${filteredProducts.map(product => `
                        <div class="product-card">
                            <div class="product-image">
                                ${product.image && product.image.startsWith('data:image/')
                                    ? `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">`
                                    : `<i class="${product.image || 'fas fa-cube'}"></i>`
                                }
                            </div>
                            <div class="product-info">
                                <div class="product-name">${product.name}</div>
                                <div class="product-price">¥${product.price}</div>
                                <span class="product-status status-${product.status}">
                                    ${getStatusText(product.status)}
                                </span>
                                <div class="product-actions">
                                    <button class="btn-sm btn-secondary" data-action="edit" data-product-id="${product.id}">编辑</button>
                                    <button class="btn-sm btn-danger" data-action="delete" data-product-id="${product.id}">删除</button>
                                    <button class="btn-sm btn-primary" data-action="view" data-product-id="${product.id}">查看</button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 渲染分类管理内容
        function renderCategoriesContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '商品分类';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary" onclick="exportCategories()">
                    <i class="fas fa-download"></i>
                    导出分类
                </button>
                <button class="btn btn-primary" onclick="openCreateCategoryModal()">
                    <i class="fas fa-plus"></i>
                    新建分类
                </button>
            `;

            bodyEl.innerHTML = `
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索分类名称..." onkeyup="searchCategories(this.value)">
                    </div>
                </div>
                <div class="category-tree">
                    ${appState.categories.map(category => `
                        <div class="category-item">
                            <div class="category-info">
                                <i class="fas fa-folder"></i>
                                <span class="category-name">${category.name}</span>
                                <span class="category-count">(${category.count}个商品)</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn-sm btn-secondary" data-action="edit-category" data-category-id="${category.id}">编辑</button>
                                <button class="btn-sm btn-danger" data-action="delete-category" data-category-id="${category.id}">删除</button>
                                <button class="btn-sm btn-primary" data-action="manage-category" data-category-id="${category.id}">管理</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 渲染价格管理内容
        function renderPricingContent(titleEl, actionsEl, bodyEl) {
            titleEl.textContent = '价格管理';

            actionsEl.innerHTML = `
                <button class="btn btn-secondary" onclick="showPriceAnalysis()">
                    <i class="fas fa-chart-line"></i>
                    价格分析
                </button>
                <button class="btn btn-primary" onclick="openCreatePricingModal()">
                    <i class="fas fa-plus"></i>
                    新建策略
                </button>
            `;

            bodyEl.innerHTML = `
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索定价策略..." onkeyup="searchPricingStrategies(this.value)">
                    </div>
                </div>
                <div class="pricing-strategies">
                    ${appState.pricingStrategies.map(strategy => `
                        <div class="strategy-item">
                            <div class="strategy-info">
                                <i class="fas fa-tag"></i>
                                <div>
                                    <div class="strategy-name">${strategy.name}</div>
                                    <div class="strategy-desc">折扣: ${strategy.discount}%</div>
                                </div>
                            </div>
                            <div class="strategy-actions">
                                <button class="btn-sm btn-secondary" data-action="edit-pricing" data-pricing-id="${strategy.id}">编辑</button>
                                <button class="btn-sm btn-danger" data-action="delete-pricing" data-pricing-id="${strategy.id}">删除</button>
                                <button class="btn-sm btn-primary" data-action="apply-pricing" data-pricing-id="${strategy.id}">应用</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '在售',
                'inactive': '下架',
                'draft': '草稿'
            };
            return statusMap[status] || status;
        }

        // 搜索商品
        function searchProducts(query) {
            // 这里可以实现搜索逻辑
            console.log('搜索商品:', query);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化子菜单
            updateSubMenu(appState.currentMainMenu);

            // 初始化内容区域
            updateContent();

            // 初始化事件委托
            initializeEventDelegation();
        });

        // Toast 提示功能
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1f2937'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // ==================== 商品管理功能 ====================

        // 全局变量
        let currentEditingProduct = null;
        let currentDeletingProduct = null;
        let uploadedImageData = null;

        // 初始化事件委托
        function initializeEventDelegation() {
            // 为动态生成的按钮添加事件委托
            document.addEventListener('click', function(event) {
                const target = event.target;

                // 处理各种操作按钮
                if (target.hasAttribute('data-action')) {
                    const action = target.getAttribute('data-action');
                    const productId = target.getAttribute('data-product-id');
                    const categoryId = target.getAttribute('data-category-id');
                    const pricingId = target.getAttribute('data-pricing-id');

                    switch (action) {
                        // 商品操作
                        case 'edit':
                            editProduct(productId);
                            break;
                        case 'delete':
                            deleteProduct(productId);
                            break;
                        case 'view':
                            viewProduct(productId);
                            break;

                        // 分类操作
                        case 'edit-category':
                            editCategory(categoryId);
                            break;
                        case 'delete-category':
                            deleteCategory(categoryId);
                            break;
                        case 'manage-category':
                            manageCategory(categoryId);
                            break;

                        // 定价操作
                        case 'edit-pricing':
                            editPricing(pricingId);
                            break;
                        case 'delete-pricing':
                            deletePricing(pricingId);
                            break;
                        case 'apply-pricing':
                            applyPricing(pricingId);
                            break;
                    }
                }
            });

            // 模态框点击外部关闭
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    closeProductModal();
                    closeDeleteModal();
                    closeCategoryModal();
                    closePricingModal();
                }
            });
        }

        // 打开新建商品模态框
        function openCreateProductModal() {
            currentEditingProduct = null;
            document.getElementById('modalTitle').textContent = '新建商品';
            document.getElementById('productForm').reset();
            clearFormErrors();
            resetImageUpload();
            document.getElementById('productModal').style.display = 'flex';
        }

        // 编辑商品
        function editProduct(productId) {
            // 从模拟数据中查找商品
            const product = findProductById(productId);
            if (!product) {
                showToast('商品不存在', 'error');
                return;
            }

            currentEditingProduct = product;
            document.getElementById('modalTitle').textContent = '编辑商品';

            // 填充表单数据
            document.getElementById('productName').value = product.name;
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productCategory').value = product.category || 'switches';
            document.getElementById('productDescription').value = product.description || '';
            document.getElementById('productStatus').value = product.status;

            clearFormErrors();

            // 处理图片显示
            if (product.image && product.image.startsWith('data:image/')) {
                // 如果是上传的图片，显示预览
                uploadedImageData = product.image;
                showImagePreview(product.image);
            } else {
                // 如果是图标或没有图片，重置上传区域
                resetImageUpload();
                uploadedImageData = null;
            }

            document.getElementById('productModal').style.display = 'flex';
        }

        // 删除商品
        function deleteProduct(productId) {
            const product = findProductById(productId);
            if (!product) {
                showToast('商品不存在', 'error');
                return;
            }

            currentDeletingProduct = product;
            document.getElementById('deleteProductName').textContent = product.name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        // 查看商品详情
        function viewProduct(productId) {
            const product = findProductById(productId);
            if (!product) {
                showToast('商品不存在', 'error');
                return;
            }

            // 这里可以打开商品详情页面或模态框
            showToast(`查看商品: ${product.name}`, 'info');
        }

        // 查找商品
        function findProductById(productId) {
            // 从appState中查找商品
            return appState.products.find(product => product.id == productId);
        }

        // 关闭商品模态框
        function closeProductModal() {
            document.getElementById('productModal').style.display = 'none';
            currentEditingProduct = null;
            uploadedImageData = null;
        }

        // 关闭删除模态框
        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
            currentDeletingProduct = null;
        }

        // 提交商品表单
        async function submitProductForm(event) {
            event.preventDefault();

            // 清除之前的错误信息
            clearFormErrors();

            // 获取表单数据
            const formData = new FormData(event.target);
            const productData = {
                name: formData.get('name').trim(),
                price: parseFloat(formData.get('price')),
                category: formData.get('category'),
                description: formData.get('description').trim(),
                status: formData.get('status'),
                image: uploadedImageData
            };

            // 表单验证
            if (!validateProductForm(productData)) {
                return;
            }

            // 显示加载状态
            setSubmitButtonLoading(true);

            try {
                // 模拟API调用
                await simulateAPICall();

                if (currentEditingProduct) {
                    // 更新商品
                    updateProductInData(currentEditingProduct.id, productData);
                    showToast('商品更新成功', 'success');
                } else {
                    // 创建新商品
                    addProductToData(productData);
                    showToast('商品创建成功', 'success');
                }

                // 刷新页面内容
                updateContent();
                closeProductModal();

            } catch (error) {
                showToast('操作失败，请重试', 'error');
            } finally {
                setSubmitButtonLoading(false);
            }
        }

        // 确认删除
        async function confirmDelete() {
            if (!currentDeletingProduct) return;

            setDeleteButtonLoading(true);

            try {
                // 模拟API调用
                await simulateAPICall();

                // 从数据中删除商品
                removeProductFromData(currentDeletingProduct.id);

                showToast('商品删除成功', 'success');
                updateContent();
                closeDeleteModal();

            } catch (error) {
                showToast('删除失败，请重试', 'error');
            } finally {
                setDeleteButtonLoading(false);
            }
        }

        // 表单验证
        function validateProductForm(data) {
            let isValid = true;

            // 商品名称验证
            if (!data.name) {
                showFieldError('nameError', '请输入商品名称');
                isValid = false;
            } else if (data.name.length < 2) {
                showFieldError('nameError', '商品名称至少2个字符');
                isValid = false;
            }

            // 价格验证
            if (!data.price || data.price <= 0) {
                showFieldError('priceError', '请输入有效的商品价格');
                isValid = false;
            }

            // 分类验证
            if (!data.category) {
                showFieldError('categoryError', '请选择商品分类');
                isValid = false;
            }

            return isValid;
        }

        // 显示字段错误
        function showFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        // 清除表单错误
        function clearFormErrors() {
            const errorElements = document.querySelectorAll('.error-message');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }

        // 设置提交按钮加载状态
        function setSubmitButtonLoading(loading) {
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            if (loading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                submitBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // 设置删除按钮加载状态
        function setDeleteButtonLoading(loading) {
            const deleteBtn = document.getElementById('deleteBtn');
            const btnText = deleteBtn.querySelector('.btn-text');
            const btnLoading = deleteBtn.querySelector('.btn-loading');

            if (loading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                deleteBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                deleteBtn.disabled = false;
            }
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 文件类型验证
            if (!file.type.startsWith('image/')) {
                showFieldError('imageError', '请选择图片文件');
                return;
            }

            // 文件大小验证 (5MB)
            if (file.size > 5 * 1024 * 1024) {
                showFieldError('imageError', '图片大小不能超过5MB');
                return;
            }

            // 读取文件并显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedImageData = e.target.result;
                showImagePreview(e.target.result);
                clearFieldError('imageError');
            };
            reader.readAsDataURL(file);
        }

        // 显示图片预览
        function showImagePreview(imageSrc) {
            const placeholder = document.getElementById('uploadPlaceholder');
            const preview = document.getElementById('uploadPreview');
            const previewImage = document.getElementById('previewImage');

            placeholder.style.display = 'none';
            preview.style.display = 'block';
            previewImage.src = imageSrc;
        }

        // 移除图片
        function removeImage() {
            uploadedImageData = null;
            resetImageUpload();
            document.getElementById('productImageFile').value = '';
        }

        // 重置图片上传区域
        function resetImageUpload() {
            const placeholder = document.getElementById('uploadPlaceholder');
            const preview = document.getElementById('uploadPreview');

            placeholder.style.display = 'block';
            preview.style.display = 'none';
        }

        // 清除字段错误
        function clearFieldError(errorId) {
            const errorElement = document.getElementById(errorId);
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            }
        }

        // 模拟API调用
        function simulateAPICall() {
            return new Promise((resolve) => {
                setTimeout(resolve, 1000 + Math.random() * 1000);
            });
        }

        // 数据操作函数
        function addProductToData(productData) {
            const newProduct = {
                id: Date.now(),
                name: productData.name,
                price: productData.price,
                category: productData.category,
                description: productData.description,
                status: productData.status,
                image: productData.image || 'fas fa-cube'
            };

            // 添加到appState.products数组
            appState.products.push(newProduct);
        }

        function updateProductInData(productId, productData) {
            // 在appState.products中查找并更新商品
            const index = appState.products.findIndex(p => p.id == productId);
            if (index !== -1) {
                appState.products[index] = {
                    ...appState.products[index],
                    name: productData.name,
                    price: productData.price,
                    category: productData.category,
                    description: productData.description,
                    status: productData.status,
                    image: productData.image || appState.products[index].image
                };
            }
        }

        function removeProductFromData(productId) {
            // 在appState.products中查找并删除商品
            const index = appState.products.findIndex(p => p.id == productId);
            if (index !== -1) {
                appState.products.splice(index, 1);
            }
        }

        // 增强搜索功能
        function searchProducts(query) {
            if (!query.trim()) {
                updateContent();
                return;
            }

            const searchTerm = query.toLowerCase();
            const allProducts = [];

            // 收集所有商品
            appState.products.forEach(product => {
                if (product.name.toLowerCase().includes(searchTerm) ||
                    (product.description && product.description.toLowerCase().includes(searchTerm))) {
                    allProducts.push(product);
                }
            });

            // 显示搜索结果
            const contentBody = document.getElementById('contentBody');
            if (allProducts.length === 0) {
                contentBody.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>未找到相关商品</h3>
                        <p>尝试使用其他关键词搜索</p>
                    </div>
                `;
            } else {
                contentBody.innerHTML = `
                    <div class="search-results">
                        <p class="search-info">找到 ${allProducts.length} 个相关商品</p>
                        <div class="product-grid">
                            ${allProducts.map(product => `
                                <div class="product-card">
                                    <div class="product-image">
                                        ${product.image && product.image.startsWith('data:image/')
                                            ? `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">`
                                            : `<i class="${product.image || 'fas fa-cube'}"></i>`
                                        }
                                    </div>
                                    <div class="product-info">
                                        <div class="product-name">${product.name}</div>
                                        <div class="product-price">¥${product.price}</div>
                                        <span class="product-status status-${product.status}">
                                            ${getStatusText(product.status)}
                                        </span>
                                        <div class="product-actions">
                                            <button class="btn-sm btn-secondary" data-action="edit" data-product-id="${product.id}">编辑</button>
                                            <button class="btn-sm btn-danger" data-action="delete" data-product-id="${product.id}">删除</button>
                                            <button class="btn-sm btn-primary" data-action="view" data-product-id="${product.id}">查看</button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // ==================== 分类管理功能 ====================

        // 全局变量
        let currentEditingCategory = null;
        let currentDeletingCategory = null;

        // 打开新建分类模态框
        function openCreateCategoryModal() {
            currentEditingCategory = null;
            document.getElementById('categoryModalTitle').textContent = '新建分类';
            document.getElementById('categoryForm').reset();
            clearCategoryFormErrors();
            document.getElementById('categoryModal').style.display = 'flex';
        }

        // 编辑分类
        function editCategory(categoryId) {
            const category = appState.categories.find(c => c.id == categoryId);
            if (!category) {
                showToast('分类不存在', 'error');
                return;
            }

            currentEditingCategory = category;
            document.getElementById('categoryModalTitle').textContent = '编辑分类';

            // 填充表单数据
            document.getElementById('categoryName').value = category.name;
            document.getElementById('categoryDescription').value = category.description || '';
            document.getElementById('categoryIcon').value = category.icon || 'fas fa-folder';

            clearCategoryFormErrors();
            document.getElementById('categoryModal').style.display = 'flex';
        }

        // 删除分类
        function deleteCategory(categoryId) {
            const category = appState.categories.find(c => c.id == categoryId);
            if (!category) {
                showToast('分类不存在', 'error');
                return;
            }

            if (category.count > 0) {
                showToast('该分类下还有商品，无法删除', 'error');
                return;
            }

            if (confirm(`确定要删除分类"${category.name}"吗？`)) {
                const index = appState.categories.findIndex(c => c.id == categoryId);
                if (index !== -1) {
                    appState.categories.splice(index, 1);
                    updateContent();
                    showToast('分类删除成功', 'success');
                }
            }
        }

        // 管理分类
        function manageCategory(categoryId) {
            const category = appState.categories.find(c => c.id == categoryId);
            if (!category) {
                showToast('分类不存在', 'error');
                return;
            }

            // 这里可以跳转到分类商品管理页面
            showToast(`管理分类: ${category.name}`, 'info');
        }

        // 关闭分类模态框
        function closeCategoryModal() {
            document.getElementById('categoryModal').style.display = 'none';
            currentEditingCategory = null;
        }

        // 提交分类表单
        async function submitCategoryForm(event) {
            event.preventDefault();

            clearCategoryFormErrors();

            const formData = new FormData(event.target);
            const categoryData = {
                name: formData.get('name').trim(),
                description: formData.get('description').trim(),
                icon: formData.get('icon')
            };

            // 表单验证
            if (!validateCategoryForm(categoryData)) {
                return;
            }

            setCategorySubmitButtonLoading(true);

            try {
                await simulateAPICall();

                if (currentEditingCategory) {
                    // 更新分类
                    const index = appState.categories.findIndex(c => c.id == currentEditingCategory.id);
                    if (index !== -1) {
                        appState.categories[index] = {
                            ...appState.categories[index],
                            name: categoryData.name,
                            description: categoryData.description,
                            icon: categoryData.icon
                        };
                    }
                    showToast('分类更新成功', 'success');
                } else {
                    // 创建新分类
                    const newCategory = {
                        id: Date.now(),
                        name: categoryData.name,
                        description: categoryData.description,
                        icon: categoryData.icon,
                        count: 0,
                        parent: null
                    };
                    appState.categories.push(newCategory);
                    showToast('分类创建成功', 'success');
                }

                updateContent();
                closeCategoryModal();

            } catch (error) {
                showToast('操作失败，请重试', 'error');
            } finally {
                setCategorySubmitButtonLoading(false);
            }
        }

        // 验证分类表单
        function validateCategoryForm(data) {
            let isValid = true;

            if (!data.name) {
                showCategoryFieldError('categoryNameError', '请输入分类名称');
                isValid = false;
            } else if (data.name.length < 2) {
                showCategoryFieldError('categoryNameError', '分类名称至少2个字符');
                isValid = false;
            }

            return isValid;
        }

        // 显示分类字段错误
        function showCategoryFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        // 清除分类表单错误
        function clearCategoryFormErrors() {
            const errorElements = document.querySelectorAll('#categoryModal .error-message');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }

        // 设置分类提交按钮加载状态
        function setCategorySubmitButtonLoading(loading) {
            const submitBtn = document.getElementById('categorySubmitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            if (loading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                submitBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // 搜索分类
        function searchCategories(query) {
            if (!query.trim()) {
                updateContent();
                return;
            }

            const searchTerm = query.toLowerCase();
            const filteredCategories = appState.categories.filter(category =>
                category.name.toLowerCase().includes(searchTerm) ||
                (category.description && category.description.toLowerCase().includes(searchTerm))
            );

            const contentBody = document.getElementById('contentBody');
            if (filteredCategories.length === 0) {
                contentBody.innerHTML = `
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索分类名称..." onkeyup="searchCategories(this.value)" value="${query}">
                        </div>
                    </div>
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>未找到相关分类</h3>
                        <p>尝试使用其他关键词搜索</p>
                    </div>
                `;
            } else {
                contentBody.innerHTML = `
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索分类名称..." onkeyup="searchCategories(this.value)" value="${query}">
                        </div>
                    </div>
                    <div class="search-results">
                        <p class="search-info">找到 ${filteredCategories.length} 个相关分类</p>
                        <div class="category-tree">
                            ${filteredCategories.map(category => `
                                <div class="category-item">
                                    <div class="category-info">
                                        <i class="${category.icon || 'fas fa-folder'}"></i>
                                        <span class="category-name">${category.name}</span>
                                        <span class="category-count">(${category.count}个商品)</span>
                                    </div>
                                    <div class="category-actions">
                                        <button class="btn-sm btn-secondary" data-action="edit-category" data-category-id="${category.id}">编辑</button>
                                        <button class="btn-sm btn-danger" data-action="delete-category" data-category-id="${category.id}">删除</button>
                                        <button class="btn-sm btn-primary" data-action="manage-category" data-category-id="${category.id}">管理</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // 导出分类
        function exportCategories() {
            const csvContent = "data:text/csv;charset=utf-8,"
                + "分类ID,分类名称,商品数量,描述\n"
                + appState.categories.map(category =>
                    `${category.id},"${category.name}",${category.count},"${category.description || ''}"`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `商品分类_${new Date().toISOString().slice(0, 10)}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('分类数据导出成功', 'success');
        }

        // ==================== 定价管理功能 ====================

        // 全局变量
        let currentEditingPricing = null;

        // 打开新建定价策略模态框
        function openCreatePricingModal() {
            currentEditingPricing = null;
            document.getElementById('pricingModalTitle').textContent = '新建定价策略';
            document.getElementById('pricingForm').reset();
            clearPricingFormErrors();
            document.getElementById('pricingModal').style.display = 'flex';
        }

        // 编辑定价策略
        function editPricing(pricingId) {
            const pricing = appState.pricingStrategies.find(p => p.id == pricingId);
            if (!pricing) {
                showToast('定价策略不存在', 'error');
                return;
            }

            currentEditingPricing = pricing;
            document.getElementById('pricingModalTitle').textContent = '编辑定价策略';

            // 填充表单数据
            document.getElementById('strategyName').value = pricing.name;
            document.getElementById('strategyType').value = pricing.type;
            document.getElementById('strategyDiscount').value = pricing.discount;
            document.getElementById('strategyDescription').value = pricing.description || '';
            document.getElementById('strategyStatus').value = pricing.status || 'active';

            clearPricingFormErrors();
            document.getElementById('pricingModal').style.display = 'flex';
        }

        // 删除定价策略
        function deletePricing(pricingId) {
            const pricing = appState.pricingStrategies.find(p => p.id == pricingId);
            if (!pricing) {
                showToast('定价策略不存在', 'error');
                return;
            }

            if (confirm(`确定要删除定价策略"${pricing.name}"吗？`)) {
                const index = appState.pricingStrategies.findIndex(p => p.id == pricingId);
                if (index !== -1) {
                    appState.pricingStrategies.splice(index, 1);
                    updateContent();
                    showToast('定价策略删除成功', 'success');
                }
            }
        }

        // 应用定价策略
        function applyPricing(pricingId) {
            const pricing = appState.pricingStrategies.find(p => p.id == pricingId);
            if (!pricing) {
                showToast('定价策略不存在', 'error');
                return;
            }

            // 这里可以实现将定价策略应用到商品的逻辑
            showToast(`应用定价策略: ${pricing.name}`, 'success');
        }

        // 关闭定价模态框
        function closePricingModal() {
            document.getElementById('pricingModal').style.display = 'none';
            currentEditingPricing = null;
        }

        // 提交定价表单
        async function submitPricingForm(event) {
            event.preventDefault();

            clearPricingFormErrors();

            const formData = new FormData(event.target);
            const pricingData = {
                name: formData.get('name').trim(),
                type: formData.get('type'),
                discount: parseFloat(formData.get('discount')),
                description: formData.get('description').trim(),
                status: formData.get('status')
            };

            // 表单验证
            if (!validatePricingForm(pricingData)) {
                return;
            }

            setPricingSubmitButtonLoading(true);

            try {
                await simulateAPICall();

                if (currentEditingPricing) {
                    // 更新定价策略
                    const index = appState.pricingStrategies.findIndex(p => p.id == currentEditingPricing.id);
                    if (index !== -1) {
                        appState.pricingStrategies[index] = {
                            ...appState.pricingStrategies[index],
                            name: pricingData.name,
                            type: pricingData.type,
                            discount: pricingData.discount,
                            description: pricingData.description,
                            status: pricingData.status
                        };
                    }
                    showToast('定价策略更新成功', 'success');
                } else {
                    // 创建新定价策略
                    const newPricing = {
                        id: Date.now(),
                        name: pricingData.name,
                        type: pricingData.type,
                        discount: pricingData.discount,
                        description: pricingData.description,
                        status: pricingData.status
                    };
                    appState.pricingStrategies.push(newPricing);
                    showToast('定价策略创建成功', 'success');
                }

                updateContent();
                closePricingModal();

            } catch (error) {
                showToast('操作失败，请重试', 'error');
            } finally {
                setPricingSubmitButtonLoading(false);
            }
        }

        // 验证定价表单
        function validatePricingForm(data) {
            let isValid = true;

            if (!data.name) {
                showPricingFieldError('strategyNameError', '请输入策略名称');
                isValid = false;
            } else if (data.name.length < 2) {
                showPricingFieldError('strategyNameError', '策略名称至少2个字符');
                isValid = false;
            }

            if (!data.type) {
                showPricingFieldError('strategyTypeError', '请选择策略类型');
                isValid = false;
            }

            if (data.discount < 0 || data.discount > 100) {
                showPricingFieldError('strategyDiscountError', '折扣比例必须在0-100之间');
                isValid = false;
            }

            return isValid;
        }

        // 显示定价字段错误
        function showPricingFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        // 清除定价表单错误
        function clearPricingFormErrors() {
            const errorElements = document.querySelectorAll('#pricingModal .error-message');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }

        // 设置定价提交按钮加载状态
        function setPricingSubmitButtonLoading(loading) {
            const submitBtn = document.getElementById('pricingSubmitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            if (loading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                submitBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // 搜索定价策略
        function searchPricingStrategies(query) {
            if (!query.trim()) {
                updateContent();
                return;
            }

            const searchTerm = query.toLowerCase();
            const filteredStrategies = appState.pricingStrategies.filter(strategy =>
                strategy.name.toLowerCase().includes(searchTerm) ||
                strategy.type.toLowerCase().includes(searchTerm) ||
                (strategy.description && strategy.description.toLowerCase().includes(searchTerm))
            );

            const contentBody = document.getElementById('contentBody');
            if (filteredStrategies.length === 0) {
                contentBody.innerHTML = `
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索定价策略..." onkeyup="searchPricingStrategies(this.value)" value="${query}">
                        </div>
                    </div>
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>未找到相关定价策略</h3>
                        <p>尝试使用其他关键词搜索</p>
                    </div>
                `;
            } else {
                contentBody.innerHTML = `
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索定价策略..." onkeyup="searchPricingStrategies(this.value)" value="${query}">
                        </div>
                    </div>
                    <div class="search-results">
                        <p class="search-info">找到 ${filteredStrategies.length} 个相关策略</p>
                        <div class="pricing-strategies">
                            ${filteredStrategies.map(strategy => `
                                <div class="strategy-item">
                                    <div class="strategy-info">
                                        <i class="fas fa-tag"></i>
                                        <div>
                                            <div class="strategy-name">${strategy.name}</div>
                                            <div class="strategy-desc">折扣: ${strategy.discount}%</div>
                                        </div>
                                    </div>
                                    <div class="strategy-actions">
                                        <button class="btn-sm btn-secondary" data-action="edit-pricing" data-pricing-id="${strategy.id}">编辑</button>
                                        <button class="btn-sm btn-danger" data-action="delete-pricing" data-pricing-id="${strategy.id}">删除</button>
                                        <button class="btn-sm btn-primary" data-action="apply-pricing" data-pricing-id="${strategy.id}">应用</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // 显示价格分析
        function showPriceAnalysis() {
            // 计算价格统计
            const totalProducts = appState.products.length;
            const avgPrice = totalProducts > 0 ?
                (appState.products.reduce((sum, p) => sum + p.price, 0) / totalProducts).toFixed(2) : 0;
            const maxPrice = totalProducts > 0 ?
                Math.max(...appState.products.map(p => p.price)) : 0;
            const minPrice = totalProducts > 0 ?
                Math.min(...appState.products.map(p => p.price)) : 0;

            showToast(`价格分析: 商品总数${totalProducts}个，平均价格¥${avgPrice}，最高价格¥${maxPrice}，最低价格¥${minPrice}`, 'info');
        }
    </script>

    <style>
        /* 分类树样式 */
        .category-tree {
            margin-top: 20px;
        }

        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #ffffff;
            transition: all 0.2s ease;
        }

        .category-item:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .category-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .category-info i {
            color: #6b7280;
            font-size: 16px;
        }

        .category-name {
            font-weight: 600;
            color: #1f2937;
        }

        .category-count {
            color: #6b7280;
            font-size: 14px;
        }

        .category-actions {
            display: flex;
            gap: 8px;
        }

        /* 定价策略样式 */
        .pricing-strategies {
            margin-top: 20px;
        }

        .strategy-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #ffffff;
            transition: all 0.2s ease;
        }

        .strategy-item:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .strategy-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .strategy-info i {
            color: #6b7280;
            font-size: 16px;
        }

        .strategy-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .strategy-desc {
            color: #6b7280;
            font-size: 14px;
        }

        .strategy-actions {
            display: flex;
            gap: 8px;
        }

        /* ==================== 模态框样式 ==================== */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-small .modal-content {
            max-width: 400px;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 24px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            line-height: 1;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 0 24px 24px;
        }

        /* ==================== 表单样式 ==================== */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .required {
            color: #ef4444;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .error-message {
            display: none;
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        }

        /* ==================== 文件上传样式 ==================== */
        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.2s ease, background-color 0.2s ease;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .upload-placeholder i {
            font-size: 32px;
            color: #9ca3af;
            margin-bottom: 12px;
        }

        .upload-placeholder p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }

        .upload-hint {
            font-size: 12px !important;
            color: #9ca3af !important;
            margin-top: 4px !important;
        }

        .upload-preview {
            position: relative;
        }

        .upload-preview img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 6px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #ef4444;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .remove-image:hover {
            background: #dc2626;
        }

        /* ==================== 表单操作按钮 ==================== */
        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #e5e7eb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #dc2626;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-loading {
            display: none;
            align-items: center;
            gap: 6px;
        }

        .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* ==================== 警告文本 ==================== */
        .warning-text {
            color: #f59e0b;
            font-size: 13px;
            margin-top: 8px;
        }

        /* ==================== 搜索结果样式 ==================== */
        .search-results {
            margin-top: 20px;
        }

        .search-info {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d1d5db;
        }

        .empty-state h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #374151;
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
        }
    </style>
</body>
</html>

/**
 * 只修复左侧菜单样式脚本
 * 仅修复侧边栏相关样式，不触碰右侧内容样式
 */

const fs = require('fs');
const path = require('path');

// 只包含侧边栏相关的样式
const sidebarOnlyStyles = `        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }`;

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 只修复侧边栏样式，不触碰其他样式
function fixSidebarOnly(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        let hasChanges = false;
        
        // 只替换侧边栏相关的样式块
        const sidebarStylePattern = /\/\*\s*侧边栏样式\s*\*\/[\s\S]*?(?=\/\*(?!\s*隐藏)|<\/style>|\.main-content|\.page-|\.orders-|\.content-|\.container-)/;
        
        if (sidebarStylePattern.test(content)) {
            content = content.replace(sidebarStylePattern, sidebarOnlyStyles);
            hasChanges = true;
        }
        
        // 确保主内容区域有正确的左边距（只修改这一个属性）
        const mainContentPattern = /\.main-content\s*\{([^}]*)\}/g;
        content = content.replace(mainContentPattern, (match) => {
            if (!match.includes('margin-left: 200px')) {
                return match.replace(/(\{[^}]*)(flex:\s*1;?)([^}]*)\}/, 
                    '$1$2\n            margin-left: 200px;$3}');
            }
            return match;
        });
        
        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已修复: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (无需修复)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🎯 开始修复左侧菜单样式...\n');
    console.log('📋 修复范围:');
    console.log('   - 仅修复侧边栏相关样式');
    console.log('   - 不触碰右侧内容样式');
    console.log('   - 只确保主内容区域有正确左边距\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const file of htmlFiles) {
        if (fixSidebarOnly(file)) {
            successCount++;
        } else {
            skipCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 已修复: ${successCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 左侧菜单修复完成！');
        console.log('🎯 只修复了侧边栏样式');
        console.log('✅ 右侧内容样式保持不变');
        console.log('📏 布局结构恢复正常');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixSidebarOnly,
    main
};

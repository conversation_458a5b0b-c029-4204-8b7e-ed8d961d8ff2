/**
 * 内部权限管理主要逻辑
 * 处理角色选择、权限配置、保存等功能
 */

class InternalPermissionsManager {
    constructor() {
        this.currentRole = null;
        this.permissions = {};
        this.hasChanges = false;
        this.isLoading = false;
        
        this.init();
    }
    
    /**
     * 初始化权限管理器
     */
    init() {
        this.renderRoles();
        this.bindEvents();
        console.log('内部权限管理器初始化完成');
    }
    
    /**
     * 渲染角色列表
     */
    renderRoles() {
        const rolesPanel = document.getElementById('rolesPanel');
        const { ROLES } = window.INTERNAL_PERMISSIONS_DATA;
        
        let html = '';
        
        Object.values(ROLES).forEach(role => {
            html += `
                <div class="role-item" data-role="${role.id}">
                    <div class="role-icon" style="background: ${role.color}">
                        <i class="${role.icon}"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">${role.name}</div>
                        <div class="role-description">${role.description}</div>
                    </div>
                    <div class="role-level" style="background: ${window.INTERNAL_PERMISSIONS_DATA.LEVEL_COLORS[role.level]}; color: white;">
                        ${role.levelName}
                    </div>
                </div>
            `;
        });
        
        rolesPanel.innerHTML = html;
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 角色选择事件
        document.getElementById('rolesPanel').addEventListener('click', (e) => {
            const roleItem = e.target.closest('.role-item');
            if (roleItem) {
                this.selectRole(roleItem.dataset.role);
            }
        });
        
        // 保存按钮事件
        document.getElementById('saveButton').addEventListener('click', () => {
            this.savePermissions();
        });
        
        // 权限变更事件（事件委托）
        document.getElementById('permissionsContent').addEventListener('change', (e) => {
            if (e.target.classList.contains('toggle-switch') || 
                e.target.classList.contains('operation-checkbox')) {
                this.markAsChanged();
            }
        });
        
        // 模块展开/折叠事件
        document.getElementById('permissionsContent').addEventListener('click', (e) => {
            const moduleHeader = e.target.closest('.module-header');
            if (moduleHeader) {
                this.toggleModule(moduleHeader);
            }
            
            const toggleSwitch = e.target.closest('.toggle-switch');
            if (toggleSwitch) {
                this.toggleModulePermission(toggleSwitch);
            }
            
            const operationCheckbox = e.target.closest('.operation-checkbox');
            if (operationCheckbox) {
                this.toggleOperationPermission(operationCheckbox);
            }
        });
    }
    
    /**
     * 选择角色
     */
    selectRole(roleId) {
        // 检查是否有未保存的更改
        if (this.hasChanges) {
            if (!confirm('当前有未保存的更改，确定要切换角色吗？')) {
                return;
            }
        }
        
        // 更新选中状态
        document.querySelectorAll('.role-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-role="${roleId}"]`).classList.add('active');
        
        // 设置当前角色
        this.currentRole = roleId;
        this.permissions = JSON.parse(JSON.stringify(
            window.INTERNAL_PERMISSIONS_DATA.ROLES[roleId].permissions
        ));
        this.hasChanges = false;
        
        // 渲染权限配置
        this.renderPermissions();
        
        // 更新保存按钮状态
        this.updateSaveButton();
    }
    
    /**
     * 渲染权限配置
     */
    renderPermissions() {
        if (!this.currentRole) {
            return;
        }
        
        const permissionsContent = document.getElementById('permissionsContent');
        const { MODULES, OPERATION_LABELS } = window.INTERNAL_PERMISSIONS_DATA;
        const role = window.INTERNAL_PERMISSIONS_DATA.ROLES[this.currentRole];
        
        let html = `
            <div class="alert alert-info">
                <i class="${role.icon}"></i>
                正在配置 <strong>${role.name}</strong> 的权限 | 权限等级: <strong>${role.levelName}</strong>
                ${role.requireMFA ? ' | <i class="fas fa-shield-alt"></i> 需要多因子认证' : ''}
                ${role.ipRestriction ? ' | <i class="fas fa-map-marker-alt"></i> IP限制' : ''}
                | 会话超时: <strong>${role.maxSessionTime}分钟</strong>
            </div>
        `;
        
        // 按类别分组模块
        const modulesByCategory = {};
        Object.values(MODULES).forEach(module => {
            if (!modulesByCategory[module.category]) {
                modulesByCategory[module.category] = [];
            }
            modulesByCategory[module.category].push(module);
        });
        
        // 渲染每个类别的模块
        Object.entries(modulesByCategory).forEach(([category, modules]) => {
            html += `<h4 style="margin: 24px 0 16px 0; color: #374151; font-size: 14px; font-weight: 600;">${category}</h4>`;
            
            modules.forEach(module => {
                const hasPermissions = this.permissions[module.id] && this.permissions[module.id].length > 0;
                
                html += `
                    <div class="permission-module">
                        <div class="module-header" data-module="${module.id}">
                            <div class="module-icon" style="background: ${module.color}">
                                <i class="${module.icon}"></i>
                            </div>
                            <div class="module-info">
                                <div class="module-name">${module.name}</div>
                                <div class="module-description">${module.description}</div>
                            </div>
                            <div class="module-toggle">
                                <div class="toggle-switch ${hasPermissions ? 'active' : ''}" data-module="${module.id}">
                                    <div class="toggle-slider"></div>
                                </div>
                                <i class="fas fa-chevron-down" style="color: #9ca3af; transition: transform 0.2s;"></i>
                            </div>
                        </div>
                        <div class="module-body ${hasPermissions ? 'expanded' : ''}" data-module="${module.id}">
                            ${this.renderSubModules(module)}
                        </div>
                    </div>
                `;
            });
        });
        
        permissionsContent.innerHTML = html;
    }
    
    /**
     * 渲染子模块
     */
    renderSubModules(module) {
        const { OPERATION_LABELS } = window.INTERNAL_PERMISSIONS_DATA;
        let html = '';
        
        Object.entries(module.subModules).forEach(([subModuleId, subModule]) => {
            html += `
                <div class="submodule-item">
                    <div class="submodule-header">
                        <div class="submodule-name">${subModule.name}</div>
                    </div>
                    <div class="submodule-description">${subModule.description}</div>
                    <div class="operations-grid">
            `;
            
            subModule.operations.forEach(operation => {
                const isChecked = this.permissions[module.id] && 
                                this.permissions[module.id].includes(operation);
                
                html += `
                    <div class="operation-item">
                        <div class="operation-checkbox ${isChecked ? 'checked' : ''}" 
                             data-module="${module.id}" 
                             data-operation="${operation}">
                        </div>
                        <span>${OPERATION_LABELS[operation] || operation}</span>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        });
        
        return html;
    }
    
    /**
     * 切换模块展开/折叠
     */
    toggleModule(moduleHeader) {
        const moduleId = moduleHeader.dataset.module;
        const moduleBody = document.querySelector(`.module-body[data-module="${moduleId}"]`);
        const chevron = moduleHeader.querySelector('.fas.fa-chevron-down');
        
        if (moduleBody.classList.contains('expanded')) {
            moduleBody.classList.remove('expanded');
            chevron.style.transform = 'rotate(0deg)';
        } else {
            moduleBody.classList.add('expanded');
            chevron.style.transform = 'rotate(180deg)';
        }
    }
    
    /**
     * 切换模块权限开关
     */
    toggleModulePermission(toggleSwitch) {
        const moduleId = toggleSwitch.dataset.module;
        const isActive = toggleSwitch.classList.contains('active');
        
        if (isActive) {
            // 禁用模块 - 清空所有权限
            this.permissions[moduleId] = [];
            toggleSwitch.classList.remove('active');
            
            // 取消选中所有操作
            document.querySelectorAll(`[data-module="${moduleId}"].operation-checkbox`).forEach(checkbox => {
                checkbox.classList.remove('checked');
            });
        } else {
            // 启用模块 - 给予查看权限
            this.permissions[moduleId] = ['VIEW'];
            toggleSwitch.classList.add('active');
            
            // 选中查看权限
            const viewCheckbox = document.querySelector(`[data-module="${moduleId}"][data-operation="VIEW"]`);
            if (viewCheckbox) {
                viewCheckbox.classList.add('checked');
            }
        }
        
        this.markAsChanged();
    }
    
    /**
     * 切换操作权限
     */
    toggleOperationPermission(operationCheckbox) {
        const moduleId = operationCheckbox.dataset.module;
        const operation = operationCheckbox.dataset.operation;
        const isChecked = operationCheckbox.classList.contains('checked');
        
        if (!this.permissions[moduleId]) {
            this.permissions[moduleId] = [];
        }
        
        if (isChecked) {
            // 取消选中
            operationCheckbox.classList.remove('checked');
            const index = this.permissions[moduleId].indexOf(operation);
            if (index > -1) {
                this.permissions[moduleId].splice(index, 1);
            }
        } else {
            // 选中
            operationCheckbox.classList.add('checked');
            if (!this.permissions[moduleId].includes(operation)) {
                this.permissions[moduleId].push(operation);
            }
        }
        
        // 更新模块开关状态
        const moduleToggle = document.querySelector(`.toggle-switch[data-module="${moduleId}"]`);
        if (this.permissions[moduleId].length > 0) {
            moduleToggle.classList.add('active');
        } else {
            moduleToggle.classList.remove('active');
        }
        
        this.markAsChanged();
    }
    
    /**
     * 标记为已更改
     */
    markAsChanged() {
        this.hasChanges = true;
        this.updateSaveButton();
    }
    
    /**
     * 更新保存按钮状态
     */
    updateSaveButton() {
        const saveButton = document.getElementById('saveButton');
        saveButton.disabled = !this.hasChanges || this.isLoading;
        
        if (this.hasChanges) {
            saveButton.innerHTML = '<i class="fas fa-save"></i> 保存权限配置 *';
        } else {
            saveButton.innerHTML = '<i class="fas fa-save"></i> 保存权限配置';
        }
    }
    
    /**
     * 保存权限配置
     */
    async savePermissions() {
        if (!this.currentRole || !this.hasChanges) {
            return;
        }
        
        this.isLoading = true;
        this.updateSaveButton();
        this.showLoading();
        
        try {
            // 模拟API调用
            await this.simulateApiCall();
            
            // 更新本地数据
            window.INTERNAL_PERMISSIONS_DATA.ROLES[this.currentRole].permissions = 
                JSON.parse(JSON.stringify(this.permissions));
            
            this.hasChanges = false;
            this.showSuccessMessage('权限配置保存成功！');
            
        } catch (error) {
            console.error('保存权限配置失败:', error);
            this.showErrorMessage('保存权限配置失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.updateSaveButton();
            this.hideLoading();
        }
    }
    
    /**
     * 模拟API调用
     */
    async simulateApiCall() {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // 模拟成功/失败
                if (Math.random() > 0.1) {
                    resolve();
                } else {
                    reject(new Error('网络连接失败'));
                }
            }, 1500);
        });
    }
    
    /**
     * 显示加载状态
     */
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }
    
    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type) {
        const alertClass = `alert-${type}`;
        const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
        
        const alertHtml = `
            <div class="alert ${alertClass}" style="margin-bottom: 16px;">
                <i class="${icon}"></i>
                ${message}
            </div>
        `;
        
        const permissionsContent = document.getElementById('permissionsContent');
        permissionsContent.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 3秒后自动移除消息
        setTimeout(() => {
            const alertElement = permissionsContent.querySelector(`.${alertClass}`);
            if (alertElement) {
                alertElement.remove();
            }
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.internalPermissionsManager = new InternalPermissionsManager();
});


/**
 * PC端需求管理页面API集成代码
 * 替换原有的模拟数据，使用真实API
 */

class RequirementsAPI {
    constructor() {
        this.baseURL = 'http://localhost:3004';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    // 获取所有需求（模拟实现，实际需要后端支持）
    async getRequirements(params = {}) {
        const cacheKey = 'requirements_list';
        const cached = this.cache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }

        try {
            // 由于后端暂不支持列表查询，这里使用模拟数据
            // 实际开发中应该调用: GET /api/v1/requirements
            const mockRequirements = await this.getMockRequirements();
            
            this.cache.set(cacheKey, {
                data: mockRequirements,
                timestamp: Date.now()
            });
            
            return mockRequirements;
        } catch (error) {
            console.error('获取需求列表失败:', error);
            throw error;
        }
    }

    // 获取单个需求详情
    async getRequirement(id) {
        try {
            const response = await fetch(`${this.baseURL}/api/v1/requirements/${id}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            return result.data;
        } catch (error) {
            console.error('获取需求详情失败:', error);
            throw error;
        }
    }

    // 创建新需求（PC端管理员创建）
    async createRequirement(requirementData) {
        try {
            const apiData = this.transformPCDataToAPI(requirementData);
            
            const response = await fetch(`${this.baseURL}/api/v1/requirements`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(apiData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '创建需求失败');
            }

            const result = await response.json();
            
            // 清除缓存
            this.cache.delete('requirements_list');
            
            return result.data;
        } catch (error) {
            console.error('创建需求失败:', error);
            throw error;
        }
    }

    // 更新需求状态（如果后端支持）
    async updateRequirement(id, updateData) {
        try {
            // 实际应该调用 PUT /api/v1/requirements/{id}
            // 目前后端未实现，使用模拟
            console.log('更新需求:', id, updateData);
            
            // 清除缓存
            this.cache.delete('requirements_list');
            
            return { success: true, message: '更新成功（模拟）' };
        } catch (error) {
            console.error('更新需求失败:', error);
            throw error;
        }
    }

    // 删除需求（如果后端支持）
    async deleteRequirement(id) {
        try {
            // 实际应该调用 DELETE /api/v1/requirements/{id}
            // 目前后端未实现，使用模拟
            console.log('删除需求:', id);
            
            // 清除缓存
            this.cache.delete('requirements_list');
            
            return { success: true, message: '删除成功（模拟）' };
        } catch (error) {
            console.error('删除需求失败:', error);
            throw error;
        }
    }

    // 转换PC端表单数据为API格式
    transformPCDataToAPI(pcData) {
        return {
            source: 'pc_admin',
            customer_name: pcData.customerName,
            customer_phone: pcData.customerPhone,
            house_rooms: parseInt(pcData.rooms) || 1,
            house_halls: parseInt(pcData.halls) || 1,
            house_bathrooms: parseInt(pcData.bathrooms) || 1,
            address: pcData.customerAddress,
            requirements: pcData.specialRequirements || pcData.description,
            budget: pcData.budget,
            selected_products: pcData.selectedProducts || [],
            device_user_agent: navigator.userAgent,
            device_screen_size: `${screen.width}x${screen.height}`
        };
    }

    // 获取模拟需求数据（过渡期使用）
    async getMockRequirements() {
        // 这里返回页面原有的模拟数据，逐步替换为真实API
        return [
            {
                id: 'REQ-MOCK-001',
                source: 'h5_miniprogram',
                customer_name: '张先生',
                customer_phone: '13812345678',
                house_description: '3室2厅2卫',
                address: '上海市浦东新区张江高科技园区',
                requirements: '全屋智能照明控制系统',
                status: 'pending',
                priority: 'medium',
                created_at: new Date().toISOString()
            }
            // ... 更多模拟数据
        ];
    }

    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch(`${this.baseURL}/health`);
            return response.ok;
        } catch (error) {
            console.error('API健康检查失败:', error);
            return false;
        }
    }
}

// 全局API实例
const requirementsAPI = new RequirementsAPI();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🔧 初始化需求管理API集成...');
    
    // 检查API连接
    const isHealthy = await requirementsAPI.healthCheck();
    if (isHealthy) {
        console.log('✅ API连接正常');
        
        // 加载需求数据
        try {
            const requirements = await requirementsAPI.getRequirements();
            console.log(`✅ 加载到 ${requirements.length} 条需求数据`);
            
            // 更新页面数据（替换原有的requirementsData）
            if (typeof renderTable === 'function') {
                renderTable(requirements);
            }
        } catch (error) {
            console.error('❌ 加载需求数据失败:', error);
        }
    } else {
        console.warn('⚠️ API连接失败，使用模拟数据');
    }
});

// 导出供页面使用
window.requirementsAPI = requirementsAPI;

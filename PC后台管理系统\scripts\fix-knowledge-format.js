/**
 * 修复04-knowledge目录下页面的格式问题
 */

const fs = require('fs');
const path = require('path');

// 需要修复的页面列表
const knowledgePages = [
    'debugging-knowledge.html',
    'design-knowledge.html',
    'installation-knowledge.html',
    'knowledge-template.html',
    'product-knowledge.html'
];

// 修复单个页面的格式
function fixPageFormat(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        console.log(`🔧 修复格式: ${fileName}`);
        
        // 修复样式标签格式问题
        content = content.replace(
            /(<link rel="stylesheet" href="[^"]*font-awesome[^"]*">)<style>/g,
            '$1\n    \n    <style>'
        );
        
        // 确保CSS变量正确工作，添加兼容性样式
        if (content.includes('var(--')) {
            // 在style标签开始后添加CSS变量定义
            content = content.replace(
                /(<style>\s*)(\/\* 知识库特定样式 \*\/)/,
                `$1/* CSS变量兼容性定义 */
        :root {
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-4: 1rem;
            --radius-lg: 8px;
            --duration-fast: 0.15s;
            --ease-in-out: ease-in-out;
            --bg-primary: #ffffff;
            --border-primary: #e5e7eb;
            --color-primary: #3b82f6;
            --color-warning: #f59e0b;
            --color-info: #06b6d4;
            --color-success: #10b981;
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
        }
        
        $2`
            );
        }
        
        // 保存文件
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 已修复格式: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🚀 开始修复04-knowledge目录页面格式...\n');
    
    const knowledgeDir = path.join(__dirname, '../src/pc/components/pages/04-knowledge');
    let successCount = 0;
    let errorCount = 0;
    
    for (const pageName of knowledgePages) {
        const fullPath = path.join(knowledgeDir, pageName);
        
        if (!fs.existsSync(fullPath)) {
            console.log(`⚠️  文件不存在: ${pageName}`);
            errorCount++;
            continue;
        }
        
        const result = fixPageFormat(fullPath);
        if (result === true) {
            successCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 成功修复: ${successCount} 个页面`);
    console.log(`❌ 修复失败: ${errorCount} 个页面`);
    console.log(`📁 总计页面: ${knowledgePages.length} 个页面`);
    
    if (successCount > 0) {
        console.log('\n🎉 格式修复完成！');
        console.log('💡 建议刷新浏览器查看修复效果');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    fixPageFormat,
    main
};

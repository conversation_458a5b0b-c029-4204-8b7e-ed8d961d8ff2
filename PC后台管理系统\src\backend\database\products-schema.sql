-- 商品管理系统数据库设计
-- 兼容MedusaJS电商框架
-- 支持多租户架构

-- 商品表
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（多租户隔离）',
    title VARCHAR(255) NOT NULL COMMENT '商品名称',
    sku VARCHAR(100) NOT NULL COMMENT '商品SKU',
    description TEXT COMMENT '商品描述',
    category VARCHAR(100) COMMENT '商品分类',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '商品状态',
    
    -- 价格信息（以分为单位存储）
    price BIGINT UNSIGNED NOT NULL COMMENT '销售价格（分）',
    cost_price BIGINT UNSIGNED COMMENT '成本价格（分）',
    market_price BIGINT UNSIGNED COMMENT '市场价格（分）',
    
    -- 库存信息
    inventory_quantity INT UNSIGNED DEFAULT 0 COMMENT '库存数量',
    inventory_warning_level INT UNSIGNED DEFAULT 10 COMMENT '库存预警阈值',
    
    -- 商品属性
    weight DECIMAL(8,2) COMMENT '重量（克）',
    dimensions JSON COMMENT '尺寸信息 {"length": 0, "width": 0, "height": 0}',
    
    -- 图片和媒体
    thumbnail VARCHAR(500) COMMENT '缩略图URL',
    images JSON COMMENT '商品图片URLs数组',
    
    -- SEO信息
    meta_title VARCHAR(255) COMMENT 'SEO标题',
    meta_description TEXT COMMENT 'SEO描述',
    meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
    
    -- MedusaJS集成
    medusa_id VARCHAR(100) COMMENT 'MedusaJS商品ID',
    medusa_synced_at TIMESTAMP NULL COMMENT 'MedusaJS同步时间',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_medusa_id (medusa_id),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_user_sku (user_id, sku)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品分类表
CREATE TABLE product_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（多租户隔离）',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    slug VARCHAR(100) NOT NULL COMMENT '分类标识',
    description TEXT COMMENT '分类描述',
    parent_id BIGINT UNSIGNED COMMENT '父分类ID',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    -- 图片
    image VARCHAR(500) COMMENT '分类图片URL',
    
    -- SEO信息
    meta_title VARCHAR(255) COMMENT 'SEO标题',
    meta_description TEXT COMMENT 'SEO描述',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY uk_user_slug (user_id, slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品变体表（支持多规格商品）
CREATE TABLE product_variants (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    title VARCHAR(255) NOT NULL COMMENT '变体名称',
    sku VARCHAR(100) NOT NULL COMMENT '变体SKU',
    
    -- 价格信息
    price BIGINT UNSIGNED NOT NULL COMMENT '销售价格（分）',
    cost_price BIGINT UNSIGNED COMMENT '成本价格（分）',
    
    -- 库存信息
    inventory_quantity INT UNSIGNED DEFAULT 0 COMMENT '库存数量',
    
    -- 变体属性
    weight DECIMAL(8,2) COMMENT '重量（克）',
    dimensions JSON COMMENT '尺寸信息',
    
    -- 变体选项（如：颜色=红色，尺寸=L）
    options JSON COMMENT '变体选项 {"color": "红色", "size": "L"}',
    
    -- 图片
    image VARCHAR(500) COMMENT '变体图片URL',
    
    -- MedusaJS集成
    medusa_id VARCHAR(100) COMMENT 'MedusaJS变体ID',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_product_id (product_id),
    INDEX idx_sku (sku),
    INDEX idx_medusa_id (medusa_id),
    UNIQUE KEY uk_sku (sku),
    
    -- 外键约束
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品变体表';

-- 商品属性表
CREATE TABLE product_attributes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（多租户隔离）',
    name VARCHAR(100) NOT NULL COMMENT '属性名称',
    type ENUM('text', 'number', 'boolean', 'select', 'multiselect') DEFAULT 'text' COMMENT '属性类型',
    options JSON COMMENT '选项值（用于select类型）',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY uk_user_name (user_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品属性表';

-- 商品属性值表
CREATE TABLE product_attribute_values (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    attribute_id BIGINT UNSIGNED NOT NULL COMMENT '属性ID',
    value TEXT COMMENT '属性值',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_product_id (product_id),
    INDEX idx_attribute_id (attribute_id),
    UNIQUE KEY uk_product_attribute (product_id, attribute_id),
    
    -- 外键约束
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_id) REFERENCES product_attributes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品属性值表';

-- 库存记录表
CREATE TABLE inventory_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL COMMENT '商品ID',
    variant_id BIGINT UNSIGNED COMMENT '变体ID',
    type ENUM('in', 'out', 'adjust') NOT NULL COMMENT '记录类型：入库/出库/调整',
    quantity INT NOT NULL COMMENT '数量变化（正数为增加，负数为减少）',
    quantity_before INT UNSIGNED NOT NULL COMMENT '变化前数量',
    quantity_after INT UNSIGNED NOT NULL COMMENT '变化后数量',
    reason VARCHAR(255) COMMENT '变化原因',
    reference_type VARCHAR(50) COMMENT '关联类型（order/adjustment/import等）',
    reference_id BIGINT UNSIGNED COMMENT '关联ID',
    operator_id BIGINT UNSIGNED COMMENT '操作人ID',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_product_id (product_id),
    INDEX idx_variant_id (variant_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_reference (reference_type, reference_id),
    
    -- 外键约束
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存记录表';

-- 商品导入记录表
CREATE TABLE product_import_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    filename VARCHAR(255) NOT NULL COMMENT '导入文件名',
    total_count INT UNSIGNED NOT NULL COMMENT '总记录数',
    success_count INT UNSIGNED DEFAULT 0 COMMENT '成功数量',
    error_count INT UNSIGNED DEFAULT 0 COMMENT '失败数量',
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing' COMMENT '导入状态',
    error_details JSON COMMENT '错误详情',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品导入记录表';

-- MedusaJS同步日志表
CREATE TABLE medusa_sync_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    sync_type ENUM('pull', 'push') NOT NULL COMMENT '同步类型：拉取/推送',
    entity_type VARCHAR(50) NOT NULL COMMENT '实体类型（product/category等）',
    entity_id BIGINT UNSIGNED COMMENT '本地实体ID',
    medusa_id VARCHAR(100) COMMENT 'MedusaJS实体ID',
    status ENUM('success', 'failed') NOT NULL COMMENT '同步状态',
    error_message TEXT COMMENT '错误信息',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_sync_type (sync_type),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_medusa_id (medusa_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MedusaJS同步日志表';

-- 创建视图：商品统计
CREATE VIEW product_stats AS
SELECT 
    user_id,
    COUNT(*) as total_products,
    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_products,
    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_products,
    SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived_products,
    SUM(inventory_quantity) as total_inventory,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM products 
WHERE deleted_at IS NULL
GROUP BY user_id;

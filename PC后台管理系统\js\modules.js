// 模块内容加载函数

// 数据概览模块
function loadDashboard() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">数据概览</h1>
            <p style="color: var(--text-secondary);">智能设计与施工管理系统总览</p>
        </div>
        
        <!-- 统计卡片 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin-bottom: 32px;">
            <div class="content-card">
                <div style="padding: 24px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #1a1a1a, #374151); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                            <i class="fas fa-users"></i>
                        </div>
                        <div style="font-size: 12px; color: var(--success); background: rgba(16, 185, 129, 0.1); padding: 4px 8px; border-radius: 4px;">
                            +12%
                        </div>
                    </div>
                    <div style="font-size: 32px; font-weight: 700; color: var(--text-primary); margin-bottom: 4px;">2,847</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">总用户数</div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 24px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                        <div style="width: 48px; height: 48px; background: #374151; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div style="font-size: 12px; color: var(--success); background: rgba(16, 185, 129, 0.1); padding: 4px 8px; border-radius: 4px;">
                            +8%
                        </div>
                    </div>
                    <div style="font-size: 32px; font-weight: 700; color: var(--text-primary); margin-bottom: 4px;">156</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">需求总数</div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 24px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                        <div style="width: 48px; height: 48px; background: #6b7280; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                            <i class="fas fa-hammer"></i>
                        </div>
                        <div style="font-size: 12px; color: var(--warning); background: rgba(217, 119, 6, 0.1); padding: 4px 8px; border-radius: 4px;">
                            +3%
                        </div>
                    </div>
                    <div style="font-size: 32px; font-weight: 700; color: var(--text-primary); margin-bottom: 4px;">89</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">施工项目</div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 24px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                        <div style="width: 48px; height: 48px; background: #9ca3af; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div style="font-size: 12px; color: var(--success); background: rgba(16, 185, 129, 0.1); padding: 4px 8px; border-radius: 4px;">
                            +25%
                        </div>
                    </div>
                    <div style="font-size: 32px; font-weight: 700; color: var(--text-primary); margin-bottom: 4px;">¥1.2M</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">月度营收</div>
                </div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px;">
            <div class="content-card">
                <div class="card-header">
                    <h3 class="card-title">业务趋势</h3>
                </div>
                <div class="card-content">
                    <div style="height: 300px; display: flex; items-center; justify-content: center; color: var(--text-muted);">
                        <div style="text-align: center;">
                            <i class="fas fa-chart-area" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                            <div>图表区域 - 可集成 Chart.js 或其他图表库</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <div class="card-header">
                    <h3 class="card-title">最近活动</h3>
                </div>
                <div class="card-content">
                    <div style="space-y: 16px;">
                        <div style="display: flex; items-start; gap: 12px; padding-bottom: 16px; border-bottom: 1px solid var(--border-light);">
                            <div style="width: 8px; height: 8px; background: var(--success); border-radius: 50%; margin-top: 6px;"></div>
                            <div>
                                <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">新用户注册</div>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 2px;">张先生 完成注册</div>
                                <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">2分钟前</div>
                            </div>
                        </div>
                        
                        <div style="display: flex; items-start; gap: 12px; padding-bottom: 16px; border-bottom: 1px solid var(--border-light);">
                            <div style="width: 8px; height: 8px; background: var(--info); border-radius: 50%; margin-top: 6px;"></div>
                            <div>
                                <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">需求已提交</div>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 2px;">客厅智能照明设计</div>
                                <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">15分钟前</div>
                            </div>
                        </div>
                        
                        <div style="display: flex; items-start; gap: 12px; padding-bottom: 16px; border-bottom: 1px solid var(--border-light);">
                            <div style="width: 8px; height: 8px; background: var(--warning); border-radius: 50%; margin-top: 6px;"></div>
                            <div>
                                <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">施工进度更新</div>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 2px;">项目进度更新至60%</div>
                                <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">1小时前</div>
                            </div>
                        </div>
                        
                        <div style="display: flex; items-start; gap: 12px;">
                            <div style="width: 8px; height: 8px; background: var(--success); border-radius: 50%; margin-top: 6px;"></div>
                            <div>
                                <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">订单完成</div>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 2px;">智能开关订单已完成</div>
                                <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">3小时前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('page-content').innerHTML = content;
}

// 用户管理模块
function loadUsersModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">用户管理</h1>
            <p style="color: var(--text-secondary);">管理系统用户信息与权限</p>
        </div>
        
        <!-- 操作工具栏 -->
        <div class="content-card" style="margin-bottom: 24px;">
            <div style="padding: 20px; display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; gap: 16px; align-items: center;">
                    <input type="text" placeholder="搜索用户..." style="padding: 8px 12px; border: 1px solid var(--border-light); border-radius: 6px; font-size: 14px; width: 280px;">
                    <select style="padding: 8px 12px; border: 1px solid var(--border-light); border-radius: 6px; font-size: 14px;">
                        <option>全部状态</option>
                        <option>正常</option>
                        <option>已禁用</option>
                    </select>
                </div>
                <button style="background: var(--primary-black); color: white; padding: 8px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>添加用户
                </button>
            </div>
        </div>
        
        <!-- 用户列表 -->
        <div class="content-card">
            <div class="card-header">
                <h3 class="card-title">用户列表</h3>
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 1px solid var(--border-light);">
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">用户信息</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">联系方式</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">角色</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">状态</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">注册时间</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid var(--border-light);">
                            <td style="padding: 16px;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">张</div>
                                    <div>
                                        <div style="font-weight: 500; color: var(--text-primary);">张先生</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">ID: 001</div>
                                    </div>
                                </div>
                            </td>
                            <td style="padding: 16px;">
                                <div style="color: var(--text-primary);">138****8888</div>
                                <div style="font-size: 12px; color: var(--text-secondary);"><EMAIL></div>
                            </td>
                            <td style="padding: 16px;">
                                <span style="background: rgba(16, 185, 129, 0.1); color: var(--success); padding: 4px 8px; border-radius: 4px; font-size: 12px;">普通用户</span>
                            </td>
                            <td style="padding: 16px;">
                                <span style="background: rgba(16, 185, 129, 0.1); color: var(--success); padding: 4px 8px; border-radius: 4px; font-size: 12px;">正常</span>
                            </td>
                            <td style="padding: 16px; color: var(--text-secondary);">2024-01-15</td>
                            <td style="padding: 16px;">
                                <div style="display: flex; gap: 8px;">
                                    <button style="padding: 4px 8px; background: transparent; border: 1px solid var(--border-light); border-radius: 4px; color: var(--text-secondary); cursor: pointer; font-size: 12px;">查看</button>
                                    <button style="padding: 4px 8px; background: transparent; border: 1px solid var(--border-light); border-radius: 4px; color: var(--text-secondary); cursor: pointer; font-size: 12px;">编辑</button>
                                </div>
                            </td>
                        </tr>
                        <!-- 可以添加更多用户行 -->
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    document.getElementById('page-content').innerHTML = content;
}

// 需求管理模块
function loadRequirementsModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">需求管理</h1>
            <p style="color: var(--text-secondary);">管理客户设计和施工需求</p>
        </div>
        
        <!-- 统计概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px;">
            <div class="content-card">
                <div style="padding: 20px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: var(--primary-black); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: 700; color: var(--text-primary);">156</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">总需求数</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 20px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #374151; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: 700; color: var(--text-primary);">28</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">待处理</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 20px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #6b7280; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: 700; color: var(--text-primary);">45</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">处理中</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <div style="padding: 20px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #9ca3af; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: 700; color: var(--text-primary);">83</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">已完成</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 需求列表 -->
        <div class="content-card">
            <div class="card-header">
                <h3 class="card-title">需求列表</h3>
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 1px solid var(--border-light);">
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">需求编号</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">客户信息</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">需求类型</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">优先级</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">状态</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">创建时间</th>
                            <th style="padding: 16px; text-align: left; font-weight: 600; color: var(--text-primary);">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid var(--border-light);">
                            <td style="padding: 16px; color: var(--text-primary); font-weight: 500;">#REQ-001</td>
                            <td style="padding: 16px;">
                                <div style="color: var(--text-primary); font-weight: 500;">张先生</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">138****8888</div>
                            </td>
                            <td style="padding: 16px; color: var(--text-primary);">室内设计</td>
                            <td style="padding: 16px;">
                                <span style="background: rgba(239, 68, 68, 0.1); color: var(--error); padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                    <i class="fas fa-arrow-up" style="margin-right: 4px;"></i>高
                                </span>
                            </td>
                            <td style="padding: 16px;">
                                <span style="background: rgba(217, 119, 6, 0.1); color: var(--warning); padding: 4px 8px; border-radius: 4px; font-size: 12px;">待处理</span>
                            </td>
                            <td style="padding: 16px; color: var(--text-secondary);">2024-01-15</td>
                            <td style="padding: 16px;">
                                <div style="display: flex; gap: 8px;">
                                    <button style="padding: 4px 8px; background: transparent; border: 1px solid var(--border-light); border-radius: 4px; color: var(--text-secondary); cursor: pointer; font-size: 12px;">查看</button>
                                    <button style="padding: 4px 8px; background: transparent; border: 1px solid var(--border-light); border-radius: 4px; color: var(--text-secondary); cursor: pointer; font-size: 12px;">处理</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    document.getElementById('page-content').innerHTML = content;
}

// 其他模块的简化版本
function loadConstructionModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">施工管理</h1>
            <p style="color: var(--text-secondary);">管理施工项目和进度</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-hammer" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>施工管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadDesignModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">设计管理</h1>
            <p style="color: var(--text-secondary);">管理设计方案和设计师</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-paint-brush" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>设计管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadProductsModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">产品管理</h1>
            <p style="color: var(--text-secondary);">管理商品信息和库存</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-cubes" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>产品管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadOrdersModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">订单管理</h1>
            <p style="color: var(--text-secondary);">管理订单状态和处理</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>订单管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadInventoryModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">库存管理</h1>
            <p style="color: var(--text-secondary);">管理商品库存和仓储</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-warehouse" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>库存管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadKnowledgeModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">知识库管理</h1>
            <p style="color: var(--text-secondary);">管理知识文章和教程</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-book" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>知识库管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadCasesModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">案例展示</h1>
            <p style="color: var(--text-secondary);">管理项目案例和展示</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-images" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>案例展示模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadEquipmentModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">设备管理</h1>
            <p style="color: var(--text-secondary);">管理智能设备和配置</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-cogs" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>设备管理模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadSettingsModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">系统配置</h1>
            <p style="color: var(--text-secondary);">管理系统参数和配置</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-cog" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>系统配置模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadLogsModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">操作日志</h1>
            <p style="color: var(--text-secondary);">查看系统操作记录</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-file-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>操作日志模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
}

function loadBackupModule() {
    const content = `
        <div style="margin-bottom: 24px;">
            <h1 style="font-size: 28px; font-weight: 700; color: var(--text-primary); margin: 0 0 8px 0;">数据备份</h1>
            <p style="color: var(--text-secondary);">管理数据备份和恢复</p>
        </div>
        <div class="content-card">
            <div class="card-content">
                <div style="text-align: center; padding: 60px; color: var(--text-muted);">
                    <i class="fas fa-database" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>数据备份模块开发中...</div>
                </div>
            </div>
        </div>
    `;
    document.getElementById('page-content').innerHTML = content;
} 
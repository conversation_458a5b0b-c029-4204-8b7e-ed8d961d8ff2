/**
 * 商品筛选器组件
 * 支持搜索、分类、品牌、状态等多维度筛选
 */

class ProductFilters {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            enableSearch: true,
            enableCategory: true,
            enableBrand: true,
            enableStatus: true,
            enableDateRange: true,
            debounceDelay: 300,
            ...options
        };
        
        this.filters = {};
        this.debounceTimers = {};
        
        // 事件回调
        this.onFilter = options.onFilter || (() => {});
        this.onReset = options.onReset || (() => {});
        
        this.init();
    }

    init() {
        this.createFilterStructure();
        this.setupEventListeners();
        this.loadFilterOptions();
    }

    createFilterStructure() {
        this.container.innerHTML = `
            <div class="product-filters">
                <div class="filters-header">
                    <h6 class="filters-title">
                        <i class="fas fa-filter"></i> 筛选条件
                    </h6>
                    <button class="btn btn-sm btn-outline-secondary reset-filters">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
                
                <div class="filters-body">
                    ${this.options.enableSearch ? this.createSearchFilter() : ''}
                    ${this.options.enableCategory ? this.createCategoryFilter() : ''}
                    ${this.options.enableBrand ? this.createBrandFilter() : ''}
                    ${this.options.enableStatus ? this.createStatusFilter() : ''}
                    ${this.options.enableDateRange ? this.createDateRangeFilter() : ''}
                </div>
                
                <div class="filters-footer">
                    <div class="active-filters">
                        <span class="active-filters-label">已选筛选:</span>
                        <div class="active-filters-list"></div>
                    </div>
                </div>
            </div>
        `;
    }

    createSearchFilter() {
        return `
            <div class="filter-group search-filter">
                <label class="filter-label">搜索</label>
                <div class="search-input-group">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-input" 
                           placeholder="搜索商品名称、SKU、品牌..." 
                           data-filter="search">
                    <button class="btn btn-outline-secondary clear-search" type="button" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    createCategoryFilter() {
        return `
            <div class="filter-group category-filter">
                <label class="filter-label">商品分类</label>
                <select class="form-select" data-filter="category">
                    <option value="">全部分类</option>
                    <option value="switch">智能开关</option>
                    <option value="lighting">智能照明</option>
                    <option value="security">安防设备</option>
                    <option value="sensor">传感器</option>
                    <option value="environment">环境控制</option>
                </select>
            </div>
        `;
    }

    createBrandFilter() {
        return `
            <div class="filter-group brand-filter">
                <label class="filter-label">品牌</label>
                <select class="form-select" data-filter="brand">
                    <option value="">全部品牌</option>
                    <!-- 品牌选项将动态加载 -->
                </select>
            </div>
        `;
    }

    createStatusFilter() {
        return `
            <div class="filter-group status-filter">
                <label class="filter-label">商品状态</label>
                <div class="status-checkboxes">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="active" 
                               id="status-active" data-filter="status">
                        <label class="form-check-label" for="status-active">
                            <span class="status-indicator status-active"></span>
                            上架中
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="inactive" 
                               id="status-inactive" data-filter="status">
                        <label class="form-check-label" for="status-inactive">
                            <span class="status-indicator status-inactive"></span>
                            已下架
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="draft" 
                               id="status-draft" data-filter="status">
                        <label class="form-check-label" for="status-draft">
                            <span class="status-indicator status-draft"></span>
                            草稿
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="out_of_stock" 
                               id="status-out-of-stock" data-filter="status">
                        <label class="form-check-label" for="status-out-of-stock">
                            <span class="status-indicator status-out-of-stock"></span>
                            缺货
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    createDateRangeFilter() {
        return `
            <div class="filter-group date-range-filter">
                <label class="filter-label">创建时间</label>
                <div class="date-range-inputs">
                    <input type="date" class="form-control" data-filter="date_from" placeholder="开始日期">
                    <span class="date-separator">至</span>
                    <input type="date" class="form-control" data-filter="date_to" placeholder="结束日期">
                </div>
                <div class="date-presets">
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-preset="today">今天</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-preset="week">本周</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-preset="month">本月</button>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const container = this.container;
        
        // 搜索输入防抖
        const searchInput = container.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.debounceFilter('search', e.target.value);
                this.toggleClearButton(e.target);
            });
            
            // 清除搜索
            const clearBtn = container.querySelector('.clear-search');
            if (clearBtn) {
                clearBtn.addEventListener('click', () => {
                    searchInput.value = '';
                    this.setFilter('search', '');
                    this.toggleClearButton(searchInput);
                });
            }
        }
        
        // 下拉选择器
        const selects = container.querySelectorAll('select[data-filter]');
        selects.forEach(select => {
            select.addEventListener('change', (e) => {
                this.setFilter(e.target.dataset.filter, e.target.value);
            });
        });
        
        // 状态复选框
        const statusCheckboxes = container.querySelectorAll('input[data-filter="status"]');
        statusCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateStatusFilter();
            });
        });
        
        // 日期范围
        const dateInputs = container.querySelectorAll('input[data-filter^="date_"]');
        dateInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.setFilter(e.target.dataset.filter, e.target.value);
            });
        });
        
        // 日期预设
        const datePresets = container.querySelectorAll('[data-preset]');
        datePresets.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.applyDatePreset(e.target.dataset.preset);
            });
        });
        
        // 重置筛选
        const resetBtn = container.querySelector('.reset-filters');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetFilters();
            });
        }
    }

    debounceFilter(filterName, value) {
        // 清除之前的定时器
        if (this.debounceTimers[filterName]) {
            clearTimeout(this.debounceTimers[filterName]);
        }
        
        // 设置新的定时器
        this.debounceTimers[filterName] = setTimeout(() => {
            this.setFilter(filterName, value);
        }, this.options.debounceDelay);
    }

    setFilter(filterName, value) {
        if (value && value.trim() !== '') {
            this.filters[filterName] = value.trim();
        } else {
            delete this.filters[filterName];
        }
        
        this.updateActiveFilters();
        this.onFilter(this.filters);
    }

    updateStatusFilter() {
        const statusCheckboxes = this.container.querySelectorAll('input[data-filter="status"]:checked');
        const selectedStatuses = Array.from(statusCheckboxes).map(cb => cb.value);
        
        if (selectedStatuses.length > 0) {
            this.filters.status = selectedStatuses;
        } else {
            delete this.filters.status;
        }
        
        this.updateActiveFilters();
        this.onFilter(this.filters);
    }

    toggleClearButton(searchInput) {
        const clearBtn = this.container.querySelector('.clear-search');
        if (clearBtn) {
            clearBtn.style.display = searchInput.value ? 'block' : 'none';
        }
    }

    applyDatePreset(preset) {
        const today = new Date();
        let startDate, endDate;
        
        switch (preset) {
            case 'today':
                startDate = endDate = today;
                break;
            case 'week':
                startDate = new Date(today);
                startDate.setDate(today.getDate() - today.getDay());
                endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + 6);
                break;
            case 'month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                break;
        }
        
        if (startDate && endDate) {
            const dateFromInput = this.container.querySelector('[data-filter="date_from"]');
            const dateToInput = this.container.querySelector('[data-filter="date_to"]');
            
            if (dateFromInput) {
                dateFromInput.value = this.formatDate(startDate);
                this.setFilter('date_from', dateFromInput.value);
            }
            
            if (dateToInput) {
                dateToInput.value = this.formatDate(endDate);
                this.setFilter('date_to', dateToInput.value);
            }
        }
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    updateActiveFilters() {
        const activeFiltersList = this.container.querySelector('.active-filters-list');
        if (!activeFiltersList) return;
        
        activeFiltersList.innerHTML = '';
        
        Object.keys(this.filters).forEach(filterName => {
            const value = this.filters[filterName];
            const filterTag = this.createFilterTag(filterName, value);
            if (filterTag) {
                activeFiltersList.appendChild(filterTag);
            }
        });
        
        // 显示/隐藏活动筛选区域
        const activeFiltersContainer = this.container.querySelector('.active-filters');
        if (activeFiltersContainer) {
            activeFiltersContainer.style.display = Object.keys(this.filters).length > 0 ? 'block' : 'none';
        }
    }

    createFilterTag(filterName, value) {
        const tag = document.createElement('span');
        tag.className = 'filter-tag';
        
        let displayText = '';
        
        switch (filterName) {
            case 'search':
                displayText = `搜索: ${value}`;
                break;
            case 'category':
                displayText = `分类: ${this.getCategoryName(value)}`;
                break;
            case 'brand':
                displayText = `品牌: ${value}`;
                break;
            case 'status':
                if (Array.isArray(value)) {
                    displayText = `状态: ${value.map(s => this.getStatusName(s)).join(', ')}`;
                } else {
                    displayText = `状态: ${this.getStatusName(value)}`;
                }
                break;
            case 'date_from':
                displayText = `开始: ${value}`;
                break;
            case 'date_to':
                displayText = `结束: ${value}`;
                break;
            default:
                displayText = `${filterName}: ${value}`;
        }
        
        tag.innerHTML = `
            ${displayText}
            <button type="button" class="remove-filter" data-filter="${filterName}">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // 移除筛选标签
        tag.querySelector('.remove-filter').addEventListener('click', () => {
            this.removeFilter(filterName);
        });
        
        return tag;
    }

    removeFilter(filterName) {
        delete this.filters[filterName];
        
        // 清除对应的UI控件
        const filterElement = this.container.querySelector(`[data-filter="${filterName}"]`);
        if (filterElement) {
            if (filterElement.type === 'checkbox') {
                // 如果是状态筛选，需要取消所有相关复选框
                if (filterName === 'status') {
                    const statusCheckboxes = this.container.querySelectorAll('input[data-filter="status"]');
                    statusCheckboxes.forEach(cb => cb.checked = false);
                }
            } else {
                filterElement.value = '';
            }
        }
        
        // 特殊处理搜索清除按钮
        if (filterName === 'search') {
            this.toggleClearButton(filterElement);
        }
        
        this.updateActiveFilters();
        this.onFilter(this.filters);
    }

    resetFilters() {
        this.filters = {};
        
        // 清除所有UI控件
        const inputs = this.container.querySelectorAll('input, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });
        
        // 隐藏搜索清除按钮
        const clearBtn = this.container.querySelector('.clear-search');
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
        
        this.updateActiveFilters();
        this.onReset();
        this.onFilter(this.filters);
    }

    loadFilterOptions() {
        // 动态加载品牌选项
        this.loadBrandOptions();
    }

    async loadBrandOptions() {
        const brandSelect = this.container.querySelector('[data-filter="brand"]');
        if (!brandSelect) return;
        
        try {
            // 这里应该从API获取品牌列表
            const brands = ['小米', '华为', 'Philips', '海康威视', '杜亚', 'Aqara', '绿米'];
            
            // 清除现有选项（保留"全部品牌"）
            const defaultOption = brandSelect.querySelector('option[value=""]');
            brandSelect.innerHTML = '';
            brandSelect.appendChild(defaultOption);
            
            // 添加品牌选项
            brands.forEach(brand => {
                const option = document.createElement('option');
                option.value = brand;
                option.textContent = brand;
                brandSelect.appendChild(option);
            });
        } catch (error) {
            console.error('加载品牌选项失败:', error);
        }
    }

    getCategoryName(categoryId) {
        const categoryMap = {
            'switch': '智能开关',
            'lighting': '智能照明',
            'security': '安防设备',
            'sensor': '传感器',
            'environment': '环境控制'
        };
        return categoryMap[categoryId] || categoryId;
    }

    getStatusName(statusId) {
        const statusMap = {
            'active': '上架中',
            'inactive': '已下架',
            'draft': '草稿',
            'out_of_stock': '缺货'
        };
        return statusMap[statusId] || statusId;
    }

    getFilters() {
        return { ...this.filters };
    }

    setFilters(filters) {
        this.filters = { ...filters };
        
        // 更新UI控件
        Object.keys(filters).forEach(filterName => {
            const value = filters[filterName];
            const filterElement = this.container.querySelector(`[data-filter="${filterName}"]`);
            
            if (filterElement) {
                if (filterName === 'status' && Array.isArray(value)) {
                    // 处理状态多选
                    const statusCheckboxes = this.container.querySelectorAll('input[data-filter="status"]');
                    statusCheckboxes.forEach(cb => {
                        cb.checked = value.includes(cb.value);
                    });
                } else {
                    filterElement.value = value;
                }
            }
        });
        
        this.updateActiveFilters();
    }

    destroy() {
        // 清除所有定时器
        Object.values(this.debounceTimers).forEach(timer => {
            clearTimeout(timer);
        });
        
        this.container.innerHTML = '';
    }
}

// 导出组件
window.ProductFilters = ProductFilters;

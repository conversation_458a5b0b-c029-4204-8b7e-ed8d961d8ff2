# PC后台管理系统文档索引

**最后更新**: 2025-07-24T05:58:10.557Z

## 📁 文档结构

### 01-开发规范
开发规范和最佳实践
- [页面开发规范](01-开发规范/页面开发规范--v1.0.md)
- [如何创建新页面](01-开发规范/如何创建新页面.md)
- [UI样式指南](01-开发规范/ui-style-guide.md)

### 02-API文档
API接口文档和集成指南
- [统一数据库Schema设计](docs/统一数据库Schema设计--v1.0.sql)
- [数据库Schema统一迁移脚本](docs/数据库Schema统一迁移脚本--v1.0.sql)

### 03-测试文档
测试计划和测试报告
- [P0级别问题修复总结](03-测试文档/P0级别问题修复总结--v1.0.md)
- [P1级别安全机制实现报告](03-测试文档/P1级别安全机制实现报告--v1.0.md)

### 04-部署文档
部署和配置指南
- [部署指南](04-部署文档/部署指南.md)
- [系统架构文档](04-部署文档/系统架构文档.md)

## 🔧 工具和脚本

### 开发工具
- [服务器启动脚本](server.js)
- [快速启动脚本](start-server.ps1)
- [测试服务器](start-test-server.py)

### 构建工具
- [Webpack配置](build/webpack.config.js)
- [批量修复脚本](scripts/)

## 📋 快速开始

1. **启动开发服务器**:
   ```bash
   node server.js
   # 或
   powershell start-server.ps1
   ```

2. **访问管理系统**:
   - 主页: http://localhost:3000
   - 设计管理: http://localhost:3000/src/pc/components/pages/design-management.html

3. **开发新页面**:
   参考 [如何创建新页面](01-开发规范/如何创建新页面.md)

## 📞 支持

如有问题请参考相关文档或查看项目主文档索引。

---
*此索引由PC后台文档清理脚本自动生成*

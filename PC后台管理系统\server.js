const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;

    console.log(`请求路径: ${pathname}`);

    // 如果是根路径，重定向到设计管理页面
    if (pathname === '/') {
        pathname = '/src/pc/components/pages/design-management.html';
    }

    const filePath = path.join(__dirname, pathname);
    console.log(`文件路径: ${filePath}`);
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'text/plain';
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在
            res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>404 - 文件未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #e74c3c; }
                        p { color: #666; }
                        a { color: #3498db; text-decoration: none; }
                        a:hover { text-decoration: underline; }
                    </style>
                </head>
                <body>
                    <h1>404 - 文件未找到</h1>
                    <p>请求的文件 "${pathname}" 不存在</p>
                    <p><a href="/src/pc/components/pages/design-management.html">访问设计管理页面</a></p>
                </body>
                </html>
            `);
            return;
        }
        
        // 读取文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end('服务器内部错误');
                return;
            }
            
            // 设置响应头
            res.writeHead(200, {
                'Content-Type': contentType + (contentType.startsWith('text/') ? '; charset=utf-8' : ''),
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            });
            
            res.end(data);
        });
    });
});

server.listen(PORT, () => {
    console.log('🚀 服务器启动成功！');
    console.log(`📍 访问地址: http://localhost:${PORT}`);
    console.log(`🎨 设计管理页面: http://localhost:${PORT}/src/pc/components/pages/design-management.html`);
    console.log(`📁 当前目录: ${__dirname}`);
    console.log('⏹️  按 Ctrl+C 停止服务器');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
    } else {
        console.log(`❌ 服务器启动失败: ${err.message}`);
    }
});

/**
 * 修复菜单链接路径和图标问题
 */

const fs = require('fs');
const path = require('path');

// 加载菜单配置
function loadMenuConfig() {
    try {
        const configPath = path.join(__dirname, 'menu-config.json');
        const configContent = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configContent);
    } catch (error) {
        console.error('❌ 无法加载菜单配置:', error.message);
        process.exit(1);
    }
}

// 生成正确的菜单HTML（无图标版本）
function generateCorrectMenuHTML(menuConfig, currentFilePath) {
    const { logo, sections } = menuConfig;
    const relativePath = path.relative(path.join(__dirname, '../src/pc/components/pages'), currentFilePath);
    const currentDir = path.dirname(relativePath);
    
    // 计算相对路径前缀
    function getCorrectPath(targetHref) {
        // 如果目标文件在同一目录，直接使用文件名
        if (currentDir === path.dirname(targetHref) || !targetHref.includes('/')) {
            return path.basename(targetHref);
        }
        
        // 计算从当前目录到目标文件的相对路径
        const currentDepth = currentDir.split(path.sep).length;
        const targetDir = path.dirname(targetHref);
        
        if (targetDir === '.') {
            // 目标在根目录
            return '../'.repeat(currentDepth) + path.basename(targetHref);
        } else {
            // 目标在其他子目录
            return '../'.repeat(currentDepth) + targetHref;
        }
    }
    
    let menuHTML = `            <nav class="nav-menu">`;

    sections.forEach(section => {
        menuHTML += `
                <div class="nav-section">
                    <div class="nav-section-title">${section.title}</div>`;
        
        section.items.forEach(item => {
            const href = getCorrectPath(item.href);
            const currentFileName = path.basename(currentFilePath);
            const isActive = path.basename(item.href) === currentFileName ? ' active' : '';
            
            // 移除图标，只保留文本
            menuHTML += `
                    <a href="${href}" class="nav-item${isActive}">${item.text}</a>`;
        });
        
        menuHTML += `
                </div>`;
    });

    menuHTML += `
            </nav>`;

    return menuHTML;
}

// 修复单个页面的菜单
function fixPageMenu(filePath, menuConfig) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        console.log(`🔧 修复菜单: ${fileName}`);
        
        // 查找现有的nav-menu
        const navMenuRegex = /<nav class="nav-menu">[\s\S]*?<\/nav>/;
        const match = content.match(navMenuRegex);
        
        if (!match) {
            console.log(`⚠️  未找到nav-menu: ${fileName}`);
            return false;
        }
        
        // 生成新的菜单HTML
        const newMenuHTML = generateCorrectMenuHTML(menuConfig, filePath);
        
        // 替换菜单内容
        content = content.replace(navMenuRegex, newMenuHTML);
        
        // 保存文件
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 已修复菜单: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 主函数
function main() {
    console.log('🚀 开始修复菜单链接路径和移除图标...\n');
    
    // 加载菜单配置
    const menuConfig = loadMenuConfig();
    console.log('✅ 菜单配置加载成功\n');
    
    // 获取所有HTML文件
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (const filePath of htmlFiles) {
        const relativePath = path.relative(pagesDir, filePath);
        
        // 跳过特殊页面
        if (relativePath.includes('logout.html') || 
            relativePath.includes('register.html') || 
            relativePath.includes('index.html')) {
            console.log(`⏭️  跳过特殊页面: ${relativePath}`);
            skipCount++;
            continue;
        }
        
        const result = fixPageMenu(filePath, menuConfig);
        if (result === true) {
            successCount++;
        } else if (result === false) {
            skipCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 成功修复: ${successCount} 个页面`);
    console.log(`⏭️  跳过页面: ${skipCount} 个页面`);
    console.log(`❌ 修复失败: ${errorCount} 个页面`);
    console.log(`📁 总计页面: ${htmlFiles.length} 个页面`);
    
    if (successCount > 0) {
        console.log('\n🎉 菜单修复完成！');
        console.log('💡 建议运行菜单一致性检查验证结果');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    loadMenuConfig,
    generateCorrectMenuHTML,
    fixPageMenu,
    main
};

/**
 * 批量更新页面样式引用脚本
 * 将所有页面的样式引用更新为统一的样式文件
 */

const fs = require('fs');
const path = require('path');

// 页面目录
const pagesDir = '../src/pc/components/pages';

// 需要更新的文件列表
const filesToUpdate = [
    'admin-dashboard.html',
    'admin-dashboard-v2.html', 
    'analytics.html',
    'api-tester.html',
    'auth-test.html',
    'contract-management.html',
    'create-requirement-optimized.html',
    'dashboard.html',
    'demo.html',
    'design.html',
    'login.html',
    'optimized-requirements.html',
    'orders.html',
    'permissions.html',
    'products.html',
    'projects.html',
    'requirements-management-fixed.html',
    'settings.html',
    'unified-admin-system.html',
    'user-profile-test.html',
    'user-profile.html',
    'users.html',
    '一装ERP-API文档.html'
];

// 旧的样式引用模式
const oldStylePatterns = [
    /href="\.\.\/01-开发规范\/pc-ui-styles\.css"/g,
    /href="\.\.\/\.\.\/01-开发规范\/pc-ui-styles\.css"/g,
    /href="\.\.\/\.\.\/\.\.\/01-开发规范\/pc-ui-styles\.css"/g,
    /href="01-开发规范\/pc-ui-styles\.css"/g
];

// 新的样式引用
const newStyleRef = 'href="../../../../styles/unified-admin-styles.css"';

function updateFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let updated = false;
        
        // 替换所有旧的样式引用
        oldStylePatterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, newStyleRef);
                updated = true;
            }
        });
        
        if (updated) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已更新: ${path.basename(filePath)}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${path.basename(filePath)} (未找到旧样式引用)`);
            return false;
        }
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🚀 开始批量更新页面样式引用...\n');
    
    let updatedCount = 0;
    let totalCount = 0;
    
    filesToUpdate.forEach(fileName => {
        const filePath = path.join(pagesDir, fileName);
        
        if (fs.existsSync(filePath)) {
            totalCount++;
            if (updateFile(filePath)) {
                updatedCount++;
            }
        } else {
            console.log(`⚠️  文件不存在: ${fileName}`);
        }
    });
    
    console.log(`\n📊 更新完成:`);
    console.log(`   - 总文件数: ${totalCount}`);
    console.log(`   - 已更新: ${updatedCount}`);
    console.log(`   - 跳过: ${totalCount - updatedCount}`);
    
    if (updatedCount > 0) {
        console.log('\n✨ 所有页面现在都使用统一的样式文件: unified-admin-styles.css');
        console.log('🎨 页面将自动应用黑白灰设计规范和紧凑布局');
    }
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { updateFile, main };

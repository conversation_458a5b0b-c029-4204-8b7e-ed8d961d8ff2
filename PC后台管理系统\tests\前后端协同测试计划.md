# 前后端协同测试计划

## 📋 测试概览

**测试目标**: 验证微信用户管理功能的前后端集成  
**测试范围**: 用户注册、图纸上传、需求提交完整流程  
**测试环境**: 开发环境 + 模拟生产环境  
**测试时间**: 预计2-3天  

---

## 🌐 测试环境配置

### 前端环境
- **服务器**: 本地开发服务器 (http://localhost:3000)
- **技术栈**: HTML5 + CSS3 + JavaScript ES6+
- **测试页面**: 
  - `wechat-users.html`
  - `user-registration-flow.html` 
  - `requirement-management.html`

### 后端环境
- **API服务器**: http://localhost:8080 (开发环境)
- **数据库**: PostgreSQL (本地测试库)
- **认证方式**: JWT Bearer Token
- **文件存储**: 本地存储 + CDN模拟

### 微信环境
- **微信开发者工具**: 模拟微信小程序环境
- **测试账号**: 使用微信测试号
- **授权域名**: 配置本地开发域名

---

## 🧪 测试阶段划分

### Phase 1: API接口测试 (Day 1)
- [ ] 接口连通性测试
- [ ] 数据格式验证
- [ ] 错误处理测试
- [ ] 权限验证测试

### Phase 2: 功能集成测试 (Day 2)
- [ ] 用户注册流程测试
- [ ] 图纸上传功能测试
- [ ] 需求提交流程测试
- [ ] 数据同步测试

### Phase 3: 端到端测试 (Day 3)
- [ ] 完整业务流程测试
- [ ] 性能压力测试
- [ ] 兼容性测试
- [ ] 异常场景测试

---

## 🔧 测试环境搭建

### 1. 启动后端服务
```bash
# 启动数据库
docker-compose up -d postgres

# 启动API服务
npm run dev

# 检查服务状态
curl http://localhost:8080/health
```

### 2. 配置前端环境
```bash
# 启动前端服务
cd PC后台管理系统
python -m http.server 3000

# 或使用Node.js
npx http-server -p 3000
```

### 3. 配置测试数据
```sql
-- 创建测试用户
INSERT INTO users (open_id, nickname, avatar, phone) VALUES
('test_openid_001', '测试用户1', 'https://via.placeholder.com/50', '13800138001'),
('test_openid_002', '测试用户2', 'https://via.placeholder.com/50', '13800138002');

-- 创建测试需求
INSERT INTO requirements (user_id, house_type, house_area, design_style, budget_min, budget_max) VALUES
(1, '住宅', 120, '现代简约', 150000, 200000),
(2, '公寓', 80, '北欧风', 100000, 150000);
```

---

## 📝 API接口测试用例

### 1. 用户认证接口测试

#### 1.1 微信登录接口
**测试用例**: POST /api/auth/wechat/login

```javascript
// 测试用例1: 正常登录
const testLogin = async () => {
  const response = await fetch('http://localhost:8080/api/auth/wechat/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      code: 'mock_wechat_code',
      encryptedData: 'mock_encrypted_data',
      iv: 'mock_iv',
      signature: 'mock_signature'
    })
  });
  
  const result = await response.json();
  console.log('登录测试结果:', result);
  
  // 验证响应格式
  assert(result.code === 200, '登录应该成功');
  assert(result.data.token, '应该返回token');
  assert(result.data.userInfo, '应该返回用户信息');
};

// 测试用例2: 无效code
const testInvalidLogin = async () => {
  const response = await fetch('http://localhost:8080/api/auth/wechat/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      code: 'invalid_code'
    })
  });
  
  const result = await response.json();
  assert(result.code === 401, '应该返回认证失败');
};
```

#### 1.2 用户信息完善接口
**测试用例**: PUT /api/users/profile

```javascript
const testUpdateProfile = async (token) => {
  const response = await fetch('http://localhost:8080/api/users/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      phone: '13800138000',
      realName: '张三',
      region: {
        province: '广东省',
        city: '深圳市',
        district: '南山区'
      }
    })
  });
  
  const result = await response.json();
  assert(result.code === 200, '更新应该成功');
};
```

### 2. 图纸管理接口测试

#### 2.1 图纸上传接口
**测试用例**: POST /api/blueprints/upload

```javascript
const testUploadBlueprint = async (token) => {
  const formData = new FormData();
  
  // 创建测试图片文件
  const canvas = document.createElement('canvas');
  canvas.width = 800;
  canvas.height = 600;
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, 800, 600);
  ctx.fillStyle = '#333';
  ctx.font = '24px Arial';
  ctx.fillText('测试户型图', 350, 300);
  
  canvas.toBlob(async (blob) => {
    formData.append('file', blob, 'test-blueprint.jpg');
    formData.append('type', 'floor_plan');
    formData.append('description', '测试户型图');
    
    const response = await fetch('http://localhost:8080/api/blueprints/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });
    
    const result = await response.json();
    assert(result.code === 200, '上传应该成功');
    assert(result.data.fileId, '应该返回文件ID');
    assert(result.data.fileUrl, '应该返回文件URL');
  }, 'image/jpeg');
};
```

#### 2.2 获取图纸列表
**测试用例**: GET /api/blueprints/list

```javascript
const testGetBlueprints = async (token) => {
  const response = await fetch('http://localhost:8080/api/blueprints/list?page=1&limit=10', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  assert(result.code === 200, '获取列表应该成功');
  assert(Array.isArray(result.data.blueprints), '应该返回数组');
};
```

### 3. 需求管理接口测试

#### 3.1 提交需求接口
**测试用例**: POST /api/requirements/submit

```javascript
const testSubmitRequirement = async (token) => {
  const response = await fetch('http://localhost:8080/api/requirements/submit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      projectInfo: {
        houseType: '住宅',
        houseArea: 120,
        houseLayout: '三室两厅',
        currentStatus: '毛坯房',
        location: {
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          address: '测试地址123号'
        }
      },
      designRequirements: {
        style: '现代简约',
        budget: {
          min: 150000,
          max: 200000
        },
        timeline: '2个月内',
        priorities: ['实用性', '美观性'],
        description: '希望设计一个现代简约风格的家居空间'
      },
      specialRequirements: [
        {
          type: 'accessibility',
          description: '家中有老人，需要无障碍设计'
        }
      ],
      contactPreferences: {
        preferredTime: '工作日9-18点',
        urgency: 'normal'
      }
    })
  });
  
  const result = await response.json();
  assert(result.code === 200, '提交需求应该成功');
  assert(result.data.requirementId, '应该返回需求ID');
};
```

---

## 🎭 前端集成测试

### 1. 页面加载测试

```javascript
// 页面加载测试脚本
const testPageLoad = () => {
  console.log('开始页面加载测试...');
  
  const startTime = performance.now();
  
  // 测试微信用户管理页面
  window.location.href = 'src/pages/user-management/wechat-users.html';
  
  window.addEventListener('load', () => {
    const loadTime = performance.now() - startTime;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 验证页面元素
    assert(document.getElementById('usersTable'), '用户表格应该存在');
    assert(document.getElementById('userSearch'), '搜索框应该存在');
    assert(document.querySelector('.stats-grid'), '统计卡片应该存在');
    
    console.log('页面加载测试通过 ✅');
  });
};
```

### 2. API集成测试

```javascript
// API集成测试脚本
class APIIntegrationTest {
  constructor() {
    this.baseURL = 'http://localhost:8080';
    this.token = null;
  }
  
  async runAllTests() {
    console.log('开始API集成测试...');
    
    try {
      // 1. 测试登录
      await this.testLogin();
      
      // 2. 测试用户列表
      await this.testUserList();
      
      // 3. 测试图纸上传
      await this.testBlueprintUpload();
      
      // 4. 测试需求提交
      await this.testRequirementSubmit();
      
      // 5. 测试管理员功能
      await this.testAdminFunctions();
      
      console.log('所有API集成测试通过 ✅');
      
    } catch (error) {
      console.error('API集成测试失败 ❌', error);
    }
  }
  
  async testLogin() {
    console.log('测试登录接口...');
    
    const response = await fetch(`${this.baseURL}/api/auth/wechat/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        code: 'test_code_123',
        encryptedData: 'test_data',
        iv: 'test_iv',
        signature: 'test_signature'
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      this.token = result.data.token;
      console.log('登录测试通过 ✅');
    } else {
      throw new Error(`登录失败: ${result.message}`);
    }
  }
  
  async testUserList() {
    console.log('测试用户列表接口...');
    
    const response = await fetch(`${this.baseURL}/admin/api/users/list?page=1&limit=20`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`获取到${result.data.total}个用户`);
      console.log('用户列表测试通过 ✅');
    } else {
      throw new Error(`获取用户列表失败: ${result.message}`);
    }
  }
  
  async testBlueprintUpload() {
    console.log('测试图纸上传接口...');
    
    // 创建测试文件
    const formData = new FormData();
    const blob = new Blob(['test image data'], { type: 'image/jpeg' });
    formData.append('file', blob, 'test.jpg');
    formData.append('type', 'floor_plan');
    formData.append('description', '测试图纸');
    
    const response = await fetch(`${this.baseURL}/api/blueprints/upload`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.token}` },
      body: formData
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`图纸上传成功，文件ID: ${result.data.fileId}`);
      console.log('图纸上传测试通过 ✅');
    } else {
      throw new Error(`图纸上传失败: ${result.message}`);
    }
  }
  
  async testRequirementSubmit() {
    console.log('测试需求提交接口...');
    
    const requirement = {
      projectInfo: {
        houseType: '住宅',
        houseArea: 120,
        houseLayout: '三室两厅'
      },
      designRequirements: {
        style: '现代简约',
        budget: { min: 150000, max: 200000 },
        timeline: '2个月内',
        description: '测试需求提交'
      }
    };
    
    const response = await fetch(`${this.baseURL}/api/requirements/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body: JSON.stringify(requirement)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`需求提交成功，需求ID: ${result.data.requirementId}`);
      console.log('需求提交测试通过 ✅');
    } else {
      throw new Error(`需求提交失败: ${result.message}`);
    }
  }
  
  async testAdminFunctions() {
    console.log('测试管理员功能...');
    
    // 测试获取需求列表
    const response = await fetch(`${this.baseURL}/admin/api/requirements/list?page=1&limit=20`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`获取到${result.data.total}个需求`);
      console.log('管理员功能测试通过 ✅');
    } else {
      throw new Error(`管理员功能测试失败: ${result.message}`);
    }
  }
}
```

---

## 🚀 自动化测试脚本

### 测试启动脚本

```javascript
// 自动化测试启动脚本
class AutomatedTest {
  constructor() {
    this.testResults = [];
    this.apiTest = new APIIntegrationTest();
  }
  
  async runFullTest() {
    console.log('🚀 开始前后端协同测试...\n');
    
    // 1. 环境检查
    await this.checkEnvironment();
    
    // 2. API接口测试
    await this.runAPITests();
    
    // 3. 前端集成测试
    await this.runFrontendTests();
    
    // 4. 端到端测试
    await this.runE2ETests();
    
    // 5. 生成测试报告
    this.generateReport();
  }
  
  async checkEnvironment() {
    console.log('🔍 检查测试环境...');
    
    try {
      // 检查后端服务
      const backendResponse = await fetch('http://localhost:8080/health');
      if (backendResponse.ok) {
        console.log('✅ 后端服务正常');
      } else {
        throw new Error('后端服务异常');
      }
      
      // 检查前端服务
      const frontendResponse = await fetch('http://localhost:3000');
      if (frontendResponse.ok) {
        console.log('✅ 前端服务正常');
      } else {
        throw new Error('前端服务异常');
      }
      
      console.log('✅ 环境检查通过\n');
      
    } catch (error) {
      console.error('❌ 环境检查失败:', error.message);
      throw error;
    }
  }
  
  async runAPITests() {
    console.log('🔧 运行API接口测试...');
    
    try {
      await this.apiTest.runAllTests();
      this.testResults.push({
        category: 'API接口测试',
        status: 'PASS',
        details: '所有API接口测试通过'
      });
      
    } catch (error) {
      this.testResults.push({
        category: 'API接口测试',
        status: 'FAIL',
        details: error.message
      });
    }
  }
  
  async runFrontendTests() {
    console.log('🎨 运行前端集成测试...');
    
    try {
      // 测试页面加载
      await this.testPageLoad();
      
      // 测试用户交互
      await this.testUserInteraction();
      
      // 测试数据绑定
      await this.testDataBinding();
      
      this.testResults.push({
        category: '前端集成测试',
        status: 'PASS',
        details: '前端功能测试通过'
      });
      
    } catch (error) {
      this.testResults.push({
        category: '前端集成测试',
        status: 'FAIL',
        details: error.message
      });
    }
  }
  
  async runE2ETests() {
    console.log('🔄 运行端到端测试...');
    
    try {
      // 测试完整业务流程
      await this.testCompleteUserFlow();
      
      this.testResults.push({
        category: '端到端测试',
        status: 'PASS',
        details: '完整业务流程测试通过'
      });
      
    } catch (error) {
      this.testResults.push({
        category: '端到端测试',
        status: 'FAIL',
        details: error.message
      });
    }
  }
  
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('==========================================');
    
    let passCount = 0;
    let failCount = 0;
    
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.category}: ${result.details}`);
      
      if (result.status === 'PASS') {
        passCount++;
      } else {
        failCount++;
      }
    });
    
    console.log('==========================================');
    console.log(`总计: ${this.testResults.length} 项测试`);
    console.log(`通过: ${passCount} 项`);
    console.log(`失败: ${failCount} 项`);
    console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(2)}%`);
    
    if (failCount === 0) {
      console.log('\n🎉 所有测试通过! 系统可以部署到生产环境。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查并修复问题后重新测试。');
    }
  }
}

// 启动测试
const automatedTest = new AutomatedTest();
automatedTest.runFullTest().catch(console.error);
```

---

## 📋 测试检查清单

### API接口测试 ✅
- [ ] 微信登录接口 (`POST /api/auth/wechat/login`)
- [ ] 用户信息更新 (`PUT /api/users/profile`)
- [ ] 图纸上传 (`POST /api/blueprints/upload`)
- [ ] 图纸列表 (`GET /api/blueprints/list`)
- [ ] 需求提交 (`POST /api/requirements/submit`)
- [ ] 需求列表 (`GET /api/requirements/list`)
- [ ] 管理员用户列表 (`GET /admin/api/users/list`)
- [ ] 管理员需求列表 (`GET /admin/api/requirements/list`)
- [ ] 设计师分配 (`POST /admin/api/requirements/{id}/assign`)

### 前端功能测试 ✅
- [ ] 页面正常加载
- [ ] 数据正确显示
- [ ] 搜索筛选功能
- [ ] 模态框弹出关闭
- [ ] 图表正常渲染
- [ ] 分页功能正常
- [ ] 批量操作功能
- [ ] 响应式布局

### 集成测试 ✅
- [ ] 前后端数据同步
- [ ] 实时状态更新
- [ ] 文件上传下载
- [ ] 权限验证
- [ ] 错误处理
- [ ] 性能表现

### 兼容性测试 ✅
- [ ] Chrome浏览器
- [ ] Firefox浏览器  
- [ ] Safari浏览器
- [ ] 移动端适配
- [ ] 不同分辨率

---

## 🐛 常见问题和解决方案

### 1. CORS跨域问题
```javascript
// 后端配置CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  next();
});
```

### 2. 认证Token问题
```javascript
// 前端Token处理
const getAuthToken = () => {
  return localStorage.getItem('auth_token');
};

const setAuthHeaders = (headers = {}) => {
  const token = getAuthToken();
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  return headers;
};
```

### 3. 文件上传问题
```javascript
// 文件大小限制检查
const validateFile = (file) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  
  if (file.size > maxSize) {
    throw new Error('文件大小不能超过10MB');
  }
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('不支持的文件格式');
  }
  
  return true;
};
```

---

这个测试计划为前后端协同测试提供了完整的框架和指导。您可以根据实际情况调整测试用例和环境配置。 
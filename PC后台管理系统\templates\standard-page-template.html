<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        /* 页面内容样式 */
        .page-content {
            flex: 1;
            padding: 24px;
        }

        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-content {
            max-width: 1200px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
        }

        .breadcrumb-description {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
        }

        /* 内容区域样式 */
        .content-area {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-top: 24px;
        }

        .content-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .content-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1f2937;
        }

        .content-header p {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 标准侧边栏 - 直接包含完整HTML结构 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon"><i class="fas fa-home"></i></div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="my-todos.html" class="nav-item">我的代办</a>
                    <a href="my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="design-products.html" class="nav-item">设计商品</a>
                    <a href="requirements-management.html" class="nav-item">需求管理</a>
                    <a href="design-center.html" class="nav-item">设计中心</a>
                    <a href="design-cases.html" class="nav-item">设计案例</a>
                    <a href="project-center.html" class="nav-item">项目中心</a>
                    <a href="construction-management.html" class="nav-item">施工管理</a>
                    <a href="construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="products.html" class="nav-item">商品管理</a>
                    <a href="orders.html" class="nav-item">订单管理</a>
                    <a href="customer-management.html" class="nav-item">客户管理</a>
                    <a href="marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="api-tools.html" class="nav-item">API 工具</a>
                    <a href="erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="system-settings.html" class="nav-item">系统配置</a>
                    <a href="user-management.html" class="nav-item">用户管理</a>
                    <a href="internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="project-analytics.html" class="nav-item">项目分析</a>
                    <a href="order-analytics.html" class="nav-item">订单分析</a>
                    <a href="customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="demo.html" class="nav-item">演示展示</a>
                    <a href="user-profile.html" class="nav-item">个人资料</a>
                    <a href="logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">页面标题</h1>
                    <p class="breadcrumb-description">页面描述信息</p>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <div class="content-area">
                    <div class="content-header">
                        <h2>内容标题</h2>
                        <p>内容描述</p>
                    </div>
                    
                    <!-- 在这里添加页面具体内容 -->
                    <div class="page-body">
                        <p>页面内容区域...</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入侧边栏提取器 -->
    <script src="../src/pc/components/shared/sidebar-extractor.js"></script>
    <script>
        // 初始化侧边栏
        document.addEventListener('DOMContentLoaded', function() {
            const extractor = new SidebarExtractor();

            // 插入侧边栏HTML
            document.getElementById('sidebar-container').innerHTML = extractor.getFallbackSidebar();

            // 插入侧边栏CSS
            const style = document.createElement('style');
            style.textContent = extractor.getStandardCSS();
            document.head.appendChild(style);

            // 初始化激活状态
            extractor.initActiveState();

            console.log('页面已加载，标准侧边栏已初始化');
        });
    </script>
</body>
</html>

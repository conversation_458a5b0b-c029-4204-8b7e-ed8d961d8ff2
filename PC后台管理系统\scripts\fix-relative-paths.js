/**
 * 修复所有页面的相对路径问题
 */

const fs = require('fs');
const path = require('path');

// 路径映射表
const pathMappings = {
    // 个人中心
    'my-todos.html': '01-personal/my-todos.html',
    'my-orders.html': '01-personal/my-orders.html',
    
    // 业务管理
    'design-products.html': '02-business/design-products.html',
    'requirements-management.html': '02-business/requirements-management.html',
    'design-center.html': '02-business/design-center.html',
    'project-center.html': '02-business/project-center.html',
    'construction-management.html': '02-business/construction-management.html',
    'construction-guide.html': '02-business/construction-guide.html',
    
    // 商务中心
    'products.html': '03-commerce/products.html',
    'orders.html': '03-commerce/orders.html',
    'customer-management.html': '03-commerce/customer-management.html',
    'marketing-management.html': '03-commerce/marketing-management.html',
    
    // 知识库
    'design-knowledge.html': '04-knowledge/design-knowledge.html',
    'delivery-knowledge.html': '04-knowledge/delivery-knowledge.html',
    'wiring-knowledge.html': '04-knowledge/wiring-knowledge.html',
    'installation-knowledge.html': '04-knowledge/installation-knowledge.html',
    'debugging-knowledge.html': '04-knowledge/debugging-knowledge.html',
    'product-knowledge.html': '04-knowledge/product-knowledge.html',
    
    // 系统工具
    'api-tools.html': '05-tools/api-tools.html',
    'erp-documentation.html': '05-tools/erp-documentation.html',
    'system-settings.html': '05-tools/system-settings.html',
    'user-management.html': '05-tools/user-management.html',
    'internal-permissions.html': '05-tools/internal-permissions.html',
    'customer-permissions.html': '05-tools/customer-permissions.html',
    'data-management.html': '05-tools/data-management.html',
    
    // 数据分析
    'requirements-analytics.html': '06-analytics/requirements-analytics.html',
    'project-analytics.html': '06-analytics/project-analytics.html',
    'order-analytics.html': '06-analytics/order-analytics.html',
    'customer-analytics.html': '06-analytics/customer-analytics.html',
    
    // 个人资料
    'demo.html': '07-profile/demo.html',
    'user-profile.html': '07-profile/user-profile.html',
    'logout.html': '07-profile/logout.html'
};

// 计算相对路径
function getRelativePath(fromDir, toPath) {
    const fromDepth = fromDir.split('/').length;
    const toDir = path.dirname(toPath);
    const toFile = path.basename(toPath);
    
    if (fromDir === toDir) {
        // 同一目录
        return toFile;
    } else {
        // 不同目录
        const upLevels = '../'.repeat(fromDepth);
        return upLevels + toPath;
    }
}

// 修复单个页面的相对路径
function fixPagePaths(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        const relativePath = path.relative(path.join(__dirname, '../src/pc/components/pages'), filePath);
        const currentDir = path.dirname(relativePath);
        
        console.log(`🔧 修复路径: ${relativePath}`);
        
        let hasChanges = false;
        
        // 修复每个映射的路径
        for (const [fileName, fullPath] of Object.entries(pathMappings)) {
            const correctPath = getRelativePath(currentDir, fullPath);
            
            // 查找并替换错误的路径
            const patterns = [
                // 直接文件名引用
                new RegExp(`href="${fileName}"`, 'g'),
                // 可能的错误相对路径
                new RegExp(`href="[^"]*/${fileName}"`, 'g'),
                new RegExp(`href="\.\.\/[^"]*/${fileName}"`, 'g')
            ];
            
            for (const pattern of patterns) {
                const matches = content.match(pattern);
                if (matches) {
                    content = content.replace(pattern, `href="${correctPath}"`);
                    hasChanges = true;
                }
            }
        }
        
        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已修复路径: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  无需修复: ${fileName}`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 主函数
function main() {
    console.log('🚀 开始修复所有页面的相对路径...\n');
    
    // 获取所有HTML文件
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (const filePath of htmlFiles) {
        const result = fixPagePaths(filePath);
        if (result === true) {
            successCount++;
        } else if (result === false) {
            skipCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n📊 修复统计:');
    console.log(`✅ 成功修复: ${successCount} 个页面`);
    console.log(`⏭️  跳过页面: ${skipCount} 个页面`);
    console.log(`❌ 修复失败: ${errorCount} 个页面`);
    console.log(`📁 总计页面: ${htmlFiles.length} 个页面`);
    
    if (successCount > 0) {
        console.log('\n🎉 路径修复完成！');
        console.log('💡 建议运行菜单一致性检查验证结果');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    pathMappings,
    getRelativePath,
    fixPagePaths,
    main
};

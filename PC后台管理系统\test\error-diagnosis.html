<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工管理系统 - 错误诊断</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .diagnosis-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .error-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .diagnosis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .diagnosis-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pass {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-fail {
            background: #fecaca;
            color: #991b1b;
        }
        
        .status-loading {
            background: #fef3c7;
            color: #92400e;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="diagnosis-container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> 施工管理系统错误诊断</h1>
            <p>检测和诊断系统中的错误和问题</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="runDiagnosis()">
                <i class="fas fa-play"></i> 开始诊断
            </button>
            <button class="btn btn-success" onclick="clearLog()">
                <i class="fas fa-trash"></i> 清空日志
            </button>
            <a href="../src/pc/components/pages/construction-management.html" class="btn btn-danger" target="_blank">
                <i class="fas fa-external-link-alt"></i> 打开系统页面
            </a>
        </div>
        
        <div class="error-log" id="errorLog">
            <div style="color: #10b981; font-weight: bold;">🔍 施工管理系统错误诊断控制台</div>
            <div style="color: #6b7280; margin-top: 8px;">点击"开始诊断"检测系统错误...</div>
        </div>
        
        <div class="diagnosis-grid">
            <div class="diagnosis-section">
                <div class="section-header">
                    <i class="fas fa-file-code"></i>
                    <span>文件加载检查</span>
                </div>
                <div id="fileChecks">
                    <div class="check-item">
                        <span>admin-common.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>file-manager.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>knowledge-manager.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>record-manager.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>issue-manager.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>acceptance-manager.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>construction-enhanced.js</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                </div>
            </div>
            
            <div class="diagnosis-section">
                <div class="section-header">
                    <i class="fas fa-cubes"></i>
                    <span>模块初始化检查</span>
                </div>
                <div id="moduleChecks">
                    <div class="check-item">
                        <span>fileManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>knowledgeManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>recordManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>issueManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>acceptanceManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>constructionManager</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                </div>
            </div>
            
            <div class="diagnosis-section">
                <div class="section-header">
                    <i class="fas fa-code-branch"></i>
                    <span>DOM元素检查</span>
                </div>
                <div id="domChecks">
                    <div class="check-item">
                        <span>currentPersonnelList</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>currentDocumentList</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>currentKnowledgeList</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>phaseRecords</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>phaseIssues</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>phaseAcceptance</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                </div>
            </div>
            
            <div class="diagnosis-section">
                <div class="section-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>JavaScript错误</span>
                </div>
                <div id="jsErrors">
                    <div class="check-item">
                        <span>语法错误</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>运行时错误</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>网络错误</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                    <div class="check-item">
                        <span>控制台警告</span>
                        <span class="status-indicator status-loading">检查中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ErrorDiagnostic {
            constructor() {
                this.errors = [];
                this.warnings = [];
                this.fileLoadStatus = {};
                this.moduleStatus = {};
                this.domStatus = {};
                
                // 捕获所有错误
                this.setupErrorCapture();
            }

            setupErrorCapture() {
                // 捕获JavaScript错误
                window.addEventListener('error', (event) => {
                    this.errors.push({
                        type: 'JavaScript Error',
                        message: event.message,
                        filename: event.filename,
                        line: event.lineno,
                        column: event.colno,
                        stack: event.error ? event.error.stack : null,
                        timestamp: new Date().toISOString()
                    });
                    this.logError(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`);
                });

                // 捕获Promise rejection
                window.addEventListener('unhandledrejection', (event) => {
                    this.errors.push({
                        type: 'Unhandled Promise Rejection',
                        message: event.reason,
                        timestamp: new Date().toISOString()
                    });
                    this.logError(`Promise错误: ${event.reason}`);
                });

                // 重写console方法来捕获日志
                const originalConsoleError = console.error;
                console.error = (...args) => {
                    this.errors.push({
                        type: 'Console Error',
                        message: args.join(' '),
                        timestamp: new Date().toISOString()
                    });
                    this.logError(`控制台错误: ${args.join(' ')}`);
                    originalConsoleError.apply(console, args);
                };

                const originalConsoleWarn = console.warn;
                console.warn = (...args) => {
                    this.warnings.push({
                        type: 'Console Warning',
                        message: args.join(' '),
                        timestamp: new Date().toISOString()
                    });
                    this.logWarning(`控制台警告: ${args.join(' ')}`);
                    originalConsoleWarn.apply(console, args);
                };
            }

            async runDiagnosis() {
                this.log('🔍 开始系统诊断...');
                this.log('');

                // 1. 检查文件加载
                await this.checkFileLoading();
                
                // 2. 检查模块初始化
                await this.checkModuleInitialization();
                
                // 3. 检查DOM元素
                await this.checkDOMElements();
                
                // 4. 检查JavaScript错误
                await this.checkJavaScriptErrors();
                
                // 5. 生成诊断报告
                this.generateDiagnosticReport();
            }

            async checkFileLoading() {
                this.log('📋 检查文件加载状态...');
                
                const files = [
                    { name: 'admin-common.js', path: '../src/pc/js/admin-common.js' },
                    { name: 'file-manager.js', path: '../src/pc/components/js/file-manager.js' },
                    { name: 'knowledge-manager.js', path: '../src/pc/components/js/knowledge-manager.js' },
                    { name: 'record-manager.js', path: '../src/pc/components/js/record-manager.js' },
                    { name: 'issue-manager.js', path: '../src/pc/components/js/issue-manager.js' },
                    { name: 'acceptance-manager.js', path: '../src/pc/components/js/acceptance-manager.js' },
                    { name: 'construction-enhanced.js', path: '../src/pc/components/js/construction-enhanced.js' }
                ];

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    try {
                        const response = await fetch(file.path);
                        const success = response.ok;
                        this.fileLoadStatus[file.name] = success;
                        
                        this.updateCheckStatus('fileChecks', i, success, 
                            success ? '✅ 加载成功' : `❌ 加载失败 (${response.status})`);
                        
                        if (success) {
                            this.log(`✅ ${file.name} 加载成功`);
                        } else {
                            this.logError(`❌ ${file.name} 加载失败: HTTP ${response.status}`);
                        }
                    } catch (error) {
                        this.fileLoadStatus[file.name] = false;
                        this.updateCheckStatus('fileChecks', i, false, `❌ 网络错误`);
                        this.logError(`❌ ${file.name} 网络错误: ${error.message}`);
                    }
                    
                    await this.sleep(100);
                }
            }

            async checkModuleInitialization() {
                this.log('📋 检查模块初始化状态...');
                
                // 等待一段时间让模块加载
                await this.sleep(1000);
                
                const modules = [
                    'fileManager',
                    'knowledgeManager',
                    'recordManager',
                    'issueManager',
                    'acceptanceManager',
                    'constructionManager'
                ];

                modules.forEach((module, index) => {
                    const exists = window[module] !== undefined;
                    this.moduleStatus[module] = exists;
                    
                    this.updateCheckStatus('moduleChecks', index, exists,
                        exists ? '✅ 已初始化' : '❌ 未初始化');
                    
                    if (exists) {
                        this.log(`✅ ${module} 模块已初始化`);
                    } else {
                        this.logError(`❌ ${module} 模块未初始化`);
                    }
                });
            }

            async checkDOMElements() {
                this.log('📋 检查DOM元素...');
                
                // 创建iframe来加载主页面
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = '../src/pc/components/pages/construction-management.html';
                document.body.appendChild(iframe);
                
                await new Promise((resolve) => {
                    iframe.onload = resolve;
                    setTimeout(resolve, 3000); // 超时保护
                });
                
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const elements = [
                    'currentPersonnelList',
                    'currentDocumentList',
                    'currentKnowledgeList',
                    'phaseRecords',
                    'phaseIssues',
                    'phaseAcceptance'
                ];

                elements.forEach((elementId, index) => {
                    const exists = iframeDoc.getElementById(elementId) !== null;
                    this.domStatus[elementId] = exists;
                    
                    this.updateCheckStatus('domChecks', index, exists,
                        exists ? '✅ 元素存在' : '❌ 元素缺失');
                    
                    if (exists) {
                        this.log(`✅ DOM元素 ${elementId} 存在`);
                    } else {
                        this.logError(`❌ DOM元素 ${elementId} 缺失`);
                    }
                });
                
                document.body.removeChild(iframe);
            }

            async checkJavaScriptErrors() {
                this.log('📋 检查JavaScript错误...');
                
                const errorTypes = [
                    { name: '语法错误', count: this.errors.filter(e => e.type === 'JavaScript Error' && e.message.includes('Syntax')).length },
                    { name: '运行时错误', count: this.errors.filter(e => e.type === 'JavaScript Error' && !e.message.includes('Syntax')).length },
                    { name: '网络错误', count: this.errors.filter(e => e.message && e.message.includes('fetch')).length },
                    { name: '控制台警告', count: this.warnings.length }
                ];

                errorTypes.forEach((errorType, index) => {
                    const hasErrors = errorType.count > 0;
                    
                    this.updateCheckStatus('jsErrors', index, !hasErrors,
                        hasErrors ? `❌ ${errorType.count}个错误` : '✅ 无错误');
                    
                    if (hasErrors) {
                        this.logError(`❌ 发现 ${errorType.count} 个${errorType.name}`);
                    } else {
                        this.log(`✅ 无${errorType.name}`);
                    }
                });
            }

            generateDiagnosticReport() {
                this.log('');
                this.log('📊 生成诊断报告...');
                this.log('='.repeat(50));
                
                const totalFiles = Object.keys(this.fileLoadStatus).length;
                const loadedFiles = Object.values(this.fileLoadStatus).filter(Boolean).length;
                
                const totalModules = Object.keys(this.moduleStatus).length;
                const initializedModules = Object.values(this.moduleStatus).filter(Boolean).length;
                
                const totalElements = Object.keys(this.domStatus).length;
                const existingElements = Object.values(this.domStatus).filter(Boolean).length;
                
                this.log(`文件加载: ${loadedFiles}/${totalFiles} (${Math.round(loadedFiles/totalFiles*100)}%)`);
                this.log(`模块初始化: ${initializedModules}/${totalModules} (${Math.round(initializedModules/totalModules*100)}%)`);
                this.log(`DOM元素: ${existingElements}/${totalElements} (${Math.round(existingElements/totalElements*100)}%)`);
                this.log(`JavaScript错误: ${this.errors.length}个`);
                this.log(`控制台警告: ${this.warnings.length}个`);
                
                this.log('');
                if (this.errors.length > 0) {
                    this.log('🔴 发现的错误:');
                    this.errors.forEach((error, index) => {
                        this.logError(`${index + 1}. ${error.type}: ${error.message}`);
                    });
                }
                
                if (this.warnings.length > 0) {
                    this.log('');
                    this.log('🟡 发现的警告:');
                    this.warnings.forEach((warning, index) => {
                        this.logWarning(`${index + 1}. ${warning.type}: ${warning.message}`);
                    });
                }
                
                this.log('');
                this.log('✅ 诊断完成！');
            }

            updateCheckStatus(sectionId, itemIndex, success, statusText) {
                const section = document.getElementById(sectionId);
                const items = section.querySelectorAll('.check-item');
                const statusElement = items[itemIndex].querySelector('.status-indicator');
                
                statusElement.textContent = statusText;
                statusElement.className = `status-indicator ${success ? 'status-pass' : 'status-fail'}`;
            }

            log(message) {
                const output = document.getElementById('errorLog');
                const div = document.createElement('div');
                div.style.marginBottom = '4px';
                
                if (message.includes('✅')) {
                    div.style.color = '#10b981';
                } else if (message.includes('📋') || message.includes('🔍') || message.includes('📊')) {
                    div.style.color = '#3b82f6';
                    div.style.fontWeight = 'bold';
                } else if (message.includes('=')) {
                    div.style.color = '#6b7280';
                }
                
                div.textContent = message;
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }

            logError(message) {
                const output = document.getElementById('errorLog');
                const div = document.createElement('div');
                div.style.marginBottom = '4px';
                div.style.color = '#ef4444';
                div.style.fontWeight = 'bold';
                div.textContent = message;
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }

            logWarning(message) {
                const output = document.getElementById('errorLog');
                const div = document.createElement('div');
                div.style.marginBottom = '4px';
                div.style.color = '#f59e0b';
                div.textContent = message;
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 全局诊断实例
        const diagnostic = new ErrorDiagnostic();

        // 全局函数
        async function runDiagnosis() {
            await diagnostic.runDiagnosis();
        }

        function clearLog() {
            const output = document.getElementById('errorLog');
            output.innerHTML = `
                <div style="color: #10b981; font-weight: bold;">🔍 施工管理系统错误诊断控制台</div>
                <div style="color: #6b7280; margin-top: 8px;">日志已清空，准备开始新的诊断...</div>
            `;
        }

        // 页面加载完成后自动运行诊断
        window.addEventListener('load', () => {
            console.log('🔍 错误诊断页面已加载');
            setTimeout(() => {
                runDiagnosis();
            }, 1000);
        });
    </script>
</body>
</html>

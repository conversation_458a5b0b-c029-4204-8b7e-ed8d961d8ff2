/**
 * 营销分享系统
 * 与项目空间分享完全不同的营销推广功能
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

class MarketingSharingSystem {
    constructor() {
        // 分享渠道定义
        this.shareChannels = {
            WECHAT_MOMENTS: {
                name: '微信朋友圈',
                icon: 'fab fa-weixin',
                color: '#07c160',
                platform: 'wechat',
                type: 'moments'
            },
            WECHAT_GROUP: {
                name: '微信群聊',
                icon: 'fab fa-weixin',
                color: '#07c160',
                platform: 'wechat',
                type: 'group'
            },
            WECHAT_FRIEND: {
                name: '微信好友',
                icon: 'fab fa-weixin',
                color: '#07c160',
                platform: 'wechat',
                type: 'friend'
            },
            QQ_ZONE: {
                name: 'QQ空间',
                icon: 'fab fa-qq',
                color: '#12b7f5',
                platform: 'qq',
                type: 'zone'
            },
            WEIBO: {
                name: '新浪微博',
                icon: 'fab fa-weibo',
                color: '#e6162d',
                platform: 'weibo',
                type: 'post'
            },
            COPY_LINK: {
                name: '复制链接',
                icon: 'fas fa-link',
                color: '#6b7280',
                platform: 'universal',
                type: 'link'
            }
        }

        // 分享内容模板
        this.shareTemplates = {
            APP_PROMOTION: {
                title: '智能家居设计神器，让你的家更智能！',
                description: '专业的智能家居设计平台，一站式解决方案，从设计到施工全程跟踪。',
                image: '/assets/images/app-share-banner.jpg',
                url: 'https://geeyan.cn?from=share&type=app',
                tags: ['智能家居', '装修设计', '家居改造']
            },
            DESIGN_SHOWCASE: {
                title: '看看这个智能家居设计案例，太棒了！',
                description: '专业设计师打造的智能家居方案，科技感满满，生活更便捷。',
                image: '/assets/images/design-showcase.jpg',
                url: 'https://geeyan.cn/showcase?from=share&type=design',
                tags: ['设计案例', '智能家居', '装修灵感']
            },
            SERVICE_PROMOTION: {
                title: '免费智能家居设计咨询，专业设计师在线服务！',
                description: '注册即可获得免费设计咨询，专业设计师一对一服务。',
                image: '/assets/images/service-promotion.jpg',
                url: 'https://geeyan.cn/service?from=share&type=service',
                tags: ['免费咨询', '专业设计', '智能家居']
            }
        }

        // 分享奖励机制
        this.rewardSystem = {
            SHARER_REWARDS: {
                // 分享者奖励
                FIRST_SHARE: {
                    name: '首次分享奖励',
                    type: 'points',
                    value: 100,
                    description: '首次分享APP获得100积分'
                },
                DAILY_SHARE: {
                    name: '每日分享奖励',
                    type: 'points',
                    value: 20,
                    description: '每日分享可获得20积分'
                },
                SUCCESSFUL_INVITE: {
                    name: '成功邀请奖励',
                    type: 'coupon',
                    value: 'DESIGN_DISCOUNT_10',
                    description: '成功邀请新用户注册获得设计服务10%折扣券'
                },
                MONTHLY_TOP_SHARER: {
                    name: '月度分享达人',
                    type: 'service',
                    value: 'FREE_DESIGN_CONSULTATION',
                    description: '月度分享次数前10名获得免费设计咨询服务'
                }
            },
            INVITEE_REWARDS: {
                // 被邀请者奖励
                REGISTRATION_BONUS: {
                    name: '注册奖励',
                    type: 'points',
                    value: 200,
                    description: '通过分享链接注册获得200积分'
                },
                WELCOME_COUPON: {
                    name: '新用户专享券',
                    type: 'coupon',
                    value: 'NEWUSER_DESIGN_50',
                    description: '新用户专享设计服务50元抵扣券'
                },
                FIRST_ORDER_DISCOUNT: {
                    name: '首单优惠',
                    type: 'discount',
                    value: 0.15,
                    description: '首次下单享受15%折扣'
                }
            }
        }

        // 分享数据统计
        this.shareStats = new Map()
        this.userRewards = new Map()
        this.shareHistory = new Map()
    }

    /**
     * 生成分享内容
     */
    generateShareContent(userId, shareType = 'APP_PROMOTION', customData = {}) {
        const template = this.shareTemplates[shareType]
        if (!template) {
            throw new Error('分享模板不存在')
        }

        // 生成分享码
        const shareCode = this.generateShareCode(userId, shareType)
        
        // 构建分享URL
        const shareUrl = `${template.url}&shareCode=${shareCode}&userId=${userId}`
        
        const shareContent = {
            shareCode: shareCode,
            shareType: shareType,
            sharerId: userId,
            title: customData.title || template.title,
            description: customData.description || template.description,
            image: customData.image || template.image,
            url: shareUrl,
            tags: template.tags,
            createdAt: new Date(),
            
            // 微信小程序分享参数
            miniProgram: {
                title: template.title,
                path: `/pages/index/index?shareCode=${shareCode}&userId=${userId}`,
                imageUrl: template.image
            },
            
            // H5分享参数
            h5: {
                title: template.title,
                desc: template.description,
                link: shareUrl,
                imgUrl: template.image
            }
        }

        // 记录分享行为
        this.recordShareAction(userId, shareContent)
        
        return shareContent
    }

    /**
     * 执行分享到指定渠道
     */
    async shareToChannel(shareContent, channelType, additionalParams = {}) {
        const channel = this.shareChannels[channelType]
        if (!channel) {
            throw new Error('分享渠道不支持')
        }

        try {
            let shareResult = null

            switch (channel.platform) {
                case 'wechat':
                    shareResult = await this.shareToWechat(shareContent, channel.type, additionalParams)
                    break
                case 'qq':
                    shareResult = await this.shareToQQ(shareContent, channel.type, additionalParams)
                    break
                case 'weibo':
                    shareResult = await this.shareToWeibo(shareContent, additionalParams)
                    break
                case 'universal':
                    shareResult = await this.copyToClipboard(shareContent.url)
                    break
                default:
                    throw new Error('不支持的分享平台')
            }

            // 记录分享成功
            this.recordShareSuccess(shareContent.sharerId, channelType, shareContent.shareCode)
            
            // 给予分享奖励
            await this.giveShareReward(shareContent.sharerId, channelType)

            return shareResult
        } catch (error) {
            console.error('分享失败:', error)
            throw error
        }
    }

    /**
     * 微信分享
     */
    async shareToWechat(shareContent, shareType, params) {
        if (typeof wx === 'undefined') {
            throw new Error('微信环境不可用')
        }

        const shareConfig = {
            title: shareContent.title,
            desc: shareContent.description,
            link: shareContent.url,
            imgUrl: shareContent.image,
            success: () => {
                console.log('微信分享成功')
            },
            cancel: () => {
                console.log('微信分享取消')
            },
            fail: (error) => {
                console.error('微信分享失败:', error)
            }
        }

        switch (shareType) {
            case 'moments':
                wx.updateTimelineShareData(shareConfig)
                break
            case 'friend':
            case 'group':
                wx.updateAppMessageShareData(shareConfig)
                break
        }

        return { success: true, platform: 'wechat', type: shareType }
    }

    /**
     * QQ分享
     */
    async shareToQQ(shareContent, shareType, params) {
        // QQ分享实现
        const qqShareUrl = `mqqapi://share/to_${shareType}?src_type=web&version=1&file_type=news` +
            `&title=${encodeURIComponent(shareContent.title)}` +
            `&description=${encodeURIComponent(shareContent.description)}` +
            `&url=${encodeURIComponent(shareContent.url)}` +
            `&image_url=${encodeURIComponent(shareContent.image)}`

        window.location.href = qqShareUrl
        return { success: true, platform: 'qq', type: shareType }
    }

    /**
     * 微博分享
     */
    async shareToWeibo(shareContent, params) {
        const weiboShareUrl = `https://service.weibo.com/share/share.php?` +
            `title=${encodeURIComponent(shareContent.title + ' ' + shareContent.description)}` +
            `&url=${encodeURIComponent(shareContent.url)}` +
            `&pic=${encodeURIComponent(shareContent.image)}`

        window.open(weiboShareUrl, '_blank', 'width=600,height=400')
        return { success: true, platform: 'weibo', type: 'post' }
    }

    /**
     * 复制链接到剪贴板
     */
    async copyToClipboard(url) {
        try {
            await navigator.clipboard.writeText(url)
            return { success: true, platform: 'universal', type: 'link' }
        } catch (error) {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = url
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            return { success: true, platform: 'universal', type: 'link' }
        }
    }

    /**
     * 处理分享链接访问
     */
    async handleShareLinkVisit(shareCode, visitorInfo = {}) {
        const shareRecord = this.getShareRecord(shareCode)
        if (!shareRecord) {
            return { success: false, error: '分享链接无效' }
        }

        // 记录访问
        const visitRecord = {
            shareCode: shareCode,
            sharerId: shareRecord.sharerId,
            visitorId: visitorInfo.userId || null,
            visitorIP: visitorInfo.ip || '',
            userAgent: visitorInfo.userAgent || '',
            visitedAt: new Date(),
            converted: false
        }

        this.recordShareVisit(visitRecord)

        // 返回分享内容和奖励信息
        return {
            success: true,
            shareContent: shareRecord,
            availableRewards: this.getAvailableRewards('INVITEE_REWARDS'),
            welcomeMessage: '欢迎来到智能家居设计平台！'
        }
    }

    /**
     * 处理新用户注册（通过分享链接）
     */
    async handleInviteeRegistration(shareCode, newUserId) {
        const shareRecord = this.getShareRecord(shareCode)
        if (!shareRecord) {
            return { success: false, error: '分享链接无效' }
        }

        // 给被邀请者发放奖励
        const inviteeRewards = await this.giveInviteeRewards(newUserId)
        
        // 给分享者发放邀请成功奖励
        const sharerRewards = await this.giveInviteSuccessReward(shareRecord.sharerId, newUserId)

        // 记录转化
        this.recordShareConversion(shareCode, newUserId)

        return {
            success: true,
            inviteeRewards: inviteeRewards,
            sharerRewards: sharerRewards,
            message: '注册成功！您已获得新用户专享奖励！'
        }
    }

    /**
     * 生成分享码
     */
    generateShareCode(userId, shareType) {
        const timestamp = Date.now()
        const random = Math.random().toString(36).substr(2, 6)
        return `${shareType}_${userId}_${timestamp}_${random}`.toUpperCase()
    }

    /**
     * 记录分享行为
     */
    recordShareAction(userId, shareContent) {
        if (!this.shareHistory.has(userId)) {
            this.shareHistory.set(userId, [])
        }
        
        this.shareHistory.get(userId).push({
            shareCode: shareContent.shareCode,
            shareType: shareContent.shareType,
            createdAt: shareContent.createdAt,
            status: 'created'
        })

        console.log(`📤 用户 ${userId} 创建分享: ${shareContent.shareCode}`)
    }

    /**
     * 记录分享成功
     */
    recordShareSuccess(userId, channelType, shareCode) {
        // 更新分享统计
        const userStats = this.shareStats.get(userId) || {
            totalShares: 0,
            channelStats: {},
            lastShareDate: null
        }

        userStats.totalShares++
        userStats.channelStats[channelType] = (userStats.channelStats[channelType] || 0) + 1
        userStats.lastShareDate = new Date()

        this.shareStats.set(userId, userStats)

        console.log(`✅ 用户 ${userId} 分享成功: ${shareCode} -> ${channelType}`)
    }

    /**
     * 给予分享奖励
     */
    async giveShareReward(userId, channelType) {
        const userStats = this.shareStats.get(userId)
        const rewards = []

        // 首次分享奖励
        if (userStats.totalShares === 1) {
            rewards.push(this.rewardSystem.SHARER_REWARDS.FIRST_SHARE)
        }

        // 每日分享奖励
        const today = new Date().toDateString()
        const lastShareDate = userStats.lastShareDate?.toDateString()
        if (lastShareDate !== today) {
            rewards.push(this.rewardSystem.SHARER_REWARDS.DAILY_SHARE)
        }

        // 发放奖励
        for (const reward of rewards) {
            await this.giveReward(userId, reward)
        }

        return rewards
    }

    /**
     * 给被邀请者发放奖励
     */
    async giveInviteeRewards(userId) {
        const rewards = [
            this.rewardSystem.INVITEE_REWARDS.REGISTRATION_BONUS,
            this.rewardSystem.INVITEE_REWARDS.WELCOME_COUPON
        ]

        for (const reward of rewards) {
            await this.giveReward(userId, reward)
        }

        return rewards
    }

    /**
     * 发放奖励
     */
    async giveReward(userId, reward) {
        if (!this.userRewards.has(userId)) {
            this.userRewards.set(userId, [])
        }

        const rewardRecord = {
            ...reward,
            userId: userId,
            receivedAt: new Date(),
            status: 'received'
        }

        this.userRewards.get(userId).push(rewardRecord)
        
        console.log(`🎁 用户 ${userId} 获得奖励: ${reward.name}`)
        return rewardRecord
    }

    /**
     * 获取用户分享统计
     */
    getUserShareStats(userId) {
        return this.shareStats.get(userId) || {
            totalShares: 0,
            channelStats: {},
            lastShareDate: null
        }
    }

    /**
     * 获取用户奖励记录
     */
    getUserRewards(userId) {
        return this.userRewards.get(userId) || []
    }

    /**
     * 获取分享记录
     */
    getShareRecord(shareCode) {
        // 这里应该从数据库获取分享记录
        // 暂时返回模拟数据
        return {
            shareCode: shareCode,
            sharerId: 'user_123',
            shareType: 'APP_PROMOTION',
            createdAt: new Date(),
            visits: 0,
            conversions: 0
        }
    }
}

// 导出类
window.MarketingSharingSystem = MarketingSharingSystem

// 使用示例
/*
const marketingShare = new MarketingSharingSystem()

// 生成分享内容
const shareContent = marketingShare.generateShareContent('user_123', 'APP_PROMOTION')

// 分享到微信朋友圈
await marketingShare.shareToChannel(shareContent, 'WECHAT_MOMENTS')

// 处理分享链接访问
const visitResult = await marketingShare.handleShareLinkVisit('SHARE_CODE_123', {
    userId: 'visitor_456',
    ip: '***********'
})

// 处理新用户注册
const registerResult = await marketingShare.handleInviteeRegistration('SHARE_CODE_123', 'new_user_789')
*/

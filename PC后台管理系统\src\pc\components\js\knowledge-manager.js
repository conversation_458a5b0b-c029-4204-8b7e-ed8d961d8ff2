/**
 * 知识库管理模块
 * 实现知识库内容的增删改查功能，支持富文本编辑，支持文档上传和关联
 * 版本: v2.0
 */

class KnowledgeManager {
    constructor() {
        this.knowledgeBase = this.loadKnowledgeBase();
        this.editor = null;
        this.init();
    }

    /**
     * 初始化知识库管理器
     */
    init() {
        console.log('📚 知识库管理器已初始化');
        this.addStyles();
        this.initQuillEditor();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('knowledge-manager-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'knowledge-manager-styles';
        styles.textContent = `
            .knowledge-editor-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .knowledge-editor-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 900px;
                height: 80vh;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .knowledge-editor-header {
                padding: 20px 24px;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f9fafb;
            }

            .knowledge-editor-body {
                flex: 1;
                padding: 24px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .knowledge-form-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .knowledge-form-group label {
                font-weight: 600;
                color: #374151;
                font-size: 14px;
            }

            .knowledge-form-group input,
            .knowledge-form-group textarea {
                padding: 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                transition: border-color 0.2s ease;
            }

            .knowledge-form-group input:focus,
            .knowledge-form-group textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .knowledge-editor-container {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                min-height: 300px;
            }

            .knowledge-attachments {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 16px;
                background: #f9fafb;
            }

            .attachment-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                background: white;
                border-radius: 4px;
                margin-bottom: 8px;
                border: 1px solid #e5e7eb;
            }

            .attachment-info {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .knowledge-preview-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
            }

            .knowledge-preview-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 800px;
                max-height: 90vh;
                overflow-y: auto;
            }

            .knowledge-preview-header {
                padding: 20px 24px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .knowledge-preview-body {
                padding: 24px;
                line-height: 1.6;
            }

            .ql-editor {
                min-height: 200px;
                font-size: 14px;
                line-height: 1.6;
            }

            .ql-toolbar {
                border-top: 1px solid #d1d5db;
                border-left: 1px solid #d1d5db;
                border-right: 1px solid #d1d5db;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }

            .ql-container {
                border-bottom: 1px solid #d1d5db;
                border-left: 1px solid #d1d5db;
                border-right: 1px solid #d1d5db;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * 初始化Quill富文本编辑器
     */
    initQuillEditor() {
        // 动态加载Quill.js
        if (!window.Quill) {
            this.loadQuillLibrary();
        }
    }

    /**
     * 加载Quill库
     */
    loadQuillLibrary() {
        // 加载CSS
        const quillCSS = document.createElement('link');
        quillCSS.rel = 'stylesheet';
        quillCSS.href = 'https://cdn.quilljs.com/1.3.6/quill.snow.css';
        document.head.appendChild(quillCSS);

        // 加载JS
        const quillJS = document.createElement('script');
        quillJS.src = 'https://cdn.quilljs.com/1.3.6/quill.min.js';
        quillJS.onload = () => {
            console.log('✅ Quill编辑器库已加载');
        };
        document.head.appendChild(quillJS);
    }

    /**
     * 创建/编辑知识库条目
     */
    createKnowledge(phase, knowledgeId = null) {
        try {
            const knowledge = knowledgeId ? this.findKnowledge(knowledgeId) : null;
            const modal = this.createEditorModal(phase, knowledge);
            document.body.appendChild(modal);
            modal.style.display = 'flex';
            
            // 初始化编辑器
            setTimeout(() => {
                this.initEditor(modal, knowledge);
            }, 100);

        } catch (error) {
            console.error('创建知识库编辑器失败:', error);
            constructionManager.showErrorMessage('知识库编辑器初始化失败');
        }
    }

    /**
     * 创建编辑器模态框
     */
    createEditorModal(phase, knowledge = null) {
        const isEdit = knowledge !== null;
        const modal = document.createElement('div');
        modal.className = 'knowledge-editor-modal';
        
        modal.innerHTML = `
            <div class="knowledge-editor-content">
                <div class="knowledge-editor-header">
                    <h3><i class="fas fa-book"></i> ${isEdit ? '编辑' : '创建'}知识库条目</h3>
                    <button class="btn-close" onclick="this.closest('.knowledge-editor-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="knowledge-editor-body">
                    <div class="knowledge-form-group">
                        <label for="knowledgeTitle">标题 *</label>
                        <input type="text" id="knowledgeTitle" placeholder="请输入知识库标题" 
                               value="${knowledge ? knowledge.title : ''}" required>
                    </div>
                    
                    <div class="knowledge-form-group">
                        <label for="knowledgeDescription">简介</label>
                        <textarea id="knowledgeDescription" rows="3" placeholder="请输入简介描述">${knowledge ? knowledge.description : ''}</textarea>
                    </div>
                    
                    <div class="knowledge-form-group">
                        <label>内容编辑</label>
                        <div id="knowledgeEditor" class="knowledge-editor-container"></div>
                    </div>
                    
                    <div class="knowledge-form-group">
                        <label>相关文档</label>
                        <div class="knowledge-attachments">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 14px; color: #6b7280;">关联的参考文档</span>
                                <button class="btn btn-sm btn-primary" onclick="knowledgeManager.selectAttachments(this)">
                                    <i class="fas fa-plus"></i> 添加文档
                                </button>
                            </div>
                            <div id="attachmentsList">
                                ${this.renderAttachments(knowledge ? knowledge.attachments : [])}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer" style="padding: 20px 24px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.knowledge-editor-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="knowledgeManager.saveKnowledge('${phase}', ${knowledge ? knowledge.id : 'null'}, this)">
                        <i class="fas fa-save"></i> ${isEdit ? '更新' : '保存'}
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * 初始化编辑器
     */
    initEditor(modal, knowledge = null) {
        if (!window.Quill) {
            console.warn('Quill编辑器未加载，使用简单文本编辑器');
            this.initSimpleEditor(modal, knowledge);
            return;
        }

        try {
            const editorContainer = modal.querySelector('#knowledgeEditor');
            
            this.editor = new Quill(editorContainer, {
                theme: 'snow',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'align': [] }],
                        ['link', 'image'],
                        ['clean']
                    ]
                },
                placeholder: '请输入知识库内容...'
            });

            // 如果是编辑模式，加载现有内容
            if (knowledge && knowledge.content) {
                this.editor.root.innerHTML = knowledge.content;
            }

        } catch (error) {
            console.error('Quill编辑器初始化失败:', error);
            this.initSimpleEditor(modal, knowledge);
        }
    }

    /**
     * 初始化简单编辑器（备用方案）
     */
    initSimpleEditor(modal, knowledge = null) {
        const editorContainer = modal.querySelector('#knowledgeEditor');
        editorContainer.innerHTML = `
            <textarea id="simpleEditor" style="width: 100%; min-height: 300px; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;" 
                      placeholder="请输入知识库内容...">${knowledge ? knowledge.content : ''}</textarea>
        `;
    }

    /**
     * 渲染附件列表
     */
    renderAttachments(attachments = []) {
        if (attachments.length === 0) {
            return '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无关联文档</div>';
        }

        return attachments.map(attachment => `
            <div class="attachment-item" data-id="${attachment.id}">
                <div class="attachment-info">
                    <i class="fas ${fileManager.getFileIcon(attachment.type)}"></i>
                    <div>
                        <div style="font-size: 14px; color: #374151;">${attachment.name}</div>
                        <div style="font-size: 12px; color: #6b7280;">${constructionManager.formatFileSize(attachment.size)}</div>
                    </div>
                </div>
                <button class="btn btn-sm btn-danger" onclick="knowledgeManager.removeAttachment(${attachment.id}, this)" title="移除">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    /**
     * 选择附件
     */
    selectAttachments(button) {
        const modal = button.closest('.knowledge-editor-modal');
        const attachmentsList = modal.querySelector('#attachmentsList');
        
        // 创建文档选择器
        const selector = this.createDocumentSelector(attachmentsList);
        document.body.appendChild(selector);
        selector.style.display = 'flex';
    }

    /**
     * 创建文档选择器
     */
    createDocumentSelector(attachmentsList) {
        const selector = document.createElement('div');
        selector.className = 'knowledge-editor-modal';
        selector.style.zIndex = '10002';
        
        const documents = fileManager.documents;
        
        selector.innerHTML = `
            <div class="knowledge-editor-content" style="max-width: 600px; height: 70vh;">
                <div class="knowledge-editor-header">
                    <h3><i class="fas fa-file-alt"></i> 选择关联文档</h3>
                    <button class="btn-close" onclick="this.closest('.knowledge-editor-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="knowledge-editor-body">
                    <div style="margin-bottom: 16px;">
                        <input type="text" id="documentSearch" placeholder="搜索文档..." 
                               style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;"
                               oninput="knowledgeManager.filterDocuments(this.value, this)">
                    </div>
                    
                    <div id="documentList" style="max-height: 400px; overflow-y: auto;">
                        ${documents.length === 0 ? 
                            '<div style="text-align: center; color: #6b7280; padding: 40px;">暂无可用文档</div>' :
                            documents.map(doc => `
                                <div class="document-selector-item" data-id="${doc.id}" style="display: flex; align-items: center; padding: 12px; border: 1px solid #e5e7eb; border-radius: 6px; margin-bottom: 8px; cursor: pointer; transition: all 0.2s ease;"
                                     onclick="knowledgeManager.toggleDocumentSelection(this)">
                                    <input type="checkbox" style="margin-right: 12px;">
                                    <i class="fas ${fileManager.getFileIcon(doc.type)}" style="margin-right: 8px; color: #6b7280;"></i>
                                    <div style="flex: 1;">
                                        <div style="font-size: 14px; color: #374151;">${doc.name}</div>
                                        <div style="font-size: 12px; color: #6b7280;">${constructionManager.formatFileSize(doc.size)} • ${constructionManager.formatDate(doc.uploadTime)}</div>
                                    </div>
                                </div>
                            `).join('')
                        }
                    </div>
                </div>
                
                <div class="modal-footer" style="padding: 20px 24px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="this.closest('.knowledge-editor-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="knowledgeManager.confirmAttachments(this, '${attachmentsList.id}')">
                        <i class="fas fa-check"></i> 确认选择
                    </button>
                </div>
            </div>
        `;

        return selector;
    }

    /**
     * 切换文档选择状态
     */
    toggleDocumentSelection(item) {
        const checkbox = item.querySelector('input[type="checkbox"]');
        checkbox.checked = !checkbox.checked;
        
        if (checkbox.checked) {
            item.style.background = '#eff6ff';
            item.style.borderColor = '#3b82f6';
        } else {
            item.style.background = '';
            item.style.borderColor = '#e5e7eb';
        }
    }

    /**
     * 确认附件选择
     */
    confirmAttachments(button, attachmentsListId) {
        const selector = button.closest('.knowledge-editor-modal');
        const selectedItems = selector.querySelectorAll('.document-selector-item input[type="checkbox"]:checked');
        
        if (selectedItems.length === 0) {
            constructionManager.showWarningMessage('请选择要关联的文档');
            return;
        }

        const selectedDocuments = Array.from(selectedItems).map(checkbox => {
            const item = checkbox.closest('.document-selector-item');
            const docId = item.dataset.id;
            return fileManager.findDocument(docId);
        }).filter(doc => doc);

        // 更新附件列表
        const attachmentsList = document.querySelector('#attachmentsList');
        if (attachmentsList) {
            const currentAttachments = this.getCurrentAttachments(attachmentsList);
            const newAttachments = [...currentAttachments, ...selectedDocuments];
            attachmentsList.innerHTML = this.renderAttachments(newAttachments);
        }

        selector.remove();
        constructionManager.showSuccessMessage(`已添加 ${selectedDocuments.length} 个关联文档`);
    }

    /**
     * 获取当前附件
     */
    getCurrentAttachments(container) {
        const items = container.querySelectorAll('.attachment-item');
        return Array.from(items).map(item => {
            const id = item.dataset.id;
            return fileManager.findDocument(id);
        }).filter(doc => doc);
    }

    /**
     * 移除附件
     */
    removeAttachment(attachmentId, button) {
        if (confirm('确认移除此关联文档吗？')) {
            const item = button.closest('.attachment-item');
            item.remove();
            constructionManager.showSuccessMessage('关联文档已移除');
        }
    }

    /**
     * 保存知识库
     */
    saveKnowledge(phase, knowledgeId, button) {
        try {
            const modal = button.closest('.knowledge-editor-modal');
            const title = modal.querySelector('#knowledgeTitle').value.trim();
            const description = modal.querySelector('#knowledgeDescription').value.trim();
            
            if (!title) {
                constructionManager.showWarningMessage('请输入标题');
                return;
            }

            // 获取内容
            let content = '';
            if (this.editor) {
                content = this.editor.root.innerHTML;
            } else {
                const simpleEditor = modal.querySelector('#simpleEditor');
                content = simpleEditor ? simpleEditor.value : '';
            }

            // 获取附件
            const attachments = this.getCurrentAttachments(modal.querySelector('#attachmentsList'));

            const knowledgeData = {
                id: knowledgeId || Date.now(),
                title,
                description,
                content,
                attachments: attachments.map(att => ({
                    id: att.id,
                    name: att.name,
                    type: att.type,
                    size: att.size
                })),
                phase,
                createTime: knowledgeId ? this.findKnowledge(knowledgeId).createTime : Date.now(),
                updateTime: Date.now()
            };

            if (knowledgeId) {
                // 更新现有知识库
                this.updateKnowledge(knowledgeData);
            } else {
                // 创建新知识库
                this.addKnowledge(phase, knowledgeData);
            }

            modal.remove();
            constructionManager.renderKnowledge(phase);
            constructionManager.showSuccessMessage(knowledgeId ? '知识库更新成功' : '知识库创建成功');

        } catch (error) {
            console.error('保存知识库失败:', error);
            constructionManager.showErrorMessage('保存失败: ' + error.message);
        }
    }

    /**
     * 添加知识库
     */
    addKnowledge(phase, knowledgeData) {
        constructionManager.phaseData[phase].knowledge.push(knowledgeData);
        this.knowledgeBase.push(knowledgeData);
        this.saveKnowledgeBase();
        constructionManager.savePhaseData();
    }

    /**
     * 更新知识库
     */
    updateKnowledge(knowledgeData) {
        // 更新全局知识库
        const globalIndex = this.knowledgeBase.findIndex(k => k.id === knowledgeData.id);
        if (globalIndex !== -1) {
            this.knowledgeBase[globalIndex] = knowledgeData;
        }

        // 更新阶段知识库
        const phaseKnowledge = constructionManager.phaseData[knowledgeData.phase].knowledge;
        const phaseIndex = phaseKnowledge.findIndex(k => k.id === knowledgeData.id);
        if (phaseIndex !== -1) {
            phaseKnowledge[phaseIndex] = knowledgeData;
        }

        this.saveKnowledgeBase();
        constructionManager.savePhaseData();
    }

    /**
     * 查看知识库
     */
    viewKnowledge(knowledgeId) {
        try {
            const knowledge = this.findKnowledge(knowledgeId);
            if (!knowledge) {
                constructionManager.showErrorMessage('知识库条目不存在');
                return;
            }

            const modal = this.createViewModal(knowledge);
            document.body.appendChild(modal);
            modal.style.display = 'flex';

        } catch (error) {
            console.error('查看知识库失败:', error);
            constructionManager.showErrorMessage('知识库查看失败');
        }
    }

    /**
     * 创建查看模态框
     */
    createViewModal(knowledge) {
        const modal = document.createElement('div');
        modal.className = 'knowledge-preview-modal';
        
        modal.innerHTML = `
            <div class="knowledge-preview-content">
                <div class="knowledge-preview-header">
                    <div>
                        <h3 style="margin: 0; color: #374151;">${knowledge.title}</h3>
                        <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">
                            ${knowledge.description || '暂无描述'}
                        </p>
                        <p style="margin: 4px 0 0 0; color: #9ca3af; font-size: 12px;">
                            创建时间: ${constructionManager.formatDate(knowledge.createTime)}
                            ${knowledge.updateTime !== knowledge.createTime ? ` • 更新时间: ${constructionManager.formatDate(knowledge.updateTime)}` : ''}
                        </p>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-secondary btn-sm" onclick="knowledgeManager.createKnowledge('${knowledge.phase}', ${knowledge.id})" title="编辑">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="this.closest('.knowledge-preview-modal').remove()" title="关闭">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="knowledge-preview-body">
                    <div class="knowledge-content">
                        ${knowledge.content || '<p style="color: #6b7280; text-align: center; padding: 40px;">暂无内容</p>'}
                    </div>
                    
                    ${knowledge.attachments && knowledge.attachments.length > 0 ? `
                        <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                            <h4 style="margin: 0 0 16px 0; color: #374151;">相关文档 (${knowledge.attachments.length})</h4>
                            <div class="attachments-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 12px;">
                                ${knowledge.attachments.map(att => `
                                    <div class="attachment-card" style="padding: 12px; border: 1px solid #e5e7eb; border-radius: 6px; cursor: pointer; transition: all 0.2s ease;"
                                         onclick="fileManager.previewDocument(${att.id})"
                                         onmouseover="this.style.borderColor='#3b82f6'; this.style.background='#eff6ff';"
                                         onmouseout="this.style.borderColor='#e5e7eb'; this.style.background='';">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <i class="fas ${fileManager.getFileIcon(att.type)}" style="color: #6b7280;"></i>
                                            <div style="flex: 1; min-width: 0;">
                                                <div style="font-size: 14px; color: #374151; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${att.name}</div>
                                                <div style="font-size: 12px; color: #6b7280;">${constructionManager.formatFileSize(att.size)}</div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    /**
     * 编辑知识库
     */
    editKnowledge(knowledgeId) {
        const knowledge = this.findKnowledge(knowledgeId);
        if (knowledge) {
            this.createKnowledge(knowledge.phase, knowledgeId);
        }
    }

    /**
     * 删除知识库
     */
    deleteKnowledge(phase, knowledgeId) {
        if (confirm('确认删除此知识库条目吗？此操作不可恢复。')) {
            // 从阶段数据中删除
            const phaseKnowledge = constructionManager.phaseData[phase].knowledge;
            const phaseIndex = phaseKnowledge.findIndex(k => k.id == knowledgeId);
            if (phaseIndex !== -1) {
                phaseKnowledge.splice(phaseIndex, 1);
            }

            // 从全局知识库中删除
            const globalIndex = this.knowledgeBase.findIndex(k => k.id == knowledgeId);
            if (globalIndex !== -1) {
                this.knowledgeBase.splice(globalIndex, 1);
            }

            this.saveKnowledgeBase();
            constructionManager.savePhaseData();
            constructionManager.renderKnowledge(phase);
            constructionManager.showSuccessMessage('知识库条目已删除');
        }
    }

    /**
     * 工具方法
     */
    findKnowledge(knowledgeId) {
        return this.knowledgeBase.find(k => k.id == knowledgeId);
    }

    filterDocuments(searchTerm, input) {
        const container = input.closest('.knowledge-editor-modal').querySelector('#documentList');
        const items = container.querySelectorAll('.document-selector-item');
        
        items.forEach(item => {
            const name = item.querySelector('div > div').textContent.toLowerCase();
            const visible = name.includes(searchTerm.toLowerCase());
            item.style.display = visible ? 'flex' : 'none';
        });
    }

    /**
     * 数据持久化
     */
    loadKnowledgeBase() {
        try {
            const data = localStorage.getItem('construction_knowledge_base');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('加载知识库数据失败:', error);
            return [];
        }
    }

    saveKnowledgeBase() {
        try {
            localStorage.setItem('construction_knowledge_base', JSON.stringify(this.knowledgeBase));
        } catch (error) {
            console.error('保存知识库数据失败:', error);
        }
    }
}

// 全局实例
let knowledgeManager;

// 延迟初始化，等待其他模块加载
if (typeof window !== 'undefined') {
    window.KnowledgeManager = KnowledgeManager;
}

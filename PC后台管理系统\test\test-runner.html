<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工管理系统功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin: 0 0 10px 0;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .test-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .result-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .result-card h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            font-weight: 500;
        }
        
        .status-pass {
            color: #10b981;
        }
        
        .status-fail {
            color: #ef4444;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> 增强版施工管理系统功能测试</h1>
            <p>全面测试6大核心模块的功能完整性</p>
        </div>
        
        <div class="test-controls">
            <button class="btn btn-primary" onclick="runFullTest()">
                <i class="fas fa-play"></i> 开始全面测试
            </button>
            <button class="btn btn-secondary" onclick="clearOutput()">
                <i class="fas fa-trash"></i> 清空输出
            </button>
            <a href="../src/pc/components/pages/construction-management.html" class="btn btn-success" target="_blank">
                <i class="fas fa-external-link-alt"></i> 打开系统
            </a>
        </div>
        
        <div id="testOutput" class="test-output">
            <div style="color: #10b981;">🧪 施工管理系统测试控制台</div>
            <div style="color: #6b7280;">点击"开始全面测试"按钮开始测试...</div>
        </div>
        
        <div id="testResults" class="test-results hidden">
            <!-- 测试结果将在这里显示 -->
        </div>
        
        <div id="loadingIndicator" class="hidden" style="text-align: center; padding: 20px;">
            <div class="loading"></div>
            <p style="margin-top: 10px; color: #6b7280;">正在运行测试，请稍候...</p>
        </div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="../src/pc/js/admin-common.js"></script>
    <script src="../src/pc/components/js/file-manager.js"></script>
    <script src="../src/pc/components/js/knowledge-manager.js"></script>
    <script src="../src/pc/components/js/record-manager.js"></script>
    <script src="../src/pc/components/js/issue-manager.js"></script>
    <script src="../src/pc/components/js/acceptance-manager.js"></script>
    <script src="../src/pc/components/js/construction-enhanced.js"></script>
    <script src="construction-system-test.js"></script>

    <script>
        let testOutput = document.getElementById('testOutput');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        
        // 重定向console输出到页面
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            appendToOutput(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            appendToOutput(args.join(' '), 'error');
        };
        
        function appendToOutput(message, type = 'log') {
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            
            if (type === 'error') {
                div.style.color = '#ef4444';
            } else if (message.includes('✅')) {
                div.style.color = '#10b981';
            } else if (message.includes('❌')) {
                div.style.color = '#ef4444';
            } else if (message.includes('📋') || message.includes('🚀')) {
                div.style.color = '#3b82f6';
                div.style.fontWeight = 'bold';
            }
            
            div.textContent = message;
            testOutput.appendChild(div);
            testOutput.scrollTop = testOutput.scrollHeight;
        }
        
        async function runFullTest() {
            // 显示加载指示器
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('testResults').classList.add('hidden');
            
            // 清空输出
            clearOutput();
            
            try {
                // 等待所有模块加载完成
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 创建测试实例并运行
                const tester = new ConstructionSystemTester();
                const results = await tester.runAllTests();
                
                // 显示测试结果
                displayTestResults(results);
                
            } catch (error) {
                console.error('测试运行失败:', error);
            } finally {
                // 隐藏加载指示器
                document.getElementById('loadingIndicator').classList.add('hidden');
            }
        }
        
        function displayTestResults(results) {
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.classList.remove('hidden');
            
            // 创建汇总卡片
            const summaryCard = document.createElement('div');
            summaryCard.className = 'result-card';
            summaryCard.innerHTML = `
                <h3><i class="fas fa-chart-pie"></i> 测试汇总</h3>
                <div class="test-item">
                    <span>总测试数</span>
                    <span class="test-status">${results.total}</span>
                </div>
                <div class="test-item">
                    <span>通过测试</span>
                    <span class="test-status status-pass">${results.passed}</span>
                </div>
                <div class="test-item">
                    <span>失败测试</span>
                    <span class="test-status status-fail">${results.failed}</span>
                </div>
                <div class="test-item">
                    <span>成功率</span>
                    <span class="test-status" style="color: ${results.successRate >= 80 ? '#10b981' : '#ef4444'}">${results.successRate.toFixed(2)}%</span>
                </div>
                <div class="test-item">
                    <span>测试耗时</span>
                    <span class="test-status">${results.duration}ms</span>
                </div>
            `;
            
            // 创建详细结果卡片
            const detailCard = document.createElement('div');
            detailCard.className = 'result-card';
            detailCard.innerHTML = `
                <h3><i class="fas fa-list"></i> 详细结果</h3>
                ${results.results.map(result => `
                    <div class="test-item">
                        <span>${result.name}</span>
                        <span class="test-status ${result.passed ? 'status-pass' : 'status-fail'}">
                            ${result.passed ? '✅ 通过' : '❌ 失败'}
                        </span>
                    </div>
                `).join('')}
            `;
            
            // 清空并添加结果
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(summaryCard);
            resultsContainer.appendChild(detailCard);
            
            // 如果有失败的测试，创建失败详情卡片
            const failedTests = results.results.filter(r => !r.passed);
            if (failedTests.length > 0) {
                const failedCard = document.createElement('div');
                failedCard.className = 'result-card';
                failedCard.style.borderColor = '#ef4444';
                failedCard.innerHTML = `
                    <h3 style="color: #ef4444;"><i class="fas fa-exclamation-triangle"></i> 失败详情</h3>
                    ${failedTests.map(result => `
                        <div class="test-item">
                            <span>${result.name}</span>
                            <span class="test-status status-fail">${result.details || '未知错误'}</span>
                        </div>
                    `).join('')}
                `;
                resultsContainer.appendChild(failedCard);
            }
        }
        
        function clearOutput() {
            testOutput.innerHTML = `
                <div style="color: #10b981;">🧪 施工管理系统测试控制台</div>
                <div style="color: #6b7280;">输出已清空</div>
            `;
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('🎯 测试页面已加载完成');
            console.log('📝 可以开始运行功能测试');
        });
    </script>
</body>
</html>

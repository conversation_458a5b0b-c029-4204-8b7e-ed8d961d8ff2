# PC后台管理系统 - 完整目录结构和命名规范

## 🎯 设计模块完整功能 (v7.0)

### 📋 5个主模块架构
按照规划图实现的设计模块包含以下5个主模块，共41个子功能：

#### 1. 🖼️ **设计案例** (design_cases)
**API**: `/api/design/cases` | **表**: `design_cases`
- `case_category_apartment` - 户型分类 ✅已启用
- `case_category_brand` - 品牌分类 ✅已启用
- `case_category_style` - 装修风格分类 ✅已启用
- `case_business` - 商业案例 🔵开发中

#### 2. 📦 **设计商品** (design_products)
**API**: `/api/design/products` | **表**: `design_products`
- `product_apartment` - 公寓 ✅已启用
- `product_villa` - 别墅/平层 ✅已启用
- `product_commercial` - 商业空间 🔵开发中

#### 3. 📋 **需求管理** (requirements_management) - 16个功能
**API**: `/api/requirements` | **表**: `customer_requirements`
- `req_analysis` - 需求分析 ✅已启用
- `req_insights` - 数据洞察 ✅已启用
- `req_list` - 需求列表 ✅已启用
- `req_new_opportunities` - 新的用户商机 🔵开发中
- `req_history_opportunities` - 历史的商机 ✅已启用
- `req_create` - 新建需求 ✅已启用
- `req_user_info` - 用户信息 ✅已启用
- `req_product_needs` - 产品需求 ✅已启用
- `req_drawings` - 图纸提交 ✅已启用
- `req_preferences` - 喜好需求 ✅已启用
- `req_designer_assign` - 设计师分配 ✅已启用
- `req_pending_assign` - 3个待分配 🟡规划中
- `req_assign_management` - 管理需求与设计师的分配关系 ✅已启用
- `req_settings` - 需求设置 ✅已启用
- `req_config` - 配置需求管理相关设置 ✅已启用
- `req_templates` - 需求模板 🔵开发中

#### 4. 🎨 **设计中心** (design_center) - 13个功能
**API**: `/api/design/center` | **表**: `design_tasks`
- `task_management` - 设计任务 ✅已启用
- `task_new` - 新任务 ✅已启用
- `task_in_progress` - 进行中 ✅已启用
- `task_history` - 历史任务 ✅已启用
- `feedback_management` - 反馈管理 ✅已启用
- `customer_communication` - 客户沟通 ✅已启用
- `payment_management` - 支付管理 ✅已启用
- `design_progress` - 设计进度 ✅已启用
- `design_drawings` - 设计图纸 ✅已启用
- `design_effects` - 设计效果 ✅已启用
- `construction_guide` - 施工说明 🔵开发中
- `project_budget` - 项目预算 🔵开发中
- `design_versions` - 方案版本 🟡规划中

#### 5. 📊 **数据分析** (data_analysis)
**API**: `/api/analytics` | **表**: `analytics_reports`
- `req_statistics` - 需求统计分析报告 ✅已启用
- `designer_workload` - 设计师工作量分析 🔵开发中
- `customer_satisfaction` - 客户满意度分析 🟡规划中
- `revenue_analysis` - 业务收入分析 🔵开发中
- `completion_rate` - 项目完成率分析 ✅已启用

### 🔧 技术特色
- **数据库友好命名**: 所有ID、API、表名使用下划线命名规范
- **完整API设计**: 每个功能都有对应的RESTful API路径
- **状态管理**: ✅已启用(32个) 🔵开发中(7个) 🟡规划中(2个)
- **用户权限隔离**: 所有业务表包含user_id字段

### 📁 相关文件
- **主页面**: `src/pc/components/pages/design-management-new.html`
- **详细文档**: `docs/设计模块完整功能实现报告--v7.0.md`

---

## 📁 项目目录结构

```
PC后台管理系统/
├── docs/                                    # 项目文档
│   ├── api/                                 # API文档
│   │   ├── auth.md                         # 认证接口文档
│   │   ├── user.md                         # 用户管理接口
│   │   ├── requirement.md                  # 需求管理接口
│   │   └── project.md                      # 项目管理接口
│   ├── deployment/                         # 部署文档
│   │   ├── development.md                  # 开发环境部署
│   │   ├── production.md                   # 生产环境部署
│   │   └── nginx.conf                      # Nginx配置示例
│   └── user-guide/                        # 用户使用指南
│       ├── admin-guide.md                  # 管理员使用指南
│       └── operator-guide.md               # 操作员使用指南
├── src/                                     # 源代码目录
│   ├── assets/                             # 静态资源
│   │   ├── css/                            # 样式文件
│   │   │   ├── base/                       # 基础样式
│   │   │   │   ├── reset.css               # 重置样式
│   │   │   │   ├── variables.css           # CSS变量定义
│   │   │   │   └── utilities.css           # 工具类样式
│   │   │   ├── components/                 # 组件样式
│   │   │   │   ├── buttons.css             # 按钮组件
│   │   │   │   ├── forms.css               # 表单组件
│   │   │   │   ├── tables.css              # 表格组件
│   │   │   │   ├── modals.css              # 模态框组件
│   │   │   │   └── cards.css               # 卡片组件
│   │   │   ├── layouts/                    # 布局样式
│   │   │   │   ├── header.css              # 头部布局
│   │   │   │   ├── sidebar.css             # 侧边栏布局
│   │   │   │   ├── main.css                # 主内容区
│   │   │   │   └── footer.css              # 底部布局
│   │   │   └── pages/                      # 页面特定样式
│   │   │       ├── dashboard.css           # 仪表盘样式
│   │   │       ├── user-management.css     # 用户管理样式
│   │   │       └── requirement-management.css # 需求管理样式
│   │   ├── images/                         # 图片资源
│   │   │   ├── icons/                      # 图标
│   │   │   │   ├── system/                 # 系统图标
│   │   │   │   ├── products/               # 产品图标
│   │   │   │   │   ├── lighting/           # 照明产品图标
│   │   │   │   │   ├── security/           # 安防产品图标
│   │   │   │   │   ├── environment/        # 环境产品图标
│   │   │   │   │   ├── entertainment/      # 影音产品图标
│   │   │   │   │   └── automation/         # 自动化产品图标
│   │   │   │   └── ui/                     # UI图标
│   │   │   ├── backgrounds/                # 背景图片
│   │   │   ├── logos/                      # 品牌Logo
│   │   │   └── avatars/                    # 用户头像
│   │   ├── js/                             # JavaScript文件
│   │   │   ├── libs/                       # 第三方库
│   │   │   │   ├── jquery.min.js           # jQuery库
│   │   │   │   ├── chart.min.js            # 图表库
│   │   │   │   └── sweetalert2.min.js      # 提示框库
│   │   │   ├── utils/                      # 工具函数
│   │   │   │   ├── api.js                  # API请求工具
│   │   │   │   ├── validation.js           # 表单验证工具
│   │   │   │   ├── storage.js              # 本地存储工具
│   │   │   │   └── date.js                 # 日期处理工具
│   │   │   ├── components/                 # 组件脚本
│   │   │   │   ├── modal.js                # 模态框组件
│   │   │   │   ├── table.js                # 表格组件
│   │   │   │   ├── form.js                 # 表单组件
│   │   │   │   └── pagination.js           # 分页组件
│   │   │   └── common.js                   # 公共函数
│   │   └── fonts/                          # 字体文件
│   │       ├── icons.woff2                 # 图标字体
│   │       └── system.woff2                # 系统字体
│   ├── components/                         # 可复用组件
│   │   ├── layout/                         # 布局组件
│   │   │   ├── header.html                 # 页面头部
│   │   │   ├── sidebar.html                # 侧边栏导航
│   │   │   └── footer.html                 # 页面底部
│   │   ├── forms/                          # 表单组件
│   │   │   ├── input-field.html            # 输入框组件
│   │   │   ├── select-field.html           # 选择框组件
│   │   │   ├── textarea-field.html         # 文本域组件
│   │   │   └── upload-field.html           # 文件上传组件
│   │   ├── tables/                         # 表格组件
│   │   │   ├── data-table.html             # 数据表格
│   │   │   └── pagination.html             # 分页组件
│   │   └── modals/                         # 模态框组件
│   │       ├── confirm-modal.html          # 确认对话框
│   │       └── info-modal.html             # 信息对话框
│   ├── pages/                              # 页面文件
│   │   ├── auth/                           # 认证相关页面
│   │   │   ├── login.html                  # 登录页面
│   │   │   ├── register.html               # 注册页面
│   │   │   ├── forgot-password.html        # 忘记密码
│   │   │   └── reset-password.html         # 重置密码
│   │   ├── dashboard/                      # 仪表盘
│   │   │   ├── index.html                  # 仪表盘首页
│   │   │   ├── analytics.html              # 数据分析
│   │   │   └── reports.html                # 报表页面
│   │   ├── user-management/                # 用户管理
│   │   │   ├── index.html                  # 用户列表
│   │   │   ├── create-user.html            # 创建用户
│   │   │   ├── edit-user.html              # 编辑用户
│   │   │   ├── user-detail.html            # 用户详情
│   │   │   └── user-permissions.html       # 用户权限
│   │   ├── requirement-management/         # 需求管理
│   │   │   ├── index.html                  # 需求列表
│   │   │   ├── create-requirement.html     # 创建需求
│   │   │   ├── edit-requirement.html       # 编辑需求
│   │   │   ├── requirement-detail.html     # 需求详情
│   │   │   └── requirement-approval.html   # 需求审批
│   │   ├── project-management/             # 项目管理
│   │   │   ├── index.html                  # 项目列表
│   │   │   ├── create-project.html         # 创建项目
│   │   │   ├── edit-project.html           # 编辑项目
│   │   │   ├── project-detail.html         # 项目详情
│   │   │   └── project-timeline.html       # 项目时间线
│   │   ├── product-management/             # 产品管理
│   │   │   ├── index.html                  # 产品列表
│   │   │   ├── create-product.html         # 创建产品
│   │   │   ├── edit-product.html           # 编辑产品
│   │   │   ├── product-detail.html         # 产品详情
│   │   │   └── product-categories.html     # 产品分类
│   │   ├── orders/                         # 订单管理
│   │   │   ├── index.html                  # 订单列表
│   │   │   ├── order-detail.html           # 订单详情
│   │   │   └── order-tracking.html         # 订单跟踪
│   │   ├── settings/                       # 系统设置
│   │   │   ├── index.html                  # 设置首页
│   │   │   ├── system-config.html          # 系统配置
│   │   │   ├── email-config.html           # 邮件配置
│   │   │   └── backup-restore.html         # 备份恢复
│   │   └── profile/                        # 个人中心
│   │       ├── index.html                  # 个人首页
│   │       ├── edit-profile.html           # 编辑资料
│   │       ├── change-password.html        # 修改密码
│   │       └── security-settings.html      # 安全设置
│   └── templates/                          # 页面模板
│       ├── base.html                       # 基础模板
│       ├── dashboard-layout.html           # 仪表盘布局模板
│       └── form-layout.html                # 表单布局模板
├── tests/                                  # 测试文件
│   ├── unit/                               # 单元测试
│   ├── integration/                        # 集成测试
│   └── e2e/                                # 端到端测试
├── config/                                 # 配置文件
│   ├── environment.js                      # 环境配置
│   ├── database.js                         # 数据库配置
│   └── api.js                              # API配置
├── data/                                   # 数据文件
│   ├── mock/                               # 模拟数据
│   │   ├── users.json                      # 用户数据
│   │   ├── requirements.json               # 需求数据
│   │   └── products.json                   # 产品数据
│   └── seeds/                              # 种子数据
│       ├── initial-users.sql               # 初始用户数据
│       └── product-categories.sql          # 产品分类数据
├── scripts/                                # 脚本文件
│   ├── build.js                            # 构建脚本
│   ├── deploy.js                           # 部署脚本
│   └── backup.js                           # 备份脚本
├── .gitignore                              # Git忽略文件
├── README.md                               # 项目说明
├── CHANGELOG.md                            # 更新日志
├── package.json                            # 项目依赖
└── index.html                              # 主入口页面
```

## 🔧 命名规范

### 1. 文件命名规范

#### HTML文件
```
格式：功能-动作.html
示例：
- create-user.html          # 创建用户
- edit-requirement.html     # 编辑需求
- user-detail.html          # 用户详情
- requirement-list.html     # 需求列表
```

#### CSS文件
```
格式：模块名.css 或 组件名.css
示例：
- user-management.css       # 用户管理模块
- requirement-form.css      # 需求表单组件
- dashboard.css             # 仪表盘页面
- buttons.css               # 按钮组件
```

#### JavaScript文件
```
格式：功能名.js 或 组件名.js
示例：
- api.js                    # API工具
- form-validation.js        # 表单验证
- user-manager.js           # 用户管理器
- modal.js                  # 模态框组件
```

#### 图片文件
```
格式：类型-描述-尺寸.扩展名
示例：
- icon-user-24x24.svg       # 用户图标 24x24
- bg-dashboard-1920x1080.jpg # 仪表盘背景 1920x1080
- logo-primary-white.png    # 主要白色Logo
- product-lighting-thumb.jpg # 照明产品缩略图
```

### 2. CSS类命名规范

#### BEM命名法
```css
/* 块(Block)__元素(Element)--修饰符(Modifier) */

/* 示例：用户卡片组件 */
.user-card { }                      /* 块 */
.user-card__header { }              /* 元素 */
.user-card__avatar { }              /* 元素 */
.user-card__name { }                /* 元素 */
.user-card--featured { }            /* 修饰符 */
.user-card--disabled { }            /* 修饰符 */

/* 示例：需求表单组件 */
.requirement-form { }               /* 块 */
.requirement-form__section { }      /* 元素 */
.requirement-form__field { }        /* 元素 */
.requirement-form__label { }        /* 元素 */
.requirement-form__input { }        /* 元素 */
.requirement-form--readonly { }     /* 修饰符 */
```

#### 功能类命名
```css
/* 布局类 */
.container, .container-fluid
.row, .col, .col-md-6
.flex, .flex-column, .flex-center
.grid, .grid-2, .grid-3

/* 间距类 */
.m-0, .m-1, .m-2, .m-3, .m-4       /* margin */
.p-0, .p-1, .p-2, .p-3, .p-4       /* padding */
.mt-2, .mb-3, .ml-1, .mr-4         /* 方向margin */

/* 文字类 */
.text-left, .text-center, .text-right
.text-primary, .text-secondary, .text-muted
.text-sm, .text-base, .text-lg, .text-xl

/* 背景类 */
.bg-primary, .bg-secondary, .bg-white
.bg-success, .bg-warning, .bg-error

/* 状态类 */
.is-active, .is-disabled, .is-loading
.has-error, .has-success, .has-warning
```

### 3. JavaScript命名规范

#### 变量命名
```javascript
// 驼峰命名法
const userName = 'admin';
const isLoggedIn = true;
const userProfileData = {};

// 常量大写下划线
const API_BASE_URL = 'https://api.example.com';
const MAX_FILE_SIZE = 5 * 1024 * 1024;
const DEFAULT_PAGE_SIZE = 20;

// 私有变量下划线前缀
const _privateVariable = 'internal';
const _internalState = {};
```

#### 函数命名
```javascript
// 动词开头，驼峰命名
function getUserById(id) { }
function createRequirement(data) { }
function updateUserProfile(userId, data) { }
function deleteProject(projectId) { }

// Boolean返回值用is/has/can开头
function isUserActive(user) { }
function hasPermission(user, permission) { }
function canEditRequirement(user, requirement) { }

// 事件处理函数用handle开头
function handleFormSubmit(event) { }
function handleUserClick(event) { }
function handleModalClose() { }
```

#### 类命名
```javascript
// 帕斯卡命名法
class UserManager { }
class RequirementService { }
class ApiClient { }
class FormValidator { }

// 组件类
class UserCard { }
class RequirementForm { }
class DataTable { }
class Modal { }
```

### 4. ID和Class选择器命名

#### ID命名（页面唯一元素）
```html
<!-- 主要区域 -->
<header id="main-header">
<nav id="primary-nav">
<main id="main-content">
<aside id="sidebar">
<footer id="main-footer">

<!-- 表单元素 -->
<form id="requirement-form">
<input id="user-name">
<select id="requirement-type">
<textarea id="requirement-description">

<!-- 模态框 -->
<div id="confirm-modal">
<div id="user-detail-modal">
```

#### Class命名（可复用样式）
```html
<!-- 组件类 -->
<div class="user-card">
<div class="requirement-item">
<div class="data-table">
<div class="form-field">

<!-- 状态类 -->
<div class="is-active">
<div class="has-error">
<div class="is-loading">

<!-- 工具类 -->
<div class="text-center">
<div class="mb-3">
<div class="btn-primary">
```

### 5. 数据属性命名

```html
<!-- 数据属性用data-开头 -->
<div data-user-id="123">
<button data-action="delete" data-target-id="456">
<div data-component="user-card" data-state="active">
<input data-validation="required" data-type="email">

<!-- 自定义属性 -->
<div role="button" aria-label="关闭对话框">
<input aria-describedby="password-help">
<div aria-live="polite" aria-atomic="true">
```

### 6. API接口命名

```javascript
// RESTful API规范
GET    /api/users                   # 获取用户列表
GET    /api/users/{id}              # 获取指定用户
POST   /api/users                   # 创建用户
PUT    /api/users/{id}              # 更新用户
DELETE /api/users/{id}              # 删除用户

// 需求管理API
GET    /api/requirements            # 获取需求列表
POST   /api/requirements            # 创建需求
GET    /api/requirements/{id}       # 获取需求详情
PUT    /api/requirements/{id}       # 更新需求
DELETE /api/requirements/{id}       # 删除需求

// 特殊操作API
POST   /api/requirements/{id}/approve    # 审批需求
POST   /api/users/{id}/reset-password    # 重置密码
GET    /api/dashboard/statistics         # 获取统计数据
```

### 7. 数据库命名规范

#### 表名
```sql
-- 复数形式，下划线命名
users                    -- 用户表
requirements            -- 需求表
user_permissions        -- 用户权限表
requirement_attachments -- 需求附件表
```

#### 字段名
```sql
-- 下划线命名
user_id, user_name, email_address
created_at, updated_at, deleted_at
is_active, is_verified, is_admin
```

### 8. 配置文件命名

```
config/
├── app.config.js           # 应用配置
├── database.config.js      # 数据库配置
├── email.config.js         # 邮件配置
├── upload.config.js        # 上传配置
└── environment/
    ├── development.js      # 开发环境
    ├── staging.js          # 测试环境
    └── production.js       # 生产环境
```

## 📋 命名检查清单

### ✅ 文件命名检查
- [ ] HTML文件使用小写字母和连字符
- [ ] CSS文件名与功能模块对应
- [ ] JavaScript文件名清晰表达功能
- [ ] 图片文件包含尺寸和格式信息

### ✅ CSS命名检查
- [ ] 使用BEM命名法
- [ ] 类名语义化明确
- [ ] 避免缩写和简称
- [ ] 保持命名一致性

### ✅ JavaScript命名检查
- [ ] 变量使用驼峰命名法
- [ ] 函数名动词开头
- [ ] 常量使用大写下划线
- [ ] 类名使用帕斯卡命名法

### ✅ 通用命名检查
- [ ] 名称具有描述性
- [ ] 避免使用拼音和中文
- [ ] 保持命名风格统一
- [ ] 遵循项目约定规范

---

**文档版本**: v1.0  
**最后更新**: 2024年12月19日  
**维护人员**: 开发团队  
**适用范围**: PC后台管理系统全项目 
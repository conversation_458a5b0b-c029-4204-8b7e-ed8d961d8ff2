<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动本地服务器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            text-align: center;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 32px 24px;
        }
        
        .step {
            margin-bottom: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .step h3 {
            color: #667eea;
            margin-bottom: 12px;
            font-size: 18px;
        }
        
        .step p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 16px;
        }
        
        .command {
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 12px 0;
            text-align: left;
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .copy-btn:hover {
            background: #5a67d8;
        }
        
        .button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            margin: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .status {
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .links {
            margin-top: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .links h4 {
            color: #333;
            margin-bottom: 16px;
        }
        
        .link-item {
            display: block;
            margin: 8px 0;
            padding: 12px;
            background: white;
            border-radius: 8px;
            text-decoration: none;
            color: #667eea;
            border: 1px solid #e0e0e0;
            transition: all 0.3s;
        }
        
        .link-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 启动本地服务器</h1>
            <p>访问设计师工作台</p>
        </div>
        
        <div class="content">
            <div id="status" class="status info">
                📡 正在检测服务器状态...
            </div>
            
            <div class="step">
                <h3>🎯 方法1: 使用Node.js (推荐)</h3>
                <p>在项目根目录打开命令行，运行以下命令：</p>
                <div class="command">
                    node server.js
                    <button class="copy-btn" onclick="copyToClipboard('node server.js')">复制</button>
                </div>
            </div>
            
            <div class="step">
                <h3>🐍 方法2: 使用Python</h3>
                <p>在项目根目录打开命令行，运行以下命令：</p>
                <div class="command">
                    python server.py
                    <button class="copy-btn" onclick="copyToClipboard('python server.py')">复制</button>
                </div>
            </div>
            
            <div class="step">
                <h3>⚡ 方法3: 使用PowerShell脚本</h3>
                <p>右键点击 start-server.ps1 文件，选择"使用PowerShell运行"</p>
                <button class="button" onclick="openPowerShellInstructions()">查看详细说明</button>
            </div>
            
            <button class="button" onclick="checkServer()">🔄 重新检测服务器</button>
            
            <div class="links" id="links" style="display: none;">
                <h4>🌐 访问链接 (服务器启动后可用)</h4>
                <a href="http://localhost:3000/src/pc/components/pages/design-management.html" class="link-item" target="_blank">
                    🎨 设计师工作台
                </a>
                <a href="http://localhost:3000/src/pc/components/pages/index.html" class="link-item" target="_blank">
                    🏠 管理系统首页
                </a>
                <a href="http://localhost:3000/test/design-service-integration-test.html" class="link-item" target="_blank">
                    🧪 联调测试页面
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('命令已复制到剪贴板！');
            }).catch(() => {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('命令已复制到剪贴板！');
            });
        }
        
        function openPowerShellInstructions() {
            alert('PowerShell运行步骤：\n\n1. 找到项目根目录下的 start-server.ps1 文件\n2. 右键点击该文件\n3. 选择"使用PowerShell运行"\n4. 如果提示执行策略，选择"是"或"全部是"\n5. 等待服务器启动完成');
        }
        
        async function checkServer() {
            const statusDiv = document.getElementById('status');
            const linksDiv = document.getElementById('links');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '📡 正在检测服务器状态...';
            
            try {
                const response = await fetch('http://localhost:3000/src/pc/components/pages/design-management.html', {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                // 如果没有抛出错误，说明服务器可能在运行
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 服务器运行正常！可以访问设计师工作台。';
                linksDiv.style.display = 'block';
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ 服务器未启动<br>请按照上述方法启动本地服务器';
                linksDiv.style.display = 'none';
            }
        }
        
        // 页面加载时自动检测
        window.addEventListener('load', () => {
            setTimeout(checkServer, 1000);
        });
        
        // 每30秒自动检测一次
        setInterval(checkServer, 30000);
    </script>
</body>
</html>

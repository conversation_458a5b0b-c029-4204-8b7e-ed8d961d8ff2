/**
 * 内部权限管理数据定义
 * 包含内部角色、权限模块和操作权限的完整定义
 */

// 内部角色定义
const INTERNAL_ROLES = {
    DESIGNER: {
        id: 'DESIGNER',
        name: '设计师',
        description: '公司核心业务人员，负责智能家居设计方案和灯光设计，为客户提供专业设计服务',
        level: 'L3',
        levelName: 'L3 - 管理权限',
        color: '#3b82f6',
        icon: 'fas fa-drafting-compass',
        maxSessionTime: 240,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
            MARKETING_MANAGEMENT: ['VIEW'],
            PRODUCT_MANAGEMENT: ['VIEW', 'EDIT', 'UPLOAD', 'DOWNLOAD'],
            CONSTRUCTION_MANAGEMENT: ['VIEW', 'COMMENT'],
            KNOWLEDGE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'UPLOAD'],

            CUSTOMER_MANAGEMENT: ['VIEW', 'EDIT'],
            PROJECT_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
            ORDER_MANAGEMENT: ['VIEW'],
            USER_MANAGEMENT: [],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: [],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    MARKETING_STAFF: {
        id: 'MARKETING_STAFF',
        name: '营销人员',
        description: '负责市场推广、客户获取、销售支持和客户关系维护',
        level: 'L2',
        levelName: 'L2 - 操作权限',
        color: '#10b981',
        icon: 'fas fa-chart-line',
        maxSessionTime: 180,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW', 'SHARE'],
            MARKETING_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE'],
            PRODUCT_MANAGEMENT: ['VIEW', 'DOWNLOAD', 'SHARE'],
            CONSTRUCTION_MANAGEMENT: [],
            KNOWLEDGE_MANAGEMENT: ['VIEW', 'SHARE'],

            CUSTOMER_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
            PROJECT_MANAGEMENT: ['VIEW', 'CREATE'],
            ORDER_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT'],
            USER_MANAGEMENT: [],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW', 'EXPORT'],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: ['VIEW'],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    CONSTRUCTION_MANAGER: {
        id: 'CONSTRUCTION_MANAGER',
        name: '施工管理',
        description: '负责施工项目管理、质量控制、进度协调',
        level: 'L3',
        levelName: 'L3 - 管理权限',
        color: '#f59e0b',
        icon: 'fas fa-hard-hat',
        maxSessionTime: 200,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW'],
            MARKETING_MANAGEMENT: [],
            PRODUCT_MANAGEMENT: ['VIEW'],
            CONSTRUCTION_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR'],
            KNOWLEDGE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'UPLOAD'],

            CUSTOMER_MANAGEMENT: ['VIEW', 'EDIT'],
            PROJECT_MANAGEMENT: ['VIEW', 'EDIT'],
            ORDER_MANAGEMENT: ['VIEW', 'EDIT'],
            USER_MANAGEMENT: [],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: [],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    CUSTOMER_SERVICE: {
        id: 'CUSTOMER_SERVICE',
        name: '客服人员',
        description: '负责用户咨询和基础问题处理',
        level: 'L1',
        levelName: 'L1 - 查看权限',
        color: '#8b5cf6',
        icon: 'fas fa-headset',
        maxSessionTime: 240,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW'],
            MARKETING_MANAGEMENT: ['VIEW'],
            PRODUCT_MANAGEMENT: ['VIEW', 'DOWNLOAD'],
            CONSTRUCTION_MANAGEMENT: ['VIEW'],
            KNOWLEDGE_MANAGEMENT: ['VIEW', 'SHARE'],
            CUSTOMER_MANAGEMENT: ['VIEW', 'EDIT'],
            PROJECT_MANAGEMENT: ['VIEW'],
            ORDER_MANAGEMENT: ['VIEW', 'EDIT'],
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: [],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    SUPER_ADMIN: {
        id: 'SUPER_ADMIN',
        name: '超级管理员',
        description: '系统最高权限管理者，负责整个系统的架构管理和安全控制',
        level: 'L5',
        levelName: 'L5 - 安全权限',
        color: '#dc2626',
        icon: 'fas fa-crown',
        maxSessionTime: 30,
        requireMFA: true,
        ipRestriction: true,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW', 'CONFIGURE'],
            MARKETING_MANAGEMENT: ['VIEW', 'CONFIGURE'],
            PRODUCT_MANAGEMENT: ['VIEW', 'CONFIGURE'],
            CONSTRUCTION_MANAGEMENT: ['VIEW', 'CONFIGURE'],
            KNOWLEDGE_MANAGEMENT: ['VIEW', 'CONFIGURE'],
            CUSTOMER_MANAGEMENT: ['VIEW', 'EXPORT'],
            PROJECT_MANAGEMENT: ['VIEW', 'EXPORT'],
            ORDER_MANAGEMENT: ['VIEW', 'EXPORT'],
            USER_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'EXPORT'],
            ROLE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE'],
            PERMISSION_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE'],
            SYSTEM_CONFIG: ['VIEW', 'EDIT', 'CONFIGURE', 'BACKUP', 'RESTORE'],
            DATA_STATISTICS: ['VIEW', 'EXPORT', 'CONFIGURE'],
            AUDIT_LOGS: ['VIEW', 'EXPORT', 'AUDIT'],
            FINANCIAL_MANAGEMENT: ['VIEW', 'EXPORT'],
            SYSTEM_MONITORING: ['VIEW', 'MONITOR', 'CONFIGURE'],
            SECURITY_MANAGEMENT: ['VIEW', 'CONFIGURE', 'AUDIT'],
            BACKUP_MANAGEMENT: ['VIEW', 'BACKUP', 'RESTORE', 'CONFIGURE']
        }
    },
    
    SYSTEM_ADMIN: {
        id: 'SYSTEM_ADMIN',
        name: '系统管理员',
        description: '负责系统日常运维和技术管理',
        level: 'L4',
        levelName: 'L4 - 配置权限',
        color: '#3b82f6',
        icon: 'fas fa-cogs',
        maxSessionTime: 60,
        requireMFA: true,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'EXPORT'],
            ROLE_MANAGEMENT: ['VIEW'],
            PERMISSION_MANAGEMENT: ['VIEW'],
            SYSTEM_CONFIG: ['VIEW', 'EDIT', 'CONFIGURE'],
            DATA_STATISTICS: ['VIEW', 'EXPORT'],
            AUDIT_LOGS: ['VIEW', 'EXPORT'],
            CONTENT_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
            FINANCIAL_MANAGEMENT: ['VIEW'],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW', 'EDIT'],
            SYSTEM_MONITORING: ['VIEW', 'MONITOR'],
            SECURITY_MANAGEMENT: ['VIEW'],
            BACKUP_MANAGEMENT: ['VIEW', 'BACKUP', 'RESTORE']
        }
    },
    
    OPERATION_ADMIN: {
        id: 'OPERATION_ADMIN',
        name: '运营管理员',
        description: '负责业务运营和用户服务管理',
        level: 'L3',
        levelName: 'L3 - 管理权限',
        color: '#10b981',
        icon: 'fas fa-chart-line',
        maxSessionTime: 120,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW', 'EDIT'],
            ROLE_MANAGEMENT: ['VIEW'],
            PERMISSION_MANAGEMENT: ['VIEW'],
            SYSTEM_CONFIG: ['VIEW'],
            DATA_STATISTICS: ['VIEW', 'EXPORT'],
            AUDIT_LOGS: ['VIEW'],
            CONTENT_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'],
            FINANCIAL_MANAGEMENT: ['VIEW', 'EDIT'],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
            SYSTEM_MONITORING: ['VIEW'],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },
    
    CUSTOMER_SERVICE: {
        id: 'CUSTOMER_SERVICE',
        name: '客服人员',
        description: '负责用户咨询和基础问题处理',
        level: 'L1',
        levelName: 'L1 - 查看权限',
        color: '#8b5cf6',
        icon: 'fas fa-headset',
        maxSessionTime: 240,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: [],
            CONTENT_MANAGEMENT: ['VIEW'],
            FINANCIAL_MANAGEMENT: [],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT'],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },
    
    FINANCE_STAFF: {
        id: 'FINANCE_STAFF',
        name: '财务人员',
        description: '负责财务数据管理和财务报表',
        level: 'L2',
        levelName: 'L2 - 操作权限',
        color: '#059669',
        icon: 'fas fa-dollar-sign',
        maxSessionTime: 120,
        requireMFA: true,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW', 'EXPORT'],
            AUDIT_LOGS: ['VIEW'],
            CONTENT_MANAGEMENT: [],
            FINANCIAL_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'EXPORT', 'APPROVE'],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW'],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },
    
    TECH_SUPPORT: {
        id: 'TECH_SUPPORT',
        name: '技术支持',
        description: '负责技术问题诊断和用户技术支持',
        level: 'L2',
        levelName: 'L2 - 操作权限',
        color: '#f59e0b',
        icon: 'fas fa-tools',
        maxSessionTime: 180,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: ['VIEW'],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: ['VIEW'],
            CONTENT_MANAGEMENT: ['VIEW', 'EDIT'],
            FINANCIAL_MANAGEMENT: [],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT'],
            SYSTEM_MONITORING: ['VIEW', 'MONITOR'],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },
    
    DATA_ANALYST: {
        id: 'DATA_ANALYST',
        name: '数据分析师',
        description: '负责数据分析和商业智能',
        level: 'L1',
        levelName: 'L1 - 查看权限',
        color: '#6366f1',
        icon: 'fas fa-chart-bar',
        maxSessionTime: 240,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW', 'EXPORT'],
            AUDIT_LOGS: ['VIEW'],
            CONTENT_MANAGEMENT: ['VIEW'],
            FINANCIAL_MANAGEMENT: ['VIEW'],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW'],
            SYSTEM_MONITORING: ['VIEW'],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },
    
    CONTENT_MODERATOR: {
        id: 'CONTENT_MODERATOR',
        name: '内容审核员',
        description: '负责平台内容审核和管理',
        level: 'L2',
        levelName: 'L2 - 操作权限',
        color: '#ec4899',
        icon: 'fas fa-file-alt',
        maxSessionTime: 180,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            USER_MANAGEMENT: ['VIEW'],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: ['VIEW'],
            AUDIT_LOGS: ['VIEW'],
            CONTENT_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'REJECT'],
            FINANCIAL_MANAGEMENT: [],
            CUSTOMER_SERVICE_MANAGEMENT: ['VIEW'],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    CUSTOMER: {
        id: 'CUSTOMER',
        name: '客户',
        description: '项目业主，参与施工验收和反馈',
        level: 'L1',
        levelName: 'L1 - 查看权限',
        color: '#06b6d4',
        icon: 'fas fa-user',
        maxSessionTime: 480,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW'],
            MARKETING_MANAGEMENT: [],
            PRODUCT_MANAGEMENT: ['VIEW'],
            CONSTRUCTION_MANAGEMENT: ['VIEW', 'APPROVE', 'COMMENT', 'RATE', 'UPLOAD'],
            KNOWLEDGE_MANAGEMENT: ['VIEW'],

            CUSTOMER_MANAGEMENT: ['VIEW', 'EDIT'],
            PROJECT_MANAGEMENT: ['VIEW'],
            ORDER_MANAGEMENT: ['VIEW'],
            USER_MANAGEMENT: [],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: [],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: [],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    },

    EXTERNAL_CONTRACTOR: {
        id: 'EXTERNAL_CONTRACTOR',
        name: '外部施工队伍',
        description: '外部合作施工团队，负责具体施工作业',
        level: 'L2',
        levelName: 'L2 - 操作权限',
        color: '#84cc16',
        icon: 'fas fa-users',
        maxSessionTime: 300,
        requireMFA: false,
        ipRestriction: false,
        permissions: {
            DESIGN_MANAGEMENT: ['VIEW'],
            MARKETING_MANAGEMENT: [],
            PRODUCT_MANAGEMENT: ['VIEW'],
            CONSTRUCTION_MANAGEMENT: ['VIEW', 'CREATE', 'EDIT', 'UPLOAD', 'COMMENT'],
            KNOWLEDGE_MANAGEMENT: ['VIEW'],
            CUSTOMER_MANAGEMENT: [],
            PROJECT_MANAGEMENT: ['VIEW'],
            ORDER_MANAGEMENT: [],
            USER_MANAGEMENT: [],
            ROLE_MANAGEMENT: [],
            PERMISSION_MANAGEMENT: [],
            SYSTEM_CONFIG: [],
            DATA_STATISTICS: [],
            AUDIT_LOGS: [],
            FINANCIAL_MANAGEMENT: [],
            SYSTEM_MONITORING: [],
            SECURITY_MANAGEMENT: [],
            BACKUP_MANAGEMENT: []
        }
    }
};

// 权限模块定义
const INTERNAL_MODULES = {
    DESIGN_MANAGEMENT: {
        id: 'DESIGN_MANAGEMENT',
        name: '设计管理',
        description: '智能家居设计方案和灯光设计管理',
        category: '业务管理',
        icon: 'fas fa-drafting-compass',
        color: '#3b82f6',
        subModules: {
            design_services: {
                name: '设计服务管理',
                description: '管理户型优化(¥299)、效果图设计(¥999)、定制设计等服务产品',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE', 'UPLOAD']
            },
            design_projects: {
                name: '设计项目',
                description: '管理客户设计项目和方案执行',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'ASSIGN']
            },
            design_cases: {
                name: '设计案例',
                description: '成功案例展示、案例分类管理、客户评价',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'PUBLISH', 'SHARE']
            },
            design_templates: {
                name: '设计模板',
                description: '按房型(1-4室)、风格(现代/北欧/中式等)、预算的标准化模板',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            ai_design_tools: {
                name: 'AI设计工具',
                description: 'AI户型分析、方案生成、效果图渲染工具',
                operations: ['VIEW', 'EXECUTE', 'CONFIGURE', 'MONITOR']
            },
            design_pricing: {
                name: '设计定价',
                description: '不同设计服务的价格策略和套餐管理',
                operations: ['VIEW', 'EDIT', 'APPROVE', 'CONFIGURE']
            },
            customer_requirements: {
                name: '客户需求',
                description: '客户设计需求收集、分析和匹配',
                operations: ['VIEW', 'CREATE', 'EDIT', 'ASSIGN']
            },
            design_review: {
                name: '设计审核',
                description: '设计方案审核和客户确认流程',
                operations: ['VIEW', 'APPROVE', 'REJECT', 'COMMENT']
            }
        }
    },

    MARKETING_MANAGEMENT: {
        id: 'MARKETING_MANAGEMENT',
        name: '营销管理',
        description: '市场推广和客户获取管理',
        category: '业务管理',
        icon: 'fas fa-bullhorn',
        color: '#10b981',
        subModules: {
            campaigns: {
                name: '营销活动',
                description: '营销活动策划和执行',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE']
            },
            leads: {
                name: '潜在客户',
                description: '潜在客户信息和跟进管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'EXPORT']
            },
            content: {
                name: '营销内容',
                description: '营销素材和内容管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            analytics: {
                name: '营销分析',
                description: '营销效果分析和报表',
                operations: ['VIEW', 'EXPORT']
            }
        }
    },

    PRODUCT_MANAGEMENT: {
        id: 'PRODUCT_MANAGEMENT',
        name: '产品管理',
        description: '智能家居产品和供应链管理',
        category: '业务管理',
        icon: 'fas fa-cube',
        color: '#8b5cf6',
        subModules: {
            product_catalog: {
                name: '产品目录',
                description: '智能设备产品信息管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            product_upload: {
                name: '产品上传',
                description: '产品基础信息和规格上传管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'IMPORT', 'EXPORT']
            },
            product_documents: {
                name: '产品文档管理',
                description: '产品海报、PPT、参数表、说明书、图片、视频等文档管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'DOWNLOAD', 'SHARE']
            },
            inventory: {
                name: '库存管理',
                description: '产品库存监控和管理',
                operations: ['VIEW', 'EDIT', 'EXPORT']
            },
            suppliers: {
                name: '供应商管理',
                description: '供应商信息和合作管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            pricing: {
                name: '价格管理',
                description: '产品价格策略和管理',
                operations: ['VIEW', 'EDIT', 'APPROVE']
            }
        }
    },

    CONSTRUCTION_MANAGEMENT: {
        id: 'CONSTRUCTION_MANAGEMENT',
        name: '施工管理',
        description: '智能家居5阶段施工流程管理（交底-布线-安装-调试-售后）',
        category: '业务管理',
        icon: 'fas fa-hard-hat',
        color: '#f59e0b',
        subModules: {
            briefing_management: {
                name: '交底管理',
                description: '设计方案交底、技术要求说明、施工标准确认、交底文档管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'ASSIGN']
            },
            wiring_management: {
                name: '水电管理',
                description: '水电布线方案、施工进度跟踪、质量验收、隐蔽工程记录',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR']
            },
            ceiling_management: {
                name: '吊顶管理',
                description: '吊顶设计实施、材料管理、安装进度、验收标准',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR']
            },
            installation_management: {
                name: '安装管理',
                description: '智能设备安装、系统集成、设备调试、安装文档',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR']
            },
            debugging_management: {
                name: '调试管理',
                description: '系统联调、功能测试、性能优化、调试报告',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR']
            },
            afterservice_management: {
                name: '售后管理',
                description: '质保服务、维护保养、问题处理、客户反馈',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'UPLOAD', 'MONITOR']
            },
            construction_records: {
                name: '施工记录管理',
                description: '施工进度记录、照片视频记录、质量检查记录、客户反馈记录等全方位记录管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'APPROVE', 'SHARE', 'EXPORT']
            },
            construction_teams: {
                name: '施工团队',
                description: '施工人员和团队管理、角色分配、技能认证',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'ASSIGN']
            },
            quality_control: {
                name: '质量控制',
                description: '施工质量检查、验收标准、质量评估、改进措施',
                operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE', 'RATE', 'MONITOR']
            }
        }
    },

    KNOWLEDGE_MANAGEMENT: {
        id: 'KNOWLEDGE_MANAGEMENT',
        name: '知识库管理',
        description: '知识内容和培训材料管理',
        category: '业务管理',
        icon: 'fas fa-book',
        color: '#6366f1',
        subModules: {
            articles: {
                name: '知识文章',
                description: '技术文档和知识文章',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE']
            },
            courses: {
                name: '培训课程',
                description: '培训课程和教学材料',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            videos: {
                name: '视频教程',
                description: '视频教程和演示材料',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            product_knowledge: {
                name: '产品知识库',
                description: '产品使用文档、教学视频、参考图片、应用案例、技术支持文档',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'APPROVE', 'SHARE']
            },
            product_knowledge_mapping: {
                name: '产品知识映射',
                description: '产品与知识库内容的智能关联管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            construction_knowledge: {
                name: '施工知识库',
                description: '施工标准文档、操作规范、质量检查清单、常见问题解决方案、培训材料',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'APPROVE', 'SHARE']
            },
            construction_knowledge_mapping: {
                name: '施工知识映射',
                description: '施工阶段与知识库内容的智能关联，根据施工进度开放相应权限',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            project_knowledge: {
                name: '项目知识库',
                description: '项目相关的知识内容，包括图纸说明、施工指导、设备使用等',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'SHARE']
            },
            space_knowledge_access: {
                name: '空间知识访问',
                description: '根据项目空间成员角色控制知识库内容的访问权限',
                operations: ['VIEW', 'CONFIGURE', 'ASSIGN', 'MONITOR']
            },
            certification: {
                name: '认证管理',
                description: '专业认证和考试管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE']
            }
        }
    },



    CUSTOMER_MANAGEMENT: {
        id: 'CUSTOMER_MANAGEMENT',
        name: '客户管理',
        description: '客户信息和关系管理',
        category: '客户服务',
        icon: 'fas fa-users',
        color: '#ec4899',
        subModules: {
            customer_profiles: {
                name: '客户档案',
                description: '客户基本信息和档案',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'EXPORT']
            },
            communication: {
                name: '客户沟通',
                description: '客户沟通记录和历史',
                operations: ['VIEW', 'CREATE', 'EDIT']
            },
            feedback: {
                name: '客户反馈',
                description: '客户意见和反馈管理',
                operations: ['VIEW', 'CREATE', 'EDIT']
            },
            service_tickets: {
                name: '服务工单',
                description: '客户服务工单处理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            }
        }
    },

    PROJECT_MANAGEMENT: {
        id: 'PROJECT_MANAGEMENT',
        name: '项目管理',
        description: '智能家居项目全生命周期管理，包括项目空间、成员协作、进度跟踪',
        category: '业务管理',
        icon: 'fas fa-project-diagram',
        color: '#14b8a6',
        subModules: {
            project_overview: {
                name: '项目概览',
                description: '项目基本信息和状态，如朝阳明园智能家居项目',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'ARCHIVE']
            },
            project_spaces: {
                name: '项目空间',
                description: '管理用户的项目空间，包括朝阳明园等具体项目',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'SHARE', 'ARCHIVE']
            },
            space_members: {
                name: '空间成员',
                description: '管理项目空间中的用户和邀请人员权限',
                operations: ['VIEW', 'INVITE', 'EDIT', 'REMOVE', 'ASSIGN_ROLE']
            },
            space_content: {
                name: '空间内容',
                description: '管理项目空间中的图纸、效果图、设备清单、施工说明等内容',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'UPLOAD', 'DOWNLOAD', 'SHARE']
            },
            timeline: {
                name: '项目时间线',
                description: '项目进度和里程碑管理',
                operations: ['VIEW', 'EDIT', 'UPDATE', 'MONITOR']
            },
            collaboration: {
                name: '项目协作',
                description: '团队协作和沟通管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT']
            },
            deliverables: {
                name: '交付物管理',
                description: '项目交付物和文档管理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE']
            },
            space_permissions: {
                name: '空间权限',
                description: '管理项目空间中不同角色的访问权限和操作权限',
                operations: ['VIEW', 'CONFIGURE', 'ASSIGN', 'REVOKE']
            },
            space_notifications: {
                name: '空间通知',
                description: '管理项目空间的消息通知和提醒',
                operations: ['VIEW', 'CREATE', 'SEND', 'CONFIGURE']
            }
        }
    },

    ORDER_MANAGEMENT: {
        id: 'ORDER_MANAGEMENT',
        name: '订单管理',
        description: '客户订单和交易管理',
        category: '客户服务',
        icon: 'fas fa-shopping-cart',
        color: '#f97316',
        subModules: {
            orders: {
                name: '订单处理',
                description: '客户订单创建和处理',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            payments: {
                name: '支付管理',
                description: '订单支付状态和处理',
                operations: ['VIEW', 'EDIT']
            },
            delivery: {
                name: '交付管理',
                description: '产品交付和物流管理',
                operations: ['VIEW', 'EDIT']
            },
            refunds: {
                name: '退款处理',
                description: '退款申请和处理',
                operations: ['VIEW', 'EDIT', 'APPROVE']
            }
        }
    },

    USER_MANAGEMENT: {
        id: 'USER_MANAGEMENT',
        name: '用户管理',
        description: '系统用户账号和权限管理',
        category: '系统管理',
        icon: 'fas fa-user-cog',
        color: '#6b7280',
        subModules: {
            external_users: {
                name: '外部用户管理',
                description: '管理智能家居系统的外部用户',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'EXPORT']
            },
            internal_users: {
                name: '内部用户管理',
                description: '管理系统内部员工账号',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            },
            user_profiles: {
                name: '用户档案管理',
                description: '管理用户详细信息和档案',
                operations: ['VIEW', 'EDIT', 'EXPORT']
            },
            user_authentication: {
                name: '用户认证管理',
                description: '管理用户登录认证和安全设置',
                operations: ['VIEW', 'EDIT', 'CONFIGURE']
            }
        }
    },
    
    ROLE_MANAGEMENT: {
        id: 'ROLE_MANAGEMENT',
        name: '角色管理',
        description: '管理系统角色定义和分配',
        category: '权限管理',
        icon: 'fas fa-user-tag',
        color: '#8b5cf6',
        subModules: {
            internal_roles: {
                name: '内部角色管理',
                description: '管理内部员工角色',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            external_roles: {
                name: '外部角色管理',
                description: '管理外部用户角色',
                operations: ['VIEW', 'EDIT', 'CONFIGURE']
            },
            role_assignment: {
                name: '角色分配',
                description: '为用户分配和管理角色',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE']
            }
        }
    },
    
    PERMISSION_MANAGEMENT: {
        id: 'PERMISSION_MANAGEMENT',
        name: '权限管理',
        description: '管理系统权限配置和控制',
        category: '权限管理',
        icon: 'fas fa-shield-alt',
        color: '#ef4444',
        subModules: {
            permission_config: {
                name: '权限配置',
                description: '配置系统权限规则',
                operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'CONFIGURE']
            },
            access_control: {
                name: '访问控制',
                description: '管理访问控制策略',
                operations: ['VIEW', 'EDIT', 'CONFIGURE']
            },
            permission_audit: {
                name: '权限审计',
                description: '审计权限使用情况',
                operations: ['VIEW', 'AUDIT', 'EXPORT']
            }
        }
    }
};

// 操作权限标签
const OPERATION_LABELS = {
    VIEW: '查看',
    CREATE: '创建',
    EDIT: '编辑',
    DELETE: '删除',
    APPROVE: '审批',
    REJECT: '拒绝',
    EXPORT: '导出',
    IMPORT: '导入',
    CONFIGURE: '配置',
    MONITOR: '监控',
    AUDIT: '审计',
    BACKUP: '备份',
    RESTORE: '恢复',
    EXECUTE: '执行'
};

// 权限等级颜色
const LEVEL_COLORS = {
    'L1': '#6b7280',
    'L2': '#10b981',
    'L3': '#f59e0b',
    'L4': '#3b82f6',
    'L5': '#dc2626'
};

// 导出数据供其他脚本使用
window.INTERNAL_PERMISSIONS_DATA = {
    ROLES: INTERNAL_ROLES,
    MODULES: INTERNAL_MODULES,
    OPERATION_LABELS: OPERATION_LABELS,
    LEVEL_COLORS: LEVEL_COLORS
};

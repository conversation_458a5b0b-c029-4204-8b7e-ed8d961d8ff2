/* 
智能设计与施工管理系统 - 统一后台管理系统样式
版本: v2.0
基于: PC端UI设计规范文档 + 现代化B端设计
*/

/* ==================== 全局变量定义 ==================== */
:root {
  /* 主色系 - 黑白主题 */
  --primary-black: #1a1a1a;
  --primary-white: #ffffff;
  --primary-gray: #374151;
  
  /* 中性色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 功能色系 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, var(--primary-black), var(--gray-700));
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  
  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  
  /* 间距系统 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  
  /* 布局变量 */
  --sidebar-width: 280px;
  --header-height: 72px;
  --sidebar-collapsed-width: 80px;
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
  --font-mono: 'SFMono-Regular', Menlo, Monaco, Consolas, monospace;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.4s ease-out;
}

/* ==================== 基础样式重置 ==================== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--gray-900);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==================== 主布局框架 ==================== */

.admin-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ==================== 顶部导航栏 ==================== */

.admin-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background: var(--primary-white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: 100;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-6);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-white);
  font-size: 18px;
}

.logo-text h1 {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  line-height: 1.2;
}

.logo-text span {
  font-size: 12px;
  color: var(--gray-500);
  font-weight: 400;
}

.header-center {
  flex: 1;
  max-width: 500px;
  margin: 0 var(--space-8);
}

.global-search {
  position: relative;
  width: 100%;
}

.global-search i {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: 16px;
}

.global-search input {
  width: 100%;
  height: 44px;
  padding: 0 var(--space-4) 0 var(--space-12);
  background: var(--gray-100);
  border: 1px solid transparent;
  border-radius: var(--radius-xl);
  font-size: 14px;
  outline: none;
  transition: all var(--transition-fast);
}

.global-search input:focus {
  background: var(--primary-white);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.action-item {
  position: relative;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-item:hover {
  background: var(--gray-100);
}

.action-item i {
  font-size: 18px;
  color: var(--gray-600);
}

.action-item .badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  background: var(--error);
  color: var(--primary-white);
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-left: var(--space-4);
}

.user-menu:hover {
  background: var(--gray-100);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-900);
}

.user-menu i {
  font-size: 12px;
  color: var(--gray-500);
}

/* ==================== 侧边栏 ==================== */

.admin-sidebar {
  position: fixed;
  left: 0;
  top: var(--header-height);
  width: var(--sidebar-width);
  height: calc(100vh - var(--header-height));
  background: var(--primary-white);
  border-right: 1px solid var(--gray-200);
  overflow-y: auto;
  z-index: 90;
  transition: transform var(--transition-normal);
}

.sidebar-nav {
  padding: var(--space-6) 0;
}

.nav-section {
  margin-bottom: var(--space-8);
}

.nav-section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: 0 var(--space-6);
  margin-bottom: var(--space-4);
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  color: var(--gray-600);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.nav-link.active {
  background: var(--gray-100);
  color: var(--primary-black);
  font-weight: 600;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-black);
}

.nav-link i {
  width: 20px;
  text-align: center;
  font-size: 16px;
}

.nav-badge {
  margin-left: auto;
  padding: 2px 8px;
  background: var(--gray-200);
  color: var(--gray-700);
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 600;
}

.nav-badge.new {
  background: var(--info);
  color: var(--primary-white);
}

.nav-badge.urgent {
  background: var(--error);
  color: var(--primary-white);
}

/* ==================== 主内容区域 ==================== */

.admin-main {
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
  transition: margin-left var(--transition-normal);
}

.page-content {
  padding: var(--space-8);
}

.module-content {
  display: none;
}

.module-content.active {
  display: block;
}

/* ==================== 统计卡片 ==================== */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: var(--primary-white);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--primary-white);
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: var(--gradient-primary);
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, var(--success), #059669);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, var(--warning), #d97706);
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, var(--info), #2563eb);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: 14px;
  color: var(--gray-600);
  margin-bottom: var(--space-3);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: var(--success);
}

.stat-change.urgent {
  color: var(--error);
}

.stat-change i {
  font-size: 10px;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 1024px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .header-center {
    max-width: 300px;
    margin: 0 var(--space-4);
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--space-4);
  }
  
  .logo-text h1 {
    display: none;
  }
  
  .logo-text span {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .header-center {
    display: none;
  }
  
  .page-content {
    padding: var(--space-4);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* ==================== 工具类 ==================== */

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--primary-white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn:hover {
  background: var(--gray-50);
}

.btn-primary {
  background: var(--gradient-primary);
  border-color: var(--primary-black);
  color: var(--primary-white);
}

.btn-primary:hover {
  background: var(--gray-800);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: 12px;
}

/* ==================== 侧边栏折叠状态 ==================== */

.admin-layout.sidebar-collapsed .admin-sidebar {
  width: var(--sidebar-collapsed-width);
}

.admin-layout.sidebar-collapsed .admin-main {
  margin-left: var(--sidebar-collapsed-width);
}

.admin-layout.sidebar-collapsed .logo-text,
.admin-layout.sidebar-collapsed .nav-section-title span,
.admin-layout.sidebar-collapsed .nav-link span,
.admin-layout.sidebar-collapsed .nav-badge {
  display: none;
}

.admin-layout.sidebar-collapsed .nav-link {
  justify-content: center;
  padding: var(--space-3);
}

.admin-layout.sidebar-collapsed .nav-section-title {
  justify-content: center;
  padding: 0 var(--space-3);
} 
/**
 * 从配置文件批量更新菜单
 * 使用方法：
 * 1. 修改 menu-config.json 文件
 * 2. 运行 node update-menu-from-config.js
 */

const fs = require('fs');
const path = require('path');

// 📋 从配置文件加载菜单配置
function loadMenuConfig() {
    try {
        const configPath = path.join(__dirname, 'menu-config.json');
        const configContent = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configContent);
    } catch (error) {
        console.error('❌ 无法加载菜单配置文件:', error.message);
        console.log('💡 请确保 menu-config.json 文件存在且格式正确');
        process.exit(1);
    }
}

// 🔗 计算相对路径
function getRelativePath(currentFile, targetFile) {
    // 定义文件到目录的映射
    const fileToDir = {
        // 个人中心
        'my-todos.html': '../01-personal/',
        'my-orders.html': '../01-personal/',

        // 业务管理
        'design-products.html': '../02-business/',
        'requirements-management.html': '../02-business/',
        'design-center.html': '../02-business/',
        'design-cases.html': '../02-business/',
        'project-center.html': '../02-business/',
        'construction-management.html': '../02-business/',
        'construction-guide.html': '../02-business/',

        // 商务中心
        'products.html': '../03-commerce/',
        'orders.html': '../03-commerce/',
        'customer-management.html': '../03-commerce/',
        'marketing-management.html': '../03-commerce/',

        // 知识库
        'knowledge-management.html': '../04-knowledge/',
        'design-knowledge.html': '../04-knowledge/',
        'delivery-knowledge.html': '../04-knowledge/',
        'wiring-knowledge.html': '../04-knowledge/',
        'installation-knowledge.html': '../04-knowledge/',
        'debugging-knowledge.html': '../04-knowledge/',
        'product-knowledge.html': '../04-knowledge/',

        // 系统工具
        'admin-dashboard.html': '../05-tools/',
        'api-tools.html': '../05-tools/',
        'erp-documentation.html': '../05-tools/',
        'system-settings.html': '../05-tools/',
        'user-management.html': '../05-tools/',
        'internal-permissions.html': '../05-tools/',
        'customer-permissions.html': '../05-tools/',
        'data-management.html': '../05-tools/',

        // 数据分析
        'requirements-analytics.html': '../06-analytics/',
        'project-analytics.html': '../06-analytics/',
        'order-analytics.html': '../06-analytics/',
        'customer-analytics.html': '../06-analytics/',

        // 个人资料
        'demo.html': '../07-profile/',
        'user-profile.html': '../07-profile/',
        'logout.html': '../07-profile/'
    };

    // 如果目标文件有特定目录，返回完整路径
    if (fileToDir[targetFile]) {
        return fileToDir[targetFile] + targetFile;
    }

    // 否则返回相对路径
    return targetFile;
}

// 🎯 生成菜单HTML
function generateMenuHTML(menuConfig, activePageFile = '') {
    const { logo, sections } = menuConfig;
    
    let menuHTML = `        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="${logo.icon}"></i>
                    </div>
                    <div>
                        <div class="logo-text">${logo.title}</div>
                        <div style="font-size: 10px; color: #6b7280;">${logo.subtitle}</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">`;

    // 生成菜单分组
    sections.forEach(section => {
        menuHTML += `
                <div class="nav-section">
                    <div class="nav-section-title">${section.title}</div>`;
        
        // 生成菜单项
        section.items.forEach(item => {
            const isActive = activePageFile === item.href ? ' active' : '';
            // 移除图标显示，只保留文字
            const relativePath = getRelativePath(activePageFile, item.href);

            menuHTML += `
                    <a href="${relativePath}" class="nav-item${isActive}">${item.text}</a>`;
        });
        
        menuHTML += `
                </div>`;
    });

    menuHTML += `
            </nav>
        </aside>`;

    return menuHTML;
}

// 📁 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 🔄 更新单个文件的菜单
function updateMenuInFile(filePath, menuConfig) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 查找侧边栏的开始和结束位置
        const sidebarStartRegex = /<aside[^>]*class="sidebar"[^>]*>/;
        const sidebarEndRegex = /<\/aside>/;
        
        const startMatch = content.match(sidebarStartRegex);
        if (!startMatch) {
            console.log(`⏭️  跳过: ${fileName} (未找到侧边栏)`);
            return false;
        }
        
        const startIndex = startMatch.index;
        const afterStart = content.substring(startIndex);
        const endMatch = afterStart.match(sidebarEndRegex);
        
        if (!endMatch) {
            console.log(`⚠️  警告: ${fileName} (侧边栏结构不完整)`);
            return false;
        }
        
        const endIndex = startIndex + endMatch.index + endMatch[0].length;
        
        // 分割内容
        const beforeSidebar = content.substring(0, startIndex);
        const afterSidebar = content.substring(endIndex);
        
        // 生成新的菜单HTML（带有正确的激活状态）
        const newMenuHTML = generateMenuHTML(menuConfig, fileName);
        
        // 重新组合内容
        const newContent = beforeSidebar + newMenuHTML + afterSidebar;
        
        // 保存文件
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ 已更新: ${fileName}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 📊 显示菜单配置预览
function showMenuPreview(menuConfig) {
    console.log('📋 菜单配置预览:');
    console.log(`🏠 Logo: ${menuConfig.logo.title} - ${menuConfig.logo.subtitle}`);
    console.log(`📂 菜单分组数量: ${menuConfig.sections.length}`);
    
    menuConfig.sections.forEach((section, index) => {
        console.log(`   ${index + 1}. ${section.title} (${section.items.length} 个菜单项)`);
        section.items.forEach(item => {
            console.log(`      - ${item.text} → ${item.href}`);
        });
    });
    console.log('');
}

// 🚀 主函数
function main() {
    console.log('🚀 开始从配置文件批量更新菜单...\n');
    
    // 加载菜单配置
    const menuConfig = loadMenuConfig();
    console.log('✅ 菜单配置加载成功\n');
    
    // 显示配置预览
    showMenuPreview(menuConfig);
    
    // 确认是否继续
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('🤔 确认要应用此菜单配置到所有页面吗？(y/N): ', (answer) => {
        rl.close();
        
        if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
            console.log('❌ 操作已取消');
            return;
        }
        
        // 开始更新
        const pagesDir = path.join(__dirname, '../src/pc/components/pages');
        
        if (!fs.existsSync(pagesDir)) {
            console.error('❌ 页面目录不存在:', pagesDir);
            return;
        }
        
        const htmlFiles = getAllHtmlFiles(pagesDir);
        console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
        
        let successCount = 0;
        let failCount = 0;
        
        for (const file of htmlFiles) {
            if (updateMenuInFile(file, menuConfig)) {
                successCount++;
            } else {
                failCount++;
            }
        }
        
        console.log('\n📊 更新统计:');
        console.log(`✅ 成功更新: ${successCount} 个文件`);
        console.log(`❌ 更新失败: ${failCount} 个文件`);
        console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
        
        if (successCount > 0) {
            console.log('\n🎉 菜单批量更新完成！');
            console.log('💡 所有页面现在都使用新的菜单配置');
            console.log('🎯 菜单激活状态已根据页面自动设置');
            console.log('\n📝 下次更新菜单时，只需：');
            console.log('   1. 修改 menu-config.json 文件');
            console.log('   2. 运行 node update-menu-from-config.js');
        }
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    loadMenuConfig,
    generateMenuHTML,
    updateMenuInFile,
    main
};

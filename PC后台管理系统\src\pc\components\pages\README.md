# 归档文件说明文档

## 📁 目录用途

本目录 `archived-duplicates` 存放了从 `src/pages` 中移动出来的重复文件、旧版本文件和非核心业务文件。这些文件被保留作为备份和参考，但不会在日常开发中使用。

## 📋 归档文件分类

### 🔄 重复版本文件

#### 管理仪表板类
- `admin-dashboard-v2.html` (16KB) - 管理仪表板第二版
  - **归档原因**: 功能被 `admin-dashboard-optimized.html` 和 `admin-dashboard-complete.html` 覆盖
  - **保留原因**: 可能包含特定的UI设计思路

- `dashboard.html` (27KB) - 基础仪表板
  - **归档原因**: 功能已被更优化的admin-dashboard系列替代
  - **保留原因**: 作为基础版本的参考

#### 需求管理类
- `requirements-management-fixed.html` (13KB) - 需求管理修复版
  - **归档原因**: 功能已被 `requirements-management-optimized.html` 完全覆盖
  - **保留原因**: 记录了特定的bug修复过程

- `create-requirement-optimized.html` (10KB) - 创建需求优化版（精简）
  - **归档原因**: 功能被更完整的 `optimized-create-requirement.html` 替代
  - **保留原因**: 精简版的UI设计参考

- `optimized-requirements.html` (5.7KB) - 简化需求管理
  - **归档原因**: 功能过于简化，不适合实际使用
  - **保留原因**: 最小化实现的参考

### 🧭 导航和页面结构类
- `nav.html` (9.0KB) - 独立导航页面
  - **归档原因**: 导航功能已集成到各主要页面中
  - **保留原因**: 独立导航组件的设计参考

- `navigation-test.html` (6.2KB) - 导航测试页面
  - **归档原因**: 测试用途，非生产环境需要
  - **保留原因**: 导航功能的测试基准

### 🛠️ 开发测试工具类
- `api-integration-test.html` (39KB) - 在线API集成测试
- `api-integration-test-offline.html` (27KB) - 离线API集成测试
  - **归档原因**: 保留主要的 `api-tester.html` 即可满足开发需要
  - **保留原因**: 特定测试场景的工具备份

- `clear-cache.html` (12KB) - 缓存清理工具
  - **归档原因**: 非核心业务功能，使用频率低
  - **保留原因**: 开发调试时可能需要

### 🎨 设计和前端页面类
- `design.html` (30KB) - 设计页面
  - **归档原因**: 属于前端用户界面，不属于后台管理系统核心功能
  - **保留原因**: 可能包含有用的设计组件和样式

## 📊 归档统计

- **归档文件总数**: 11个
- **归档文件总大小**: 约204KB
- **归档日期**: 2024年12月
- **归档原因分布**:
  - 重复版本: 5个文件
  - 测试工具: 4个文件
  - 导航组件: 2个文件

## 🎯 核心文件保留策略

### 保留在 `src/pages` 的核心文件
1. **管理仪表板**: `admin-dashboard-complete.html` (最完整) + `admin-dashboard-optimized.html` (最优化)
2. **用户管理**: `user-management-optimized.html` (最优化) + `users.html` (基础完整版)
3. **需求管理**: `requirements-management-optimized.html` (最优化) + `optimized-create-requirement.html` (创建功能)
4. **产品管理**: `product-management-optimized.html` (优化版) + `products.html` (完整版)
5. **业务管理**: `orders.html` (订单) + `projects.html` (项目)
6. **系统功能**: `login.html` (登录) + `index.html` (首页)
7. **开发工具**: `api-tester.html` (主要API测试工具)

## 🔍 使用建议

### 何时使用归档文件
- 需要参考特定版本的UI设计时
- 研究功能演进历史时
- 需要恢复某个特定功能实现时
- 开发新功能需要借鉴旧版本思路时

### 归档文件管理
- **不建议直接修改归档文件**
- **如需恢复功能，建议复制到 `src/pages` 并重命名**
- **定期review归档文件，清理真正不需要的内容**
- **重要功能变更前，建议先备份到此目录**

## ⚠️ 注意事项

1. **归档文件可能存在过时的依赖和样式引用**
2. **归档文件未经过最新的兼容性测试**
3. **归档文件的功能可能与当前系统不匹配**
4. **使用归档文件前请仔细检查代码质量和安全性**

## 📝 维护日志

- **2024年12月**: 初始归档，移入11个重复和非核心文件
- **下次清理建议**: 2025年3月，评估归档文件的实际价值

---

*本归档目录遵循"保留但不使用"的原则，确保重要的开发历史得以保存，同时保持主要工作目录的整洁性。* 
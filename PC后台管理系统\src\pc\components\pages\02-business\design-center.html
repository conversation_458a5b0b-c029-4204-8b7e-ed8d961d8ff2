<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计中心 - 智能家居管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 顶部导航样式 */
        .top-nav {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }

        /* 面包屑导航样式 */
        .breadcrumb-section {
            flex: 1;
        }

        .page-title-section {
            margin-top: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
            max-width: 600px;
        }

        .nav-breadcrumb {
            flex: 1;
        }

        .breadcrumb-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .breadcrumb-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        .nav-actions {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        /* 流程导航样式 */
        .process-nav {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 20px;
            margin-bottom: 24px;
        }

        .process-steps {
            display: flex;
            gap: 0;
            position: relative;
        }

        .process-step {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .process-step:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 40px;
            background: #e5e7eb;
        }

        .process-step.active {
            background: #f8fafc;
            border-radius: 6px;
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            transition: all 0.2s;
        }

        .process-step.active .step-number {
            background: #1f2937;
            color: #ffffff;
        }

        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            text-align: center;
            transition: all 0.2s;
        }

        .process-step.active .step-title {
            color: #1f2937;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .top-nav {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .page-title {
                font-size: 20px;
            }

            .page-description {
                font-size: 13px;
            }

            .nav-actions {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }

            .nav-actions .btn {
                width: 100%;
                justify-content: center;
            }

            .process-steps {
                flex-direction: column;
                gap: 8px;
            }

            .process-step:not(:last-child)::after {
                display: none;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 页面内容样式 */
        .page-content {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            min-height: 600px;
        }

        .tab-content {
            display: none;
            padding: 24px;
        }

        .tab-content.active {
            display: block;
        }

        /* 页面头部样式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .header-left h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
        }

        .header-left p {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        .header-right {
            display: flex;
            gap: 12px;
        }

        /* 搜索筛选栏样式 */
        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .search-box {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-box input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            transition: border-color 0.2s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .filter-group {
            display: flex;
            gap: 12px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            color: #1f2937;
            cursor: pointer;
            transition: border-color 0.2s;
        }

        .filter-select:focus {
            outline: none;
            border-color: #1f2937;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 文件上传区域样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f9fafb;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 24px;
        }

        .upload-area:hover {
            border-color: #1f2937;
            background: #f3f4f6;
        }

        .upload-icon {
            font-size: 48px;
            color: #6b7280;
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: #6b7280;
        }

        /* 设计方案分类展示样式 */
        .design-categories {
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        .category-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .category-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .category-count {
            font-size: 14px;
            color: #6b7280;
            background: #ffffff;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }

        .file-item {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            transition: all 0.2s;
        }

        .file-item:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .file-preview {
            position: relative;
            width: 100%;
            height: 150px;
            overflow: hidden;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .file-icon {
            font-size: 48px;
            color: #6b7280;
        }

        .video-preview .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 48px;
            height: 48px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 16px;
        }

        .file-info {
            padding: 16px;
        }

        .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin: 0 0 4px 0;
            line-height: 1.4;
        }

        .file-meta {
            font-size: 12px;
            color: #6b7280;
            margin: 0 0 12px 0;
        }

        .file-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* 上传进度样式 */
        .upload-progress {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 300px;
        }

        .progress-text {
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .progress-line {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #1f2937;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* 预览模态框样式 */
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-content {
            background: #ffffff;
            border-radius: 8px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .close-btn:hover {
            background: #e5e7eb;
            color: #1f2937;
        }

        .modal-body {
            flex: 1;
            padding: 20px;
            overflow: auto;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 20px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        /* 编辑器样式 */
        .editor-toolbar {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            padding: 12px;
            background: #f9fafb;
            border-radius: 6px;
            flex-wrap: wrap;
        }

        .editor-canvas {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        #imageCanvas {
            max-width: 100%;
            height: auto;
        }

        .cad-viewer, .pdf-viewer {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
        }

        /* 危险按钮样式 */
        .btn-danger {
            background: #dc2626;
            color: #ffffff;
            border-color: #dc2626;
        }

        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-content {
                max-width: 95vw;
                max-height: 95vh;
            }

            .file-actions {
                justify-content: center;
            }

            .editor-toolbar {
                justify-content: center;
            }

            .upload-progress {
                right: 10px;
                left: 10px;
                min-width: auto;
            }
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                        <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item active">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 第一层：面包屑导航 -->
            <div class="top-nav">
                <div class="breadcrumb-section">
                    <!-- 页面标题和描述 -->
                    <div class="page-title-section">
                        <h1 class="page-title">设计中心</h1>
                        <p class="page-description">
                            管理设计任务、方案、文档和预算的一站式平台，提供从需求分析到方案交付的完整设计流程管理
                        </p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="nav-actions">
                    <button class="btn btn-secondary" onclick="exportData()" title="导出设计数据">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                    <button class="btn btn-primary" onclick="createNewProject()" title="创建新的设计项目">
                        <i class="fas fa-plus"></i>
                        新建项目
                    </button>
                </div>
            </div>

            <!-- 流程步骤导航 -->
            <div class="process-nav">
                <div class="process-steps">
                    <div class="process-step active" onclick="showTab('tab1')">
                        <div class="step-title">设计任务</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab2')">
                        <div class="step-title">设计方案</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab3')">
                        <div class="step-title">设计文档</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab4')">
                        <div class="step-title">设计预算</div>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 标签页1：设计任务 -->
                <div id="tab1" class="tab-content active">
                    <!-- 页面头部：标题和新建按钮 -->
                    <div class="section-header">
                        <div class="header-left">
                            <h3>设计任务</h3>
                            <p>管理设计任务的创建、分配和进度跟踪</p>
                        </div>
                        <div class="header-right">
                            <button class="btn btn-secondary" onclick="exportTasks()">导出任务</button>
                            <button class="btn btn-primary" onclick="createNewTask()">新建任务</button>
                        </div>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <input type="text" placeholder="搜索任务名称、客户姓名..." id="taskSearch">
                        </div>
                        <div class="filter-group">
                            <select class="filter-select" id="taskStatusFilter">
                                <option value="">全部状态</option>
                                <option value="new">新任务</option>
                                <option value="in-progress">进行中</option>
                                <option value="completed">已完成</option>
                            </select>
                            <select class="filter-select" id="taskTypeFilter">
                                <option value="">全部类型</option>
                                <option value="floor-plan">平面方案</option>
                                <option value="rendering">效果图</option>
                                <option value="smart-home">智能家居设计</option>
                                <option value="lighting">灯光设计</option>
                            </select>
                        </div>
                    </div>

                    <!-- 任务列表表格 -->
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>任务名称</th>
                                    <th>客户信息</th>
                                    <th>设计类型</th>
                                    <th>分配设计师</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasksTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 标签页2：设计方案 -->
                <div id="tab2" class="tab-content">
                    <!-- 页面头部：标题和上传按钮 -->
                    <div class="page-header">
                        <div class="header-left">
                            <h2>设计方案</h2>
                            <p>管理和展示各类设计方案文件</p>
                        </div>
                        <div class="header-right">
                            <button class="btn btn-secondary" onclick="previewAll()">预览全部</button>
                            <button class="btn btn-primary" onclick="uploadDesign()">上传方案</button>
                        </div>
                    </div>

                    <!-- 方案类型筛选 -->
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <input type="text" placeholder="搜索方案名称..." id="designSearch">
                        </div>
                        <div class="filter-group">
                            <select class="filter-select" id="designTypeFilter">
                                <option value="">全部类型</option>
                                <option value="floor-plan">平面方案</option>
                                <option value="rendering">效果图</option>
                                <option value="smart-home">智能家居设计</option>
                                <option value="lighting">灯光设计</option>
                            </select>
                        </div>
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="upload-area" onclick="triggerFileUpload()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">点击上传或拖拽文件到此处</div>
                        <div class="upload-hint">支持 JPG、PNG、PDF、DWG 格式，单个文件不超过 50MB</div>
                        <input type="file" id="fileInput" style="display: none;" multiple accept=".jpg,.jpeg,.png,.pdf,.dwg">
                    </div>

                    <!-- 设计方案分类展示 -->
                    <div class="design-categories">
                        <!-- 平面方案 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h3 class="category-title">平面方案</h3>
                                <span class="category-count">3 个文件</span>
                            </div>
                            <div class="file-grid">
                                <div class="file-item">
                                    <div class="file-preview">
                                        <div class="file-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                    </div>
                                    <div class="file-info">
                                        <h4 class="file-name">客厅平面布置图.dwg</h4>
                                        <p class="file-meta">2.5MB • 2024-01-15</p>
                                        <div class="file-actions">
                                            <button class="btn btn-sm btn-secondary" onclick="previewFile('客厅平面布置图.dwg')">预览</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('客厅平面布置图.dwg')">下载</button>
                                            <button class="btn btn-sm btn-secondary" onclick="editFile('客厅平面布置图.dwg')">编辑</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteFile('客厅平面布置图.dwg')">删除</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="file-item">
                                    <div class="file-preview">
                                        <div class="file-icon">
                                            <i class="fas fa-file-pdf"></i>
                                        </div>
                                    </div>
                                    <div class="file-info">
                                        <h4 class="file-name">户型家具布置图.pdf</h4>
                                        <p class="file-meta">1.8MB • 2024-01-14</p>
                                        <div class="file-actions">
                                            <button class="btn btn-sm btn-secondary" onclick="previewFile('户型家具布置图.pdf')">预览</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('户型家具布置图.pdf')">下载</button>
                                            <button class="btn btn-sm btn-secondary" onclick="editFile('户型家具布置图.pdf')">编辑</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteFile('户型家具布置图.pdf')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 3D效果图 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h3 class="category-title">3D效果图</h3>
                                <span class="category-count">5 个文件</span>
                            </div>
                            <div class="file-grid">
                                <div class="file-item">
                                    <div class="file-preview">
                                        <div class="file-icon">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    </div>
                                    <div class="file-info">
                                        <h4 class="file-name">客厅3D效果图.jpg</h4>
                                        <p class="file-meta">4.2MB • 2024-01-13</p>
                                        <div class="file-actions">
                                            <button class="btn btn-sm btn-secondary" onclick="previewFile('客厅3D效果图.jpg')">预览</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('客厅3D效果图.jpg')">下载</button>
                                            <button class="btn btn-sm btn-secondary" onclick="editFile('客厅3D效果图.jpg')">编辑</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteFile('客厅3D效果图.jpg')">删除</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="file-item">
                                    <div class="file-preview">
                                        <div class="file-icon">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    </div>
                                    <div class="file-info">
                                        <h4 class="file-name">主卧室渲染图.png</h4>
                                        <p class="file-meta">3.7MB • 2024-01-12</p>
                                        <div class="file-actions">
                                            <button class="btn btn-sm btn-secondary" onclick="previewFile('主卧室渲染图.png')">预览</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('主卧室渲染图.png')">下载</button>
                                            <button class="btn btn-sm btn-secondary" onclick="editFile('主卧室渲染图.png')">编辑</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteFile('主卧室渲染图.png')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 视频展示 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h3 class="category-title">视频展示</h3>
                                <span class="category-count">2 个文件</span>
                            </div>
                            <div class="file-grid">
                                <div class="file-item">
                                    <div class="file-preview video-preview">
                                        <div class="file-icon">
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="play-button"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="file-info">
                                        <h4 class="file-name">全屋漫游视频.mp4</h4>
                                        <p class="file-meta">25.6MB • 2024-01-11</p>
                                        <div class="file-actions">
                                            <button class="btn btn-sm btn-secondary" onclick="previewFile('全屋漫游视频.mp4')">播放</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('全屋漫游视频.mp4')">下载</button>
                                            <button class="btn btn-sm btn-secondary" onclick="shareVideo('全屋漫游视频.mp4')">分享</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteFile('全屋漫游视频.mp4')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页3：设计文档 -->
                <div id="tab3" class="tab-content">
                    <!-- 页面头部：标题和操作按钮 -->
                    <div class="page-header">
                        <div class="header-left">
                            <h2>设计文档</h2>
                            <p>管理用户提交的图纸、需求文档和设计方案历史版本</p>
                        </div>
                        <div class="header-right">
                            <button class="btn btn-primary" onclick="uploadDocument()">
                                <i class="fas fa-file-upload"></i> 上传文档
                            </button>
                        </div>
                    </div>

                    <!-- 文档分类筛选 -->
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索文档名称..." id="documentSearch">
                        </div>
                        <div class="filter-group">
                            <select class="filter-select" id="documentTypeFilter">
                                <option value="">全部类型</option>
                                <option value="user-requirements">用户需求</option>
                                <option value="technical-drawings">技术图纸</option>
                                <option value="material-list">材料清单</option>
                                <option value="version-history">历史版本</option>
                            </select>
                            <select class="filter-select" id="documentStatusFilter">
                                <option value="">全部状态</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已审核</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                    </div>

                    <!-- 文档列表表格 -->
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>文档名称</th>
                                    <th>文档类型</th>
                                    <th>关联项目</th>
                                    <th>上传者</th>
                                    <th>上传时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="documentsTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 标签页4：设计预算 -->
                <div id="tab4" class="tab-content">
                    <!-- 页面头部：标题和操作按钮 -->
                    <div class="page-header">
                        <div class="header-left">
                            <h2>设计预算</h2>
                            <p>管理设备配置清单和项目预算方案</p>
                        </div>
                        <div class="header-right">
                            <button class="btn btn-primary" onclick="createBudget()">
                                <i class="fas fa-calculator"></i> 新建预算
                            </button>
                        </div>
                    </div>

                    <!-- 预算筛选 -->
                    <div class="search-filter-bar">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索项目名称..." id="budgetSearch">
                        </div>
                        <div class="filter-group">
                            <select class="filter-select" id="budgetStatusFilter">
                                <option value="">全部状态</option>
                                <option value="draft">草稿</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已审核</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                            <select class="filter-select" id="budgetRangeFilter">
                                <option value="">全部预算</option>
                                <option value="0-50000">5万以下</option>
                                <option value="50000-100000">5-10万</option>
                                <option value="100000-200000">10-20万</option>
                                <option value="200000+">20万以上</option>
                            </select>
                        </div>
                    </div>

                    <!-- 预算列表表格 -->
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>预算编号</th>
                                    <th>项目名称</th>
                                    <th>客户信息</th>
                                    <th>设备配置</th>
                                    <th>预算金额</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="budgetsTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 设计中心页面逻辑
        class DesignCenterManager {
            constructor() {
                this.currentTab = 'tab1';
                this.tasks = [];
                this.designs = [];
                this.documents = [];
                this.budgets = [];
                this.init();
            }

            init() {
                this.loadMockData();
                this.bindEvents();
                this.renderTasks();
            }

            bindEvents() {
                // 搜索功能
                document.getElementById('taskSearch')?.addEventListener('input', () => this.filterTasks());
                document.getElementById('designSearch')?.addEventListener('input', () => this.filterDesigns());
                document.getElementById('documentSearch')?.addEventListener('input', () => this.filterDocuments());
                document.getElementById('budgetSearch')?.addEventListener('input', () => this.filterBudgets());

                // 筛选功能
                document.getElementById('taskStatusFilter')?.addEventListener('change', () => this.filterTasks());
                document.getElementById('taskTypeFilter')?.addEventListener('change', () => this.filterTasks());
                document.getElementById('designTypeFilter')?.addEventListener('change', () => this.filterDesigns());
                document.getElementById('documentTypeFilter')?.addEventListener('change', () => this.filterDocuments());
                document.getElementById('budgetStatusFilter')?.addEventListener('change', () => this.filterBudgets());

                // 文件上传
                document.getElementById('fileInput')?.addEventListener('change', (e) => this.handleFileUpload(e));
            }

            loadMockData() {
                // 模拟任务数据
                this.tasks = [
                    {
                        id: 'TASK001',
                        name: '张先生三室两厅智能家居设计',
                        customer: '张先生 (138****8001)',
                        type: 'smart-home',
                        designer: '李设计师',
                        status: 'in-progress',
                        createTime: '2024-01-15',
                        progress: 60
                    },
                    {
                        id: 'TASK002',
                        name: '王女士现代简约风格效果图',
                        customer: '王女士 (139****9002)',
                        type: 'rendering',
                        designer: '陈设计师',
                        status: 'new',
                        createTime: '2024-01-16',
                        progress: 0
                    },
                    {
                        id: 'TASK003',
                        name: '刘先生别墅灯光设计方案',
                        customer: '刘先生 (137****7003)',
                        type: 'lighting',
                        designer: '赵设计师',
                        status: 'completed',
                        createTime: '2024-01-10',
                        progress: 100
                    }
                ];

                // 模拟设计方案数据
                this.designs = [
                    {
                        id: 'DESIGN001',
                        name: '三室两厅平面布局图.dwg',
                        type: 'floor-plan',
                        size: '2.5MB',
                        uploadTime: '2024-01-15 14:30',
                        project: 'TASK001'
                    },
                    {
                        id: 'DESIGN002',
                        name: '客厅效果图渲染.jpg',
                        type: 'rendering',
                        size: '8.2MB',
                        uploadTime: '2024-01-16 09:15',
                        project: 'TASK002'
                    }
                ];

                // 模拟文档数据
                this.documents = [
                    {
                        id: 'DOC001',
                        name: '用户需求说明书.pdf',
                        type: 'user-requirements',
                        project: 'TASK001',
                        uploader: '张先生',
                        uploadTime: '2024-01-15 10:00',
                        status: 'approved'
                    },
                    {
                        id: 'DOC002',
                        name: '智能设备配置清单.xlsx',
                        type: 'material-list',
                        project: 'TASK001',
                        uploader: '李设计师',
                        uploadTime: '2024-01-15 16:20',
                        status: 'pending'
                    }
                ];

                // 模拟预算数据
                this.budgets = [
                    {
                        id: 'BUDGET001',
                        projectName: '张先生智能家居项目',
                        customer: '张先生 (138****8001)',
                        equipment: '智能开关、传感器、控制面板',
                        amount: 85000,
                        status: 'approved',
                        createTime: '2024-01-15'
                    },
                    {
                        id: 'BUDGET002',
                        projectName: '王女士现代简约项目',
                        customer: '王女士 (139****9002)',
                        equipment: '智能照明、窗帘控制',
                        amount: 45000,
                        status: 'draft',
                        createTime: '2024-01-16'
                    }
                ];
            }

            renderTasks() {
                const tbody = document.getElementById('tasksTableBody');
                if (!tbody) return;

                tbody.innerHTML = this.tasks.map(task => `
                    <tr>
                        <td>${task.id}</td>
                        <td>${task.name}</td>
                        <td>${task.customer}</td>
                        <td>${this.getTypeLabel(task.type)}</td>
                        <td>${task.designer}</td>
                        <td><span class="status-badge status-${task.status}">${this.getStatusLabel(task.status)}</span></td>
                        <td>${task.createTime}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="designCenter.viewTask('${task.id}')">查看</button>
                            <button class="btn btn-secondary btn-sm" onclick="designCenter.editTask('${task.id}')">编辑</button>
                        </td>
                    </tr>
                `).join('');
            }

            renderDesigns() {
                const container = document.getElementById('designFileList');
                if (!container) return;

                container.innerHTML = this.designs.map(design => `
                    <div class="file-item">
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas ${this.getFileIcon(design.type)}"></i>
                            </div>
                            <div class="file-details">
                                <h4>${design.name}</h4>
                                <p>${design.size} • ${design.uploadTime}</p>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-primary btn-sm" onclick="designCenter.previewFile('${design.id}')">预览</button>
                            <button class="btn btn-secondary btn-sm" onclick="designCenter.downloadFile('${design.id}')">下载</button>
                        </div>
                    </div>
                `).join('');
            }

            renderDocuments() {
                const tbody = document.getElementById('documentsTableBody');
                if (!tbody) return;

                tbody.innerHTML = this.documents.map(doc => `
                    <tr>
                        <td>${doc.name}</td>
                        <td>${this.getDocTypeLabel(doc.type)}</td>
                        <td>${doc.project}</td>
                        <td>${doc.uploader}</td>
                        <td>${doc.uploadTime}</td>
                        <td><span class="status-badge status-${doc.status}">${this.getStatusLabel(doc.status)}</span></td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="designCenter.viewDocument('${doc.id}')">查看</button>
                            <button class="btn btn-secondary btn-sm" onclick="designCenter.downloadDocument('${doc.id}')">下载</button>
                        </td>
                    </tr>
                `).join('');
            }

            renderBudgets() {
                const tbody = document.getElementById('budgetsTableBody');
                if (!tbody) return;

                tbody.innerHTML = this.budgets.map(budget => `
                    <tr>
                        <td>${budget.id}</td>
                        <td>${budget.projectName}</td>
                        <td>${budget.customer}</td>
                        <td>${budget.equipment}</td>
                        <td>¥${budget.amount.toLocaleString()}</td>
                        <td><span class="status-badge status-${budget.status}">${this.getStatusLabel(budget.status)}</span></td>
                        <td>${budget.createTime}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="designCenter.viewBudget('${budget.id}')">查看</button>
                            <button class="btn btn-secondary btn-sm" onclick="designCenter.editBudget('${budget.id}')">编辑</button>
                        </td>
                    </tr>
                `).join('');
            }

            getTypeLabel(type) {
                const labels = {
                    'floor-plan': '平面方案',
                    'rendering': '效果图',
                    'smart-home': '智能家居设计',
                    'lighting': '灯光设计'
                };
                return labels[type] || type;
            }

            getStatusLabel(status) {
                const labels = {
                    'new': '新任务',
                    'in-progress': '进行中',
                    'completed': '已完成',
                    'pending': '待审核',
                    'approved': '已审核',
                    'rejected': '已拒绝',
                    'draft': '草稿'
                };
                return labels[status] || status;
            }

            getDocTypeLabel(type) {
                const labels = {
                    'user-requirements': '用户需求',
                    'technical-drawings': '技术图纸',
                    'material-list': '材料清单',
                    'version-history': '历史版本'
                };
                return labels[type] || type;
            }

            getFileIcon(type) {
                const icons = {
                    'floor-plan': 'fa-drafting-compass',
                    'rendering': 'fa-image',
                    'smart-home': 'fa-home',
                    'lighting': 'fa-lightbulb'
                };
                return icons[type] || 'fa-file';
            }

            // 筛选功能
            filterTasks() {
                // 实现任务筛选逻辑
                this.renderTasks();
            }

            filterDesigns() {
                // 实现设计方案筛选逻辑
                this.renderDesigns();
            }

            filterDocuments() {
                // 实现文档筛选逻辑
                this.renderDocuments();
            }

            filterBudgets() {
                // 实现预算筛选逻辑
                this.renderBudgets();
            }

            // 文件上传处理
            handleFileUpload(event) {
                const files = event.target.files;
                for (let file of files) {
                    console.log('上传文件:', file.name);
                    // 实现文件上传逻辑
                }
            }

            // 操作方法
            viewTask(id) {
                showToast(`查看任务: ${id}`, 'info');
            }

            editTask(id) {
                showToast(`编辑任务: ${id}`, 'info');
            }

            previewFile(id) {
                showToast(`预览文件: ${id}`, 'info');
            }

            downloadFile(id) {
                showToast(`下载文件: ${id}`, 'success');
            }

            viewDocument(id) {
                showToast(`查看文档: ${id}`, 'info');
            }

            downloadDocument(id) {
                showToast(`下载文档: ${id}`, 'success');
            }

            viewBudget(id) {
                showToast(`查看预算: ${id}`, 'info');
            }

            editBudget(id) {
                showToast(`编辑预算: ${id}`, 'info');
            }
        }

        // 标签页切换功能
        function showTab(tabId) {
            console.log('切换到标签页:', tabId);

            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航标签的激活状态
            document.querySelectorAll('.process-step').forEach(step => {
                step.classList.remove('active');
            });

            // 显示选中的标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活对应的导航标签
            const stepIndex = parseInt(tabId.replace('tab', '')) - 1;
            const steps = document.querySelectorAll('.process-step');
            if (steps[stepIndex]) {
                steps[stepIndex].classList.add('active');
            }

            // 根据标签页渲染对应内容
            switch(tabId) {
                case 'tab1':
                    designCenter.renderTasks();
                    break;
                case 'tab2':
                    designCenter.renderDesigns();
                    break;
                case 'tab3':
                    designCenter.renderDocuments();
                    break;
                case 'tab4':
                    designCenter.renderBudgets();
                    break;
            }
        }



        // 全局操作函数
        function createNewProject() {
            // 显示创建新项目的模态框
            showToast('打开创建新项目表单', 'info');
        }

        function exportData() {
            // 导出当前页面的设计数据
            showToast('开始导出设计中心数据...', 'info');

            // 模拟导出过程
            setTimeout(() => {
                showToast('数据导出完成！', 'success');
            }, 2000);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        function createNewTask() {
            showToast('创建新任务功能开发中...', 'info');
        }

        function exportTasks() {
            showToast('导出任务功能开发中...', 'info');
        }

        function uploadDesign() {
            document.getElementById('fileInput').click();
        }

        // 文件预览功能
        function previewFile(filename) {
            const fileItem = document.querySelector(`[data-filename="${filename}"]`);
            if (!fileItem) return;

            const fileExtension = filename.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                // 图片预览
                showImagePreview(filename, fileItem);
            } else if (fileExtension === 'pdf') {
                // PDF预览
                showPdfPreview(filename);
            } else if (['mp4', 'avi', 'mov'].includes(fileExtension)) {
                // 视频预览
                showVideoPreview(filename);
            } else if (fileExtension === 'dwg') {
                // CAD文件预览
                showCadPreview(filename);
            } else {
                showToast('该文件类型不支持预览', 'warning');
            }
        }

        // 图片预览
        function showImagePreview(filename, fileItem) {
            const imgSrc = fileItem.querySelector('img').src;
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content image-preview">
                    <div class="modal-header">
                        <h3>${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <img src="${imgSrc}" alt="${filename}" style="max-width: 100%; max-height: 80vh;">
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="downloadFile('${filename}')">下载</button>
                        <button class="btn btn-primary" onclick="editFile('${filename}')">编辑</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // PDF预览
        function showPdfPreview(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content pdf-preview">
                    <div class="modal-header">
                        <h3>${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <iframe src="/api/files/preview/${filename}" width="100%" height="600px"></iframe>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="downloadFile('${filename}')">下载</button>
                        <button class="btn btn-primary" onclick="editFile('${filename}')">编辑</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 视频预览
        function showVideoPreview(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content video-preview">
                    <div class="modal-header">
                        <h3>${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <video controls width="100%" height="400px">
                            <source src="/api/files/video/${filename}" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="downloadFile('${filename}')">下载</button>
                        <button class="btn btn-primary" onclick="shareVideo('${filename}')">分享</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // CAD文件预览
        function showCadPreview(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content cad-preview">
                    <div class="modal-header">
                        <h3>${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="cad-viewer">
                            <p>CAD文件预览功能</p>
                            <p>文件名: ${filename}</p>
                            <p>需要专业的CAD查看器支持</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="downloadFile('${filename}')">下载</button>
                        <button class="btn btn-primary" onclick="openWithCad('${filename}')">用CAD软件打开</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 创建预览模态框
        function createPreviewModal() {
            const modal = document.createElement('div');
            modal.className = 'preview-modal';
            modal.onclick = function(e) {
                if (e.target === modal) {
                    closePreviewModal();
                }
            };
            return modal;
        }

        // 关闭预览模态框
        function closePreviewModal() {
            const modal = document.querySelector('.preview-modal');
            if (modal) {
                modal.remove();
            }
        }

        function previewAll() {
            showToast('预览全部方案功能开发中...', 'info');
        }

        function uploadDocument() {
            showToast('上传文档功能开发中...', 'info');
        }

        function createBudget() {
            showToast('创建预算功能开发中...', 'info');
        }

        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = event.dataTransfer.files;
            handleFileUpload(files);
        }

        // 真实的文件上传处理
        function handleFileUpload(files) {
            const fileList = Array.from(files);
            const allowedTypes = ['.jpg', '.jpeg', '.png', '.pdf', '.dwg', '.mp4'];
            const maxSize = 50 * 1024 * 1024; // 50MB

            fileList.forEach(file => {
                // 检查文件类型
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExtension)) {
                    showToast(`不支持的文件格式: ${file.name}`, 'warning');
                    return;
                }

                // 检查文件大小
                if (file.size > maxSize) {
                    showToast(`文件过大: ${file.name} (最大50MB)`, 'warning');
                    return;
                }

                // 创建文件预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    addFileToDisplay(file, e.target.result);
                };

                if (file.type.startsWith('image/')) {
                    reader.readAsDataURL(file);
                } else {
                    reader.readAsDataURL(file);
                }

                // 模拟上传到服务器
                uploadFileToServer(file);
            });
        }

        // 添加文件到显示列表
        function addFileToDisplay(file, dataUrl) {
            const fileGrid = document.querySelector('.file-grid');
            if (!fileGrid) return;

            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.setAttribute('data-filename', file.name);

            let previewContent = '';
            if (file.type.startsWith('image/')) {
                previewContent = `<img src="${dataUrl}" alt="${file.name}">`;
            } else if (file.type.startsWith('video/')) {
                previewContent = `
                    <div class="file-icon"><i class="fas fa-video"></i></div>
                    <div class="play-button"><i class="fas fa-play"></i></div>
                `;
            } else {
                const iconClass = file.type.includes('pdf') ? 'fa-file-pdf' : 'fa-file-alt';
                previewContent = `<div class="file-icon"><i class="fas ${iconClass}"></i></div>`;
            }

            fileItem.innerHTML = `
                <div class="file-preview ${file.type.startsWith('video/') ? 'video-preview' : ''}">
                    ${previewContent}
                </div>
                <div class="file-info">
                    <h4 class="file-name">${file.name}</h4>
                    <p class="file-meta">${formatFileSize(file.size)} • ${new Date().toLocaleDateString()}</p>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-secondary" onclick="previewFile('${file.name}')">预览</button>
                        <button class="btn btn-sm btn-secondary" onclick="downloadFile('${file.name}')">下载</button>
                        <button class="btn btn-sm btn-secondary" onclick="editFile('${file.name}')">编辑</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteFile('${file.name}')">删除</button>
                    </div>
                </div>
            `;

            fileGrid.appendChild(fileItem);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 模拟上传到服务器
        function uploadFileToServer(file) {
            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file);
            formData.append('category', getCurrentCategory());
            formData.append('projectId', getCurrentProjectId());

            // 显示上传进度
            showUploadProgress(file.name);

            // 模拟上传请求
            setTimeout(() => {
                hideUploadProgress();
                console.log(`文件 ${file.name} 上传成功`);

                // 这里应该是真实的API调用
                // fetch('/api/design/upload', {
                //     method: 'POST',
                //     body: formData
                // }).then(response => response.json())
                //   .then(data => console.log('上传成功:', data))
                //   .catch(error => console.error('上传失败:', error));
            }, 2000);
        }

        // 获取当前分类
        function getCurrentCategory() {
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab && activeTab.id === 'tab2') {
                return 'design-plan';
            }
            return 'document';
        }

        // 获取当前项目ID
        function getCurrentProjectId() {
            // 这里应该从URL参数或全局变量获取
            return 'project-001';
        }

        // 显示上传进度
        function showUploadProgress(filename) {
            const progressDiv = document.createElement('div');
            progressDiv.className = 'upload-progress';
            progressDiv.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-text">正在上传: ${filename}</div>
                    <div class="progress-line">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(progressDiv);

            // 模拟进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                const fill = progressDiv.querySelector('.progress-fill');
                if (fill) {
                    fill.style.width = progress + '%';
                }
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }

        // 隐藏上传进度
        function hideUploadProgress() {
            const progressDiv = document.querySelector('.upload-progress');
            if (progressDiv) {
                progressDiv.remove();
            }
        }

        // 文件下载功能
        function downloadFile(filename) {
            // 创建下载链接
            const downloadUrl = `/api/files/download/${filename}`;

            // 创建临时链接进行下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 记录下载日志
            console.log(`下载文件: ${filename}`);

            // 可以添加下载统计
            trackFileDownload(filename);
        }

        // 文件编辑功能
        function editFile(filename) {
            const fileExtension = filename.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                // 图片编辑
                openImageEditor(filename);
            } else if (fileExtension === 'pdf') {
                // PDF编辑
                openPdfEditor(filename);
            } else if (fileExtension === 'dwg') {
                // CAD编辑
                openCadEditor(filename);
            } else {
                showToast('该文件类型不支持在线编辑', 'warning');
            }
        }

        // 图片编辑器
        function openImageEditor(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content image-editor">
                    <div class="modal-header">
                        <h3>编辑图片: ${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="editor-toolbar">
                            <button class="btn btn-sm btn-secondary" onclick="cropImage()">裁剪</button>
                            <button class="btn btn-sm btn-secondary" onclick="rotateImage()">旋转</button>
                            <button class="btn btn-sm btn-secondary" onclick="adjustBrightness()">亮度</button>
                            <button class="btn btn-sm btn-secondary" onclick="addText()">添加文字</button>
                        </div>
                        <div class="editor-canvas">
                            <canvas id="imageCanvas" width="800" height="600"></canvas>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closePreviewModal()">取消</button>
                        <button class="btn btn-primary" onclick="saveEditedImage('${filename}')">保存</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // 加载图片到画布
            loadImageToCanvas(filename);
        }

        // PDF编辑器
        function openPdfEditor(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content pdf-editor">
                    <div class="modal-header">
                        <h3>编辑PDF: ${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="editor-toolbar">
                            <button class="btn btn-sm btn-secondary" onclick="addAnnotation()">添加注释</button>
                            <button class="btn btn-sm btn-secondary" onclick="addSignature()">添加签名</button>
                            <button class="btn btn-sm btn-secondary" onclick="highlightText()">高亮文本</button>
                        </div>
                        <div class="pdf-viewer">
                            <iframe src="/api/files/edit/${filename}" width="100%" height="600px"></iframe>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closePreviewModal()">取消</button>
                        <button class="btn btn-primary" onclick="saveEditedPdf('${filename}')">保存</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // CAD编辑器
        function openCadEditor(filename) {
            const modal = createPreviewModal();
            modal.innerHTML = `
                <div class="modal-content cad-editor">
                    <div class="modal-header">
                        <h3>编辑CAD: ${filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="editor-toolbar">
                            <button class="btn btn-sm btn-secondary" onclick="addDimension()">添加尺寸</button>
                            <button class="btn btn-sm btn-secondary" onclick="addLayer()">添加图层</button>
                            <button class="btn btn-sm btn-secondary" onclick="modifyGeometry()">修改几何</button>
                        </div>
                        <div class="cad-viewer">
                            <p>CAD在线编辑器</p>
                            <p>文件: ${filename}</p>
                            <p>这里将集成专业的CAD编辑组件</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closePreviewModal()">取消</button>
                        <button class="btn btn-primary" onclick="saveEditedCad('${filename}')">保存</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 删除文件功能
        function deleteFile(filename) {
            if (confirm(`确定要删除文件 "${filename}" 吗？此操作不可撤销。`)) {
                // 从显示列表中移除
                const fileItem = document.querySelector(`[data-filename="${filename}"]`);
                if (fileItem) {
                    fileItem.remove();
                }

                // 发送删除请求到服务器
                fetch(`/api/files/delete/${filename}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('文件删除成功:', data);
                })
                .catch(error => {
                    console.error('文件删除失败:', error);
                    showToast('删除失败，请重试', 'error');
                });
            }
        }

        // 文件下载统计
        function trackFileDownload(filename) {
            fetch('/api/analytics/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: filename,
                    timestamp: new Date().toISOString(),
                    userId: getCurrentUserId()
                })
            });
        }

        // 获取当前用户ID
        function getCurrentUserId() {
            // 这里应该从session或token获取
            return 'user-001';
        }

        // 保存编辑后的图片
        function saveEditedImage(filename) {
            const canvas = document.getElementById('imageCanvas');
            if (canvas) {
                canvas.toBlob(function(blob) {
                    const formData = new FormData();
                    formData.append('file', blob, filename);

                    fetch('/api/files/update', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('图片保存成功:', data);
                        closePreviewModal();
                        showToast('图片保存成功', 'success');
                    })
                    .catch(error => {
                        console.error('图片保存失败:', error);
                        showToast('保存失败，请重试', 'error');
                    });
                });
            }
        }

        // 加载图片到画布
        function loadImageToCanvas(filename) {
            const canvas = document.getElementById('imageCanvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
            };

            img.src = `/api/files/image/${filename}`;
        }

        // 初始化页面
        let designCenter;
        document.addEventListener('DOMContentLoaded', function() {
            designCenter = new DesignCenterManager();

            // 绑定文件输入事件
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    handleFileUpload(e.target.files);
                });
            }

            console.log('设计中心页面初始化完成');
        });

        // 图片编辑功能
        function cropImage() {
            showToast('裁剪功能开发中...', 'info');
        }

        function rotateImage() {
            const canvas = document.getElementById('imageCanvas');
            const ctx = canvas.getContext('2d');
            if (canvas) {
                // 简单的90度旋转示例
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                // 这里应该实现真实的旋转逻辑
                showToast('图片旋转功能开发中...', 'info');
            }
        }

        function adjustBrightness() {
            showToast('亮度调整功能开发中...', 'info');
        }

        function addText() {
            const text = prompt('请输入要添加的文字:');
            if (text) {
                const canvas = document.getElementById('imageCanvas');
                const ctx = canvas.getContext('2d');
                if (canvas) {
                    ctx.font = '20px Arial';
                    ctx.fillStyle = '#1f2937';
                    ctx.fillText(text, 50, 50);
                }
            }
        }

        // PDF编辑功能
        function addAnnotation() {
            showToast('添加注释功能开发中...', 'info');
        }

        function addSignature() {
            showToast('添加签名功能开发中...', 'info');
        }

        function highlightText() {
            showToast('高亮文本功能开发中...', 'info');
        }

        function saveEditedPdf(filename) {
            showToast(`保存PDF: ${filename}`, 'success');
            closePreviewModal();
        }

        // CAD编辑功能
        function addDimension() {
            showToast('添加尺寸功能开发中...', 'info');
        }

        function addLayer() {
            showToast('添加图层功能开发中...', 'info');
        }

        function modifyGeometry() {
            showToast('修改几何功能开发中...', 'info');
        }

        function saveEditedCad(filename) {
            showToast(`保存CAD: ${filename}`, 'success');
            closePreviewModal();
        }

        function openWithCad(filename) {
            showToast(`用CAD软件打开: ${filename}`, 'info');
        }

        // 视频分享功能
        function shareVideo(filename) {
            const shareUrl = `${window.location.origin}/api/files/share/${filename}`;

            if (navigator.share) {
                navigator.share({
                    title: `分享视频: ${filename}`,
                    url: shareUrl
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareUrl).then(() => {
                    showToast('分享链接已复制到剪贴板', 'success');
                });
            }
        }

        // 批量操作功能
        function selectAllFiles() {
            const checkboxes = document.querySelectorAll('.file-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function deleteSelectedFiles() {
            const selectedFiles = [];
            const checkboxes = document.querySelectorAll('.file-checkbox:checked');

            checkboxes.forEach(checkbox => {
                selectedFiles.push(checkbox.dataset.filename);
            });

            if (selectedFiles.length === 0) {
                showToast('请先选择要删除的文件', 'warning');
                return;
            }

            if (confirm(`确定要删除选中的 ${selectedFiles.length} 个文件吗？`)) {
                selectedFiles.forEach(filename => {
                    deleteFile(filename);
                });
            }
        }

        function downloadSelectedFiles() {
            const selectedFiles = [];
            const checkboxes = document.querySelectorAll('.file-checkbox:checked');

            checkboxes.forEach(checkbox => {
                selectedFiles.push(checkbox.dataset.filename);
            });

            if (selectedFiles.length === 0) {
                showToast('请先选择要下载的文件', 'warning');
                return;
            }

            // 创建压缩包下载
            const downloadUrl = '/api/files/download-batch';
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = downloadUrl;

            selectedFiles.forEach(filename => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'files[]';
                input.value = filename;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
</body>
</html>

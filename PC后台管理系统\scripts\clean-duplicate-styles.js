/**
 * 清理重复样式脚本
 * 移除重复的菜单样式定义，保持统一标准
 */

const fs = require('fs');
const path = require('path');

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 清理单个文件的重复样式
function cleanDuplicateStyles(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        
        // 移除重复的 .nav-item 样式定义（保留第一个）
        const navItemPattern = /\.nav-item\s*\{[^}]*\}/g;
        const matches = content.match(navItemPattern);
        
        if (matches && matches.length > 1) {
            // 保留第一个，移除其他的
            let firstMatch = true;
            content = content.replace(navItemPattern, (match) => {
                if (firstMatch) {
                    firstMatch = false;
                    return match;
                } else {
                    return '';
                }
            });
            
            // 移除重复的 .nav-item:hover 和 .nav-item.active 样式
            const hoverPattern = /\.nav-item:hover\s*\{[^}]*\}/g;
            const activePattern = /\.nav-item\.active\s*\{[^}]*\}/g;
            
            const hoverMatches = content.match(hoverPattern);
            const activeMatches = content.match(activePattern);
            
            if (hoverMatches && hoverMatches.length > 1) {
                let firstHover = true;
                content = content.replace(hoverPattern, (match) => {
                    if (firstHover) {
                        firstHover = false;
                        return match;
                    } else {
                        return '';
                    }
                });
            }
            
            if (activeMatches && activeMatches.length > 1) {
                let firstActive = true;
                content = content.replace(activePattern, (match) => {
                    if (firstActive) {
                        firstActive = false;
                        return match;
                    } else {
                        return '';
                    }
                });
            }
            
            // 移除重复的 .nav-section-title 样式
            const sectionTitlePattern = /\.nav-section-title\s*\{[^}]*\}/g;
            const titleMatches = content.match(sectionTitlePattern);
            
            if (titleMatches && titleMatches.length > 1) {
                let firstTitle = true;
                content = content.replace(sectionTitlePattern, (match) => {
                    if (firstTitle) {
                        firstTitle = false;
                        return match;
                    } else {
                        return '';
                    }
                });
            }
            
            // 清理多余的空行
            content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
            
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已清理: ${fileName} (移除了 ${matches.length - 1} 个重复样式)`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (无重复样式)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🧹 开始清理重复样式...\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const file of htmlFiles) {
        if (cleanDuplicateStyles(file)) {
            successCount++;
        } else {
            skipCount++;
        }
    }
    
    console.log('\n📊 清理统计:');
    console.log(`✅ 已清理: ${successCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 重复样式清理完成！');
        console.log('🧹 移除了重复的CSS定义');
        console.log('📏 保持了统一的样式标准');
        console.log('✨ 代码更加简洁整齐');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    cleanDuplicateStyles,
    main
};

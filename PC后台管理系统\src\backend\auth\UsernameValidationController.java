package com.smarthome.auth.controller;

import com.smarthome.auth.service.UsernameValidationService;
import com.smarthome.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户名验证控制器
 * 提供用户名唯一性检查和格式验证功能
 */
@RestController
@RequestMapping("/api/auth")
@Tag(name = "用户名验证", description = "用户名唯一性检查和格式验证")
@Slf4j
public class UsernameValidationController {

    @Autowired
    private UsernameValidationService usernameValidationService;

    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @param request HTTP请求
     * @return 用户名可用性检查结果
     */
    @PostMapping("/check-username")
    @Operation(
        summary = "检查用户名是否可用",
        description = "检查用户名是否已被注册，同时验证用户名格式是否符合要求"
    )
    public ApiResponse<Map<String, Object>> checkUsernameAvailable(
            @Parameter(description = "用户名", required = true)
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String username = request.get("username");
            
            if (username == null || username.trim().isEmpty()) {
                return ApiResponse.error("用户名不能为空");
            }
            
            username = username.trim();
            
            // 记录检查请求
            String clientIP = getClientIP(httpRequest);
            log.info("检查用户名可用性，用户名: {}, IP: {}", username, clientIP);
            
            // 执行用户名验证
            UsernameValidationService.ValidationResult result = 
                usernameValidationService.validateUsername(username);
            
            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("available", result.isAvailable());
            responseData.put("valid", result.isValid());
            responseData.put("username", username);
            
            if (!result.isValid()) {
                responseData.put("errors", result.getErrors());
                return ApiResponse.success(responseData, "用户名格式不正确");
            }
            
            if (!result.isAvailable()) {
                return ApiResponse.success(responseData, "用户名已被使用");
            }
            
            return ApiResponse.success(responseData, "用户名可用");
            
        } catch (Exception e) {
            log.error("检查用户名可用性失败", e);
            return ApiResponse.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 批量检查用户名可用性
     * 
     * @param request 包含用户名列表的请求
     * @param httpRequest HTTP请求
     * @return 批量检查结果
     */
    @PostMapping("/check-usernames-batch")
    @Operation(
        summary = "批量检查用户名可用性",
        description = "一次性检查多个用户名的可用性，用于用户名推荐等场景"
    )
    public ApiResponse<Map<String, Object>> checkUsernamesBatch(
            @Parameter(description = "用户名列表", required = true)
            @RequestBody Map<String, Object> request,
            HttpServletRequest httpRequest) {
        
        try {
            @SuppressWarnings("unchecked")
            java.util.List<String> usernames = (java.util.List<String>) request.get("usernames");
            
            if (usernames == null || usernames.isEmpty()) {
                return ApiResponse.error("用户名列表不能为空");
            }
            
            if (usernames.size() > 10) {
                return ApiResponse.error("一次最多检查10个用户名");
            }
            
            String clientIP = getClientIP(httpRequest);
            log.info("批量检查用户名可用性，数量: {}, IP: {}", usernames.size(), clientIP);
            
            Map<String, Object> results = new HashMap<>();
            
            for (String username : usernames) {
                if (username != null && !username.trim().isEmpty()) {
                    UsernameValidationService.ValidationResult result = 
                        usernameValidationService.validateUsername(username.trim());
                    
                    Map<String, Object> usernameResult = new HashMap<>();
                    usernameResult.put("available", result.isAvailable());
                    usernameResult.put("valid", result.isValid());
                    usernameResult.put("errors", result.getErrors());
                    
                    results.put(username.trim(), usernameResult);
                }
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("results", results);
            responseData.put("total", usernames.size());
            
            return ApiResponse.success(responseData, "批量检查完成");
            
        } catch (Exception e) {
            log.error("批量检查用户名可用性失败", e);
            return ApiResponse.error("批量检查失败: " + e.getMessage());
        }
    }

    /**
     * 生成用户名推荐
     * 
     * @param request 包含基础信息的请求
     * @param httpRequest HTTP请求
     * @return 用户名推荐列表
     */
    @PostMapping("/suggest-usernames")
    @Operation(
        summary = "生成用户名推荐",
        description = "基于用户提供的信息生成可用的用户名推荐"
    )
    public ApiResponse<Map<String, Object>> suggestUsernames(
            @Parameter(description = "基础信息", required = true)
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String baseName = request.get("baseName");
            String email = request.get("email");
            String realName = request.get("realName");
            
            if (baseName == null && email == null && realName == null) {
                return ApiResponse.error("请提供基础信息用于生成用户名推荐");
            }
            
            String clientIP = getClientIP(httpRequest);
            log.info("生成用户名推荐，基础信息: {}, IP: {}", baseName, clientIP);
            
            java.util.List<String> suggestions = usernameValidationService.generateUsernameSuggestions(
                baseName, email, realName);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("suggestions", suggestions);
            responseData.put("count", suggestions.size());
            
            return ApiResponse.success(responseData, "用户名推荐生成成功");
            
        } catch (Exception e) {
            log.error("生成用户名推荐失败", e);
            return ApiResponse.error("生成推荐失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户名规则说明
     * 
     * @return 用户名规则说明
     */
    @GetMapping("/username-rules")
    @Operation(
        summary = "获取用户名规则说明",
        description = "获取用户名格式要求和规则说明"
    )
    public ApiResponse<Map<String, Object>> getUsernameRules() {
        
        try {
            Map<String, Object> rules = new HashMap<>();
            rules.put("minLength", 3);
            rules.put("maxLength", 20);
            rules.put("allowedCharacters", "字母、数字、下划线");
            rules.put("pattern", "^[a-zA-Z0-9_]{3,20}$");
            rules.put("restrictions", java.util.Arrays.asList(
                "不能以数字开头",
                "不能包含特殊字符（除下划线外）",
                "不能使用系统保留词",
                "长度必须在3-20位之间"
            ));
            rules.put("examples", java.util.Arrays.asList(
                "user123",
                "smart_home_user",
                "designer_wang",
                "owner2024"
            ));
            
            return ApiResponse.success(rules, "用户名规则获取成功");
            
        } catch (Exception e) {
            log.error("获取用户名规则失败", e);
            return ApiResponse.error("获取规则失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty() && !"unknown".equalsIgnoreCase(xRealIP)) {
            return xRealIP;
        }
        
        return request.getRemoteAddr();
    }
}

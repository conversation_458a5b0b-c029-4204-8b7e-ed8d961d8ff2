/**
 * 商品管理API接口模块
 * 提供与后端API的交互功能
 * 兼容MedusaJS电商框架
 */

class ProductsAPI {
    constructor() {
        this.baseUrl = '/api/products';
        this.medusaBaseUrl = '/api/medusa/products';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    /**
     * 获取CSRF Token
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    /**
     * 获取请求头
     */
    getHeaders(includeCSRF = false) {
        const headers = { ...this.defaultHeaders };
        
        if (includeCSRF) {
            headers['X-CSRF-Token'] = this.getCSRFToken();
        }

        // 添加认证头
        const authToken = localStorage.getItem('auth_token');
        if (authToken) {
            headers['Authorization'] = `Bearer ${authToken}`;
        }

        return headers;
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }

        return await response.text();
    }

    /**
     * 获取商品列表
     */
    async getProducts(params = {}) {
        try {
            const queryParams = new URLSearchParams({
                page: params.page || 1,
                limit: params.limit || 20,
                sort_by: params.sort_by || 'created_at',
                sort_order: params.sort_order || 'desc',
                ...params.filters
            });

            const response = await fetch(`${this.baseUrl}?${queryParams}`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取商品列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取单个商品详情
     */
    async getProduct(productId) {
        try {
            const response = await fetch(`${this.baseUrl}/${productId}`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取商品详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建商品
     */
    async createProduct(productData) {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: this.getHeaders(true),
                body: JSON.stringify(productData)
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('创建商品失败:', error);
            throw error;
        }
    }

    /**
     * 更新商品
     */
    async updateProduct(productId, productData) {
        try {
            const response = await fetch(`${this.baseUrl}/${productId}`, {
                method: 'PUT',
                headers: this.getHeaders(true),
                body: JSON.stringify(productData)
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('更新商品失败:', error);
            throw error;
        }
    }

    /**
     * 删除商品
     */
    async deleteProduct(productId) {
        try {
            const response = await fetch(`${this.baseUrl}/${productId}`, {
                method: 'DELETE',
                headers: this.getHeaders(true)
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('删除商品失败:', error);
            throw error;
        }
    }

    /**
     * 批量删除商品
     */
    async deleteProducts(productIds) {
        try {
            const response = await fetch(`${this.baseUrl}/batch-delete`, {
                method: 'POST',
                headers: this.getHeaders(true),
                body: JSON.stringify({ product_ids: productIds })
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('批量删除商品失败:', error);
            throw error;
        }
    }

    /**
     * 批量导入商品
     */
    async importProducts(products) {
        try {
            const response = await fetch(`${this.baseUrl}/import`, {
                method: 'POST',
                headers: this.getHeaders(true),
                body: JSON.stringify({ products })
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('批量导入商品失败:', error);
            throw error;
        }
    }

    /**
     * 上传商品图片
     */
    async uploadProductImage(file, productId = null) {
        try {
            const formData = new FormData();
            formData.append('image', file);
            
            if (productId) {
                formData.append('product_id', productId);
            }

            const headers = {};
            const authToken = localStorage.getItem('auth_token');
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            
            const csrfToken = this.getCSRFToken();
            if (csrfToken) {
                headers['X-CSRF-Token'] = csrfToken;
            }

            const response = await fetch(`${this.baseUrl}/upload-image`, {
                method: 'POST',
                headers,
                body: formData
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('上传商品图片失败:', error);
            throw error;
        }
    }

    /**
     * 获取商品分类
     */
    async getCategories() {
        try {
            const response = await fetch(`${this.baseUrl}/categories`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取商品分类失败:', error);
            throw error;
        }
    }

    /**
     * 同步MedusaJS商品
     */
    async syncWithMedusa() {
        try {
            const response = await fetch(`${this.baseUrl}/sync-medusa`, {
                method: 'POST',
                headers: this.getHeaders(true)
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('同步MedusaJS失败:', error);
            throw error;
        }
    }

    /**
     * 获取MedusaJS商品列表
     */
    async getMedusaProducts(params = {}) {
        try {
            const queryParams = new URLSearchParams(params);

            const response = await fetch(`${this.medusaBaseUrl}?${queryParams}`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取MedusaJS商品列表失败:', error);
            throw error;
        }
    }

    /**
     * 推送商品到MedusaJS
     */
    async pushToMedusa(productIds) {
        try {
            const response = await fetch(`${this.baseUrl}/push-to-medusa`, {
                method: 'POST',
                headers: this.getHeaders(true),
                body: JSON.stringify({ product_ids: productIds })
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('推送商品到MedusaJS失败:', error);
            throw error;
        }
    }

    /**
     * 获取库存信息
     */
    async getInventory(productId) {
        try {
            const response = await fetch(`${this.baseUrl}/${productId}/inventory`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取库存信息失败:', error);
            throw error;
        }
    }

    /**
     * 更新库存
     */
    async updateInventory(productId, inventoryData) {
        try {
            const response = await fetch(`${this.baseUrl}/${productId}/inventory`, {
                method: 'PUT',
                headers: this.getHeaders(true),
                body: JSON.stringify(inventoryData)
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('更新库存失败:', error);
            throw error;
        }
    }

    /**
     * 获取商品统计信息
     */
    async getProductStats() {
        try {
            const response = await fetch(`${this.baseUrl}/stats`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取商品统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 搜索商品
     */
    async searchProducts(query, filters = {}) {
        try {
            const params = {
                q: query,
                ...filters
            };

            const queryParams = new URLSearchParams(params);

            const response = await fetch(`${this.baseUrl}/search?${queryParams}`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('搜索商品失败:', error);
            throw error;
        }
    }

    /**
     * 导出商品数据
     */
    async exportProducts(format = 'xlsx', filters = {}) {
        try {
            const params = {
                format,
                ...filters
            };

            const queryParams = new URLSearchParams(params);

            const response = await fetch(`${this.baseUrl}/export?${queryParams}`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 返回Blob用于下载
            return await response.blob();
        } catch (error) {
            console.error('导出商品数据失败:', error);
            throw error;
        }
    }
}

// 创建全局实例
const productsAPI = new ProductsAPI();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductsAPI;
} else {
    window.ProductsAPI = ProductsAPI;
    window.productsAPI = productsAPI;
}

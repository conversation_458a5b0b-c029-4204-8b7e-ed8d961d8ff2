<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="../../../../styles/glass-effects.css">
    <link rel="stylesheet" href="../../../../styles/mobile-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item active">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">用户管理</h1>
                            <p class="breadcrumb-description">管理系统用户账户、角色权限和访问控制</p>
                        </div>
                    </nav>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 数据统计概览 -->
                <div class="user-stats">
                    <div class="stat-card glass-stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalUsers">0</span>
                            <span class="stat-label">总用户数</span>
                        </div>
                    </div>
                    <div class="stat-card glass-stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="activeUsers">0</span>
                            <span class="stat-label">活跃用户</span>
                        </div>
                    </div>
                    <div class="stat-card glass-stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="newUsers">0</span>
                            <span class="stat-label">本月新增</span>
                        </div>
                    </div>
                    <div class="stat-card glass-stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="adminUsers">0</span>
                            <span class="stat-label">管理员</span>
                        </div>
                    </div>
                </div>

                <!-- 用户管理工具栏 -->
                <div class="user-toolbar glass-panel">
                    <div class="toolbar-left">
                        <button class="btn btn-primary glass-btn-primary" onclick="showAddUserModal()">
                            <i class="fas fa-plus"></i> 新增用户
                        </button>
                        <button class="btn btn-success glass-btn" onclick="showInviteModal()">
                            <i class="fas fa-user-plus"></i> 邀请用户
                        </button>
                        <button class="btn btn-secondary glass-btn" onclick="showBatchModal()">
                            <i class="fas fa-tasks"></i> 批量操作
                        </button>
                        <button class="btn btn-secondary glass-btn" onclick="exportUsers()">
                            <i class="fas fa-download"></i> 导出用户
                        </button>
                        <button class="btn btn-secondary glass-btn" onclick="refreshUsers()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" class="glass-search" placeholder="搜索用户..." onkeyup="searchUsers()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="roleFilter" class="glass-input" onchange="filterByRole()">
                            <option value="">所有角色</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="designer">设计师</option>
                            <option value="constructor">施工方</option>
                        </select>
                        <select id="statusFilter" class="glass-input" onchange="filterByStatus()">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="banned">已禁用</option>
                        </select>
                    </div>
                </div>

                <!-- 用户列表表格 -->
                <div class="user-table-container glass-container">
                    <table class="user-table glass-table" id="userTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortTable('id')">ID <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('username')">用户名 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('email')">邮箱 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('role')">角色 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('status')">状态 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('lastLogin')">最后登录 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('createdAt')">创建时间 <i class="fas fa-sort"></i></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div class="page-numbers" id="pageNumbers">
                            <!-- 页码将通过JavaScript动态生成 -->
                        </div>
                        <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑用户模态框 -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增用户</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="email">邮箱 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">密码 *</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">确认密码 *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">姓名</label>
                            <input type="text" id="name" name="name">
                        </div>
                        <div class="form-group">
                            <label for="phone">手机号</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="role">角色 *</label>
                            <select id="role" name="role" required>
                                <option value="">请选择角色</option>
                                <option value="admin">管理员</option>
                                <option value="user">普通用户</option>
                                <option value="designer">设计师</option>
                                <option value="constructor">施工方</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="status">状态 *</label>
                            <select id="status" name="status" required>
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="banned">已禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">描述</label>
                        <textarea id="description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                <button class="btn btn-primary" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content glass-modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 "<span id="deleteUserName"></span>" 吗？</p>
                <p class="text-warning">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeDeleteModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div class="modal" id="userDetailModal">
        <div class="modal-content glass-modal-content">
            <div class="modal-header">
                <h3>用户详情</h3>
                <button class="modal-close" onclick="closeUserDetailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="user-detail-content">
                    <div class="user-avatar-section">
                        <div class="user-avatar" id="detailAvatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-basic-info">
                            <h4 id="detailName">用户姓名</h4>
                            <p id="detailUsername">@username</p>
                            <span class="status-badge" id="detailStatus">活跃</span>
                        </div>
                    </div>

                    <div class="user-info-grid">
                        <div class="info-item">
                            <label>邮箱</label>
                            <span id="detailEmail"><EMAIL></span>
                        </div>
                        <div class="info-item">
                            <label>手机号</label>
                            <span id="detailPhone">未设置</span>
                        </div>
                        <div class="info-item">
                            <label>角色</label>
                            <span class="role-badge" id="detailRole">普通用户</span>
                        </div>
                        <div class="info-item">
                            <label>注册时间</label>
                            <span id="detailCreatedAt">2024-01-01</span>
                        </div>
                        <div class="info-item">
                            <label>最后登录</label>
                            <span id="detailLastLogin">2024-01-27</span>
                        </div>
                        <div class="info-item">
                            <label>登录次数</label>
                            <span id="detailLoginCount">0</span>
                        </div>
                    </div>

                    <div class="user-description">
                        <label>描述</label>
                        <p id="detailDescription">暂无描述</p>
                    </div>

                    <div class="user-activity">
                        <h5>最近活动</h5>
                        <div class="activity-list" id="userActivityList">
                            <div class="activity-item">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>用户登录系统</span>
                                <time>2024-01-27 10:30</time>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserDetailModal()">关闭</button>
                <button class="btn btn-primary" onclick="editUserFromDetail()">编辑用户</button>
            </div>
        </div>
    </div>

    <!-- 用户邀请模态框 -->
    <div class="modal" id="inviteModal">
        <div class="modal-content glass-modal-content">
            <div class="modal-header">
                <h3>邀请用户</h3>
                <button class="modal-close" onclick="closeInviteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="inviteForm">
                    <div class="invite-method">
                        <label>邀请方式</label>
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="inviteMethod" value="email" checked>
                                <span>邮箱邀请</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="inviteMethod" value="phone">
                                <span>短信邀请</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="inviteMethod" value="link">
                                <span>邀请链接</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group" id="emailInviteGroup">
                        <label for="inviteEmails">邮箱地址 *</label>
                        <textarea id="inviteEmails" name="inviteEmails" rows="3"
                                placeholder="输入邮箱地址，多个邮箱用换行或逗号分隔"></textarea>
                        <small>支持批量邀请，每行一个邮箱地址</small>
                    </div>

                    <div class="form-group" id="phoneInviteGroup" style="display: none;">
                        <label for="invitePhones">手机号码 *</label>
                        <textarea id="invitePhones" name="invitePhones" rows="3"
                                placeholder="输入手机号码，多个号码用换行或逗号分隔"></textarea>
                        <small>支持批量邀请，每行一个手机号码</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="inviteRole">默认角色 *</label>
                            <select id="inviteRole" name="inviteRole" required>
                                <option value="user">普通用户</option>
                                <option value="designer">设计师</option>
                                <option value="constructor">施工方</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="inviteExpiry">邀请有效期</label>
                            <select id="inviteExpiry" name="inviteExpiry">
                                <option value="24">24小时</option>
                                <option value="72" selected>3天</option>
                                <option value="168">7天</option>
                                <option value="720">30天</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="inviteMessage">邀请消息</label>
                        <textarea id="inviteMessage" name="inviteMessage" rows="3"
                                placeholder="可选的个人邀请消息"></textarea>
                    </div>

                    <div class="invite-preview" id="invitePreview" style="display: none;">
                        <h5>邀请链接预览</h5>
                        <div class="link-preview">
                            <input type="text" id="inviteLink" readonly>
                            <button type="button" class="btn btn-secondary" onclick="copyInviteLink()">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeInviteModal()">取消</button>
                <button class="btn btn-primary" onclick="sendInvites()">发送邀请</button>
            </div>
        </div>
    </div>

    <style>
        /* 用户管理专用样式 */
        .user-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl); }
        .stat-card { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-lg); display: flex; align-items: center; gap: var(--spacing-md); transition: all var(--transition-base); }
        .stat-card:hover { border-color: var(--primary-black); transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .stat-icon { width: 48px; height: 48px; border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; color: var(--text-inverse); font-size: 20px; }
        .stat-info { display: flex; flex-direction: column; }
        .stat-value { font-size: 24px; font-weight: 700; color: var(--text-primary); line-height: 1.2; }
        .stat-label { font-size: 12px; color: var(--text-secondary); margin-top: 2px; }
        
        /* 工具栏样式 */
        .user-toolbar { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-md); }
        .toolbar-left, .toolbar-right { display: flex; align-items: center; gap: var(--spacing-sm); }
        .search-box { position: relative; }
        .search-box input { padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) var(--spacing-md); border: 1px solid var(--border-color); border-radius: var(--radius-sm); width: 200px; }
        .search-box i { position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: var(--text-secondary); }
        
        /* 表格样式 */
        .user-table-container { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-md); overflow: hidden; margin-bottom: var(--spacing-lg); }
        .user-table { width: 100%; border-collapse: collapse; }
        .user-table th, .user-table td { padding: var(--spacing-md); text-align: left; border-bottom: 1px solid var(--border-color); }
        .user-table th { background: var(--bg-muted); font-weight: 600; color: var(--text-primary); cursor: pointer; user-select: none; }
        .user-table th:hover { background: var(--bg-hover); }
        .user-table tbody tr:hover { background: var(--bg-hover); }
        .user-table .status-badge { padding: 2px 8px; border-radius: var(--radius-sm); font-size: 11px; font-weight: 500; }
        .status-active { background: var(--success-green); color: var(--text-inverse); }
        .status-inactive { background: var(--gray-500); color: var(--text-inverse); }
        .status-banned { background: var(--error-red); color: var(--text-inverse); }
        .role-badge { padding: 2px 8px; border-radius: var(--radius-sm); font-size: 11px; font-weight: 500; background: var(--accent-blue); color: var(--text-inverse); }
        
        /* 分页样式 */
        .pagination-container { display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md); background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-md); }
        .pagination-controls { display: flex; align-items: center; gap: var(--spacing-sm); }
        .page-numbers { display: flex; gap: 4px; }
        .page-number { padding: 6px 12px; border: 1px solid var(--border-color); border-radius: var(--radius-sm); cursor: pointer; transition: all var(--transition-base); }
        .page-number:hover, .page-number.active { background: var(--primary-black); color: var(--text-inverse); border-color: var(--primary-black); }
        
        /* 模态框样式 */
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1000; }
        .modal-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--bg-card); border-radius: var(--radius-lg); width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-lg); border-bottom: 1px solid var(--border-color); }
        .modal-header h3 { margin: 0; font-size: 18px; font-weight: 600; color: var(--text-primary); }
        .modal-close { background: none; border: none; font-size: 18px; color: var(--text-secondary); cursor: pointer; }
        .modal-body { padding: var(--spacing-lg); }
        .modal-footer { display: flex; justify-content: flex-end; gap: var(--spacing-sm); padding: var(--spacing-lg); border-top: 1px solid var(--border-color); }
        
        /* 表单样式 */
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md); }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { font-size: 12px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px; }
        .form-group input, .form-group select, .form-group textarea { padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: var(--primary-black); }
        
        /* 按钮样式 */
        .btn { padding: var(--spacing-sm) var(--spacing-md); border: 1px solid transparent; border-radius: var(--radius-sm); font-size: 12px; font-weight: 500; cursor: pointer; transition: all var(--transition-base); text-decoration: none; display: inline-flex; align-items: center; justify-content: center; gap: var(--spacing-xs); }
        .btn-primary { background: var(--primary-black); color: var(--text-inverse); }
        .btn-primary:hover { background: var(--gray-800); transform: translateY(-1px); }
        .btn-secondary { background: var(--bg-muted); color: var(--text-primary); border-color: var(--border-color); }
        .btn-secondary:hover { background: var(--bg-hover); border-color: var(--primary-black); }
        .btn-danger { background: var(--error-red); color: var(--text-inverse); }
        .btn-danger:hover { background: #dc2626; transform: translateY(-1px); }
        .btn-sm { padding: 4px 8px; font-size: 11px; }
        
        /* 响应式设计 */
        @media (max-width: 1200px) { .user-stats { grid-template-columns: repeat(2, 1fr); } .form-row { grid-template-columns: 1fr; } }
        @media (max-width: 768px) { .user-stats { grid-template-columns: 1fr; } .user-toolbar { flex-direction: column; gap: var(--spacing-md); } .toolbar-left, .toolbar-right { width: 100%; justify-content: center; } .pagination-container { flex-direction: column; gap: var(--spacing-md); } }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-danger {
            background: #ef4444;
            color: #ffffff;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 角色和状态徽章样式 */
        .role-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .role-admin {
            background: #fef3c7;
            color: #92400e;
        }

        .role-manager {
            background: #dbeafe;
            color: #1e40af;
        }

        .role-user {
            background: #f3f4f6;
            color: #374151;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-inactive {
            background: #fef3c7;
            color: #92400e;
        }

        .status-banned {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 用户详情模态框样式 */
        .user-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script>
        // 设计系统模拟 (简化版)
        const ds = {
            showLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0.5';
                    element.style.pointerEvents = 'none';
                }
            },
            hideLoading: (selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '1';
                    element.style.pointerEvents = 'auto';
                }
            },
            showToast: (message, type = 'info') => {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            },
            openModal: (modalId) => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'block';
                    setTimeout(() => modal.classList.add('modal-open'), 10);
                }
            },
            closeModal: () => {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.classList.remove('modal-open');
                    setTimeout(() => modal.style.display = 'none', 300);
                });
            },
            formatDate: (dateString, format = 'YYYY-MM-DD HH:mm') => {
                if (!dateString) return '从未登录';
                const date = new Date(dateString);
                if (format === 'YYYY-MM-DD') {
                    return date.toLocaleDateString('zh-CN');
                } else if (format === 'HH:mm:ss') {
                    return date.toLocaleTimeString('zh-CN');
                }
                return date.toLocaleString('zh-CN');
            }
        };

        // 用户管理类
        class UserManager {
            constructor() {
                this.users = [];
                this.filteredUsers = [];
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.sortField = 'id';
                this.sortOrder = 'asc';
                this.currentEditId = null;
                this.init();
            }

            init() {
                this.loadData();
                this.loadUsers();
                this.updateStats();
                this.renderTable();
                this.renderPagination();
                this.bindEvents();
            }

            // 数据管理
            loadData() {
                // 加载用户数据
                this.users = JSON.parse(localStorage.getItem('users') || '[]');

                // 初始化默认数据
                if (this.users.length === 0) {
                    this.users = [
                        {
                            id: 1,
                            username: 'admin',
                            email: '<EMAIL>',
                            name: '系统管理员',
                            role: 'admin',
                            status: 'active',
                            phone: '13800138001',
                            department: '技术部',
                            position: '系统管理员',
                            lastLogin: new Date().toISOString(),
                            createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),
                            avatar: 'https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff'
                        },
                        {
                            id: 2,
                            username: 'zhangsan',
                            email: '<EMAIL>',
                            name: '张三',
                            role: 'manager',
                            status: 'active',
                            phone: '13800138002',
                            department: '销售部',
                            position: '销售经理',
                            lastLogin: new Date(Date.now() - 3600000).toISOString(),
                            createdAt: new Date(Date.now() - 86400000 * 15).toISOString(),
                            avatar: 'https://ui-avatars.com/api/?name=张三&background=10b981&color=fff'
                        },
                        {
                            id: 3,
                            username: 'lisi',
                            email: '<EMAIL>',
                            name: '李四',
                            role: 'user',
                            status: 'active',
                            phone: '13800138003',
                            department: '客服部',
                            position: '客服专员',
                            lastLogin: new Date(Date.now() - 86400000).toISOString(),
                            createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),
                            avatar: 'https://ui-avatars.com/api/?name=李四&background=f59e0b&color=fff'
                        },
                        {
                            id: 4,
                            username: 'wangwu',
                            email: '<EMAIL>',
                            name: '王五',
                            role: 'user',
                            status: 'inactive',
                            phone: '13800138004',
                            department: '技术部',
                            position: '开发工程师',
                            lastLogin: new Date(Date.now() - 86400000 * 7).toISOString(),
                            createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
                            avatar: 'https://ui-avatars.com/api/?name=王五&background=8b5cf6&color=fff'
                        },
                        {
                            id: 5,
                            username: 'zhaoliu',
                            email: '<EMAIL>',
                            name: '赵六',
                            role: 'user',
                            status: 'banned',
                            phone: '13800138005',
                            department: '财务部',
                            position: '财务专员',
                            lastLogin: null,
                            createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
                            avatar: 'https://ui-avatars.com/api/?name=赵六&background=ef4444&color=fff'
                        }
                    ];
                    this.saveUsers();
                }
            }

            saveUsers() {
                localStorage.setItem('users', JSON.stringify(this.users));
            }

            getNextUserId() {
                return this.users.length > 0 ? Math.max(...this.users.map(u => u.id)) + 1 : 1;
            }

            // 加载用户数据
            loadUsers() {
                // 应用筛选条件
                this.filteredUsers = [...this.users];

                // 搜索筛选
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    const keyword = searchInput.value.trim().toLowerCase();
                    this.filteredUsers = this.filteredUsers.filter(user =>
                        user.username.toLowerCase().includes(keyword) ||
                        user.email.toLowerCase().includes(keyword) ||
                        (user.name && user.name.toLowerCase().includes(keyword))
                    );
                }

                // 角色筛选
                const roleFilter = document.getElementById('roleFilter');
                if (roleFilter && roleFilter.value) {
                    this.filteredUsers = this.filteredUsers.filter(user =>
                        user.role === roleFilter.value
                    );
                }

                // 状态筛选
                const statusFilter = document.getElementById('statusFilter');
                if (statusFilter && statusFilter.value) {
                    this.filteredUsers = this.filteredUsers.filter(user =>
                        user.status === statusFilter.value
                    );
                }

                // 排序
                this.filteredUsers.sort((a, b) => {
                    let aValue = a[this.sortField];
                    let bValue = b[this.sortField];

                    // 处理日期字段
                    if (this.sortField === 'lastLogin' || this.sortField === 'createdAt') {
                        aValue = aValue ? new Date(aValue) : new Date(0);
                        bValue = bValue ? new Date(bValue) : new Date(0);
                    }

                    if (this.sortOrder === 'asc') {
                        return aValue > bValue ? 1 : -1;
                    } else {
                        return aValue < bValue ? 1 : -1;
                    }
                });

                this.totalCount = this.filteredUsers.length;
            }

            // 更新统计数据
            updateStats() {
                const totalUsers = this.users.length;
                const activeUsers = this.users.filter(u => u.status === 'active').length;
                const adminUsers = this.users.filter(u => u.role === 'admin').length;
                const managerUsers = this.users.filter(u => u.role === 'manager').length;
                const newUsers = this.users.filter(u => {
                    const created = new Date(u.createdAt);
                    const now = new Date();
                    const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);
                    return created >= monthAgo;
                }).length;

                // 更新统计卡片
                const totalUsersEl = document.getElementById('totalUsers');
                const activeUsersEl = document.getElementById('activeUsers');
                const adminUsersEl = document.getElementById('adminUsers');
                const newUsersEl = document.getElementById('newUsers');

                if (totalUsersEl) totalUsersEl.textContent = totalUsers;
                if (activeUsersEl) activeUsersEl.textContent = activeUsers;
                if (adminUsersEl) adminUsersEl.textContent = adminUsers;
                if (newUsersEl) newUsersEl.textContent = newUsers;

                // 如果没有找到统计元素，创建简单的统计显示
                if (!totalUsersEl) {
                    console.log('用户统计:', {
                        总用户数: totalUsers,
                        活跃用户: activeUsers,
                        管理员: adminUsers,
                        经理: managerUsers,
                        本月新增: newUsers
                    });
                }
            }

            // 渲染用户表格
            renderTable() {
                const tbody = document.getElementById('userTableBody');
                if (!tbody) return;

                tbody.innerHTML = '';

                if (this.filteredUsers.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px; color: #6b7280;">
                                <i class="fas fa-users fa-3x" style="display: block; margin-bottom: 16px; opacity: 0.3;"></i>
                                暂无用户数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                // 分页处理
                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

                pageUsers.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><input type="checkbox" class="user-checkbox" value="${user.id}"></td>
                        <td>${user.id}</td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <img src="${user.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(user.name || user.username) + '&background=3b82f6&color=fff'}"
                                     alt="${user.name}"
                                     style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                                <div>
                                    <div style="font-weight: 500;">${user.username}</div>
                                    <div style="font-size: 12px; color: #6b7280;">${user.name || ''}</div>
                                </div>
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td><span class="role-badge role-${user.role}">${this.getRoleText(user.role)}</span></td>
                        <td><span class="status-badge status-${user.status}">${this.getStatusText(user.status)}</span></td>
                        <td>${ds.formatDate(user.lastLogin)}</td>
                        <td>${ds.formatDate(user.createdAt, 'YYYY-MM-DD')}</td>
                        <td>
                            <div style="display: flex; gap: 4px;">
                                <button class="btn btn-sm btn-secondary" onclick="userManager.viewUser(${user.id})" title="查看">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="userManager.editUser(${user.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="userManager.deleteUser(${user.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 渲染分页
            renderPagination() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageStart').textContent = start;
                document.getElementById('pageEnd').textContent = end;
                document.getElementById('totalCount').textContent = this.totalCount;

                // 更新按钮状态
                document.getElementById('prevBtn').disabled = this.currentPage <= 1;
                document.getElementById('nextBtn').disabled = this.currentPage >= totalPages;

                // 生成页码
                const pageNumbers = document.getElementById('pageNumbers');
                pageNumbers.innerHTML = '';

                for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                    const pageBtn = document.createElement('div');
                    pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    pageNumbers.appendChild(pageBtn);
                }
            }

            // 绑定事件
            bindEvents() {
                // 搜索框事件
                document.getElementById('searchInput').addEventListener('input', () => {
                    this.searchUsers();
                });

                // 筛选器事件
                document.getElementById('roleFilter').addEventListener('change', () => {
                    this.filterUsers();
                });

                document.getElementById('statusFilter').addEventListener('change', () => {
                    this.filterUsers();
                });
            }

            // 搜索用户
            searchUsers() {
                const keyword = document.getElementById('searchInput').value.toLowerCase();
                this.filteredUsers = this.users.filter(user =>
                    user.username.toLowerCase().includes(keyword) ||
                    user.email.toLowerCase().includes(keyword) ||
                    (user.name && user.name.toLowerCase().includes(keyword))
                );
                this.renderTable();
            }

            // 筛选用户
            filterUsers() {
                const roleFilter = document.getElementById('roleFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;

                this.filteredUsers = this.users.filter(user => {
                    const roleMatch = !roleFilter || user.role === roleFilter;
                    const statusMatch = !statusFilter || user.status === statusFilter;
                    return roleMatch && statusMatch;
                });

                this.renderTable();
            }

            // 排序表格
            sortTable(field) {
                if (this.sortField === field) {
                    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortField = field;
                    this.sortOrder = 'asc';
                }

                this.filteredUsers.sort((a, b) => {
                    let aVal = a[field];
                    let bVal = b[field];

                    if (field === 'lastLogin' || field === 'createdAt') {
                        aVal = new Date(aVal);
                        bVal = new Date(bVal);
                    }

                    if (this.sortOrder === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                this.renderTable();
            }

            // 分页控制
            goToPage(page) {
                this.currentPage = page;
                this.loadUsers();
            }

            previousPage() {
                if (this.currentPage > 1) {
                    this.goToPage(this.currentPage - 1);
                }
            }

            nextPage() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                if (this.currentPage < totalPages) {
                    this.goToPage(this.currentPage + 1);
                }
            }

            // 工具函数
            getRoleText(role) {
                const roleMap = {
                    'admin': '管理员',
                    'manager': '经理',
                    'user': '普通用户',
                    'designer': '设计师',
                    'constructor': '施工方'
                };
                return roleMap[role] || role;
            }

            getStatusText(status) {
                const statusMap = {
                    'active': '活跃',
                    'inactive': '非活跃',
                    'banned': '已禁用'
                };
                return statusMap[status] || status;
            }

            // 用户操作方法
            showAddUserModal() {
                this.currentEditId = null;
                const modalTitle = document.getElementById('modalTitle');
                const userForm = document.getElementById('userForm');
                const userModal = document.getElementById('userModal');

                if (modalTitle) modalTitle.textContent = '新增用户';
                if (userForm) userForm.reset();
                if (userModal) userModal.style.display = 'block';

                // 显示密码字段
                const passwordField = document.getElementById('password');
                const confirmPasswordField = document.getElementById('confirmPassword');
                if (passwordField) passwordField.style.display = 'block';
                if (confirmPasswordField) confirmPasswordField.style.display = 'block';
            }

            editUser(id) {
                const user = this.users.find(u => u.id === parseInt(id));
                if (!user) {
                    ds.showToast('用户不存在', 'error');
                    return;
                }

                this.currentEditId = id;
                const modalTitle = document.getElementById('modalTitle');
                if (modalTitle) modalTitle.textContent = '编辑用户';

                // 填充表单
                const fields = {
                    'username': user.username,
                    'email': user.email,
                    'name': user.name || '',
                    'phone': user.phone || '',
                    'role': user.role,
                    'status': user.status,
                    'department': user.department || '',
                    'position': user.position || ''
                };

                Object.keys(fields).forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) field.value = fields[fieldId];
                });

                // 编辑时隐藏密码字段
                const passwordField = document.getElementById('password');
                const confirmPasswordField = document.getElementById('confirmPassword');
                if (passwordField) passwordField.style.display = 'none';
                if (confirmPasswordField) confirmPasswordField.style.display = 'none';

                const userModal = document.getElementById('userModal');
                if (userModal) userModal.style.display = 'block';
            }

            viewUser(id) {
                const user = this.users.find(u => u.id === parseInt(id));
                if (!user) {
                    ds.showToast('用户不存在', 'error');
                    return;
                }

                // 创建用户详情模态框
                const modal = document.createElement('div');
                modal.className = 'user-detail-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="this.parentElement.remove()">
                        <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px; padding: 24px;">
                            <div class="modal-header" style="margin-bottom: 20px;">
                                <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">用户详情</h3>
                                <button onclick="this.closest('.user-detail-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                            </div>
                            <div class="user-detail-content">
                                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px; padding: 16px; background: #f9fafb; border-radius: 8px;">
                                    <img src="${user.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(user.name || user.username) + '&background=3b82f6&color=fff'}"
                                         alt="${user.name}"
                                         style="width: 64px; height: 64px; border-radius: 50%; object-fit: cover;">
                                    <div>
                                        <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: #1f2937;">${user.name || user.username}</h4>
                                        <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">${user.email}</p>
                                    </div>
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <div><strong style="color: #374151;">用户名：</strong><span style="color: #6b7280;">${user.username}</span></div>
                                    <div><strong style="color: #374151;">角色：</strong><span style="color: #6b7280;">${this.getRoleText(user.role)}</span></div>
                                    <div><strong style="color: #374151;">状态：</strong><span style="color: ${user.status === 'active' ? '#10b981' : user.status === 'inactive' ? '#f59e0b' : '#ef4444'}; font-weight: 500;">${this.getStatusText(user.status)}</span></div>
                                    <div><strong style="color: #374151;">电话：</strong><span style="color: #6b7280;">${user.phone || '未设置'}</span></div>
                                    <div><strong style="color: #374151;">部门：</strong><span style="color: #6b7280;">${user.department || '未设置'}</span></div>
                                    <div><strong style="color: #374151;">职位：</strong><span style="color: #6b7280;">${user.position || '未设置'}</span></div>
                                    <div style="grid-column: 1 / -1;"><strong style="color: #374151;">最后登录：</strong><span style="color: #6b7280;">${ds.formatDate(user.lastLogin)}</span></div>
                                    <div style="grid-column: 1 / -1;"><strong style="color: #374151;">创建时间：</strong><span style="color: #6b7280;">${ds.formatDate(user.createdAt)}</span></div>
                                </div>
                                <div style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #e5e7eb; display: flex; gap: 8px; justify-content: flex-end;">
                                    <button onclick="this.closest('.user-detail-modal').remove()" style="padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">关闭</button>
                                    <button onclick="userManager.editUser(${user.id}); this.closest('.user-detail-modal').remove();" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">编辑用户</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 添加样式
                const overlay = modal.querySelector('.modal-overlay');
                Object.assign(overlay.style, {
                    position: 'fixed',
                    top: '0',
                    left: '0',
                    right: '0',
                    bottom: '0',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: '10000'
                });

                const content = modal.querySelector('.modal-content');
                Object.assign(content.style, {
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    maxHeight: '80vh',
                    overflow: 'auto'
                });

                const header = modal.querySelector('.modal-header');
                Object.assign(header.style, {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderBottom: '1px solid #e5e7eb',
                    paddingBottom: '12px'
                });

                document.body.appendChild(modal);
            }

            deleteUser(id) {
                const user = this.users.find(u => u.id === parseInt(id));
                if (!user) {
                    ds.showToast('用户不存在', 'error');
                    return;
                }

                if (confirm(`确定要删除用户"${user.username}"吗？此操作不可恢复。`)) {
                    this.users = this.users.filter(u => u.id !== parseInt(id));
                    this.saveUsers();

                    ds.showToast('用户删除成功', 'success');
                    this.loadUsers();
                    this.updateStats();
                    this.renderTable();
                    this.renderPagination();
                }
            }

            saveUser() {
                const form = document.getElementById('userForm');
                if (!form) {
                    ds.showToast('表单不存在', 'error');
                    return;
                }

                // 表单验证
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // 获取表单数据
                const formData = {
                    username: document.getElementById('username')?.value || '',
                    email: document.getElementById('email')?.value || '',
                    name: document.getElementById('name')?.value || '',
                    phone: document.getElementById('phone')?.value || '',
                    role: document.getElementById('role')?.value || 'user',
                    status: document.getElementById('status')?.value || 'active',
                    department: document.getElementById('department')?.value || '',
                    position: document.getElementById('position')?.value || ''
                };

                // 基本验证
                if (!formData.username || !formData.email) {
                    ds.showToast('用户名和邮箱为必填项', 'error');
                    return;
                }

                // 邮箱格式验证
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(formData.email)) {
                    ds.showToast('请输入有效的邮箱地址', 'error');
                    return;
                }

                // 检查用户名和邮箱是否已存在
                const existingUser = this.users.find(u =>
                    (u.username === formData.username || u.email === formData.email) &&
                    u.id !== parseInt(this.currentEditId)
                );
                if (existingUser) {
                    ds.showToast('用户名或邮箱已存在', 'error');
                    return;
                }

                if (this.currentEditId) {
                    // 编辑用户
                    const userIndex = this.users.findIndex(u => u.id === parseInt(this.currentEditId));
                    if (userIndex !== -1) {
                        this.users[userIndex] = {
                            ...this.users[userIndex],
                            ...formData,
                            avatar: this.users[userIndex].avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name || formData.username)}&background=3b82f6&color=fff`
                        };
                    }
                    ds.showToast('用户更新成功', 'success');
                } else {
                    // 新增用户
                    const newUser = {
                        id: this.getNextUserId(),
                        ...formData,
                        lastLogin: null,
                        createdAt: new Date().toISOString(),
                        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name || formData.username)}&background=3b82f6&color=fff`
                    };
                    this.users.push(newUser);
                    ds.showToast('用户创建成功', 'success');
                }

                this.saveUsers();
                this.closeUserModal();
                this.loadUsers();
                this.updateStats();
                this.renderTable();
                this.renderPagination();

                // 重置表单
                form.reset();
            }

            // 模态框控制
            closeUserModal() {
                const userModal = document.getElementById('userModal');
                const userForm = document.getElementById('userForm');

                if (userModal) userModal.style.display = 'none';
                if (userForm) userForm.reset();
                this.currentEditId = null;

                // 重新显示密码字段
                const passwordField = document.getElementById('password');
                const confirmPasswordField = document.getElementById('confirmPassword');
                if (passwordField) passwordField.style.display = 'block';
                if (confirmPasswordField) confirmPasswordField.style.display = 'block';
            }

            // 其他功能
            exportUsers() {
                ds.showToast('正在导出用户数据...', 'info');

                const headers = ['ID', '用户名', '邮箱', '姓名', '手机号', '角色', '状态', '部门', '职位', '最后登录', '创建时间'];
                const csvContent = [
                    headers.join(','),
                    ...this.filteredUsers.map(user => [
                        user.id,
                        user.username,
                        user.email,
                        user.name || '',
                        user.phone || '',
                        this.getRoleText(user.role),
                        this.getStatusText(user.status),
                        user.department || '',
                        user.position || '',
                        ds.formatDate(user.lastLogin),
                        ds.formatDate(user.createdAt, 'YYYY-MM-DD')
                    ].join(','))
                ].join('\n');

                // 创建下载链接
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `users_${new Date().toISOString().slice(0, 10)}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                ds.showToast(`用户数据导出成功 (${this.filteredUsers.length}条记录)`, 'success');
            }

            refreshUsers() {
                this.loadUsers();
                this.updateStats();
                this.renderTable();
                this.renderPagination();
                ds.showToast('数据已刷新', 'success');
            }

            // 搜索和筛选方法
            searchUsers() {
                this.currentPage = 1; // 重置到第一页
                this.loadUsers();
                this.renderTable();
                this.renderPagination();
            }

            filterUsers() {
                this.currentPage = 1; // 重置到第一页
                this.loadUsers();
                this.renderTable();
                this.renderPagination();
            }

            toggleSelectAll() {
                const selectAll = document.getElementById('selectAll');
                const checkboxes = document.querySelectorAll('.user-checkbox');
                checkboxes.forEach(cb => cb.checked = selectAll.checked);
            }
        }

        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            toast.innerHTML = `
                <div class="toast-content ${type}">
                    <i class="fa ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // 样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: '10000',
                backgroundColor: 'white',
                border: `1px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'}`,
                borderRadius: '8px',
                padding: '12px 16px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease',
                maxWidth: '400px'
            });

            const content = toast.querySelector('.toast-content');
            Object.assign(content.style, {
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'
            });

            document.body.appendChild(toast);

            // 动画显示
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 全局变量和函数
        let userManager;

        // 全局函数（保持兼容性）
        function showAddUserModal() { userManager.showAddUserModal(); }
        function closeUserModal() { userManager.closeUserModal(); }
        function closeDeleteModal() { userManager.closeDeleteModal(); }
        function saveUser() { userManager.saveUser(); }
        function confirmDelete() { userManager.confirmDelete(); }
        function exportUsers() { userManager.exportUsers(); }
        function refreshUsers() { userManager.refreshUsers(); }
        function searchUsers() { userManager.searchUsers(); }
        function filterByRole() { userManager.filterUsers(); }
        function filterByStatus() { userManager.filterUsers(); }
        function sortTable(field) { userManager.sortTable(field); }
        function previousPage() { userManager.previousPage(); }
        function nextPage() { userManager.nextPage(); }
        function toggleSelectAll() { userManager.toggleSelectAll(); }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            userManager = new UserManager();
        });
    </script>
</body>
</html>

/* 智能家居管理系统 - 毛玻璃效果样式 */

/* 页面背景渐变 */
body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
    min-height: 100vh;
}

/* 主要容器毛玻璃效果 */
.glass-container {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.85);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(229, 231, 235, 0.3);
}

/* 卡片毛玻璃效果 */
.glass-card {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(229, 231, 235, 0.4);
}

/* 头部毛玻璃效果 */
.glass-header {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(249, 250, 251, 0.8);
    border-bottom: 1px solid rgba(229, 231, 235, 0.4);
}

/* 侧边栏毛玻璃效果 */
.glass-sidebar {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border-right: 1px solid rgba(229, 231, 235, 0.3);
}

/* 模态框毛玻璃效果 */
.glass-modal {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(0, 0, 0, 0.5);
}

.glass-modal-content {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(229, 231, 235, 0.3);
}

/* 表格毛玻璃效果 */
.glass-table {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(229, 231, 235, 0.4);
}

.glass-table thead {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(249, 250, 251, 0.8);
}

.glass-table tbody tr {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    background: rgba(255, 255, 255, 0.4);
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.glass-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 表单毛玻璃效果 */
.glass-form {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(229, 231, 235, 0.4);
}

.glass-input {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 6px;
}

.glass-input:focus {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(26, 26, 26, 0.3);
    box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

/* 按钮毛玻璃效果 */
.glass-btn {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.glass-btn-primary {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(26, 26, 26, 0.9);
    color: white;
    border: 1px solid rgba(26, 26, 26, 0.3);
}

.glass-btn-primary:hover {
    background: rgba(55, 65, 81, 0.9);
    box-shadow: 0 4px 12px rgba(26, 26, 26, 0.2);
}

/* 导航毛玻璃效果 */
.glass-nav {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(229, 231, 235, 0.4);
}

.glass-nav-item {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    background: rgba(255, 255, 255, 0.4);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.glass-nav-item:hover {
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.glass-nav-item.active {
    background: rgba(26, 26, 26, 0.1);
    color: #1a1a1a;
}

/* 统计卡片毛玻璃效果 */
.glass-stat-card {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    border: 1px solid rgba(229, 231, 235, 0.4);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.glass-stat-card:hover {
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 面板毛玻璃效果 */
.glass-panel {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(229, 231, 235, 0.3);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.glass-panel-header {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    background: rgba(249, 250, 251, 0.8);
    border-bottom: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 12px 12px 0 0;
}

/* 搜索框毛玻璃效果 */
.glass-search {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 8px;
}

.glass-search:focus {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(26, 26, 26, 0.3);
    box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

/* 标签毛玻璃效果 */
.glass-tag {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .glass-container,
    .glass-panel {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
    
    .glass-card {
        backdrop-filter: blur(6px);
        -webkit-backdrop-filter: blur(6px);
    }
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
    .glass-container,
    .glass-card,
    .glass-panel,
    .glass-btn,
    .glass-nav-item {
        transition: none;
    }
}

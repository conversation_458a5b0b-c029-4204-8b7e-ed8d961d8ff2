﻿/* 鏅鸿兘璁捐涓庢柦宸ョ鐞嗙郴缁?- PC绔悗鍙扮鐞哢I鏍峰紡搴?*/
/* 鍩轰簬PC绔疷I璁捐瑙勮寖鏂囨。 v1.0 */

/* ==================== CSS鍙橀噺瀹氫箟 ==================== */

:root {
  /* 鍝佺墝涓昏壊 */
  --primary-black: #1a1a1a;
  --primary-gray: #374151;
  --gradient-primary: linear-gradient(135deg, #1a1a1a, #374151);

  /* 涓€ц壊 */
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-light: #d1d5db;

  /* 鑳屾櫙鑹?*/
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-muted: #f0f0f0;

  /* 杈规鑹?*/
  --border-light: #e5e7eb;
  --border-muted: #e0e0e0;
  --border-translucent: rgba(255, 255, 255, 0.2);

  /* 鍔熻兘鑹?*/
  --success: #10b981;
  --warning: #d97706;
  --info: #1890ff;
  --error: #ef4444;

  /* 姣涚幓鐠冩晥鏋?*/
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-blur: blur(20px);

  /* 闃村奖閫忔槑搴?*/
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.12);
  --shadow-heavy: rgba(0, 0, 0, 0.15);

  /* 瀛椾綋鏃?*/
  --font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  /* 鏍囬瀛楀彿 */
  --text-4xl: 32px;
  --text-3xl: 26px;
  --text-2xl: 22px;
  --text-xl: 20px;
  --text-lg: 18px;

  /* 姝ｆ枃瀛楀彿 */
  --text-base: 16px;
  --text-sm: 14px;
  --text-xs: 12px;
  --text-2xs: 11px;
  --text-3xs: 10px;

  /* 瀛楅噸瑙勮寖 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* 琛岄珮瑙勮寖 */
  --leading-tight: 1.2;
  --leading-normal: 1.4;
  --leading-relaxed: 1.6;

  /* 闂磋窛鍙橀噺 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;

  /* 闃村奖灞傜骇 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 20px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 32px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 8px 32px rgba(0, 0, 0, 0.12);

  /* 鍦嗚绯荤粺 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 16px;

  /* 杩囨浮鍔ㄧ敾 */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* 甯冨眬灏哄鍙橀噺 */
  --header-height: 72px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;`n  --content-max-width: 1400px;`n  --content-padding: 40px;`n  --footer-height: 48px;
  --content-min-height: calc(100vh - var(--header-height));
}

/* ==================== 鍩虹鏍峰紡閲嶇疆 ==================== */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 甯冨眬妗嗘灦 */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

/* ==================== 渚ц竟鏍忕粍浠?==================== */

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  transition: var(--transition-fast);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: var(--shadow-md);
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-2xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--gradient-primary);
  color: white;
}

.sidebar-logo {
  height: 40px;
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
}

.sidebar-menu {
  padding: var(--spacing-2xl) 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-2xl);
  color: var(--text-secondary);
  transition: var(--transition-fast);
  cursor: pointer;
  text-decoration: none;
  border-left: 3px solid transparent;
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

.menu-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transform: translateX(2px);
}

.menu-item.active {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-left-color: var(--primary-black);
  font-weight: var(--font-medium);
}

.menu-icon {
  margin-right: 12px;
  font-size: 18px;
}

.menu-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ==================== 椤堕儴瀵艰埅鏍?==================== */

.header {
  height: var(--header-height);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  padding: 0 var(--content-padding);
  position: fixed;
  top: 0;
  right: 0;
  left: var(--sidebar-width);
  z-index: 900;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.header-collapsed {
  left: var(--sidebar-collapsed-width);
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  font-size: 20px;
  color: var(--gray-600);
  cursor: pointer;
  margin-right: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  color: var(--gray-600);
}

.breadcrumb-item:not(:last-child)::after {
  content: "/";
  margin: 0 8px;
  color: var(--gray-400);
}

.breadcrumb-item:last-child {
  color: var(--gray-800);
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-left: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-action {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
}

.header-action:hover {
  background-color: var(--bg-muted);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: var(--error);
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.user-dropdown {
  position: relative;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 8px 12px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.dropdown-trigger:hover {
  background-color: var(--bg-muted);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.user-role {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.dropdown-arrow {
  font-size: 12px;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.dropdown-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 涓诲唴瀹瑰尯鍩?*/
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  padding: var(--content-padding);
  min-height: var(--content-min-height);
  max-width: calc(100vw - var(--sidebar-width));
  transition: var(--transition-fast);
  background-color: var(--bg-secondary);
}

.main-content-collapsed {
  margin-left: var(--sidebar-collapsed-width);
  max-width: calc(100vw - var(--sidebar-collapsed-width));
}

/* 鍐呭瀹瑰櫒 - 闄愬埗鏈€澶у搴﹀苟灞呬腑 */
.content-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}

/* 椤甸潰澶撮儴 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3xl);
  padding-bottom: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-light);
}

.page-header h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* 椤佃剼 */
.footer {
  height: var(--footer-height);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-size: 12px;
  border-top: 1px solid var(--gray-200);
  margin-top: auto;
}

/* 鍗＄墖缁勪欢 */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-800);
  margin: 0;
}

.card-body {
  padding: 24px;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 琛ㄦ牸缁勪欢 */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.table th {
  font-weight: 500;
  color: var(--gray-700);
  background-color: var(--gray-50);
}

.table tr:hover {
  background-color: var(--gray-50);
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 鍒嗛〉缁勪欢 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 16px;
}

.pagination-item {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition-base);
}

.pagination-item:hover {
  background-color: var(--gray-100);
}

.pagination-item.active {
  background-color: var(--primary-600);
  color: white;
}

/* 琛ㄥ崟缁勪欢 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--gray-700);
}

.form-control {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  transition: var(--transition-base);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-control.error {
  border-color: var(--error);
}

.form-error {
  color: var(--error);
  font-size: 12px;
  margin-top: 4px;
}

.form-select {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  background-color: white;
  transition: var(--transition-base);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  transition: var(--transition-base);
  resize: vertical;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.form-check-input {
  margin-right: 8px;
}

.form-check-label {
  font-size: 14px;
  color: var(--gray-700);
}

/* ==================== 鎸夐挳缁勪欢 ==================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  border: none;
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn-icon {
  margin-right: var(--spacing-sm);
  font-size: var(--text-base);
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-muted);
}

.btn-success {
  background-color: var(--success);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  background-color: #0da271;
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--error);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
  background-color: #b45309;
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-muted);
  color: var(--text-secondary);
}

.btn-outline:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--spacing-xl) var(--spacing-2xl);
  font-size: var(--text-base);
}

/* 鎼滅储绛涢€夊尯 */
.filter-section {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  padding: 16px 24px;
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

/* 缁熻鍗＄墖 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  padding: 24px;
}

.stat-title {
  font-size: 14px;
  color: var(--gray-500);
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 8px;
}

.stat-desc {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.stat-trend-up {
  color: var(--success);
}

.stat-trend-down {
  color: var(--error);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.stat-icon-primary {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

.stat-icon-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-icon-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-icon-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

/* 鍥捐〃瀹瑰櫒 */
.chart-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-2xl);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-800);
}

.chart-actions {
  display: flex;
  gap: 8px;
}

/* 瀵硅瘽妗嗙粍浠?*/
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--gray-800);
  margin: 0;
}

.modal-close {
  font-size: 20px;
  color: var(--gray-500);
  cursor: pointer;
  background: none;
  border: none;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

/* 鎶藉眽缁勪欢 */
.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

.drawer {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 400px;
  background-color: white;
  box-shadow: var(--shadow-lg);
  z-index: 1051;
  transition: transform 0.3s ease-in-out;
}

.drawer-right {
  right: 0;
  transform: translateX(100%);
}

.drawer-right.open {
  transform: translateX(0);
}

.drawer-left {
  left: 0;
  transform: translateX(-100%);
}

.drawer-left.open {
  transform: translateX(0);
}

.drawer-header {
  height: 64px;
  padding: 0 24px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.drawer-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--gray-800);
}

.drawer-close {
  font-size: 20px;
  color: var(--gray-500);
  cursor: pointer;
  background: none;
  border: none;
}

.drawer-body {
  padding: 24px;
  height: calc(100% - 64px - 64px);
  overflow-y: auto;
}

.drawer-footer {
  height: 64px;
  padding: 0 24px;
  border-top: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

/* 鏍囩椤电粍浠?*/
.tabs {
  margin-bottom: 24px;
}

.tab-list {
  display: flex;
  border-bottom: 1px solid var(--gray-200);
}

.tab-item {
  padding: 12px 16px;
  font-weight: 500;
  color: var(--gray-600);
  cursor: pointer;
  transition: var(--transition-base);
  border-bottom: 2px solid transparent;
}

.tab-item:hover {
  color: var(--primary-600);
}

.tab-item.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
}

.tab-content {
  padding: 24px 0;
}

/* 閫氱煡鎻愰啋缁勪欢 */
.notification {
  position: fixed;
  top: 24px;
  right: 24px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: flex-start;
  width: 320px;
  z-index: 1060;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-success {
  background-color: #ecfdf5;
  border-left: 4px solid var(--success);
}

.notification-error {
  background-color: #fef2f2;
  border-left: 4px solid var(--error);
}

.notification-warning {
  background-color: #fffbeb;
  border-left: 4px solid var(--warning);
}

.notification-info {
  background-color: #eff6ff;
  border-left: 4px solid var(--info);
}

.notification-icon {
  margin-right: 12px;
  font-size: 20px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: var(--gray-600);
}

.notification-close {
  color: var(--gray-500);
  cursor: pointer;
  background: none;
  border: none;
  font-size: 16px;
  padding: 0;
  margin-left: 12px;
}

/* 鍔犺浇涓粍浠?*/
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(14, 165, 233, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-600);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-left: 12px;
  font-size: 14px;
  color: var(--gray-600);
}

/* 绌虹姸鎬佺粍浠?*/
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--gray-400);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-800);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: var(--gray-500);
  margin-bottom: 16px;
  max-width: 300px;
}

/* 鍝嶅簲寮忚皟鏁?*/
@media (max-width: 1280px) {
  .sidebar {
    width: var(--sidebar-collapsed-width);
  }
  
  .sidebar .menu-title {
    display: none;
  }
  
  .header {
    left: var(--sidebar-collapsed-width);
  }
  
  .main-content {
    margin-left: var(--sidebar-collapsed-width);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: var(--sidebar-width);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar .menu-title {
    display: block;
  }
  
  .header {
    left: 0;
  }
  
  .main-content {
    margin-left: 0;
    padding: 16px;
  }
  
  .filter-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-item {
    width: 100%;
  }
  
  .filter-actions {
    width: 100%;
    margin-top: 12px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .drawer {
    width: 100%;
  }
}

/* ==================== 棰濆鐨勫竷灞€浼樺寲 ==================== */

/* 绛涢€夎〃鍗曠殑鍝嶅簲寮忓竷灞€ */
.filter-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  align-items: end;
  margin-bottom: var(--spacing-2xl);
}

.filter-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  grid-column: -1;
}

/* 浠锋牸鑼冨洿杈撳叆缁?*/
.price-range-inputs {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.price-range-separator {
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

/* 琛ㄦ牸鍝嶅簲寮忔粴鍔?*/
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.table {
  width: 100%;
  min-width: 800px; /* 纭繚琛ㄦ牸鏈€灏忓搴?*/
  border-collapse: collapse;
}

/* 鍥捐〃瀹瑰櫒浼樺寲 */
.chart-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-2xl);
}

/* 缃戞牸甯冨眬閫氱敤绫?*/
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-4 { gap: var(--spacing-lg); }
.gap-6 { gap: var(--spacing-2xl); }
.gap-8 { gap: var(--spacing-3xl); }

/* 鏍囩鍜岀姸鎬佹牱寮?*/
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: 1;
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-info {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge-danger {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 宸ュ叿绫?*/
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }

.text-gray-500 { color: var(--text-muted); }
.text-gray-600 { color: var(--text-secondary); }

/* 鍝嶅簲寮忔柇鐐逛紭鍖?*/
@media (max-width: 1400px) {
  :root {
    --content-max-width: 1200px;
    --content-padding: 32px;
  }
}

@media (max-width: 1200px) {
  :root {
    --content-max-width: 1000px;
    --content-padding: 24px;
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  :root {
    --content-padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .filter-form {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .filter-actions {
    grid-column: auto;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }
  
  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }
}



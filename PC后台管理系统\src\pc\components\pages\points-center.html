<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分中心 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../styles/unified-admin-styles.css">
    <style>
        .points-center {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .points-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .points-balance {
            font-size: 3rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .points-level {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .tab-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .tab-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-btn {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: white;
            color: #667eea;
            font-weight: 600;
            border-bottom: 2px solid #667eea;
        }

        .tab-content {
            display: none;
            padding: 24px;
        }

        .tab-content.active {
            display: block;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-info {
            flex: 1;
        }

        .transaction-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .transaction-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .transaction-amount {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .points-positive {
            color: #28a745;
        }

        .points-negative {
            color: #dc3545;
        }

        .share-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .share-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .share-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }

        .share-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .share-stats {
            display: flex;
            gap: 20px;
            margin: 12px 0;
        }

        .share-stat {
            text-align: center;
        }

        .share-stat-number {
            font-weight: bold;
            color: #667eea;
        }

        .share-stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-outline {
            background: none;
            border: 1px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .invite-form {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="points-center">
        <!-- 积分概览头部 -->
        <div class="points-header">
            <h1>我的积分中心</h1>
            <div class="points-balance" id="currentBalance">0</div>
            <div>当前积分余额</div>
            <div class="points-level" id="userLevel">普通用户</div>
            <div class="progress-bar" style="max-width: 300px; margin: 16px auto;">
                <div class="progress-fill" id="levelProgress" style="width: 0%"></div>
            </div>
            <div style="font-size: 0.9rem; margin-top: 8px;" id="nextLevelInfo">距离下一级还需 1000 积分</div>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalEarned">0</div>
                <div class="stat-label">累计获得</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSpent">0</div>
                <div class="stat-label">累计消费</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalShares">0</div>
                <div class="stat-label">总分享数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalInvites">0</div>
                <div class="stat-label">邀请成功</div>
            </div>
        </div>

        <!-- 标签页容器 -->
        <div class="tab-container">
            <div class="tab-nav">
                <button class="tab-btn active" onclick="switchTab('transactions')">积分明细</button>
                <button class="tab-btn" onclick="switchTab('shares')">我的分享</button>
                <button class="tab-btn" onclick="switchTab('invitations')">邀请好友</button>
                <button class="tab-btn" onclick="switchTab('rewards')">积分兑换</button>
            </div>

            <!-- 积分明细 -->
            <div id="transactions" class="tab-content active">
                <div id="transactionsList"></div>
                <div id="transactionsPagination" class="pagination"></div>
            </div>

            <!-- 我的分享 -->
            <div id="shares" class="tab-content">
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="showShareDialog()">创建新分享</button>
                </div>
                <div id="sharesList"></div>
                <div id="sharesPagination" class="pagination"></div>
            </div>

            <!-- 邀请好友 -->
            <div id="invitations" class="tab-content">
                <div class="invite-form">
                    <h3>邀请新用户</h3>
                    <div class="form-group">
                        <label class="form-label">手机号或邮箱</label>
                        <input type="text" class="form-control" id="inviteeContact" placeholder="请输入手机号或邮箱">
                    </div>
                    <div class="form-group">
                        <label class="form-label">邀请类型</label>
                        <select class="form-control" id="invitationType">
                            <option value="user">普通用户</option>
                            <option value="designer">设计师</option>
                            <option value="constructor">施工方</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="createInvitation()">发送邀请</button>
                </div>
                <div id="invitationsList"></div>
                <div id="invitationsPagination" class="pagination"></div>
            </div>

            <!-- 积分兑换 -->
            <div id="rewards" class="tab-content">
                <div id="rewardsList"></div>
            </div>
        </div>
    </div>

    <!-- 分享对话框 -->
    <div id="shareDialog" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 30px; max-width: 500px; width: 90%;">
            <h3>创建分享链接</h3>
            <div class="form-group">
                <label class="form-label">分享类型</label>
                <select class="form-control" id="shareContentType">
                    <option value="knowledge">知识库</option>
                    <option value="project">项目</option>
                    <option value="product">产品</option>
                    <option value="app">应用推广</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">分享平台</label>
                <select class="form-control" id="sharePlatform">
                    <option value="wechat">微信</option>
                    <option value="moments">朋友圈</option>
                    <option value="qq">QQ</option>
                    <option value="weibo">微博</option>
                    <option value="system">系统</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">分享标题</label>
                <input type="text" class="form-control" id="shareTitle" placeholder="请输入分享标题">
            </div>
            <div style="display: flex; gap: 12px; margin-top: 20px;">
                <button class="btn btn-primary" onclick="createShare()">创建分享</button>
                <button class="btn btn-outline" onclick="closeShareDialog()">取消</button>
            </div>
        </div>
    </div>

    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 全局变量
        let currentPage = {
            transactions: 1,
            shares: 1,
            invitations: 1
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
            loadTransactions();
        });

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 显示对应内容
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');

            // 加载对应数据
            switch(tabName) {
                case 'transactions':
                    loadTransactions();
                    break;
                case 'shares':
                    loadShares();
                    break;
                case 'invitations':
                    loadInvitations();
                    break;
                case 'rewards':
                    loadRewards();
                    break;
            }
        }

        // 加载用户资料
        async function loadUserProfile() {
            try {
                const response = await fetch('/api/v1/points/profile', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateProfileUI(data.data);
                }
            } catch (error) {
                console.error('加载用户资料失败:', error);
            }
        }

        // 更新资料界面
        function updateProfileUI(data) {
            document.getElementById('currentBalance').textContent = data.points.current_balance;
            document.getElementById('userLevel').textContent = data.level?.level_name || '普通用户';
            document.getElementById('totalEarned').textContent = data.stats?.total_earned || 0;
            document.getElementById('totalSpent').textContent = data.stats?.total_spent || 0;
            document.getElementById('totalShares').textContent = data.stats?.total_shares || 0;
            document.getElementById('totalInvites').textContent = data.stats?.total_invites || 0;

            // 更新等级进度
            if (data.points.next_level_points) {
                const progress = (data.points.current_balance / data.points.next_level_points) * 100;
                document.getElementById('levelProgress').style.width = Math.min(progress, 100) + '%';
                document.getElementById('nextLevelInfo').textContent = 
                    `距离下一级还需 ${data.points.next_level_points - data.points.current_balance} 积分`;
            }
        }

        // 加载积分明细
        async function loadTransactions(page = 1) {
            const container = document.getElementById('transactionsList');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/v1/points/transactions?page=${page}&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    renderTransactions(data.data.transactions);
                    renderPagination('transactionsPagination', data.data, 'transactions');
                } else {
                    container.innerHTML = '<div class="error-message">加载失败，请重试</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="error-message">网络错误，请重试</div>';
            }
        }

        // 渲染积分明细
        function renderTransactions(transactions) {
            const container = document.getElementById('transactionsList');
            
            if (transactions.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无积分记录</div>';
                return;
            }

            const html = transactions.map(transaction => `
                <div class="transaction-item">
                    <div class="transaction-info">
                        <div class="transaction-title">${transaction.description}</div>
                        <div class="transaction-meta">
                            ${new Date(transaction.created_at).toLocaleString()} | 
                            ${transaction.source_type} | 
                            ${transaction.transaction_type}
                        </div>
                    </div>
                    <div class="transaction-amount ${transaction.points_amount > 0 ? 'points-positive' : 'points-negative'}">
                        ${transaction.points_amount > 0 ? '+' : ''}${transaction.points_amount}
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 加载分享列表
        async function loadShares(page = 1) {
            const container = document.getElementById('sharesList');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/v1/shares/my?page=${page}&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    renderShares(data.data.items);
                    renderPagination('sharesPagination', data.data, 'shares');
                } else {
                    container.innerHTML = '<div class="error-message">加载失败，请重试</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="error-message">网络错误，请重试</div>';
            }
        }

        // 渲染分享列表
        function renderShares(shares) {
            const container = document.getElementById('sharesList');
            
            if (shares.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无分享记录</div>';
                return;
            }

            const html = shares.map(share => `
                <div class="share-card">
                    <div class="share-header">
                        <div>
                            <div class="share-title">${share.content_title || '未命名分享'}</div>
                            <div style="font-size: 0.85rem; color: #6c757d;">
                                ${share.content_type} | ${share.platform} | 
                                ${new Date(share.created_at).toLocaleDateString()}
                            </div>
                        </div>
                        <div>
                            <span class="points-positive">+${share.total_earned_points}积分</span>
                        </div>
                    </div>
                    <div class="share-stats">
                        <div class="share-stat">
                            <div class="share-stat-number">${share.view_count}</div>
                            <div class="share-stat-label">浏览</div>
                        </div>
                        <div class="share-stat">
                            <div class="share-stat-number">${share.click_count}</div>
                            <div class="share-stat-label">点击</div>
                        </div>
                        <div class="share-stat">
                            <div class="share-stat-number">${share.register_count}</div>
                            <div class="share-stat-label">注册</div>
                        </div>
                        <div class="share-stat">
                            <div class="share-stat-number">${share.order_count}</div>
                            <div class="share-stat-label">下单</div>
                        </div>
                    </div>
                    <div style="margin-top: 12px;">
                        <button class="btn btn-outline" onclick="copyShareUrl('${share.share_url}')">复制链接</button>
                        <button class="btn btn-outline" onclick="viewQRCode('${share.qr_code_url}')">查看二维码</button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 加载邀请列表
        async function loadInvitations(page = 1) {
            const container = document.getElementById('invitationsList');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/v1/invitations/my?page=${page}&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    renderInvitations(data.data.items);
                    renderPagination('invitationsPagination', data.data, 'invitations');
                } else {
                    container.innerHTML = '<div class="error-message">加载失败，请重试</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="error-message">网络错误，请重试</div>';
            }
        }

        // 渲染邀请列表
        function renderInvitations(invitations) {
            const container = document.getElementById('invitationsList');
            
            if (invitations.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无邀请记录</div>';
                return;
            }

            const statusMap = {
                'pending': '待处理',
                'registered': '已注册',
                'completed': '已完成',
                'expired': '已过期'
            };

            const html = invitations.map(invitation => `
                <div class="transaction-item">
                    <div class="transaction-info">
                        <div class="transaction-title">
                            ${invitation.invitee_phone || invitation.invitee_email} 
                            (${invitation.invitation_type})
                        </div>
                        <div class="transaction-meta">
                            ${new Date(invitation.created_at).toLocaleDateString()} | 
                            状态: ${statusMap[invitation.status]} |
                            邀请码: ${invitation.invitation_code}
                        </div>
                    </div>
                    <div class="transaction-amount points-positive">
                        +${invitation.total_earned_points}积分
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 渲染分页
        function renderPagination(containerId, data, type) {
            const container = document.getElementById(containerId);
            const totalPages = Math.ceil(data.total / data.limit);
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = '';
            for (let i = 1; i <= totalPages; i++) {
                html += `<button ${i === data.page ? 'class="active"' : ''} 
                         onclick="loadPage('${type}', ${i})">${i}</button>`;
            }

            container.innerHTML = html;
        }

        // 加载指定页
        function loadPage(type, page) {
            currentPage[type] = page;
            switch(type) {
                case 'transactions':
                    loadTransactions(page);
                    break;
                case 'shares':
                    loadShares(page);
                    break;
                case 'invitations':
                    loadInvitations(page);
                    break;
            }
        }

        // 显示分享对话框
        function showShareDialog() {
            document.getElementById('shareDialog').style.display = 'block';
        }

        // 关闭分享对话框
        function closeShareDialog() {
            document.getElementById('shareDialog').style.display = 'none';
        }

        // 创建分享
        async function createShare() {
            const contentType = document.getElementById('shareContentType').value;
            const platform = document.getElementById('sharePlatform').value;
            const title = document.getElementById('shareTitle').value;

            if (!title.trim()) {
                showToast('请输入分享标题', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/v1/shares/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        content_type: contentType,
                        platform: platform,
                        content_title: title
                    })
                });

                if (response.ok) {
                    showToast('分享创建成功！', 'success');
                    closeShareDialog();
                    loadShares(); // 刷新分享列表
                } else {
                    const error = await response.json();
                    showToast('创建失败：' + error.message, 'error');
                }
            } catch (error) {
                showToast('网络错误，请重试', 'error');
            }
        }

        // 创建邀请
        async function createInvitation() {
            const contact = document.getElementById('inviteeContact').value;
            const type = document.getElementById('invitationType').value;

            if (!contact.trim()) {
                showToast('请输入手机号或邮箱', 'warning');
                return;
            }

            const isEmail = contact.includes('@');
            const requestBody = {
                invitation_type: type
            };

            if (isEmail) {
                requestBody.invitee_email = contact;
            } else {
                requestBody.invitee_phone = contact;
            }

            try {
                const response = await fetch('/api/v1/invitations/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const result = await response.json();
                    showToast(`邀请发送成功！邀请码：${result.data.invitation_code}`, 'success');
                    document.getElementById('inviteeContact').value = '';
                    loadInvitations(); // 刷新邀请列表
                } else {
                    const error = await response.json();
                    showToast('发送失败：' + error.message, 'error');
                }
            } catch (error) {
                showToast('网络错误，请重试', 'error');
            }
        }

        // 复制分享链接
        function copyShareUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showToast('链接已复制到剪贴板', 'success');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('链接已复制到剪贴板', 'success');
            });
        }

        // 查看二维码
        function viewQRCode(qrUrl) {
            if (qrUrl) {
                window.open(qrUrl, '_blank');
            } else {
                showToast('二维码暂未生成', 'warning');
            }
        }

        // 加载积分商城（占位）
        async function loadRewards() {
            const container = document.getElementById('rewardsList');
            container.innerHTML = `
                <div class="empty-state">
                    <h3>积分商城</h3>
                    <p>敬请期待，积分兑换功能即将上线！</p>
                    <div style="margin-top: 20px;">
                        <div>可兑换物品包括：</div>
                        <ul style="text-align: left; display: inline-block; margin-top: 10px;">
                            <li>优惠券（50-200元）</li>
                            <li>免费设计咨询服务</li>
                            <li>VIP会员权益</li>
                            <li>智能家居产品</li>
                        </ul>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
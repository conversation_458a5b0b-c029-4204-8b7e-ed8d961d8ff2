/**
 * 权限管理常量配置
 * 版本: v1.0
 * 创建时间: 2025-07-15
 */

import React from 'react';
import {
  UserOutlined,
  HomeOutlined,
  TeamOutlined,
  ToolOutlined,
  BulbOutlined,
  BuildOutlined,
  SettingOutlined,
  DesktopOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  CommentOutlined,
  ShareAltOutlined,
  DollarOutlined
} from '@ant-design/icons';
import type {
  SmartHomeRole,
  PermissionModule,
  PermissionOperation,
  Role,
  Module,
  PermissionConstants
} from './types';

// 智能家居角色定义
export const SMART_HOME_ROLES: Record<SmartHomeRole, Role> = {
  OWNER: {
    name: '业主/空间所有者',
    icon: <HomeOutlined style={{ color: '#2563eb' }} />,
    color: '#2563eb',
    description: '拥有项目空间的完全控制权，可以邀请其他成员，管理项目进度，审批设计方案和施工计划。',
    permissions: {
      project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE'],
      design: ['VIEW', 'COMMENT', 'APPROVE'],
      construction: ['VIEW', 'COMMENT', 'APPROVE'],
      cost: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE'],
      files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
      comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
      system: ['VIEW', 'CONFIGURE']
    }
  },
  FAMILY_MEMBER: {
    name: '家庭成员',
    icon: <TeamOutlined style={{ color: '#059669' }} />,
    color: '#059669',
    description: '由业主邀请，权限由业主设定和控制，可以参与项目讨论和进度查看。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  },
  HOME_DESIGNER: {
    name: '家装设计师',
    icon: <ToolOutlined style={{ color: '#dc2626' }} />,
    color: '#dc2626',
    description: '被邀请进项目空间参与设计工作，负责方案设计和图纸制作。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  },
  SMART_HOME_DESIGNER: {
    name: '智能家居设计师',
    icon: <BulbOutlined style={{ color: '#7c3aed' }} />,
    color: '#7c3aed',
    description: '不需要被邀请，可以在设计模块中看到用户所有的项目文件，提供专业的智能家居解决方案。',
    permissions: {
      project: ['VIEW', 'EDIT', 'COMMENT'],
      design: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      cost: ['VIEW'],
      files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
      comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
      system: ['VIEW', 'EDIT', 'CONFIGURE']
    }
  },
  CONSTRUCTOR: {
    name: '施工人员',
    icon: <BuildOutlined style={{ color: '#ea580c' }} />,
    color: '#ea580c',
    description: '被邀请参与施工阶段，由用户决定可以看到的内容，负责施工进度更新和现场反馈。',
    permissions: {
      project: ['VIEW', 'COMMENT'],
      design: ['VIEW'],
      construction: ['VIEW', 'UPDATE', 'COMMENT'],
      files: ['VIEW', 'UPLOAD'],
      comments: ['VIEW', 'CREATE', 'EDIT'],
      marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
    }
  }
};

// 权限模块定义
export const PERMISSION_MODULES: Record<PermissionModule, Module> = {
  project: {
    name: '项目空间',
    icon: <HomeOutlined />,
    color: '#2563eb',
    subModules: {
      project_overview: { name: '项目概览', operations: ['VIEW', 'EDIT', 'COMMENT'] },
      member_management: { name: '成员管理', operations: ['VIEW', 'INVITE', 'MANAGE'] },
      project_progress: { name: '项目进度', operations: ['VIEW', 'EDIT', 'COMMENT'] },
      project_settings: { name: '项目设置', operations: ['VIEW', 'CONFIGURE'] }
    }
  },
  design: {
    name: '设计方案',
    icon: <DesktopOutlined />,
    color: '#dc2626',
    subModules: {
      requirement_collection: { name: '需求收集', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      design_schemes: { name: '方案列表', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      design_drawings: { name: '设计图纸', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      effect_renderings: { name: '3D效果图', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      smart_home_design: { name: '智能家居设计', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      lighting_design: { name: '灯光设计', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      material_list: { name: '材料清单', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      modification_records: { name: '修改记录', operations: ['VIEW', 'CREATE', 'COMMENT'] }
    }
  },
  construction: {
    name: '施工管理',
    icon: <BuildOutlined />,
    color: '#ea580c',
    subModules: {
      construction_plan: { name: '施工计划', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      progress_update: { name: '进度更新', operations: ['VIEW', 'UPDATE', 'COMMENT'] },
      site_photos: { name: '现场照片', operations: ['VIEW', 'UPLOAD', 'COMMENT'] },
      issue_tracking: { name: '问题记录', operations: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'] },
      quality_inspection: { name: '质量检查', operations: ['VIEW', 'INSPECT', 'COMMENT'] },
      acceptance_management: { name: '验收管理', operations: ['VIEW', 'APPROVE', 'COMMENT'] }
    }
  },
  cost: {
    name: '预算成本',
    icon: <DollarOutlined />,
    color: '#059669',
    subModules: {
      budget_preparation: { name: '预算编制', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      quotation_management: { name: '报价管理', operations: ['VIEW', 'CREATE', 'EDIT', 'APPROVE'] },
      payment_plan: { name: '付款计划', operations: ['VIEW', 'EDIT', 'APPROVE'] },
      cost_analysis: { name: '成本分析', operations: ['VIEW', 'ANALYZE', 'COMMENT'] }
    }
  },
  files: {
    name: '文件管理',
    icon: <FileTextOutlined />,
    color: '#7c3aed',
    subModules: {
      document_management: { name: '文档管理', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'] },
      image_management: { name: '图片管理', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'] },
      contract_files: { name: '合同文件', operations: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'MANAGE'] },
      template_files: { name: '模板文件', operations: ['VIEW', 'DOWNLOAD', 'MANAGE'] }
    }
  },
  comments: {
    name: '评论互动',
    icon: <CommentOutlined />,
    color: '#0891b2',
    subModules: {
      design_comments: { name: '设计评论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      construction_comments: { name: '施工评论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      team_discussion: { name: '团队讨论', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      comment_notifications: { name: '评论通知', operations: ['VIEW', 'MANAGE'] }
    }
  },
  marketing: {
    name: '营销分享',
    icon: <ShareAltOutlined />,
    color: '#be185d',
    subModules: {
      share_management: { name: '分享管理', operations: ['VIEW', 'SHARE', 'MANAGE'] },
      reward_management: { name: '奖励管理', operations: ['VIEW', 'EARN_REWARDS', 'MANAGE'] },
      share_statistics: { name: '分享统计', operations: ['VIEW', 'VIEW_STATS', 'ANALYZE'] },
      share_templates: { name: '分享模板', operations: ['VIEW', 'CREATE', 'EDIT'] }
    }
  },
  system: {
    name: '系统管理',
    icon: <SettingOutlined />,
    color: '#374151',
    subModules: {
      system_settings: { name: '系统设置', operations: ['VIEW', 'EDIT', 'CONFIGURE'] },
      user_management: { name: '用户管理', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      role_management: { name: '角色管理', operations: ['VIEW', 'CREATE', 'EDIT', 'DELETE'] },
      system_logs: { name: '系统日志', operations: ['VIEW', 'ANALYZE'] }
    }
  }
};

// 操作权限中文映射
export const OPERATION_LABELS: Record<PermissionOperation, string> = {
  VIEW: '查看',
  CREATE: '创建',
  EDIT: '编辑',
  DELETE: '删除',
  COMMENT: '评论',
  APPROVE: '审批',
  INVITE: '邀请',
  MANAGE: '管理',
  CONFIGURE: '配置',
  UPDATE: '更新',
  UPLOAD: '上传',
  DOWNLOAD: '下载',
  INSPECT: '巡检',
  ANALYZE: '分析',
  SHARE: '分享',
  EARN_REWARDS: '获得奖励',
  VIEW_STATS: '查看统计'
};

// 角色颜色映射
export const ROLE_COLORS: Record<SmartHomeRole, string> = {
  OWNER: '#2563eb',
  FAMILY_MEMBER: '#059669',
  HOME_DESIGNER: '#dc2626',
  SMART_HOME_DESIGNER: '#7c3aed',
  CONSTRUCTOR: '#ea580c'
};

// 模块颜色映射
export const MODULE_COLORS: Record<PermissionModule, string> = {
  project: '#2563eb',
  design: '#dc2626',
  construction: '#ea580c',
  cost: '#059669',
  files: '#7c3aed',
  comments: '#0891b2',
  marketing: '#be185d',
  system: '#374151'
};

// 模块图标映射
export const MODULE_ICONS: Record<PermissionModule, React.ReactNode> = {
  project: <HomeOutlined />,
  design: <DesktopOutlined />,
  construction: <BuildOutlined />,
  cost: <DollarOutlined />,
  files: <FileTextOutlined />,
  comments: <CommentOutlined />,
  marketing: <ShareAltOutlined />,
  system: <SettingOutlined />
};

// 权限常量集合
export const PERMISSION_CONSTANTS: PermissionConstants = {
  ROLES: SMART_HOME_ROLES,
  MODULES: PERMISSION_MODULES,
  OPERATIONS: OPERATION_LABELS,
  COLORS: ROLE_COLORS,
  ICONS: MODULE_ICONS
};

// 默认权限配置
export const DEFAULT_PERMISSIONS: Record<SmartHomeRole, Record<PermissionModule, PermissionOperation[]>> = {
  OWNER: {
    project: ['VIEW', 'EDIT', 'COMMENT', 'INVITE', 'MANAGE', 'CONFIGURE'],
    design: ['VIEW', 'COMMENT', 'APPROVE'],
    construction: ['VIEW', 'COMMENT', 'APPROVE'],
    cost: ['VIEW', 'EDIT', 'APPROVE', 'ANALYZE'],
    files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
    comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
    system: ['VIEW', 'CONFIGURE']
  },
  FAMILY_MEMBER: {
    project: ['VIEW', 'COMMENT'],
    design: ['VIEW', 'COMMENT'],
    construction: ['VIEW', 'UPDATE', 'COMMENT'],
    cost: ['VIEW'],
    files: ['VIEW', 'UPLOAD'],
    comments: ['VIEW', 'CREATE', 'EDIT'],
    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
  },
  HOME_DESIGNER: {
    project: ['VIEW', 'COMMENT'],
    design: ['VIEW', 'CREATE', 'EDIT', 'COMMENT'],
    construction: ['VIEW', 'UPDATE', 'COMMENT'],
    cost: ['VIEW'],
    files: ['VIEW', 'UPLOAD'],
    comments: ['VIEW', 'CREATE', 'EDIT'],
    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
  },
  SMART_HOME_DESIGNER: {
    project: ['VIEW', 'EDIT', 'COMMENT'],
    design: ['VIEW', 'CREATE', 'EDIT', 'DELETE', 'APPROVE', 'COMMENT'],
    construction: ['VIEW', 'UPDATE', 'COMMENT'],
    cost: ['VIEW'],
    files: ['VIEW', 'UPLOAD', 'DOWNLOAD', 'DELETE'],
    comments: ['VIEW', 'CREATE', 'EDIT', 'DELETE'],
    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS', 'VIEW_STATS'],
    system: ['VIEW', 'EDIT', 'CONFIGURE']
  },
  CONSTRUCTOR: {
    project: ['VIEW', 'COMMENT'],
    design: ['VIEW'],
    construction: ['VIEW', 'UPDATE', 'COMMENT'],
    files: ['VIEW', 'UPLOAD'],
    comments: ['VIEW', 'CREATE', 'EDIT'],
    marketing: ['VIEW', 'SHARE', 'EARN_REWARDS']
  }
};

// 权限配置选项
export const PERMISSION_CONFIG = {
  // 是否启用角色层级
  enableRoleHierarchy: true,
  // 是否启用权限继承
  enablePermissionInheritance: true,
  // 是否启用项目级权限
  enableProjectLevelPermissions: true,
  // 是否启用权限审计
  enablePermissionAudit: true,
  // 默认会话超时时间（分钟）
  defaultSessionTimeout: 120,
  // 最大邀请有效期（小时）
  maxInvitationDuration: 72,
  // 是否启用通知
  enableNotifications: true
};

// 导出所有常量
export {
  SMART_HOME_ROLES,
  PERMISSION_MODULES,
  OPERATION_LABELS,
  ROLE_COLORS,
  MODULE_COLORS,
  MODULE_ICONS,
  DEFAULT_PERMISSIONS,
  PERMISSION_CONFIG
};

/**
 * 权限管理上下文组件
 * 提供统一的权限检查和用户状态管理
 * 版本: v1.0
 * 创建时间: 2025-07-01
 */

class AuthContext {
    constructor() {
        this.authAPI = window.authAPI
        this.userProfileAPI = window.userProfileAPI
        
        // 权限状态
        this.user = null
        this.permissions = []
        this.roles = []
        this.loading = true
        this.initialized = false
        
        // 事件监听器
        this.listeners = {
            'auth-changed': [],
            'permissions-changed': [],
            'user-updated': [],
            'loading-changed': []
        }
        
        // 权限定义
        this.PERMISSIONS = {
            // 用户管理权限
            'users.read': '查看用户',
            'users.write': '编辑用户',
            'users.delete': '删除用户',
            'users.manage': '管理用户',
            
            // 项目管理权限
            'projects.read': '查看项目',
            'projects.write': '编辑项目',
            'projects.delete': '删除项目',
            'projects.manage': '管理项目',
            
            // 订单管理权限
            'orders.read': '查看订单',
            'orders.write': '编辑订单',
            'orders.delete': '删除订单',
            'orders.manage': '管理订单',
            
            // 系统管理权限
            'system.read': '查看系统信息',
            'system.write': '修改系统设置',
            'system.manage': '系统管理',
            
            // 报表权限
            'reports.read': '查看报表',
            'reports.export': '导出报表',
            'reports.manage': '管理报表'
        }
        
        // 角色权限映射
        this.ROLE_PERMISSIONS = {
            'super_admin': Object.keys(this.PERMISSIONS), // 所有权限
            'admin': [
                'users.read', 'users.write', 'users.manage',
                'projects.read', 'projects.write', 'projects.manage',
                'orders.read', 'orders.write', 'orders.manage',
                'system.read', 'system.write',
                'reports.read', 'reports.export'
            ],
            'designer': [
                'projects.read', 'projects.write',
                'orders.read',
                'reports.read'
            ],
            'contractor': [
                'projects.read',
                'orders.read', 'orders.write',
                'reports.read'
            ],
            'supplier': [
                'orders.read',
                'reports.read'
            ],
            'customer': [
                'projects.read',
                'orders.read'
            ],
            'vip_customer': [
                'projects.read',
                'orders.read',
                'reports.read'
            ]
        }
        
        console.log('✅ 权限管理上下文已初始化')
    }

    /**
     * 初始化权限上下文
     */
    async initialize() {
        if (this.initialized) {
            return this.user
        }
        
        try {
            this.setLoading(true)
            console.log('🔄 初始化权限上下文...')
            
            // 检查是否已登录
            if (!this.authAPI.isLoggedIn()) {
                console.log('⚠️ 用户未登录')
                this.setUser(null)
                this.setLoading(false)
                this.initialized = true
                return null
            }
            
            // 获取用户信息
            const userInfo = await this.authAPI.getCurrentUser()
            this.setUser(userInfo)
            
            console.log('✅ 权限上下文初始化完成:', userInfo.username)
            this.setLoading(false)
            this.initialized = true
            
            return userInfo
            
        } catch (error) {
            console.error('❌ 权限上下文初始化失败:', error)
            this.setUser(null)
            this.setLoading(false)
            this.initialized = true
            throw error
        }
    }

    /**
     * 设置用户信息
     */
    setUser(userInfo) {
        const oldUser = this.user
        this.user = userInfo
        
        if (userInfo) {
            this.roles = userInfo.roles || []
            this.permissions = this.calculateUserPermissions(this.roles)
        } else {
            this.roles = []
            this.permissions = []
        }
        
        // 触发事件
        this.emit('auth-changed', { user: this.user, oldUser })
        this.emit('permissions-changed', { permissions: this.permissions, roles: this.roles })
        
        console.log('🔄 用户状态已更新:', this.user?.username || '未登录')
    }

    /**
     * 设置加载状态
     */
    setLoading(loading) {
        if (this.loading !== loading) {
            this.loading = loading
            this.emit('loading-changed', { loading })
        }
    }

    /**
     * 计算用户权限
     */
    calculateUserPermissions(roles) {
        if (!roles || roles.length === 0) {
            return []
        }
        
        const permissions = new Set()
        
        roles.forEach(role => {
            const rolePermissions = this.ROLE_PERMISSIONS[role] || []
            rolePermissions.forEach(permission => {
                permissions.add(permission)
            })
        })
        
        return Array.from(permissions)
    }

    /**
     * 检查是否已登录
     */
    isAuthenticated() {
        return !!this.user && this.authAPI.isLoggedIn()
    }

    /**
     * 检查是否有指定角色
     */
    hasRole(role) {
        if (!this.isAuthenticated()) {
            return false
        }
        
        if (Array.isArray(role)) {
            return role.some(r => this.roles.includes(r))
        }
        
        return this.roles.includes(role)
    }

    /**
     * 检查是否有指定权限
     */
    hasPermission(permission) {
        if (!this.isAuthenticated()) {
            return false
        }
        
        if (Array.isArray(permission)) {
            return permission.every(p => this.permissions.includes(p))
        }
        
        return this.permissions.includes(permission)
    }

    /**
     * 检查是否有任一权限
     */
    hasAnyPermission(permissions) {
        if (!this.isAuthenticated()) {
            return false
        }
        
        if (!Array.isArray(permissions)) {
            return this.hasPermission(permissions)
        }
        
        return permissions.some(p => this.permissions.includes(p))
    }

    /**
     * 检查是否为管理员
     */
    isAdmin() {
        return this.hasRole(['super_admin', 'admin'])
    }

    /**
     * 检查是否为超级管理员
     */
    isSuperAdmin() {
        return this.hasRole('super_admin')
    }

    /**
     * 获取用户显示名称
     */
    getUserDisplayName() {
        if (!this.user) {
            return '未登录'
        }
        
        return this.user.real_name || this.user.nickname || this.user.username
    }

    /**
     * 获取角色显示名称
     */
    getRoleDisplayNames() {
        const roleMap = {
            'super_admin': '超级管理员',
            'admin': '系统管理员',
            'designer': '设计师',
            'contractor': '施工方',
            'supplier': '供应商',
            'customer': '客户',
            'vip_customer': 'VIP客户'
        }
        
        return this.roles.map(role => roleMap[role] || role)
    }

    /**
     * 获取权限显示名称
     */
    getPermissionDisplayNames() {
        return this.permissions.map(permission => this.PERMISSIONS[permission] || permission)
    }

    /**
     * 登录
     */
    async login(username, password, remember = false) {
        try {
            this.setLoading(true)
            
            const response = await this.authAPI.login(username, password, remember)
            
            if (response.success) {
                this.setUser(response.data.user)
                console.log('✅ 登录成功:', response.data.user.username)
                return response
            } else {
                throw new Error(response.error?.message || '登录失败')
            }
        } catch (error) {
            console.error('❌ 登录失败:', error)
            throw error
        } finally {
            this.setLoading(false)
        }
    }

    /**
     * 登出
     */
    async logout() {
        try {
            this.setLoading(true)
            
            await this.authAPI.logout()
            this.setUser(null)
            this.initialized = false
            
            console.log('✅ 登出成功')
        } catch (error) {
            console.error('❌ 登出失败:', error)
            // 即使API调用失败，也要清除本地状态
            this.setUser(null)
            this.initialized = false
            throw error
        } finally {
            this.setLoading(false)
        }
    }

    /**
     * 刷新用户信息
     */
    async refreshUser() {
        try {
            if (!this.isAuthenticated()) {
                return null
            }
            
            const userInfo = await this.authAPI.getCurrentUser()
            this.setUser(userInfo)
            this.emit('user-updated', { user: userInfo })
            
            console.log('✅ 用户信息已刷新')
            return userInfo
        } catch (error) {
            console.error('❌ 刷新用户信息失败:', error)
            throw error
        }
    }

    /**
     * 添加事件监听器
     */
    addEventListener(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback)
        }
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(event, callback) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback)
            if (index > -1) {
                this.listeners[event].splice(index, 1)
            }
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data)
                } catch (error) {
                    console.error(`❌ 事件监听器错误 [${event}]:`, error)
                }
            })
        }
    }

    /**
     * 获取当前状态
     */
    getState() {
        return {
            user: this.user,
            roles: this.roles,
            permissions: this.permissions,
            loading: this.loading,
            authenticated: this.isAuthenticated(),
            initialized: this.initialized
        }
    }

    /**
     * 权限检查辅助方法
     */
    can(permission) {
        return this.hasPermission(permission)
    }

    /**
     * 角色检查辅助方法
     */
    is(role) {
        return this.hasRole(role)
    }
}

// 创建全局权限上下文实例
if (typeof window !== 'undefined') {
    window.authContext = new AuthContext()
}

// 导出权限上下文类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthContext
}

console.log('✅ 权限管理上下文组件已加载')

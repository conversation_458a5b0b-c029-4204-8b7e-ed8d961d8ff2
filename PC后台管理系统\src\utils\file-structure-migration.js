/**
 * 项目文件结构重组脚本
 * 将现有的混乱文件结构重新组织为标准的前端项目结构
 */

const fs = require('fs').promises;
const path = require('path');

class FileStructureMigration {
    constructor() {
        this.rootPath = path.join(__dirname, '../..');
        this.newStructure = {
            'src/pages': '页面文件',
            'src/assets/js': 'JavaScript文件',
            'src/assets/css': 'CSS样式文件',
            'src/assets/images': '图片资源',
            'src/components': '可复用组件',
            'src/utils': '工具函数',
            'src/backend/api': 'API接口',
            'src/backend/database': '数据库文件',
            'src/backend/config': '配置文件',
            'docs': '文档文件'
        };
        
        this.fileMapping = {
            // HTML页面文件
            'html': 'src/pages',
            // JavaScript文件
            'js': 'src/assets/js',
            // CSS文件
            'css': 'src/assets/css',
            // 图片文件
            'jpg|jpeg|png|gif|svg|webp': 'src/assets/images',
            // 文档文件
            'md|txt|doc|docx|pdf': 'docs',
            // 配置文件
            'json|yaml|yml|env': 'src/backend/config'
        };
    }

    /**
     * 执行文件结构迁移
     */
    async migrate() {
        try {
            console.log('开始文件结构迁移...');
            
            // 1. 创建新的目录结构
            await this.createDirectories();
            
            // 2. 分析现有文件
            const fileList = await this.analyzeCurrentFiles();
            
            // 3. 生成迁移计划
            const migrationPlan = this.generateMigrationPlan(fileList);
            
            // 4. 执行文件迁移
            await this.executeFileMigration(migrationPlan);
            
            // 5. 更新文件引用路径
            await this.updateFileReferences(migrationPlan);
            
            // 6. 生成迁移报告
            await this.generateMigrationReport(migrationPlan);
            
            console.log('文件结构迁移完成！');
            
        } catch (error) {
            console.error('文件结构迁移失败:', error);
            throw error;
        }
    }

    /**
     * 创建新的目录结构
     */
    async createDirectories() {
        console.log('创建目录结构...');
        
        for (const [dirPath, description] of Object.entries(this.newStructure)) {
            const fullPath = path.join(this.rootPath, dirPath);
            
            try {
                await fs.mkdir(fullPath, { recursive: true });
                console.log(`✓ 创建目录: ${dirPath} (${description})`);
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw error;
                }
            }
        }
    }

    /**
     * 分析现有文件
     */
    async analyzeCurrentFiles() {
        console.log('分析现有文件...');
        
        const fileList = [];
        
        async function scanDirectory(dirPath, relativePath = '') {
            const items = await fs.readdir(dirPath);
            
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const relativeItemPath = path.join(relativePath, item);
                const stat = await fs.stat(itemPath);
                
                if (stat.isDirectory()) {
                    // 跳过已经是新结构的目录
                    if (!['src', 'docs', 'node_modules', '.git'].includes(item)) {
                        await scanDirectory(itemPath, relativeItemPath);
                    }
                } else {
                    fileList.push({
                        originalPath: itemPath,
                        relativePath: relativeItemPath,
                        fileName: item,
                        extension: path.extname(item).slice(1).toLowerCase(),
                        size: stat.size
                    });
                }
            }
        }
        
        await scanDirectory(this.rootPath);
        
        console.log(`发现 ${fileList.length} 个文件需要处理`);
        return fileList;
    }

    /**
     * 生成迁移计划
     */
    generateMigrationPlan(fileList) {
        console.log('生成迁移计划...');
        
        const migrationPlan = {
            moves: [],
            updates: [],
            skips: []
        };

        for (const file of fileList) {
            const targetDir = this.determineTargetDirectory(file);
            
            if (targetDir) {
                const targetPath = path.join(this.rootPath, targetDir, file.fileName);
                
                migrationPlan.moves.push({
                    source: file.originalPath,
                    target: targetPath,
                    relativePath: file.relativePath,
                    targetDir: targetDir,
                    type: this.getFileType(file.extension)
                });
            } else {
                migrationPlan.skips.push({
                    path: file.originalPath,
                    reason: '无法确定目标目录'
                });
            }
        }

        console.log(`计划迁移 ${migrationPlan.moves.length} 个文件`);
        console.log(`跳过 ${migrationPlan.skips.length} 个文件`);
        
        return migrationPlan;
    }

    /**
     * 确定目标目录
     */
    determineTargetDirectory(file) {
        // 特殊文件处理
        if (file.fileName === 'package.json' || file.fileName === 'package-lock.json') {
            return null; // 保持在根目录
        }

        // 根据文件扩展名确定目录
        for (const [extensions, targetDir] of Object.entries(this.fileMapping)) {
            const extPattern = new RegExp(`^(${extensions})$`, 'i');
            if (extPattern.test(file.extension)) {
                return targetDir;
            }
        }

        // 特殊逻辑处理
        if (file.relativePath.includes('components')) {
            return 'src/components';
        }

        if (file.relativePath.includes('api') || file.relativePath.includes('backend')) {
            return 'src/backend/api';
        }

        if (file.relativePath.includes('database') || file.relativePath.includes('sql')) {
            return 'src/backend/database';
        }

        return null;
    }

    /**
     * 获取文件类型
     */
    getFileType(extension) {
        const typeMap = {
            'html': 'page',
            'js': 'script',
            'css': 'style',
            'jpg': 'image',
            'jpeg': 'image',
            'png': 'image',
            'gif': 'image',
            'svg': 'image',
            'webp': 'image',
            'md': 'document',
            'txt': 'document',
            'json': 'config',
            'yaml': 'config',
            'yml': 'config'
        };

        return typeMap[extension] || 'other';
    }

    /**
     * 执行文件迁移
     */
    async executeFileMigration(migrationPlan) {
        console.log('执行文件迁移...');
        
        for (const move of migrationPlan.moves) {
            try {
                // 确保目标目录存在
                await fs.mkdir(path.dirname(move.target), { recursive: true });
                
                // 复制文件（而不是移动，以防出错）
                await fs.copyFile(move.source, move.target);
                
                console.log(`✓ 迁移: ${move.relativePath} → ${move.targetDir}`);
                
            } catch (error) {
                console.error(`✗ 迁移失败: ${move.relativePath}`, error.message);
                migrationPlan.updates.push({
                    file: move.relativePath,
                    error: error.message
                });
            }
        }
    }

    /**
     * 更新文件引用路径
     */
    async updateFileReferences(migrationPlan) {
        console.log('更新文件引用路径...');
        
        // 获取所有HTML和JS文件
        const filesToUpdate = migrationPlan.moves.filter(move => 
            ['html', 'js'].includes(move.type) || move.target.endsWith('.html') || move.target.endsWith('.js')
        );

        for (const file of filesToUpdate) {
            try {
                await this.updateSingleFileReferences(file.target, migrationPlan);
                console.log(`✓ 更新引用: ${path.basename(file.target)}`);
            } catch (error) {
                console.error(`✗ 更新引用失败: ${path.basename(file.target)}`, error.message);
            }
        }
    }

    /**
     * 更新单个文件的引用路径
     */
    async updateSingleFileReferences(filePath, migrationPlan) {
        let content = await fs.readFile(filePath, 'utf8');
        let updated = false;

        // 创建路径映射
        const pathMapping = {};
        for (const move of migrationPlan.moves) {
            const oldRelativePath = move.relativePath.replace(/\\/g, '/');
            const newRelativePath = path.relative(
                path.dirname(filePath),
                move.target
            ).replace(/\\/g, '/');
            
            pathMapping[oldRelativePath] = newRelativePath;
        }

        // 更新各种引用
        const patterns = [
            // script src
            /src\s*=\s*["']([^"']+\.js)["']/g,
            // link href (CSS)
            /href\s*=\s*["']([^"']+\.css)["']/g,
            // img src
            /src\s*=\s*["']([^"']+\.(jpg|jpeg|png|gif|svg|webp))["']/gi,
            // import/require
            /(?:import|require)\s*\(\s*["']([^"']+)["']\s*\)/g,
            // ES6 import
            /import\s+.*\s+from\s+["']([^"']+)["']/g
        ];

        for (const pattern of patterns) {
            content = content.replace(pattern, (match, filePath) => {
                const normalizedPath = filePath.replace(/\\/g, '/');
                
                for (const [oldPath, newPath] of Object.entries(pathMapping)) {
                    if (normalizedPath.includes(oldPath) || oldPath.includes(normalizedPath)) {
                        updated = true;
                        return match.replace(filePath, newPath);
                    }
                }
                
                return match;
            });
        }

        if (updated) {
            await fs.writeFile(filePath, content, 'utf8');
        }
    }

    /**
     * 生成迁移报告
     */
    async generateMigrationReport(migrationPlan) {
        const reportPath = path.join(this.rootPath, 'docs/file-migration-report.md');
        
        const report = `# 文件结构迁移报告

## 迁移概览

- **迁移时间**: ${new Date().toLocaleString()}
- **迁移文件数**: ${migrationPlan.moves.length}
- **跳过文件数**: ${migrationPlan.skips.length}
- **错误数**: ${migrationPlan.updates.length}

## 新目录结构

${Object.entries(this.newStructure).map(([dir, desc]) => `- \`${dir}\`: ${desc}`).join('\n')}

## 迁移详情

### 成功迁移的文件

${migrationPlan.moves.map(move => `- \`${move.relativePath}\` → \`${move.targetDir}\``).join('\n')}

### 跳过的文件

${migrationPlan.skips.map(skip => `- \`${skip.path}\`: ${skip.reason}`).join('\n')}

${migrationPlan.updates.length > 0 ? `
### 错误记录

${migrationPlan.updates.map(update => `- \`${update.file}\`: ${update.error}`).join('\n')}
` : ''}

## 后续步骤

1. 验证所有页面和功能是否正常工作
2. 删除原始文件（确认迁移成功后）
3. 更新构建脚本和配置文件
4. 更新文档和README

---

*此报告由文件结构迁移脚本自动生成*
`;

        await fs.writeFile(reportPath, report, 'utf8');
        console.log(`✓ 迁移报告已生成: ${reportPath}`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const migration = new FileStructureMigration();
    migration.migrate().catch(console.error);
}

module.exports = FileStructureMigration;

/**
 * 清理重复的主内容样式脚本
 * 移除重复的 .main-content 定义，保留最完整的版本
 */

const fs = require('fs');
const path = require('path');

// 获取所有HTML文件
function getAllHtmlFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

// 清理重复的主内容样式
function cleanDuplicateMainContent(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        let hasChanges = false;
        
        // 查找所有 .main-content 样式块
        const mainContentPattern = /\/\*\s*主内容区域\s*\*\/\s*\.main-content\s*\{[^}]*\}/g;
        const matches = content.match(mainContentPattern);
        
        if (matches && matches.length > 1) {
            // 找到最完整的版本（包含 background 和 min-height 的）
            let bestMatch = matches[0];
            for (const match of matches) {
                if (match.includes('background') && match.includes('min-height')) {
                    bestMatch = match;
                    break;
                }
            }
            
            // 移除所有匹配项
            content = content.replace(mainContentPattern, '');
            
            // 在第一个 .page-header 之前插入最佳版本
            const pageHeaderIndex = content.indexOf('.page-header');
            if (pageHeaderIndex !== -1) {
                const beforePageHeader = content.substring(0, pageHeaderIndex);
                const afterPageHeader = content.substring(pageHeaderIndex);
                content = beforePageHeader + '\n        ' + bestMatch + '\n\n        ' + afterPageHeader;
                hasChanges = true;
            }
        }
        
        // 清理重复的响应式 .main-content 样式
        const responsivePattern = /@media\s*\([^)]*\)\s*\{[^}]*\.main-content\s*\{[^}]*\}[^}]*\}/g;
        const responsiveMatches = content.match(responsivePattern);
        
        if (responsiveMatches && responsiveMatches.length > 1) {
            // 保留最后一个（通常是最完整的）
            const lastResponsive = responsiveMatches[responsiveMatches.length - 1];
            content = content.replace(responsivePattern, '');
            
            // 在 </style> 之前插入
            const styleEndIndex = content.lastIndexOf('</style>');
            if (styleEndIndex !== -1) {
                const beforeEnd = content.substring(0, styleEndIndex);
                const afterEnd = content.substring(styleEndIndex);
                content = beforeEnd + '\n        ' + lastResponsive + '\n    ' + afterEnd;
                hasChanges = true;
            }
        }
        
        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已清理: ${fileName}`);
            return true;
        } else {
            console.log(`⏭️  跳过: ${fileName} (无重复样式)`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)} - ${error.message}`);
        return false;
    }
}

// 主函数
function main() {
    console.log('🧹 开始清理重复的主内容样式...\n');
    console.log('📋 清理内容:');
    console.log('   - 移除重复的 .main-content 定义');
    console.log('   - 保留最完整的样式版本');
    console.log('   - 清理重复的响应式样式\n');
    
    const pagesDir = path.join(__dirname, '../src/pc/components/pages');
    
    if (!fs.existsSync(pagesDir)) {
        console.error('❌ 页面目录不存在:', pagesDir);
        return;
    }
    
    const htmlFiles = getAllHtmlFiles(pagesDir);
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件\n`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const file of htmlFiles) {
        if (cleanDuplicateMainContent(file)) {
            successCount++;
        } else {
            skipCount++;
        }
    }
    
    console.log('\n📊 清理统计:');
    console.log(`✅ 已清理: ${successCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`📁 总计文件: ${htmlFiles.length} 个文件`);
    
    if (successCount > 0) {
        console.log('\n🎉 重复样式清理完成！');
        console.log('🧹 移除了重复的样式定义');
        console.log('✨ 保留了最完整的样式版本');
        console.log('📏 页面样式更加整洁');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    cleanDuplicateMainContent,
    main
};

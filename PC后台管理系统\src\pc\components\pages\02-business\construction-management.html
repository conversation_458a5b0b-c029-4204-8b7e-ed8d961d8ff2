<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居施工管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #1a202c;
            --secondary-color: #2d3748;
            --accent-color: #3182ce;
            --light-gray: #f7fafc;
            --medium-gray: #e2e8f0;
            --dark-gray: #718096;
            --success-color: #38a169;
            --warning-color: #dd6b20;
            --error-color: #e53e3e;
            --text-color: #2d3748;
            --text-light: #718096;
            --border-color: #cbd5e0;
            --radius-md: 8px;
            --radius-sm: 4px;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        /* 布局结构 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(229, 231, 235, 0.8);
            flex-shrink: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-header {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.6);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .logo-icon i {
            color: white;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 12px 0;
        }

        .nav-section {
            margin-bottom: 16px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 4px 16px;
            margin: 4px 8px 2px;
            background: rgba(107, 114, 128, 0.08);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 3px;
            border-left: 2px solid rgba(107, 114, 128, 0.3);
            line-height: 1.2;
        }

        .nav-item {
            display: block;
            padding: 6px 16px;
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 1px 8px;
            position: relative;
            line-height: 1.3;
        }

        .nav-item:hover {
            background: rgba(243, 244, 246, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            color: #1f2937;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            color: #1f2937;
            font-weight: 600;
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateX(6px);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: auto;
            margin-left: 0;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 第1层 - 面包屑导航 */
        .breadcrumb {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px 32px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin: 16px 24px;
            border-radius: 12px;
            border: 1px solid rgba(229, 231, 235, 0.8);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 6px;
            line-height: 1.3;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 第2层 - 主菜单栏 */
        .phase-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px 32px;
            display: flex;
            gap: 12px;
            border: 1px solid rgba(229, 231, 235, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin: 0 24px 16px;
            border-radius: 12px;
        }

        .phase-tab {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(248, 250, 252, 0.8);
            color: #6b7280;
            border: 1px solid rgba(229, 231, 235, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .phase-tab.active {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
            box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
            transform: translateY(-2px);
        }

        .phase-tab:hover:not(.active) {
            background: rgba(243, 244, 246, 0.9);
            color: #1f2937;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 第3层 - 子菜单栏 */
        .submenu-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 16px 32px;
            display: flex;
            gap: 10px;
            overflow-x: auto;
            scrollbar-width: none;
            border: 1px solid rgba(229, 231, 235, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin: 0 24px 24px;
            border-radius: 12px;
        }

        .submenu-tabs::-webkit-scrollbar {
            display: none;
        }

        .submenu-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(248, 250, 252, 0.8);
            color: #6b7280;
            border: 1px solid rgba(229, 231, 235, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .submenu-tab.active {
            background: #6b7280;
            color: white;
            border-color: #6b7280;
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }

        .submenu-tab:hover:not(.active) {
            background: rgba(243, 244, 246, 0.9);
            color: #1f2937;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        /* 第4层 - 内容区域 */
        .content-container {
            flex: 1;
            padding: 0 24px 24px;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(229, 231, 235, 0.8);
            overflow: hidden;
            margin-bottom: 16px;
        }

        .content-header {
            padding: 20px 32px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(248, 250, 252, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.4;
        }

        .content-body {
            padding: 32px;
            background: rgba(255, 255, 255, 0.8);
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(229, 231, 235, 0.8);
            background: rgba(255, 255, 255, 0.95);
            color: #6b7280;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .btn:hover {
            background: rgba(243, 244, 246, 0.9);
            color: #1f2937;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 11px;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 12px;
            color: #1f2937;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border-radius: 6px;
            border: 1px solid rgba(229, 231, 235, 0.8);
            font-size: 12px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #1f2937;
        }

        .form-control:focus {
            outline: none;
            border-color: #6b7280;
            box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 16px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid rgba(229, 231, 235, 0.8);
            margin-top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .data-table th {
            background: rgba(248, 250, 252, 0.8);
            text-align: left;
            padding: 12px 16px;
            font-weight: 600;
            color: #1f2937;
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            font-size: 12px;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.6);
            color: #1f2937;
            font-size: 12px;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background: rgba(248, 250, 252, 0.6);
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 20px;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-in-progress {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-cancelled {
            background: #fee2e2;
            color: #b91c1c;
        }

        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            padding: 24px;
            text-align: center;
            background: white;
            margin-top: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--accent-color);
            background: rgba(49, 130, 206, 0.05);
        }

        .upload-icon {
            font-size: 36px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 8px;
        }

        .file-icon {
            font-size: 24px;
            margin-right: 12px;
            color: var(--text-light);
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .file-size {
            font-size: 12px;
            color: var(--text-light);
        }

        /* 媒体预览 */
        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .media-card {
            border-radius: var(--radius-sm);
            overflow: hidden;
            border: 1px solid var(--border-color);
            position: relative;
        }

        .media-thumb {
            width: 100%;
            height: 140px;
            object-fit: cover;
            display: block;
        }

        .media-type {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 8px;
            border-radius: var(--radius-sm);
            font-size: 12px;
        }

        .media-info {
            padding: 8px;
            font-size: 13px;
            background: white;
        }

        .media-date {
            color: var(--text-light);
            font-size: 11px;
            margin-top: 2px;
        }

        /* 异常严重程度样式 */
        .issue-severity {
            font-size: 10px;
            font-weight: 600;
            margin-top: 4px;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
        }

        .severity-high {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .severity-medium {
            background: #fef3c7;
            color: #d97706;
            border: 1px solid #fed7aa;
        }

        .severity-low {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }

        /* 弹窗 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            min-width: 500px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(229, 231, 235, 0.8);
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px 32px;
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(248, 250, 252, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
            transition: color 0.3s ease;
        }

        .modal-close:hover {
            color: #1f2937;
        }

        .modal-body {
            padding: 32px;
            background: rgba(255, 255, 255, 0.8);
        }

        .modal-footer {
            padding: 20px 32px;
            border-top: 1px solid rgba(229, 231, 235, 0.8);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: rgba(248, 250, 252, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* 响应式设计 */
        @media (max-width: 900px) {
            .admin-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                order: -1;
            }
        }

        @media (max-width: 600px) {
            .phase-tabs {
                overflow-x: auto;
                scrollbar-width: none;
            }
            
            .phase-tabs::-webkit-scrollbar {
                display: none;
            }
            
            .modal-content {
                min-width: unset;
                width: 95%;
            }
        }


        
        /* Toast通知 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
        }
        
        .toast {
            background: white;
            box-shadow: var(--shadow-md);
            border-radius: var(--radius-sm);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            width: 300px;
            margin-bottom: 12px;
            animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
            border-left: 4px solid;
        }
        
        .toast-success {
            border-left-color: var(--success-color);
        }
        
        .toast-error {
            border-left-color: var(--error-color);
        }
        
        .toast-warning {
            border-left-color: var(--warning-color);
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--accent-color);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon"><i class="fas fa-home"></i></div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item active">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 第1层 - 面包屑导航 -->
            <div class="breadcrumb">
                <h1 class="page-title">施工管理</h1>
                <p class="page-subtitle">管理智能家居施工项目进度和质量</p>
            </div>
            
            <!-- 第2层 - 主菜单栏 -->
            <div class="phase-tabs">
                <button class="phase-tab active" data-phase="briefing">交底</button>
                <button class="phase-tab" data-phase="wiring">水电</button>
                <button class="phase-tab" data-phase="ceiling">吊顶</button>
                <button class="phase-tab" data-phase="installation">安装</button>
                <button class="phase-tab" data-phase="debugging">调试</button>
                <button class="phase-tab" data-phase="aftersales">售后</button>
            </div>
            
            <!-- 第3层 - 子菜单栏 -->
            <div class="submenu-tabs">
                <button class="submenu-tab active" data-submenu="personnel">
                    <i class="fas fa-users"></i> 交底人员
                </button>
                <button class="submenu-tab" data-submenu="documents">
                    <i class="fas fa-file-alt"></i> 交底文档
                </button>
                <button class="submenu-tab" data-submenu="knowledge">
                    <i class="fas fa-book"></i> 交底知识库
                </button>
                <button class="submenu-tab" data-submenu="records">
                    <i class="fas fa-camera"></i> 交底记录
                </button>
                <button class="submenu-tab" data-submenu="issues">
                    <i class="fas fa-exclamation-triangle"></i> 交底异常
                </button>
                <button class="submenu-tab" data-submenu="approval">
                    <i class="fas fa-clipboard-check"></i> 验收确认
                </button>
            </div>
            
            <!-- 第4层 - 内容区域 -->
            <div class="content-container">
                <!-- 交底人员 -->
                <div class="content-card personnel-content" id="personnelSection">
                    <div class="content-header">
                        <h2 class="content-title">交底人员管理</h2>
                        <button class="btn btn-primary" id="addPersonBtn">
                            <i class="fas fa-plus"></i> 添加人员
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>姓名</th>
                                        <th>职位</th>
                                        <th>联系方式</th>
                                        <th>职责</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="personnelTableBody">
                                    <!-- 数据将通过JS填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 交底文档 -->
                <div class="content-card documents-content" id="documentsSection" style="display:none;">
                    <div class="content-header">
                        <h2 class="content-title">交底文档管理</h2>
                        <button class="btn btn-primary" id="addDocumentBtn">
                            <i class="fas fa-upload"></i> 上传文档
                        </button>
                    </div>
                    <div class="content-body">
                        <div id="documentsList">
                            <!-- 文档列表将通过JS填充 -->
                        </div>
                    </div>
                </div>
                
                <!-- 交底知识库 -->
                <div class="content-card knowledge-content" id="knowledgeSection" style="display:none;">
                    <div class="content-header">
                        <h2 class="content-title">交底知识库</h2>
                        <button class="btn btn-primary" id="addKnowledgeBtn">
                            <i class="fas fa-plus"></i> 添加知识项
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="grid-container" id="knowledgeGrid">
                            <!-- 知识库内容将通过JS填充 -->
                        </div>
                    </div>
                </div>
                
                <!-- 交底记录 -->
                <div class="content-card records-content" id="recordsSection" style="display:none;">
                    <div class="content-header">
                        <h2 class="content-title">交底现场记录</h2>
                        <button class="btn btn-primary" id="addRecordBtn">
                            <i class="fas fa-plus"></i> 添加记录
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="upload-area" id="recordUploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h3>添加现场照片和视频</h3>
                            <p>点击或拖放文件到此区域上传</p>
                        </div>
                        <div class="media-grid" id="recordsGrid">
                            <!-- 记录将通过JS填充 -->
                        </div>
                    </div>
                </div>

                <!-- 交底异常 -->
                <div class="content-card issues-content" id="issuesSection" style="display:none;">
                    <div class="content-header">
                        <h2 class="content-title">交底异常记录</h2>
                        <button class="btn btn-primary" id="addIssueBtn">
                            <i class="fas fa-plus"></i> 添加异常
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="upload-area" id="issueUploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h3>添加异常照片和视频</h3>
                            <p>点击或拖放文件到此区域上传</p>
                        </div>
                        <div class="media-grid" id="issuesGrid">
                            <!-- 异常记录将通过JS填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 弹窗容器 -->
    <div id="modalContainer"></div>
    <div id="toastContainer" class="toast-container"></div>
    
    <script>
        // 系统状态
        const appState = {
            currentPhase: "briefing",
            currentSubmenu: "personnel",
            personnel: [
                {id: 1, name: "张工", position: "项目经理", contact: "138****8888", role: "现场协调", status: "active"},
                {id: 2, name: "李工", position: "技术负责人", contact: "139****9999", role: "技术交底", status: "active"},
                {id: 3, name: "王工", position: "电工", contact: "137****7777", role: "电路规划", status: "pending"}
            ],
            documents: [
                {id: 1, name: "施工设计图纸.pdf", type: "pdf", size: "2.4MB", date: "2023-06-10"},
                {id: 2, name: "项目需求书.docx", type: "word", size: "860KB", date: "2023-06-12"},
                {id: 3, name: "材料清单.xlsx", type: "excel", size: "420KB", date: "2023-06-14"}
            ],
            knowledge: [
                {id: 1, title: "水电布局规范", category: "水电"},
                {id: 2, title: "弱电施工注意事项", category: "水电"},
                {id: 3, title: "吊顶材料选择指南", category: "吊顶"}
            ],
            records: [
                {id: 1, name: "客厅布局", type: "photo", date: "2023-06-15 10:23"},
                {id: 2, name: "电路走线", type: "photo", date: "2023-06-16 14:45"},
                {id: 3, name: "施工前全景", type: "video", date: "2023-06-17 09:12"},
                {id: 4, name: "材料进场", type: "photo", date: "2023-06-18 11:30"}
            ],
            issues: [
                {id: 1, name: "电路接线异常", type: "photo", date: "2023-06-15 15:30", severity: "high", description: "主线路接线不规范，存在安全隐患"},
                {id: 2, name: "材料质量问题", type: "photo", date: "2023-06-16 09:15", severity: "medium", description: "部分电线规格不符合要求"},
                {id: 3, name: "施工进度延误", type: "video", date: "2023-06-17 16:45", severity: "low", description: "因天气原因导致施工进度延误"}
            ],
            // 本地存储键名
            STORAGE_KEY: "smart-construction-system"
        };
        
        // DOM加载完毕后初始化应用
        document.addEventListener("DOMContentLoaded", function() {
            initSystem();
            setupEventListeners();
            renderPersonnelTable();
            renderDocumentsList();
            renderKnowledgeGrid();
            renderRecordsGrid();
            renderIssuesGrid();
        });
        
        // 初始化系统
        function initSystem() {
            try {
                // 检查本地存储中是否有数据
                const savedData = localStorage.getItem(appState.STORAGE_KEY);
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    appState.personnel = parsedData.personnel || appState.personnel;
                    appState.documents = parsedData.documents || appState.documents;
                    appState.knowledge = parsedData.knowledge || appState.knowledge;
                    appState.records = parsedData.records || appState.records;
                    appState.issues = parsedData.issues || appState.issues;
                }
            } catch (e) {
                console.error("加载本地数据失败:", e);
                showToast("加载数据失败，将使用默认数据", "error");
            }
        }
        
        // 保存数据到本地存储
        function saveData() {
            try {
                const dataToSave = {
                    personnel: appState.personnel,
                    documents: appState.documents,
                    knowledge: appState.knowledge,
                    records: appState.records,
                    issues: appState.issues
                };
                
                localStorage.setItem(appState.STORAGE_KEY, JSON.stringify(dataToSave));
                console.log("数据已保存到本地存储");
            } catch (e) {
                console.error("保存数据失败:", e);
                showToast("保存数据失败", "error");
            }
        }
        
        // 显示Toast通知
        function showToast(message, type = "info") {
            const toast = document.createElement("div");
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === "success" ? "check-circle" : type === "error" ? "times-circle" : "info-circle"}"></i>
                <div>${message}</div>
            `;
            
            const container = document.getElementById("toastContainer");
            container.appendChild(toast);
            
            // 5秒后移除
            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
        
        // 显示加载指示器
        function showLoader() {
            if (document.querySelector(".loading-overlay")) return;
            
            const loader = document.createElement("div");
            loader.className = "loading-overlay";
            loader.innerHTML = '<div class="spinner"></div>';
            document.body.appendChild(loader);
        }
        
        // 隐藏加载指示器
        function hideLoader() {
            const loader = document.querySelector(".loading-overlay");
            if (loader) loader.remove();
        }
        
        // 事件监听器设置
        function setupEventListeners() {
            // 阶段切换
            document.querySelectorAll(".phase-tab").forEach(tab => {
                tab.addEventListener("click", function() {
                    const phase = this.dataset.phase;
                    appState.currentPhase = phase;
                    
                    // 更新UI状态
                    document.querySelectorAll(".phase-tab").forEach(t => t.classList.remove("active"));
                    this.classList.add("active");
                    
                    updateContentTitle();
                });
            });
            
            // 子菜单切换
            document.querySelectorAll(".submenu-tab").forEach(tab => {
                tab.addEventListener("click", function() {
                    const submenu = this.dataset.submenu;
                    appState.currentSubmenu = submenu;
                    
                    // 更新UI状态
                    document.querySelectorAll(".submenu-tab").forEach(t => t.classList.remove("active"));
                    this.classList.add("active");
                    
                    // 显示当前子菜单对应的内容区域
                    document.querySelectorAll(".content-card").forEach(el => el.style.display = "none");
                    document.getElementById(`${submenu}Section`).style.display = "block";
                    
                    updateContentTitle();
                });
            });
            
            // 添加人员按钮
            document.getElementById("addPersonBtn").addEventListener("click", showAddPersonModal);
            
            // 上传文档按钮
            document.getElementById("addDocumentBtn").addEventListener("click", showUploadDocumentModal);
            
            // 添加知识项按钮
            document.getElementById("addKnowledgeBtn").addEventListener("click", showAddKnowledgeModal);
            
            // 添加现场记录按钮
            document.getElementById("addRecordBtn").addEventListener("click", () => {
                document.getElementById("recordUploadArea").dispatchEvent(new MouseEvent("click"));
            });

            // 添加异常记录按钮
            document.getElementById("addIssueBtn").addEventListener("click", () => {
                document.getElementById("issueUploadArea").dispatchEvent(new MouseEvent("click"));
            });
            
            // 记录上传区域
            document.getElementById("recordUploadArea").addEventListener("click", () => {
                showUploadRecordModal();
            });

            // 记录上传区域拖拽支持
            const uploadArea = document.getElementById("recordUploadArea");
            uploadArea.addEventListener("dragover", e => {
                e.preventDefault();
                uploadArea.style.borderColor = "#3182ce";
                uploadArea.style.backgroundColor = "#ebf4ff";
            });

            uploadArea.addEventListener("dragleave", () => {
                uploadArea.style.borderColor = "";
                uploadArea.style.backgroundColor = "";
            });

            uploadArea.addEventListener("drop", e => {
                e.preventDefault();
                uploadArea.style.borderColor = "";
                uploadArea.style.backgroundColor = "";

                if (e.dataTransfer.files.length > 0) {
                    handleFileUpload(e.dataTransfer.files);
                }
            });

            // 异常上传区域
            document.getElementById("issueUploadArea").addEventListener("click", () => {
                showUploadIssueModal();
            });

            // 异常上传区域拖拽支持
            const issueUploadArea = document.getElementById("issueUploadArea");
            issueUploadArea.addEventListener("dragover", e => {
                e.preventDefault();
                issueUploadArea.style.borderColor = "#ef4444";
                issueUploadArea.style.backgroundColor = "#fef2f2";
            });

            issueUploadArea.addEventListener("dragleave", () => {
                issueUploadArea.style.borderColor = "";
                issueUploadArea.style.backgroundColor = "";
            });

            issueUploadArea.addEventListener("drop", e => {
                e.preventDefault();
                issueUploadArea.style.borderColor = "";
                issueUploadArea.style.backgroundColor = "";

                if (e.dataTransfer.files.length > 0) {
                    handleIssueFileUpload(e.dataTransfer.files);
                }
            });
        }
        
        // 更新内容标题
        function updateContentTitle() {
            const phaseNames = {
                briefing: "交底",
                wiring: "水电",
                ceiling: "吊顶",
                installation: "安装",
                debugging: "调试",
                aftersales: "售后"
            };
            
            const submenuNames = {
                personnel: "人员管理",
                documents: "文档管理",
                knowledge: "知识库",
                records: "现场记录",
                issues: "异常处理",
                approval: "验收确认"
            };
            
            const titleElement = document.querySelector(".content-title");
            if (titleElement) {
                titleElement.textContent = `${phaseNames[appState.currentPhase]}${submenuNames[appState.currentSubmenu]}`;
            }
        }
        
        // 显示添加人员弹窗
        function showAddPersonModal() {
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">添加交底人员</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" class="form-control" id="personName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">职位</label>
                                <input type="text" class="form-control" id="personPosition">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系方式</label>
                                <input type="tel" class="form-control" id="personContact" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">职责</label>
                                <textarea class="form-control" id="personRole" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" id="cancelAddPerson">取消</button>
                            <button class="btn btn-primary" id="submitAddPerson">保存</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById("modalContainer").innerHTML = modalHTML;
            
            // 添加事件监听
            document.querySelector(".modal-close").addEventListener("click", closeModal);
            document.getElementById("cancelAddPerson").addEventListener("click", closeModal);
            document.getElementById("submitAddPerson").addEventListener("click", addPerson);
        }
        
        // 添加人员
        function addPerson() {
            const name = document.getElementById("personName").value.trim();
            const position = document.getElementById("personPosition").value.trim() || "未指定";
            const contact = document.getElementById("personContact").value.trim();
            const role = document.getElementById("personRole").value.trim() || "未指定";
            
            if (!name || !contact) {
                showToast("姓名和联系方式不能为空", "error");
                return;
            }
            
            const newPerson = {
                id: Date.now(),
                name,
                position,
                contact,
                role,
                status: "active",
                dateAdded: new Date().toISOString()
            };
            
            appState.personnel.push(newPerson);
            saveData();
            renderPersonnelTable();
            showToast("人员添加成功", "success");
            closeModal();
        }
        
        // 显示上传文档弹窗
        function showUploadDocumentModal() {
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">上传交底文档</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label class="form-label">文档名称</label>
                                <input type="text" class="form-control" id="documentName" placeholder="输入文档名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">选择文件</label>
                                <input type="file" class="form-control" id="documentFile">
                            </div>
                            <div class="progress" style="margin-top: 16px; display: none;" id="uploadProgress">
                                <div class="progress-bar" style="height: 8px; background: #3182ce; width: 0%;"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" id="cancelUploadDocument">取消</button>
                            <button class="btn btn-primary" id="submitUploadDocument">上传</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById("modalContainer").innerHTML = modalHTML;
            
            // 添加事件监听
            document.querySelector(".modal-close").addEventListener("click", closeModal);
            document.getElementById("cancelUploadDocument").addEventListener("click", closeModal);
            document.getElementById("submitUploadDocument").addEventListener("click", uploadDocument);
        }
        
        // 模拟文档上传
        function uploadDocument() {
            const nameInput = document.getElementById("documentName");
            const fileInput = document.getElementById("documentFile");
            
            const name = nameInput.value.trim();
            const file = fileInput.files[0];
            
            if (!file) {
                showToast("请选择要上传的文件", "error");
                return;
            }
            
            const fileName = name || file.name;
            const extension = fileName.split('.').pop().toLowerCase();
            const fileSize = formatFileSize(file.size);
            
            const fileTypes = {
                'pdf': 'PDF文档',
                'doc': 'Word文档',
                'docx': 'Word文档',
                'xls': 'Excel文档',
                'xlsx': 'Excel文档',
                'ppt': 'PPT文档',
                'pptx': 'PPT文档',
                'jpg': 'JPEG图片',
                'jpeg': 'JPEG图片',
                'png': 'PNG图片',
                'gif': 'GIF图片'
            };
            
            const fileType = fileTypes[extension] || '未知类型';
            
            // 显示进度条
            const progressContainer = document.getElementById("uploadProgress");
            const progressBar = progressContainer.querySelector(".progress-bar");
            
            progressContainer.style.display = "block";
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    
                    // 保存文档信息
                    const newDocument = {
                        id: Date.now(),
                        name: fileName,
                        type: fileType,
                        extension: extension,
                        size: fileSize,
                        date: new Date().toLocaleDateString()
                    };
                    
                    appState.documents.push(newDocument);
                    saveData();
                    renderDocumentsList();
                    
                    // 延迟关闭以便用户看到成功消息
                    setTimeout(() => {
                        showToast("文档上传成功", "success");
                        closeModal();
                    }, 1000);
                }
                progressBar.style.width = `${progress}%`;
            }, 200);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 渲染人员表格
        function renderPersonnelTable() {
            const container = document.getElementById("personnelTableBody");
            if (!container) return;
            
            const html = appState.personnel.map(person => `
                <tr>
                    <td>${person.name}</td>
                    <td>${person.position}</td>
                    <td>${person.contact}</td>
                    <td>${person.role}</td>
                    <td>
                        <span class="status-badge ${person.status === 'active' ? 'status-in-progress' : 'status-pending'}">
                            ${person.status === 'active' ? '工作中' : '待分配'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="editPerson(${person.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm" onclick="deletePerson(${person.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </td>
                </tr>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 编辑人员
        function editPerson(id) {
            const person = appState.personnel.find(p => p.id === id);
            if (!person) return;
            
            showToast("打开编辑人员信息表单 (模拟)", "info");
            setTimeout(() => {
                showToast("人员信息更新成功", "success");
            }, 1500);
        }
        
        // 删除人员
        function deletePerson(id) {
            if (confirm("确定要删除此人员吗？")) {
                appState.personnel = appState.personnel.filter(p => p.id !== id);
                saveData();
                renderPersonnelTable();
                showToast("人员已删除", "success");
            }
        }
        
        // 渲染文档列表
        function renderDocumentsList() {
            const container = document.getElementById("documentsList");
            if (!container) return;
            
            if (appState.documents.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-light);">
                        <i class="fas fa-file-alt" style="font-size: 36px; margin-bottom: 16px;"></i>
                        <p>暂无文档，请上传文档</p>
                    </div>
                `;
                return;
            }
            
            const html = appState.documents.map(doc => `
                <div class="file-item">
                    <div class="file-icon">
                        <i class="far fa-file-${doc.extension === 'pdf' ? 'pdf' : 
                            ['doc', 'docx'].includes(doc.extension) ? 'word' : 
                            ['xls', 'xlsx'].includes(doc.extension) ? 'excel' : 
                            ['ppt', 'pptx'].includes(doc.extension) ? 'powerpoint' : 
                            'alt'}"></i>
                    </div>
                    <div class="file-details">
                        <div class="file-name">${doc.name}</div>
                        <div class="file-info">
                            <span class="file-type">${doc.type}</span> • 
                            <span class="file-size">${doc.size}</span> • 
                            <span class="file-date">${doc.date}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm" onclick="previewDocument(${doc.id})">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="btn btn-sm" onclick="downloadDocument(${doc.id})">
                            <i class="fas fa-download"></i> 下载
                        </button>
                        <button class="btn btn-sm" onclick="printDocument(${doc.id})">
                            <i class="fas fa-print"></i> 打印
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 预览文档
        function previewDocument(id) {
            const doc = appState.documents.find(d => d.id === id);
            if (!doc) return;
            
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content" style="width: 80%; max-width: 1000px;">
                        <div class="modal-header">
                            <h3 class="modal-title">预览文档: ${doc.name}</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div style="text-align: center; padding: 20px;">
                                <i class="far fa-file-${doc.extension === 'pdf' ? 'pdf' : 
                                    ['doc', 'docx'].includes(doc.extension) ? 'word' : 
                                    ['xls', 'xlsx'].includes(doc.extension) ? 'excel' : 
                                    ['ppt', 'pptx'].includes(doc.extension) ? 'powerpoint' : 
                                    'alt'}" style="font-size: 64px; color: #4a5568;"></i>
                                <div style="margin-top: 20px;">
                                    <p>文档预览功能需要浏览器插件支持</p>
                                    <p>${doc.name} - ${doc.size}</p>
                                    <div style="margin-top: 20px;">
                                        <button class="btn btn-primary" onclick="downloadDocument(${doc.id})">
                                            <i class="fas fa-download"></i> 下载文档
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById("modalContainer").innerHTML = modalHTML;
            document.querySelector(".modal-close").addEventListener("click", closeModal);
        }
        
        // 下载文档
        function downloadDocument(id) {
            const doc = appState.documents.find(d => d.id === id);
            if (!doc) return;
            
            showToast(`开始下载: ${doc.name}`, "info");
            
            // 模拟下载
            setTimeout(() => {
                showToast(`${doc.name} 下载完成`, "success");
            }, 1500);
        }
        
        // 打印文档
        function printDocument(id) {
            const doc = appState.documents.find(d => d.id === id);
            if (!doc) return;
            
            showToast(`正在准备打印: ${doc.name}`, "info");
            
            setTimeout(() => {
                if (confirm("打印窗口将在新标签页打开，请检查浏览器的弹出窗口设置。")) {
                    window.open("about:blank", "_blank");
                }
            }, 1000);
        }
        
        // 渲染知识库
        function renderKnowledgeGrid() {
            const container = document.getElementById("knowledgeGrid");
            if (!container) return;
            
            const html = appState.knowledge.map(item => `
                <div class="content-card" style="cursor: pointer;">
                    <div class="content-header">
                        <h3 class="content-title">${item.title}</h3>
                        <span class="status-badge status-completed">${item.category}</span>
                    </div>
                    <div class="content-body">
                        <p>这里将展示知识库内容的摘要信息，支持富文本编辑和相关文档关联...</p>
                        <div style="margin-top: 16px;">
                            <button class="btn btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-sm">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                            <button class="btn btn-sm btn-primary">
                                <i class="fas fa-download"></i> 下载
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 显示添加知识库弹窗
        function showAddKnowledgeModal() {
            showToast("打开知识库管理表单 (模拟)", "info");
        }

        // 显示上传异常记录弹窗
        function showUploadIssueModal() {
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">上传异常记录</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label class="form-label">异常描述</label>
                                <textarea class="form-control" id="issueDescription" rows="3" placeholder="请详细描述发现的异常情况"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">严重程度</label>
                                <select class="form-control" id="issueSeverity">
                                    <option value="low">初级异常</option>
                                    <option value="medium">中级异常</option>
                                    <option value="high">严重异常</option>
                                </select>
                            </div>
                            <div class="upload-area" style="cursor: pointer;" id="uploadIssueArea">
                                <div class="upload-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <h3>添加异常照片和视频</h3>
                                <p>点击或拖放文件到此区域上传 (支持 JPG, PNG, MP4)</p>
                                <input type="file" id="issueFileInput" multiple style="display: none;" accept="image/*,video/*">
                            </div>
                            <div class="progress" style="margin-top: 16px; display: none;" id="issueUploadProgress">
                                <div class="progress-bar" style="height: 8px; background: #ef4444; width: 0%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById("modalContainer").innerHTML = modalHTML;

            // 添加事件监听
            document.querySelector(".modal-close").addEventListener("click", closeModal);
            document.getElementById("uploadIssueArea").addEventListener("click", triggerIssueFileInput);

            // 文件选择事件
            document.addEventListener("change", handleIssueFileSelect, {once: true});

            function triggerIssueFileInput() {
                document.getElementById("issueFileInput").click();
            }

            function handleIssueFileSelect(e) {
                if (e.target.id === "issueFileInput" && e.target.files.length > 0) {
                    const description = document.getElementById("issueDescription").value.trim();
                    const severity = document.getElementById("issueSeverity").value;

                    if (!description) {
                        showToast("请填写异常描述", "warning");
                        return;
                    }

                    // 模拟上传进度
                    const progressContainer = document.getElementById("issueUploadProgress");
                    const progressBar = progressContainer.querySelector(".progress-bar");
                    progressContainer.style.display = "block";

                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += Math.random() * 30;
                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);

                            // 添加异常记录到数据
                            Array.from(e.target.files).forEach((file, index) => {
                                const newIssue = {
                                    id: Date.now() + index,
                                    name: description,
                                    type: file.type.startsWith('image/') ? 'photo' : 'video',
                                    date: new Date().toLocaleString('zh-CN'),
                                    severity: severity,
                                    description: description
                                };
                                appState.issues.push(newIssue);
                            });

                            saveData();
                            renderIssuesGrid();

                            setTimeout(() => {
                                showToast(`已上传 ${e.target.files.length} 条异常记录`, "success");
                                closeModal();
                            }, 1000);
                        }
                        progressBar.style.width = `${progress}%`;
                    }, 200);
                } else {
                    closeModal();
                }
            }
        }
        
        // 显示上传记录弹窗
        function showUploadRecordModal() {
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">上传现场记录</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="upload-area" style="cursor: pointer;" id="uploadRecordArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3>添加现场照片和视频</h3>
                                <p>点击或拖放文件到此区域上传 (支持 JPG, PNG, MP4)</p>
                                <input type="file" id="recordFileInput" multiple style="display: none;" accept="image/*,video/*">
                            </div>
                            <div class="progress" style="margin-top: 16px; display: none;" id="recordUploadProgress">
                                <div class="progress-bar" style="height: 8px; background: #3182ce; width: 0%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById("modalContainer").innerHTML = modalHTML;
            
            // 添加事件监听
            document.querySelector(".modal-close").addEventListener("click", closeModal);
            document.getElementById("uploadRecordArea").addEventListener("click", triggerFileInput);
            
            // 文件选择事件
            document.addEventListener("change", handleRecordFileSelect, {once: true});
            
            function triggerFileInput() {
                document.getElementById("recordFileInput").click();
            }
            
            function handleRecordFileSelect(e) {
                if (e.target.files.length > 0) {
                    const progressContainer = document.getElementById("recordUploadProgress");
                    const progressBar = progressContainer.querySelector(".progress-bar");
                    
                    progressContainer.style.display = "block";
                    let progress = 0;
                    
                    const interval = setInterval(() => {
                        progress += Math.random() * 20;
                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);
                            
                            // 保存记录信息
                            Array.from(e.target.files).forEach((file, index) => {
                                const type = file.type.startsWith('image/') ? 'photo' : 
                                            file.type.startsWith('video/') ? 'video' : 'file';
                                
                                const newRecord = {
                                    id: Date.now() + index,
                                    name: file.name,
                                    type: type,
                                    size: formatFileSize(file.size),
                                    date: new Date().toLocaleString(),
                                    extension: file.name.split('.').pop(),
                                    fileType: file.type
                                };
                                
                                appState.records.push(newRecord);
                            });
                            
                            saveData();
                            renderRecordsGrid();
                            
                            setTimeout(() => {
                                showToast(`已上传 ${e.target.files.length} 条记录`, "success");
                                closeModal();
                            }, 1000);
                        }
                        progressBar.style.width = `${progress}%`;
                    }, 200);
                } else {
                    closeModal();
                }
            }
        }
        
        // 渲染现场记录
        function renderRecordsGrid() {
            const container = document.getElementById("recordsGrid");
            if (!container) return;
            
            if (appState.records.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-light); grid-column: 1 / -1;">
                        <i class="fas fa-camera" style="font-size: 36px; margin-bottom: 16px;"></i>
                        <p>暂无现场记录，请添加照片或视频</p>
                    </div>
                `;
                return;
            }
            
            const html = appState.records.map(record => `
                <div class="media-card" onclick="previewMedia(${record.id})">
                    <div class="media-thumb" style="background: #f1f5f9; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-${record.type === 'photo' ? 'camera' : 'video'}" style="font-size: 36px; color: #cbd5e0;"></i>
                    </div>
                    <span class="media-type">${record.type === 'photo' ? '图片' : '视频'}</span>
                    <div class="media-info">
                        <div class="media-name">${record.name}</div>
                        <div class="media-date">${record.date}</div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 渲染异常记录
        function renderIssuesGrid() {
            const container = document.getElementById("issuesGrid");
            if (!container) return;

            if (appState.issues.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-light); grid-column: 1 / -1;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 36px; margin-bottom: 16px;"></i>
                        <p>暂无异常记录，请添加异常照片或视频</p>
                    </div>
                `;
                return;
            }

            const html = appState.issues.map(issue => `
                <div class="media-card" onclick="previewIssue(${issue.id})">
                    <div class="media-thumb" style="background: #fef2f2; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-${issue.type === 'photo' ? 'camera' : 'video'}" style="font-size: 36px; color: #ef4444;"></i>
                    </div>
                    <span class="media-type severity-${issue.severity}">${issue.type === 'photo' ? '图片' : '视频'}</span>
                    <div class="media-info">
                        <div class="media-name">${issue.name}</div>
                        <div class="media-date">${issue.date}</div>
                        <div class="issue-severity severity-${issue.severity}">
                            ${issue.severity === 'high' ? '高' : issue.severity === 'medium' ? '中' : '低'}级异常
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 预览异常记录
        function previewIssue(id) {
            const issue = appState.issues.find(i => i.id === id);
            if (!issue) return;

            const severityText = {
                high: '严重异常',
                medium: '中级异常',
                low: '初级异常'
            };

            const severityColor = {
                high: '#dc2626',
                medium: '#d97706',
                low: '#0369a1'
            };

            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content" style="max-width: 900px;">
                        <div class="modal-header">
                            <h3 class="modal-title">${issue.name}</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body" style="text-align: center;">
                            <div style="max-width: 800px; margin: 0 auto;">
                                ${issue.type === 'photo' ? `
                                    <img src="https://picsum.photos/800/500?random=${id}" alt="${issue.name}" style="max-width: 100%;">
                                ` : `
                                    <video controls style="max-width: 100%;">
                                        <source src="#" type="video/mp4">
                                        您的浏览器不支持视频播放
                                    </video>
                                    <div style="margin-top: 16px; color: var(--text-light);">
                                        <p>视频预览功能演示</p>
                                        <button class="btn btn-primary" style="margin-top: 10px;">
                                            <i class="fas fa-play-circle"></i> 播放视频
                                        </button>
                                    </div>
                                `}
                            </div>
                            <div style="margin-top: 24px; text-align: left;">
                                <div class="file-item">
                                    <div class="file-icon">
                                        <i class="fas fa-exclamation-triangle" style="color: ${severityColor[issue.severity]};"></i>
                                    </div>
                                    <div class="file-info">
                                        <div class="file-name">${issue.name}</div>
                                        <div class="file-meta">
                                            <span style="color: ${severityColor[issue.severity]}; font-weight: 600;">
                                                ${severityText[issue.severity]}
                                            </span>
                                            <span style="margin-left: 16px;">${issue.date}</span>
                                        </div>
                                        <div style="margin-top: 8px; color: #6b7280; font-size: 14px;">
                                            ${issue.description}
                                        </div>
                                    </div>
                                </div>
                                <div style="margin-top: 20px; text-align: center;">
                                    <button class="btn btn-primary" onclick="downloadIssue(${id})">
                                        <i class="fas fa-download"></i> 下载异常记录
                                    </button>
                                    <button class="btn" onclick="deleteIssue(${id})" style="margin-left: 12px; color: #dc2626;">
                                        <i class="fas fa-trash"></i> 删除记录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById("modalContainer").innerHTML = modalHTML;
            document.querySelector(".modal-close").addEventListener("click", closeModal);
        }

        // 下载异常记录
        function downloadIssue(id) {
            const issue = appState.issues.find(i => i.id === id);
            if (!issue) return;

            showToast(`开始下载异常记录: ${issue.name}`, "info");
            closeModal();
        }

        // 删除异常记录
        function deleteIssue(id) {
            if (confirm("确定要删除此异常记录吗？")) {
                appState.issues = appState.issues.filter(i => i.id !== id);
                saveData();
                renderIssuesGrid();
                showToast("异常记录已删除", "success");
                closeModal();
            }
        }

        // 处理异常文件上传
        function handleIssueFileUpload(files) {
            showToast(`准备上传 ${files.length} 个异常文件`, "info");
            showUploadIssueModal();
        }

        // 预览媒体文件
        function previewMedia(id) {
            const record = appState.records.find(r => r.id === id);
            if (!record) return;
            
            const modalHTML = `
                <div class="modal-backdrop">
                    <div class="modal-content" style="max-width: 900px;">
                        <div class="modal-header">
                            <h3 class="modal-title">${record.name}</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body" style="text-align: center;">
                            <div style="max-width: 800px; margin: 0 auto;">
                                ${record.type === 'photo' ? `
                                    <img src="https://picsum.photos/800/500?random=${id}" alt="${record.name}" style="max-width: 100%;">
                                ` : `
                                    <video controls style="max-width: 100%;">
                                        <source src="#" type="video/mp4">
                                        您的浏览器不支持视频播放
                                    </video>
                                    <div style="margin-top: 16px; color: var(--text-light);">
                                        <p>视频预览功能演示</p>
                                        <button class="btn btn-primary" style="margin-top: 10px;">
                                            <i class="fas fa-play-circle"></i> 播放视频
                                        </button>
                                    </div>
                                `}
                            </div>
                            <div style="margin-top: 24px;">
                                <div class="file-item">
                                    <div class="file-icon">
                                        <i class="fas fa-${record.type === 'photo' ? 'camera' : 'video'}"></i>
                                    </div>
                                    <div class="file-details">
                                        <div class="file-name">${record.name}</div>
                                        <div class="file-info">
                                            <span class="file-type">${record.type === 'photo' ? '图片' : '视频'}</span> • 
                                            <span class="file-size">${record.size}</span> • 
                                            <span class="file-date">${record.date}</span>
                                        </div>
                                    </div>
                                    <div class="file-actions">
                                        <button class="btn btn-primary" onclick="downloadMedia(${record.id})">
                                            <i class="fas fa-download"></i> 下载
                                        </button>
                                        <button class="btn" onclick="deleteMedia(${record.id})">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById("modalContainer").innerHTML = modalHTML;
            document.querySelector(".modal-close").addEventListener("click", closeModal);
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById("modalContainer").innerHTML = "";
        }
        
        // 关闭弹窗事件处理
        document.addEventListener("keydown", function(e) {
            if (e.key === "Escape") {
                closeModal();
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 智能家居管理系统</title>
    <link rel="stylesheet" href="../../../../styles/unified-admin-styles.css">
    <link rel="stylesheet" href="../../../../styles/glass-effects.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
                                                                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/design-cases.html" class="nav-item">设计案例</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/knowledge-management.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">交底知识库</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/admin-dashboard.html" class="nav-item">管理仪表板</a>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="top-nav">
                <div style="display: flex; align-items: flex-start; gap: 16px; width: 100%;">
                    <nav class="nav-breadcrumb">
                        <div class="breadcrumb-content">
                            <h1 class="breadcrumb-title">项目管理</h1>
                            <p class="breadcrumb-description">管理智能家居项目进度、成员和文档</p>
                        </div>
                    </nav>
                </div>
            </header>

            <div class="page-content">
                <div class="project-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-black);">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalProjects">0</span>
                            <span class="stat-label">项目总数</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-green);">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="activeProjects">0</span>
                            <span class="stat-label">进行中</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-orange);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="delayedProjects">0</span>
                            <span class="stat-label">延期项目</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--accent-blue);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="completedProjects">0</span>
                            <span class="stat-label">已完成</span>
                        </div>
                    </div>
                </div>

                <div class="project-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddProjectModal()">
                            <i class="fas fa-plus"></i> 新增项目
                        </button>
                        <button class="btn btn-secondary" onclick="exportProjects()">
                            <i class="fas fa-download"></i> 导出项目
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索项目..." onkeyup="searchProjects()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="statusFilter" onchange="filterProjects()">
                            <option value="">所有状态</option>
                            <option value="planning">规划中</option>
                            <option value="active">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                </div>

                <div class="project-grid" id="projectGrid">
                    <!-- 项目卡片将通过JavaScript动态生成 -->
                </div>

                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span> - <span id="pageEnd">12</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div class="page-numbers" id="pageNumbers"></div>
                        <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <div class="modal" id="projectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增项目</h3>
                <button class="modal-close" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="projectForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectName">项目名称 *</label>
                            <input type="text" id="projectName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="projectClient">客户名称 *</label>
                            <input type="text" id="projectClient" name="client" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectType">项目类型 *</label>
                            <select id="projectType" name="type" required>
                                <option value="">请选择类型</option>
                                <option value="design">设计服务</option>
                                <option value="construction">施工项目</option>
                                <option value="maintenance">维护服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="projectStatus">项目状态 *</label>
                            <select id="projectStatus" name="status" required>
                                <option value="planning">规划中</option>
                                <option value="active">进行中</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectStartDate">开始日期</label>
                            <input type="date" id="projectStartDate" name="startDate">
                        </div>
                        <div class="form-group">
                            <label for="projectEndDate">结束日期</label>
                            <input type="date" id="projectEndDate" name="endDate">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="projectDescription">项目描述</label>
                        <textarea id="projectDescription" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeProjectModal()">取消</button>
                <button class="btn btn-primary" onclick="saveProject()">保存</button>
            </div>
        </div>
    </div>

        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

                        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }/* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        /* 隐藏 WebKit 浏览器的滚动条 */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        /* 侧边栏菜单样式 */
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 6px 20px 4px;
            margin-top: 12px;
            line-height: 1.3;
        }

        .nav-section:first-child .nav-section-title {
            margin-top: 4px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 13px;
            line-height: 1.3;
            font-weight: 400;
        }

        .nav-item:hover {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
        }

        .nav-item.active {
            background: #f8fafc;
            color: #1f2937;
            border-left-color: #1f2937;
            font-weight: 500;
        }

        

        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    
        

        .page-header {
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 内容容器 */
        .content-container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .content-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #1f2937;
            color: #ffffff;
            border-color: #1f2937;
        }

        .btn-primary:hover {
            background: #374151;
            border-color: #374151;
        }

        .btn-secondary {
            background: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
        }

        .table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            font-size: 14px;
            color: #1f2937;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .card-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .page-title {
                font-size: 20px;
            }

            .content-container {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Project specific styles */
        .project-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        .project-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .toolbar-left, .toolbar-right {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 8px 12px 8px 40px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            width: 200px;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .project-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }

        .project-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .project-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .project-client {
            font-size: 14px;
            color: #6b7280;
        }

        .project-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-planning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-active {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .project-meta {
            margin-bottom: 16px;
        }

        .project-meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .project-meta-item span:first-child {
            color: #6b7280;
        }

        .project-meta-item span:last-child {
            color: #1f2937;
            font-weight: 500;
        }

        .project-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }

        .pagination-info {
            font-size: 14px;
            color: #6b7280;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .page-number:hover {
            background: #f9fafb;
        }

        .page-number.active {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px 24px;
            border-top: 1px solid #e5e7eb;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #1f2937;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1f2937;
            box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
    </style>
    
    <script src="../../js/admin-common.js"></script>
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#f5222d' : type === 'warning' ? '#faad14' : '#1890ff'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                max-width: 350px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
        class ProjectManager {
            constructor() {
                this.apiBase = '/api/v1';
                this.projects = [];
                this.filteredProjects = [];
                this.currentPage = 1;
                this.pageSize = 12;
                this.totalCount = 0;
                this.currentEditId = null;
                this.init();
            }

            async init() {
                await this.loadProjects();
                this.updateStats();
                this.renderProjects();
                this.renderPagination();
                this.bindEvents();
            }

            async loadProjects() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${this.apiBase}/projects?page=${this.currentPage}&size=${this.pageSize}`, {
                        headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                    });
                    if (response.ok) {
                        const result = await response.json();
                        this.projects = result.data || [];
                        this.totalCount = result.total || 0;
                        this.filteredProjects = [...this.projects];
                    } else {
                        this.loadMockData();
                    }
                } catch (error) {
                    this.loadMockData();
                }
            }

            loadMockData() {
                this.projects = [
                    { id: 1, name: '张先生智能家居项目', client: '张先生', type: 'design', status: 'active', startDate: '2025-01-01', endDate: '2025-03-01', description: '全屋智能化改造项目', progress: 65 },
                    { id: 2, name: '李女士别墅智能化', client: '李女士', type: 'construction', status: 'planning', startDate: '2025-02-01', endDate: '2025-05-01', description: '别墅智能化施工项目', progress: 10 },
                    { id: 3, name: '王总办公室改造', client: '王总', type: 'design', status: 'completed', startDate: '2024-12-01', endDate: '2025-01-15', description: '办公室智能化设计', progress: 100 },
                    { id: 4, name: '赵家公寓项目', client: '赵先生', type: 'maintenance', status: 'active', startDate: '2025-01-10', endDate: '2025-02-10', description: '智能设备维护升级', progress: 40 }
                ];
                this.totalCount = this.projects.length;
                this.filteredProjects = [...this.projects];
            }

            updateStats() {
                const totalProjects = this.projects.length;
                const activeProjects = this.projects.filter(p => p.status === 'active').length;
                const completedProjects = this.projects.filter(p => p.status === 'completed').length;
                const delayedProjects = this.projects.filter(p => new Date(p.endDate) < new Date() && p.status !== 'completed').length;

                document.getElementById('totalProjects').textContent = totalProjects;
                document.getElementById('activeProjects').textContent = activeProjects;
                document.getElementById('completedProjects').textContent = completedProjects;
                document.getElementById('delayedProjects').textContent = delayedProjects;
            }

            renderProjects() {
                const grid = document.getElementById('projectGrid');
                grid.innerHTML = '';

                if (this.filteredProjects.length === 0) {
                    grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: var(--text-secondary);">暂无项目数据</div>';
                    return;
                }

                this.filteredProjects.forEach(project => {
                    const card = document.createElement('div');
                    card.className = 'project-card';
                    card.innerHTML = `
                        <div class="project-header">
                            <div>
                                <div class="project-name">${project.name}</div>
                                <div class="project-client">客户: ${project.client}</div>
                            </div>
                            <span class="project-status status-${project.status}">${this.getStatusText(project.status)}</span>
                        </div>
                        <div class="project-meta">
                            <div class="project-meta-item">
                                <span>项目类型:</span>
                                <span>${this.getTypeText(project.type)}</span>
                            </div>
                            <div class="project-meta-item">
                                <span>开始日期:</span>
                                <span>${project.startDate}</span>
                            </div>
                            <div class="project-meta-item">
                                <span>结束日期:</span>
                                <span>${project.endDate}</span>
                            </div>
                            <div class="project-meta-item">
                                <span>进度:</span>
                                <span>${project.progress}%</span>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-sm btn-secondary" onclick="projectManager.editProject(${project.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="projectManager.viewProject(${project.id})" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="projectManager.deleteProject(${project.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    grid.appendChild(card);
                });
            }

            renderPagination() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageStart').textContent = start;
                document.getElementById('pageEnd').textContent = end;
                document.getElementById('totalCount').textContent = this.totalCount;

                document.getElementById('prevBtn').disabled = this.currentPage <= 1;
                document.getElementById('nextBtn').disabled = this.currentPage >= totalPages;

                const pageNumbers = document.getElementById('pageNumbers');
                pageNumbers.innerHTML = '';

                for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                    const pageBtn = document.createElement('div');
                    pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    pageNumbers.appendChild(pageBtn);
                }
            }

            bindEvents() {
                document.getElementById('searchInput').addEventListener('input', () => this.searchProjects());
                document.getElementById('statusFilter').addEventListener('change', () => this.filterProjects());
            }

            searchProjects() {
                const keyword = document.getElementById('searchInput').value.toLowerCase();
                this.filteredProjects = this.projects.filter(project => 
                    project.name.toLowerCase().includes(keyword) ||
                    project.client.toLowerCase().includes(keyword)
                );
                this.renderProjects();
            }

            filterProjects() {
                const statusFilter = document.getElementById('statusFilter').value;
                this.filteredProjects = this.projects.filter(project => {
                    return !statusFilter || project.status === statusFilter;
                });
                this.renderProjects();
            }

            getStatusText(status) {
                const statusMap = { 'planning': '规划中', 'active': '进行中', 'completed': '已完成', 'cancelled': '已取消' };
                return statusMap[status] || status;
            }

            getTypeText(type) {
                const typeMap = { 'design': '设计服务', 'construction': '施工项目', 'maintenance': '维护服务' };
                return typeMap[type] || type;
            }

            showAddProjectModal() {
                this.currentEditId = null;
                document.getElementById('modalTitle').textContent = '新增项目';
                document.getElementById('projectForm').reset();
                document.getElementById('projectModal').style.display = 'block';
            }

            editProject(id) {
                const project = this.projects.find(p => p.id === id);
                if (!project) return;

                this.currentEditId = id;
                document.getElementById('modalTitle').textContent = '编辑项目';
                
                document.getElementById('projectName').value = project.name;
                document.getElementById('projectClient').value = project.client;
                document.getElementById('projectType').value = project.type;
                document.getElementById('projectStatus').value = project.status;
                document.getElementById('projectStartDate').value = project.startDate;
                document.getElementById('projectEndDate').value = project.endDate;
                document.getElementById('projectDescription').value = project.description || '';
                
                document.getElementById('projectModal').style.display = 'block';
            }

            viewProject(id) {
                const project = this.projects.find(p => p.id === id);
                if (!project) return;
                showToast(`项目详情 - ${project.name}\n客户：${project.client}\n类型：${this.getTypeText(project.type)}\n状态：${this.getStatusText(project.status)}\n进度：${project.progress}%`, 'info');
            }

            deleteProject(id) {
                if (confirm('确定要删除这个项目吗？')) {
                    this.projects = this.projects.filter(p => p.id !== id);
                    this.filteredProjects = [...this.projects];
                    this.updateStats();
                    this.renderProjects();
                    showToast('项目删除成功！', 'success');
                }
            }

            async saveProject() {
                const form = document.getElementById('projectForm');
                const formData = new FormData(form);
                const projectData = Object.fromEntries(formData);

                if (!projectData.name || !projectData.client || !projectData.type) {
                    showToast('请填写必填字段！', 'warning');
                    return;
                }

                try {
                    if (this.currentEditId) {
                        const projectIndex = this.projects.findIndex(p => p.id === this.currentEditId);
                        if (projectIndex !== -1) {
                            this.projects[projectIndex] = { ...this.projects[projectIndex], ...projectData };
                        }
                    } else {
                        const newProject = {
                            id: Math.max(...this.projects.map(p => p.id)) + 1,
                            ...projectData,
                            progress: 0
                        };
                        this.projects.push(newProject);
                    }
                    
                    showToast(this.currentEditId ? '项目更新成功！' : '项目创建成功！', 'success');
                    this.closeProjectModal();
                    this.filteredProjects = [...this.projects];
                    this.updateStats();
                    this.renderProjects();
                } catch (error) {
                    showToast('保存失败，请稍后重试', 'error');
                }
            }

            closeProjectModal() {
                document.getElementById('projectModal').style.display = 'none';
                document.getElementById('projectForm').reset();
                this.currentEditId = null;
            }

            goToPage(page) {
                this.currentPage = page;
                this.loadProjects();
            }

            previousPage() {
                if (this.currentPage > 1) this.goToPage(this.currentPage - 1);
            }

            nextPage() {
                const totalPages = Math.ceil(this.totalCount / this.pageSize);
                if (this.currentPage < totalPages) this.goToPage(this.currentPage + 1);
            }

            exportProjects() { showToast('导出项目功能即将上线，敬请期待！', 'info'); }
        }

        let projectManager;
        function showAddProjectModal() { projectManager.showAddProjectModal(); }
        function closeProjectModal() { projectManager.closeProjectModal(); }
        function saveProject() { projectManager.saveProject(); }
        function searchProjects() { projectManager.searchProjects(); }
        function filterProjects() { projectManager.filterProjects(); }
        function previousPage() { projectManager.previousPage(); }
        function nextPage() { projectManager.nextPage(); }
        function exportProjects() { projectManager.exportProjects(); }

        document.addEventListener('DOMContentLoaded', function() {
            projectManager = new ProjectManager();
        });
    </script>
</body>
</html>

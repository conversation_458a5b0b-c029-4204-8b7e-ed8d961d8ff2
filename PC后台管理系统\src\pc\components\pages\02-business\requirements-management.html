﻿
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居需求管理 - Table版本</title>
    <!-- 使用本地图标，不依赖外部资源 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 20px 8px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
        }

        .nav-item:hover {
            background: #f3f4f6;
            color: #1f2937;
        }

        .nav-item.active {
            background: #000;
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }



        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            color: #6b7280;
            font-size: 14px;
        }

        /* 流程步骤导航 */
        .process-nav {
            background: white;
            border-bottom: 2px solid #e5e7eb;
            padding: 0 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .process-steps {
            display: flex;
            align-items: center;
            gap: 0;
            min-height: 80px;
        }

        .process-step {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            border-bottom: 4px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            background: #f8fafc;
            border-right: 1px solid #e5e7eb;
            min-height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .process-step:last-child {
            border-right: none;
        }

        .process-step.active {
            background: #000;
            color: white;
            border-bottom-color: #1f2937;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .process-step:hover:not(.active) {
            background: #e5e7eb;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .process-step.active:hover {
            background: #1f2937;
        }

        .step-number {
            display: inline-block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            line-height: 32px;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .process-step.active .step-number {
            background: rgba(255, 255, 255, 0.9);
            color: #000;
            border-color: rgba(255, 255, 255, 0.9);
        }

        .step-title {
            font-size: 16px;
            font-weight: 600;
            line-height: 1.2;
        }

        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 标签页内容 */
        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-content h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .tab-content p {
            color: #6b7280;
            margin-bottom: 20px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #000;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #000;
            color: white;
            border: 2px solid #000;
        }

        .btn-primary:hover {
            background: #1f2937;
            border-color: #1f2937;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        /* 子菜单导航样式 */
        .sub-menu {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .sub-menu-btn {
            padding: 12px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .sub-menu-btn:hover {
            border-color: #1f2937;
            color: #1f2937;
            background: #f9fafb;
        }

        .sub-menu-btn.active {
            border-color: #000;
            background: #000;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 子内容区域样式 */
        .sub-content {
            min-height: 500px;
        }

        .scene-page {
            display: none;
        }

        .scene-page.active {
            display: block;
        }

        /* 场景网格布局 */
        .scene-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }

        /* 场景卡片样式 */
        .scene-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .scene-card:hover {
            border-color: #1f2937;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-4px);
        }

        .scene-card.selected {
            border-color: #000;
            background: #f9fafb;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .scene-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-bottom: 1px solid #e5e7eb;
        }

        .scene-info {
            padding: 20px;
        }

        .scene-info h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .scene-info p {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        /* 选择按钮样式 */
        .btn-select {
            width: 100%;
            padding: 10px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: white;
            color: #1f2937;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-select:hover {
            border-color: #1f2937;
            background: #f9fafb;
        }

        .btn-select.selected {
            border-color: #000;
            background: #000;
            color: white;
        }

        .btn-select.selected:hover {
            background: #1f2937;
            border-color: #1f2937;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .scene-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }

            .sub-menu {
                gap: 8px;
            }

            .sub-menu-btn {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .scene-selection-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .scene-selection-right {
                order: -1;
            }

            .scene-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .scene-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 文件上传样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-area:hover {
            border-color: #1f2937;
            background: #f8fafc;
        }

        /* 功能模块卡片 */
        .function-modules {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            margin-bottom: 24px;
        }

        .module-row {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            border-bottom: 1px solid #e5e7eb;
        }

        .module-card {
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            border-right: 1px solid #e5e7eb;
            background: white;
        }

        .module-card:last-child {
            border-right: none;
        }

        .module-card:hover {
            background: #f8fafc;
        }

        .module-card.active {
            background: #000;
            color: white;
        }

        .module-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            transition: all 0.2s;
        }

        .module-card.active .module-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .module-content h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .module-content p {
            font-size: 12px;
            color: #6b7280;
        }

        .module-card.active .module-content p {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 内容区域 */
        .content-area {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .content-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            background: #fafafa;
        }

        .content-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .content-header p {
            color: #6b7280;
            font-size: 14px;
        }

        /* 产品选择区域 */
        .product-selection-area {
            padding: 24px;
        }

        /* Tab导航 */
        .product-tab-navigation {
            margin-bottom: 24px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .product-tab-navigation::-webkit-scrollbar {
            display: none;
        }

        .product-tab-container {
            display: flex;
            gap: 4px;
            min-width: max-content;
        }

        .product-tab-btn {
            padding: 12px 20px;
            border: 1px solid #e5e7eb;
            background: white;
            color: #6b7280;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .product-tab-btn:hover {
            border-color: #d1d5db;
            background: #f9fafb;
        }

        .product-tab-btn.active {
            background: #000;
            color: white;
            border-color: #000;
        }

        /* 产品列表 */
        .product-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .product-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 20px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .product-item:hover {
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .product-item.selected {
            border-color: #000;
            background: #f8fafc;
        }

        .product-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .product-content {
            flex: 1;
        }

        .product-title {
            font-size: 16px;
            font-weight: 600;
            color: #0f172a;
            margin-bottom: 4px;
        }

        .product-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }

        .product-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #000;
            flex-shrink: 0;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .module-row {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .module-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .module-row {
                grid-template-columns: 1fr;
            }
            
            .product-item {
                flex-direction: column;
                text-align: center;
            }
            
            .product-image {
                width: 100%;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">🏠</div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
                        <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">需求管理</h1>
                    <p class="breadcrumb-description">管理客户需求，从需求收集到完成交付的全流程管理</p>
                    <div style="margin-top: 12px; padding: 12px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 14px; color: #1f2937;">
                        💡 <strong>使用提示：</strong>点击下方的流程步骤标签页可以切换到不同的功能模块。您可以直接点击任意步骤进行跳转。
                    </div>
                </div>
            </header>

            <!-- 流程步骤导航 -->
            <div class="process-nav">
                <div class="process-steps">
                    <div class="process-step active" onclick="showTab('tab1')">
                        <div class="step-title">需求列表</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab2')">
                        <div class="step-title">新建需求</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab3')">
                        <div class="step-title">场景选择</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab4')">
                        <div class="step-title">图纸上传</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab5')">
                        <div class="step-title">备注说明</div>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 标签页内容 -->
                <div id="tab1" class="tab-content active">
                    <h2>需求列表</h2>
                    <p>查看和管理所有客户需求</p>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                        <thead>
                            <tr style="background: #f9fafb;">
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">需求ID</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">客户姓名</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">联系电话</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">房屋户型</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">面积</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">预算范围</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">状态</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="requirementsTableBody">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                    <div style="text-align: right; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="showTab('tab2')">➕ 新建需求</button>
                    </div>
                </div>

                <div id="tab2" class="tab-content">
                    <h2>新建需求</h2>
                    <p>填写客户基本信息和房屋信息</p>

                    <div style="margin-top: 10px;">
                        <h3 style="margin-bottom: 15px;">客户基本信息</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">客户姓名 *</label>
                                <input type="text" class="form-input" id="customerName" placeholder="请输入客户姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话 *</label>
                                <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="form-label">详细地址 *</label>
                                <input type="text" class="form-input" id="address" placeholder="请输入详细地址">
                            </div>
                        </div>

                        <h3 style="margin: 20px 0 15px;">房屋信息</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">房间数 *</label>
                                <input type="number" class="form-input" id="rooms" value="3" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">客厅数 *</label>
                                <input type="number" class="form-input" id="halls" value="2" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">卫生间数 *</label>
                                <input type="number" class="form-input" id="bathrooms" value="2" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">房屋面积 *</label>
                                <input type="number" class="form-input" id="area" placeholder="平方米" min="20" max="1000">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="form-label">预算范围 *</label>
                                <select class="form-input" id="budget">
                                    <option value="">请选择预算范围</option>
                                    <option value="20000-50000">2-5万元</option>
                                    <option value="50000-100000">5-10万元</option>
                                    <option value="100000-200000">10-20万元</option>
                                    <option value="200000-500000">20-50万元</option>
                                    <option value="500000+">50万元以上</option>
                                </select>
                            </div>
                        </div>

                        <div style="text-align: right; margin-top: 20px;">
                            <button class="btn btn-secondary" onclick="showTab('tab1')" style="margin-right: 10px;">返回列表</button>
                            <button class="btn btn-primary" onclick="saveAndNext('tab3')">保存并下一步</button>
                        </div>
                    </div>
                </div>

                <div id="tab3" class="tab-content">
                    <h2>场景选择</h2>
                    <p>选择适合客户的智能家居场景</p>

                    <!-- 子菜单导航 -->
                    <div class="sub-menu" style="margin-top: 30px;">
                        <button class="sub-menu-btn active" data-scene="lighting" onclick="switchScenePage('lighting')">智能照明</button>
                        <button class="sub-menu-btn" data-scene="security" onclick="switchScenePage('security')">监控类</button>
                        <button class="sub-menu-btn" data-scene="custom" onclick="switchScenePage('custom')">定制场景</button>
                        <button class="sub-menu-btn" data-scene="auto" onclick="switchScenePage('auto')">自动场景</button>
                        <button class="sub-menu-btn" data-scene="safety" onclick="switchScenePage('safety')">安防类</button>
                        <button class="sub-menu-btn" data-scene="audio" onclick="switchScenePage('audio')">智能影音</button>
                        <button class="sub-menu-btn" data-scene="ai" onclick="switchScenePage('ai')">AI语音</button>
                        <button class="sub-menu-btn" data-scene="hotel" onclick="switchScenePage('hotel')">酒店民宿</button>
                        <button class="sub-menu-btn" data-scene="business" onclick="switchScenePage('business')">商业场景</button>
                    </div>

                    <!-- 子内容区域 -->
                    <div class="sub-content" style="margin-top: 30px;">
                        <!-- 智能照明 -->
                        <div id="lighting" class="scene-page active">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="lighting_1">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220525205141684168.gif" alt="客餐厅无主灯" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuqflhajljoXml6DkuLvngaE8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>客餐厅无主灯</h4>
                                        <p>无主灯设计的筒灯、射灯、灯带、轨道灯组合，色温、亮度根据场景自动调节</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'lighting_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="lighting_2">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/202204251652002727.gif" alt="客厅无主灯场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lrqLljoXml6DkuLvngaHlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>客厅无主灯场景</h4>
                                        <p>通过情景面板或语音控制让客户在几个场景间互相切换，缓开缓灭，过度柔和</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'lighting_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="lighting_3">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/2022040211110243243.gif" alt="卧室场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7ljavlrqTlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>卧室场景</h4>
                                        <p>通过智能调光的组合，实现温馨、助眠、起夜、阅读等不同场景的变化</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'lighting_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="lighting_4">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220325141322852285.gif" alt="易来Pro无主灯" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmJPog73osIPlhYk8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>易来Pro无主灯</h4>
                                        <p>智能本地化调光，色温亮度随意掌控，灯光缓开缓灭</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'lighting_4')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 监控类 -->
                        <div id="security" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="security_1">
                                    <img src="https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2021111621530337337.jpg" alt="中控屏监控视窗" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuK3mjqflsY/nm5Hmjqfop4bnqpc8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>中控屏监控视窗</h4>
                                        <p>在中控屏指定位置直接显示监控实时画面，方便随时观看大门口、户外、婴儿房</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'security_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="security_2">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220525210991359135.gif" alt="监控双向对讲" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nm5Hmjqflj4zlkJHlr7norbI8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>监控双向对讲</h4>
                                        <p>立即通过屋外的摄像头，实时观看屋外画面，并与来访者发起实时对讲功能</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'security_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="security_3">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20240816151678357835.jpg" alt="摄像头磁吸供电" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mkYTlg4/lpLTnoa7lkLjkvpvnlLE8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>摄像头磁吸供电</h4>
                                        <p>摄像头磁磁吸供电方案，无绳安装，支持5V2A标准的室内主流摄像头</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'security_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="security_4">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220525211451145114.gif" alt="智能猫眼" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3njKvnnbM8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能猫眼</h4>
                                        <p>门外广角摄像头+门内高清显示平板，当门外有人，门内的人经过屏幕则自动亮起</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'security_4')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 定制场景 -->
                        <div id="custom" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="custom_1">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220525204471857185.gif" alt="智能调光" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3osIPlhYk8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能调光</h4>
                                        <p>易来Pro无主灯智能本地化调光，色温亮度随意掌控，灯光缓开缓灭，支持中控屏定制化操作</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_2">
                                    <img src="https://www.cx-smarthome.com:443/sysmain/home/<USER>/20210927152468956895.jpg" alt="面板个性化雕刻" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7pnaLmnb/kuKrkuKrljoXpm5Hliuw8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>面板个性化雕刻</h4>
                                        <p>通过在开关面板上雕刻图案和文字提示，告知家人及亲友开关的对应作用，让使用者没有未知感</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_3">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190516162537523752.jpg" alt="预回家场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7pooTlm57lrrblnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>预回家场景</h4>
                                        <p>烈日炎炎，未到家，空调已经凉风习习；寒风凛冽，未到家，房间已经温暖如春</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_4">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190311181833303330.jpg" alt="恒温恒湿" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mgJjmuKnmgJjmuL08L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>恒温恒湿</h4>
                                        <p>通过温湿度传感器、空调智能控制系统、加温器、除湿机等设备自动联动，让您的卧室处于恒温恒湿的舒适状态</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_5">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220816165411181118.gif" alt="会客模式" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kvJrlrqLmqKHlvI88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>会客模式</h4>
                                        <p>用明亮的灯光，拉近主客间的距离，营造舒心的会客氛围</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_5')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_6">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220402102637333733.gif" alt="夏冬场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lpI/lhqzlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>夏冬场景</h4>
                                        <p>夏日清风冬日暖阳，智能调光定制场景让家中四季如春，亦可四季分明，带来舒适与放松</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_6')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_7">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222987728772.gif" alt="睡眠场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nnKHnnKDlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>睡眠场景</h4>
                                        <p>安防系统会开启来保证您的睡眠安全；空调和空气净化器也被设置成不打扰您休息的睡眠模式</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_7')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_8">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326105285718571.jpg" alt="中央空调智能化" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuK3lpK7nqbrosIPmmbrog73ljJY8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>中央空调智能化</h4>
                                        <p>支持多种品牌的中央空调智能化管理，接入米家平台以后，可以实现一键关闭所有空调语言控制某个区域的空调</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_8')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_9">
                                    <img src="https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20240326154048154815.jpg" alt="米家平板中控" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nsbPlrrblubPmnb/kuK3mjqc8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>米家平板中控</h4>
                                        <p>平板磁吸上墙，开启米家中控模式秒变家庭中控屏，可以通过卡片直观地控制智能家居设备</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_9')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="custom_10">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210907114591969196.gif" alt="米家智能控制中心" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nsbPlrrblmbrohb3mjqfliLbkuK3lv4M8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>米家智能控制中心</h4>
                                        <p>智能家居控制中心，可单手拿取，配合无线智能墙充实现无线充电，内置米家的智能家居控制中心系统</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'custom_10')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 自动场景 -->
                        <div id="auto" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="auto_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207113660186018.gif" alt="回家场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lm57lrrblnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>回家场景</h4>
                                        <p>使用指定指纹解锁可启动常用的回家场景，灯光逐个打开，窗帘打开，电视打开。预设场景可以根据您的需要来制作</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="auto_2">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326110565366536.jpg" alt="离家场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nprvlrrblnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>离家场景</h4>
                                        <p>离家时只需一个动作，关闭家中所有灯光、窗帘、空调、景观台及其他电器；扫地机器人开始清扫；传感器进入布防模式</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="auto_3">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif" alt="起床场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7otbfluprlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>起床场景</h4>
                                        <p>空调提前调到适合温度，灯光柔和舒适，窗帘缓缓开启，布防模式自动关闭，小爱同学播报今日的天气状况和新闻</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="auto_4">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190311183756815681.jpg" alt="起夜场景（网关夜灯）" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7otbflpJzlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>起夜场景（网关夜灯）</h4>
                                        <p>网关自带小夜灯，灯光颜色及亮度可随心调节。搭配人体传感器，在朦胧的夜晚，有人经过自动亮灯</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="auto_5">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926100211171117.jpg" alt="夜起灯带场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lpJzotbfngq/luKblnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>夜起灯带场景</h4>
                                        <p>搭配人体传感器，在朦胧的夜晚，有人经过床底灯带氛围灯自动亮起，并且呈现为主人最喜欢的颜色和亮度</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_5')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="auto_6">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210929120711761176.gif" alt="人在亮灯" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kurrlnKjkuq7ngq88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>人在亮灯</h4>
                                        <p>通过易来PRO的P20毫米波雷达人在传感器，实现有人就亮灯，且人在空间内没有动作也会判断为有人，而不会熄灯</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'auto_6')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 安防类 -->
                        <div id="safety" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="safety_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326103963896389.jpg" alt="预防天然气泄漏" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7pooTpmLLlpKnnhLbmsJTms6Xmvow8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>预防天然气泄漏</h4>
                                        <p>厨房天然气泄漏预警守卫厨房安全，天然气浓度只需达到爆炸阈值的百分之4，就可以触发高分贝报警声</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="safety_2">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2020032610390637637.jpg" alt="火灾预警" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7ngavngb7pooTorablpJo8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>火灾预警</h4>
                                        <p>火灾预警，防患于未然。家庭里一旦烟雾浓度过高，烟雾报警器立即发出刺耳的报警声，及时通知到您</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="safety_3">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926112142764276.jpg" alt="漏水自动断水" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mvI/msLToh6rliqjmlq3msLQ8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>漏水自动断水</h4>
                                        <p>浸水感应器检测到漏水网关发出警报，并向手机推送警报通知。对应水路的进水阀门中断水路</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="safety_4">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926111983358335.jpg" alt="漏水报警" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mvI/msLTmiqXorablpJo8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>漏水报警</h4>
                                        <p>在外度假，再也不用担心家中水管破裂浸湿地板。将浸水传感器放置在需要防水的区域附近，一旦检测到漏水，第一时间报警推送</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="safety_5">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220816155029322932.jpg" alt="智能猫眼" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3njKvnnbM8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能猫眼</h4>
                                        <p>智能猫眼由门外广角摄像头主门内高清显示平板组成，当门外有人，门内的人经过屏幕则自动亮起显示门外情况</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_5')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="safety_6">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220816170425352535.gif" alt="监控多宫格实时显示" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nm5HmjqflpmuluKfmoLzlrp7ml7bmmL7npLo8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>监控多宫格实时显示</h4>
                                        <p>基于购买了"监控视窗"付费授权窗口使用权，一页最多显示9个监控画面，并可实时播放，无需打开APP</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'safety_6')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 智能影音 -->
                        <div id="audio" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="audio_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200308131356585658.gif" alt="一键KTV" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuIDplK5LVFY8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>一键KTV</h4>
                                        <p>通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现KTV画面和声音一键切换到KTV模式，灯光、窗帘自动调整</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'audio_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="audio_2">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220816164220722072.gif" alt="一键观影" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuIDplK7op4LlvbE8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>一键观影</h4>
                                        <p>通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现电视画面和声音一键切换到观影模式，灯光、窗帘自动调整</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'audio_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="audio_3">
                                    <img src="https://www.cx-smarthome.com:443/sysmain/home/<USER>/20210926094113671367.jpg" alt="小爱背景音乐" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lsI/niLHog4zlma/pn7PkuZA8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>小爱背景音乐</h4>
                                        <p>小爱音箱语音点歌用无线开关远程切歌、开始播放、停止播放联动回家或离家场景可自动开启或停止音乐</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'audio_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="audio_4">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/2022081616430477477.gif" alt="观影模式" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7op4LlvbHmqKHlvI88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>观影模式</h4>
                                        <p>电视和影音的结合，足不出户享受电影院的视听震撼新体验。一键切换观景模式，灯光窗帘自动为您开启到最佳状态</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'audio_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="audio_5">
                                    <img src="https://www.cx-smarthome.com:443/sysmain/home/<USER>/20210926091923592359.jpg" alt="智能中控屏背景音乐" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3kuK3mjqflsY/og4zlma/pn7PkuZA8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能中控屏背景音乐</h4>
                                        <p>吸顶喇叭通过背景音乐套件连接平板控制中心，可通过控制中心的语音助手点歌，支持绑定QQ音乐、酷狗音乐、网易云音乐</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'audio_5')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI语音 -->
                        <div id="ai" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="ai_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif" alt="智能手表控制" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3miYvooajmjqfliLY8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能手表控制</h4>
                                        <p>通过小米智能手表控制全屋灯光、米家全系列智能家电、智能场景。苹果智能手表在安装了支持Homekit产品和风头后可控制全屋灯光</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'ai_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="ai_2">
                                    <img src="https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926095518211821.jpg" alt="语音控制" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7or63pn7PmjqfliLY8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>语音控制</h4>
                                        <p>劳累了一天的你只需要躺在床上，一切的控制只需一句话。寂寞的夜，一切动作都显多余，你只要安静享受属于自己的美妙时光</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'ai_2')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 酒店民宿 -->
                        <div id="hotel" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="hotel_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162498089808.jpg" alt="酒店AI语音控制" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7phZLlupdBSeivreOAjOaOp+WItuOAjTwvdGV4dD4KPC9zdmc+Cg=='">
                                    <div class="scene-info">
                                        <h4>酒店AI语音控制</h4>
                                        <p>房间内可实现语音控制窗帘开关、电视开关、电视节目内容搜索、灯光开关。可通过叫小爱同学实现：我要睡觉了；我要起床了；看电影模式</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'hotel_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="hotel_2">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162316821682.jpg" alt="回房场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lm57miL/lnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>回房场景</h4>
                                        <p>住客通过门卡开门以后，门厅灯自动亮起，同时小爱同学提示请将房卡插入卡槽，插入以后启动回房间场景，自动打开主灯，打开窗帘，打开电视</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'hotel_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="hotel_3">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162329462946.jpg" alt="离房场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7nprvmiL/lnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>离房场景</h4>
                                        <p>当住客离开房间的时候，门厅的灯会为方便他换鞋而暂时的亮起，其它的灯会自动的熄灭；空调也会自动的关闭；电视机如果打开着也会被关闭</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'hotel_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="hotel_4">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317160937213721.jpg" alt="智能插卡模块" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mmbrohb3mj5LljaHmqKHlnZc8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>智能插卡模块</h4>
                                        <p>接入米家的智能插卡模块，解决插卡后启动回酒店场景，拔卡后关闭所有灯光和电器的作用，从入门开始实现米家智能化控制场景</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'hotel_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="hotel_5">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317160230953095.jpg" alt="酒店夜起" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7phZLlupflpJzotbU8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>酒店夜起</h4>
                                        <p>夜间熄灯后，起床脚落地即亮起小夜灯，夜灯可以是在床边的嵌入式灯光。搭配人体传感器，在朦胧的夜晚，有人经过自动亮灯</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'hotel_5')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商业场景 -->
                        <div id="business" class="scene-page">
                            <div class="scene-grid">
                                <div class="scene-card" data-product="business_1">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323223031353135.jpg" alt="人体运动监测" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kurrkvaPlvZDliqjnm5HmtYs8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>人体运动监测</h4>
                                        <p>公司重要位置及每个办公室通过人体运动传感器，让您掌握公司内下班无人后的安全动态，异常情况回查。并且使用无线技术，电池供电</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_1')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="business_2">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323221149964996.jpg" alt="茶水间漏水警报" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7ojLbmsLTpl7TmvI/msLTorablpJo8L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>茶水间漏水警报</h4>
                                        <p>茶水间日常使用率高，是容易发生漏水的地方，通过水浸传感器配合多功能网关和电磁断水阀门，能在漏水的第一时间切断水路</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_2')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="business_3">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323220525172517.jpg" alt="一键上下班场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kuIDplK7kuIrkuIvnj63lnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>一键上下班场景</h4>
                                        <p>一键上下班可以节约最后离开的员工整理时间，可以有效保障公司的用电安全。一键下班：关闭所有中央空调内机。一键上班：打开公共区吸顶灯</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_3')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="business_4">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323220176837683.jpg" alt="公司门禁场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7lhazlj7jpl6jnpoHlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>公司门禁场景</h4>
                                        <p>经过门口自动亮灯，门外可通过无线开关触发公司内多功能网关响起门铃。门口安装有米家监控，下班后启动安全布防</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_4')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="business_5">
                                    <img src="http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2019032321560442442.jpg" alt="会议投影场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7kvJrorbDmipXlvbHlnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>会议投影场景</h4>
                                        <p>一键开会，自动完成以下动作：关闭会议室主灯、将投影幕布展开到适当位置、关闭窗帘、打开会议桌的氛围灯、打开投影仪</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_5')">选择</button>
                                    </div>
                                </div>
                                <div class="scene-card" data-product="business_6">
                                    <img src="https://www.cx-smarthome.com/sysmain/home/<USER>/20220414141238523852.gif" alt="商业照明场景" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7llYbkuJrnjKfmmI7lnLrmma88L3RleHQ+Cjwvc3ZnPgo='">
                                    <div class="scene-info">
                                        <h4>商业照明场景</h4>
                                        <p>智能场景随心切换，每个区域设定不同场景，让照明随场景不同而随心切换。商照全系0.1W超低待机功耗、低于行业标准1W 10倍</p>
                                        <button class="btn-select" onclick="toggleProductSelection(this, 'business_6')">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="margin-top: 30px; text-align: right;">
                        <button class="btn btn-secondary" onclick="showTab('tab2')" style="margin-right: 10px;">上一步</button>
                        <button class="btn btn-primary" onclick="saveAndNext('tab4')">保存并下一步</button>
                    </div>
                </div>

                <div id="tab4" class="tab-content">
                    <h2>图纸上传</h2>
                    <p>上传房屋平面图、设计图纸或参考图片</p>

                    <div class="upload-area" style="margin-top: 30px;" onclick="document.getElementById('fileInput').click()">
                        <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                        <div style="font-size: 16px; margin-bottom: 10px;">点击或拖拽上传文件</div>
                        <div style="font-size: 14px; color: #6b7280;">支持 JPG、PNG、PDF 格式，单个文件最大 10MB</div>
                        <input type="file" id="fileInput" accept="image/*,.pdf" multiple style="display: none;" onchange="handleFileUpload(event)">
                    </div>

                    <div style="text-align: right; margin-top: 30px;">
                        <button class="btn btn-secondary" onclick="showTab('tab3')" style="margin-right: 10px;">上一步</button>
                        <button class="btn btn-primary" onclick="saveAndNext('tab5')">保存并下一步</button>
                    </div>
                </div>

                <div id="tab5" class="tab-content">
                    <h2>备注说明</h2>
                    <p>填写特殊需求和备注，确认需求信息</p>

                    <div style="margin-top: 30px;">
                        <div class="form-group">
                            <label class="form-label">详细需求描述</label>
                            <textarea class="form-input" rows="6" placeholder="请详细描述特殊需求、偏好设置、注意事项等..."></textarea>
                        </div>

                        <h3 style="margin: 30px 0 20px;">需求汇总</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">客户姓名</div>
                                <div style="font-weight: 500;" id="summaryName">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">联系电话</div>
                                <div style="font-weight: 500;" id="summaryPhone">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">房屋户型</div>
                                <div style="font-weight: 500;" id="summaryHouseType">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">房屋面积</div>
                                <div style="font-weight: 500;" id="summaryArea">未填写</div>
                            </div>
                        </div>

                        <div style="text-align: right; margin-top: 30px;">
                            <button class="btn btn-secondary" onclick="showTab('tab4')" style="margin-right: 10px;">上一步</button>
                            <button class="btn btn-success" onclick="submitRequirement()" style="background: #10b981;">提交完整需求</button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 产品选择模块 -->
                    <div id="product-selection" class="module-content active">
                        <div class="content-header">
                            <h2>智能家居场景选择</h2>
                            <p>请根据客户需求选择智能场景，支持多选</p>
                        </div>
                        
                        <div class="product-selection-area">
                            <!-- 产品分类Tab -->
                            <div class="product-tab-navigation">
                                <div class="product-tab-container">
                                    <button class="product-tab-btn active" data-category="lighting" onclick="switchProductCategory('lighting')">
                                        💡 智能照明
                                    </button>
                                    <button class="product-tab-btn" data-category="security" onclick="switchProductCategory('security')">
                                        📷 监控类
                                    </button>
                                    <button class="product-tab-btn" data-category="custom" onclick="switchProductCategory('custom')">
                                        🎨 定制场景
                                    </button>
                                    <button class="product-tab-btn" data-category="auto" onclick="switchProductCategory('auto')">
                                        🤖 自动场景
                                    </button>
                                    <button class="product-tab-btn" data-category="safety" onclick="switchProductCategory('safety')">
                                        🛡️ 安防类
                                    </button>
                                    <button class="product-tab-btn" data-category="audio" onclick="switchProductCategory('audio')">
                                        🎵 智能影音
                                    </button>
                                    <button class="product-tab-btn" data-category="ai" onclick="switchProductCategory('ai')">
                                        🗣️ AI语音
                                    </button>
                                    <button class="product-tab-btn" data-category="hotel" onclick="switchProductCategory('hotel')">
                                        🏨 酒店民宿
                                    </button>
                                    <button class="product-tab-btn" data-category="business" onclick="switchProductCategory('business')">
                                        🏢 商业场景
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 产品列表 -->
                            <div class="product-list" id="productList">
                                <!-- 产品将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 需求表单模块 -->
                    <div id="requirement-form" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>需求表单</h2>
                            <p>填写客户需求信息，与H5端功能保持一致</p>
                        </div>

                        <div class="form-container" style="padding: 24px;">
                            <form id="requirementForm">
                                <!-- 基本信息 -->
                                <div class="form-section">
                                    <h3 class="section-title">基本信息</h3>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label" for="customerName">客户姓名 <span class="required">*</span></label>
                                            <input type="text" id="customerName" class="form-input" placeholder="请输入客户姓名" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label" for="phone">联系电话 <span class="required">*</span></label>
                                            <input type="tel" id="phone" class="form-input" placeholder="请输入联系电话" required>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="address">详细地址 <span class="required">*</span></label>
                                        <input type="text" id="address" class="form-input" placeholder="请输入详细地址" required>
                                    </div>
                                </div>

                                <!-- 房屋信息 -->
                                <div class="form-section">
                                    <h3 class="section-title">房屋信息</h3>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">房屋户型 <span class="required">*</span></label>
                                            <div class="house-type-selector">
                                                <div class="house-type-item">
                                                    <label for="rooms">室</label>
                                                    <input type="number" id="rooms" min="1" max="10" value="3" required>
                                                </div>
                                                <div class="house-type-item">
                                                    <label for="halls">厅</label>
                                                    <input type="number" id="halls" min="1" max="10" value="2" required>
                                                </div>
                                                <div class="house-type-item">
                                                    <label for="bathrooms">卫</label>
                                                    <input type="number" id="bathrooms" min="1" max="10" value="2" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label" for="area">房屋面积 <span class="required">*</span></label>
                                            <input type="number" id="area" class="form-input" placeholder="平方米" min="20" max="1000" required>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="budget">预算范围 <span class="required">*</span></label>
                                        <select id="budget" class="form-input" required>
                                            <option value="">请选择预算范围</option>
                                            <option value="20000-50000">2-5万元</option>
                                            <option value="50000-100000">5-10万元</option>
                                            <option value="100000-200000">10-20万元</option>
                                            <option value="200000-500000">20-50万元</option>
                                            <option value="500000+">50万元以上</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 产品选择提示 -->
                                <div class="form-section">
                                    <h3 class="section-title">产品选择</h3>
                                    <div class="selected-products-display">
                                        <p class="form-hint">请先在"产品选择"模块中选择所需产品，已选择的产品将自动关联到此需求。</p>
                                        <div id="formSelectedProducts" class="selected-products-list">
                                            <span class="no-selection">暂未选择任何产品</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 需求描述 -->
                                <div class="form-section">
                                    <h3 class="section-title">需求描述</h3>
                                    <div class="form-group">
                                        <label class="form-label" for="requirements">详细需求 <span class="optional">(可选)</span></label>
                                        <textarea id="requirements" class="form-input form-textarea" placeholder="请详细描述您的智能家居需求，如：希望实现哪些功能、有什么特殊要求等..." rows="4"></textarea>
                                    </div>
                                </div>

                                <!-- 参考图片 -->
                                <div class="form-section">
                                    <h3 class="section-title">参考图片</h3>
                                    <div class="form-group">
                                        <label class="form-label">上传图片 <span class="optional">(可选)</span></label>
                                        <div class="image-upload-container">
                                            <!-- 上传区域 -->
                                            <div class="upload-area" id="uploadArea">
                                                <div class="upload-content">
                                                    <div class="upload-icon">📷</div>
                                                    <div class="upload-text">点击或拖拽上传图片</div>
                                                    <div class="upload-hint">支持 JPG、PNG、WEBP 格式，单张最大 5MB，最多 6 张</div>
                                                </div>
                                                <input type="file" id="imageInput" accept="image/jpeg,image/png,image/webp" multiple style="display: none;" onchange="handleFileUpload(event)">
                                            </div>

                                            <!-- 图片预览区域 -->
                                            <div class="image-preview-container" id="imagePreviewContainer" style="display: none;">
                                                <div class="preview-header">
                                                    <span class="preview-title">已选择图片 (<span id="imageCount">0</span>/6)</span>
                                                    <button type="button" class="clear-all-btn" onclick="clearAllImages()">清空所有</button>
                                                </div>
                                                <div class="image-preview-grid" id="imagePreviewList"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="submit-section">
                                    <button type="submit" class="submit-btn" id="submitBtn">
                                        <span class="btn-text">提交需求</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div id="requirements-list" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>需求列表</h2>
                            <p>查看和管理所有需求</p>
                        </div>
                        <div style="padding: 24px;">
                            <!-- 操作工具栏 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <button class="btn btn-primary" onclick="showTab('tab2')" style="margin-right: 12px;">
                                        <span style="margin-right: 8px;">+</span>创建新需求
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportRequirements()">
                                        <span style="margin-right: 8px;">↓</span>导出数据
                                    </button>
                                </div>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <input type="text" id="searchRequirements" placeholder="搜索客户姓名或电话..." 
                                           style="padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px; width: 200px;"
                                           oninput="searchRequirements()">
                                    <select id="statusFilter" onchange="filterRequirements()" 
                                            style="padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                        <option value="">全部状态</option>
                                        <option value="待处理">待处理</option>
                                        <option value="处理中">处理中</option>
                                        <option value="已完成">已完成</option>
                                        <option value="已取消">已取消</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 需求统计卡片 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;" id="requirementsStats">
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; font-weight: 600; color: #1f2937;" id="totalCount">0</div>
                                    <div style="color: #6b7280; font-size: 14px;">总需求数</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; font-weight: 600; color: #f59e0b;" id="pendingCount">0</div>
                                    <div style="color: #6b7280; font-size: 14px;">待处理</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; font-weight: 600; color: #3b82f6;" id="processingCount">0</div>
                                    <div style="color: #6b7280; font-size: 14px;">处理中</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; font-weight: 600; color: #10b981;" id="completedCount">0</div>
                                    <div style="color: #6b7280; font-size: 14px;">已完成</div>
                                </div>
                            </div>

                            <!-- 需求表格 -->
                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
                                <table id="requirementsTable" style="width: 100%; border-collapse: collapse;">
                                    <thead style="background: #f9fafb;">
                                        <tr>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">需求编号</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">客户姓名</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">联系电话</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">房屋户型</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">面积</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">预算</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600;">状态</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; width: 120px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="requirementsTableBody">
                                        <!-- 动态内容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div id="data-analysis" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>数据分析</h2>
                            <p>需求统计和分析报告</p>
                        </div>
                        <div style="padding: 24px;">
                            <!-- 时间范围选择 -->
                            <div style="margin-bottom: 24px; display: flex; gap: 12px; align-items: center;">
                                <label style="font-weight: 500;">时间范围：</label>
                                <select id="timeRange" onchange="updateAnalysis()" style="padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                    <option value="7">最近7天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="90">最近90天</option>
                                    <option value="365">最近1年</option>
                                </select>
                                <button class="btn btn-secondary" onclick="refreshAnalysis()">刷新数据</button>
                            </div>

                            <!-- 数据概览 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 32px;">
                                <!-- 需求趋势 -->
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px;">
                                    <h3 style="margin-bottom: 16px; color: #1f2937;">需求趋势</h3>
                                    <div style="font-size: 32px; font-weight: 700; color: #3b82f6; margin-bottom: 8px;" id="trendCount">--</div>
                                    <div style="font-size: 14px; color: #6b7280;" id="trendChange">较上期对比</div>
                                </div>

                                <!-- 客户分布 -->
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px;">
                                    <h3 style="margin-bottom: 16px; color: #1f2937;">客户地区分布</h3>
                                    <div id="regionAnalysis">
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">市区</span>
                                            <span style="font-weight: 500;" id="cityCount">--</span>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">郊区</span>
                                            <span style="font-weight: 500;" id="suburbCount">--</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">其他</span>
                                            <span style="font-weight: 500;" id="otherCount">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 预算分析 -->
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px;">
                                    <h3 style="margin-bottom: 16px; color: #1f2937;">预算分布</h3>
                                    <div id="budgetAnalysis">
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">5万以下</span>
                                            <span style="font-weight: 500;" id="budget1">--</span>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">5-10万</span>
                                            <span style="font-weight: 500;" id="budget2">--</span>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">10-20万</span>
                                            <span style="font-weight: 500;" id="budget3">--</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">20万以上</span>
                                            <span style="font-weight: 500;" id="budget4">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 房型分析 -->
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px;">
                                    <h3 style="margin-bottom: 16px; color: #1f2937;">热门房型</h3>
                                    <div id="houseTypeAnalysis">
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">三室两厅</span>
                                            <span style="font-weight: 500;" id="house3">--</span>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">两室一厅</span>
                                            <span style="font-weight: 500;" id="house2">--</span>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">四室两厅</span>
                                            <span style="font-weight: 500;" id="house4">--</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #6b7280;">其他</span>
                                            <span style="font-weight: 500;" id="houseOther">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 详细报表 -->
                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <h3 style="margin: 0;">详细报表</h3>
                                    <button class="btn btn-primary" onclick="exportAnalysisReport()">导出报表</button>
                                </div>
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead style="background: #f9fafb;">
                                            <tr>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb;">日期</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb;">新增需求</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb;">处理完成</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb;">转化率</th>
                                            </tr>
                                        </thead>
                                        <tbody id="analyticsTableBody">
                                            <!-- 动态内容 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="system-settings" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>系统设置</h2>
                            <p>配置系统参数</p>
                        </div>
                        <div style="padding: 24px;">
                            <!-- 基础设置 -->
                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
                                <h3 style="margin-bottom: 20px; color: #1f2937;">基础设置</h3>
                                <div style="display: grid; gap: 20px;">
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">系统名称</label>
                                        <input type="text" id="systemName" placeholder="智能家居需求管理系统" 
                                               style="width: 100%; padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                    </div>
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">联系电话</label>
                                        <input type="text" id="contactPhone" placeholder="************" 
                                               style="width: 100%; padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                    </div>
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">服务地址</label>
                                        <input type="text" id="serviceAddress" placeholder="北京市朝阳区xxx大厦" 
                                               style="width: 100%; padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                    </div>
                                </div>
                            </div>

                            <!-- 业务设置 -->
                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
                                <h3 style="margin-bottom: 20px; color: #1f2937;">业务设置</h3>
                                <div style="display: grid; gap: 20px;">
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">默认预算范围</label>
                                        <select id="defaultBudget" style="width: 100%; padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px;">
                                            <option value="5万以下">5万以下</option>
                                            <option value="5万-10万" selected>5万-10万</option>
                                            <option value="10万-20万">10万-20万</option>
                                            <option value="20万-50万">20万-50万</option>
                                            <option value="50万以上">50万以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">自动分配处理人</label>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <input type="checkbox" id="autoAssign" style="margin-right: 8px;">
                                            <span>开启自动分配功能</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 8px;">需求跟进提醒</label>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <input type="checkbox" id="followupReminder" checked style="margin-right: 8px;">
                                            <span>开启跟进提醒功能</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据设置 -->
                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
                                <h3 style="margin-bottom: 20px; color: #1f2937;">数据管理</h3>
                                <div style="display: grid; gap: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f9fafb; border-radius: 6px;">
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px;">数据备份</div>
                                            <div style="font-size: 14px; color: #6b7280;">定期备份需求数据</div>
                                        </div>
                                        <button class="btn btn-secondary" onclick="backupData()">立即备份</button>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f9fafb; border-radius: 6px;">
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px;">数据导入</div>
                                            <div style="font-size: 14px; color: #6b7280;">从文件导入需求数据</div>
                                        </div>
                                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(this)">
                                        <button class="btn btn-secondary" onclick="document.getElementById('importFile').click()">选择文件</button>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #fef2f2; border-radius: 6px;">
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px; color: #dc2626;">清空所有数据</div>
                                            <div style="font-size: 14px; color: #6b7280;">⚠️ 此操作不可恢复</div>
                                        </div>
                                        <button class="btn" onclick="clearAllData()" style="background: #dc2626; color: white;">清空数据</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 保存设置 -->
                            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                                <button class="btn btn-secondary" onclick="resetSettings()">重置设置</button>
                                <button class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            loadRequirementsList();
            // 初始化场景选择页面
            switchScenePage('lighting');
        });

        // 切换主模块
        function showModule(moduleId) {
            // 更新模块卡片状态
            document.querySelectorAll('.module-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.module-card').classList.add('active');

            // 显示对应的内容
            document.querySelectorAll('.module-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(moduleId).style.display = 'block';
        }

        // 切换产品分类
        function switchProductCategory(category) {
            console.log('切换到分类:', category);

            // 更新Tab按钮状态
            document.querySelectorAll('.product-tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // 加载对应分类的产品
            loadProducts(category);
        }

        // 加载产品列表
        function loadProducts(category) {
            console.log('加载产品分类:', category);
            const productList = document.getElementById('productList');
            if (!productList) {
                console.error('产品列表容器未找到');
                return;
            }

            const products = productData[category] || [];
            console.log('找到产品数量:', products.length);

            if (products.length === 0) {
                productList.innerHTML = '<div style="text-align: center; padding: 40px; color: #64748b;">暂无产品数据</div>';
                return;
            }

            productList.innerHTML = products.map(product => `
                <div class="product-item" onclick="toggleProduct('${product.id}')">
                    <img src="${product.image}" alt="${product.title}" class="product-image" onerror="this.style.display='none'">
                    <div class="product-content">
                        <div class="product-title">${product.title}</div>
                        <div class="product-desc">${product.desc}</div>
                    </div>
                    <input type="checkbox" class="product-checkbox" id="${product.id}" name="selectedProducts" value="${product.id}">
                </div>
            `).join('');

            console.log('产品列表已更新');
        }

        // 切换产品选择状态
        function toggleProduct(productId) {
            const checkbox = document.getElementById(productId);
            const productItem = checkbox.closest('.product-item');

            if (checkbox.checked) {
                checkbox.checked = false;
                productItem.classList.remove('selected');
                selectedProducts = selectedProducts.filter(id => id !== productId);
            } else {
                checkbox.checked = true;
                productItem.classList.add('selected');
                selectedProducts.push(productId);
            }

            console.log('已选择的产品:', selectedProducts);
        }

        // 清空所有选择
        function clearAllSelections() {
            selectedProducts = [];
            document.querySelectorAll('.product-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.querySelectorAll('.product-item').forEach(item => {
                item.classList.remove('selected');
            });
            console.log('已清空所有选择');
        }

        // 保存选择
        function saveSelections() {
            if (selectedProducts.length === 0) {
                alert('请至少选择一个产品');
                return;
            }

            console.log('保存选择的产品:', selectedProducts);
            alert(`已保存 ${selectedProducts.length} 个产品选择`);
        }

        // ===== 新增：标签页功能 =====

        // 产品数据定义（基于传翔智家实际产品）
        const productData = {
            lighting: [
                {
                    id: 'lighting_1',
                    title: '客餐厅无主灯',
                    desc: '无主灯设计的筒灯、射灯、灯带、轨道灯组合，色温、亮度根据场景自动调节',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525205141684168.gif'
                },
                {
                    id: 'lighting_2',
                    title: '客厅无主灯场景',
                    desc: '客厅无主灯系统的设计，通过情景面板或语音控制让客户在几个场景间互相切换，缓开缓灭，过度柔和',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/202204251652002727.gif'
                },
                {
                    id: 'lighting_3',
                    title: '卧室场景',
                    desc: '通过智能调光的组合，实现温馨、助眠、起夜、阅读等不同场景的变化，帮助主人缓解压力、放松休息',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/2022040211110243243.gif'
                },
                {
                    id: 'lighting_4',
                    title: '易来Pro无主灯',
                    desc: '智能本地化调光，色温亮度随意掌控，灯光缓开缓灭',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220325141322852285.gif'
                }
            ],
            security: [
                {
                    id: 'security_1',
                    title: '中控屏监控视窗',
                    desc: '在中控屏指定位置直接显示监控实时画面，方便随时观看大门口、户外、婴儿房',
                    image: 'https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2021111621530337337.jpg'
                },
                {
                    id: 'security_2',
                    title: '监控双向对讲',
                    desc: '立即通过屋外的摄像头，实时观看屋外画面，并与来访者发起实时对讲功能',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525210991359135.gif'
                },
                {
                    id: 'security_3',
                    title: '摄像头磁吸供电',
                    desc: '摄像头磁磁吸供电方案，无绳安装，支持5V2A标准的室内主流摄像头',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20240816151678357835.jpg'
                },
                {
                    id: 'security_4',
                    title: '智能猫眼',
                    desc: '门外广角摄像头+门内高清显示平板，当门外有人，门内的人经过屏幕则自动亮起',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220525211451145114.gif'
                }
            ]
        };

        let selectedProducts = [];
        let currentTab = 'tab1';
        let currentScenePage = 'lighting';
        let requirementData = {
            customerInfo: {},
            houseInfo: {},
            selectedProducts: [],
            uploadedFiles: [],
            notes: ''
        };

        // 标签页切换
        function showTab(tabId) {
            console.log('切换到标签页:', tabId);

            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航标签的激活状态
            document.querySelectorAll('.process-step').forEach(step => {
                step.classList.remove('active');
            });

            // 显示目标标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('显示标签页:', tabId);
            } else {
                console.error('找不到标签页:', tabId);
            }

            // 激活对应的导航标签
            const tabIndex = parseInt(tabId.replace('tab', ''));
            const processSteps = document.querySelectorAll('.process-step');
            if (processSteps[tabIndex - 1]) {
                processSteps[tabIndex - 1].classList.add('active');
            }

            currentTab = tabId;

            // 根据标签页执行特定操作
            switch(tabId) {
                case 'tab1':
                    loadRequirementsList();
                    break;
                case 'tab5':
                    updateSummary();
                    break;
            }
        }

        // 切换场景页面
        function switchScenePage(scenePage) {
            console.log('切换到场景页面:', scenePage);

            // 更新子菜单按钮状态
            document.querySelectorAll('.sub-menu-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-scene="${scenePage}"]`).classList.add('active');

            // 显示对应的场景页面
            document.querySelectorAll('.scene-page').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(scenePage).classList.add('active');

            currentScenePage = scenePage;
        }

        // 切换产品选择状态
        function toggleProductSelection(button, productId) {
            const card = button.closest('.scene-card');
            const index = selectedProducts.indexOf(productId);

            if (index > -1) {
                // 取消选择
                selectedProducts.splice(index, 1);
                card.classList.remove('selected');
                button.classList.remove('selected');
                button.textContent = '选择';
            } else {
                // 添加选择
                selectedProducts.push(productId);
                card.classList.add('selected');
                button.classList.add('selected');
                button.textContent = '已选择';
            }

            console.log('当前选择的产品:', selectedProducts);
            updateSelectedProductsCount();
        }

        // 更新已选择产品数量显示
        function updateSelectedProductsCount() {
            // 可以在页面上添加一个显示已选择产品数量的元素
            console.log(`已选择 ${selectedProducts.length} 个产品`);
        }

        // 获取产品信息
        function getProductInfo(productId) {
            for (const category in productData) {
                const product = productData[category].find(p => p.id === productId);
                if (product) {
                    return {
                        ...product,
                        category: category
                    };
                }
            }
            return null;
        }

        // 获取已选择的产品详情
        function getSelectedProductsDetails() {
            return selectedProducts.map(productId => getProductInfo(productId)).filter(Boolean);
        }

        // 保存并下一步
        function saveAndNext(nextTab) {
            if (validateCurrentTab()) {
                saveCurrentTabData();
                showTab(nextTab);
            }
        }

        // 验证当前标签页
        function validateCurrentTab() {
            switch (currentTab) {
                case 'tab2':
                    return validateRequirementForm();
                default:
                    return true;
            }
        }

        // 验证需求表单
        function validateRequirementForm() {
            const requiredFields = ['customerName', 'phone', 'address', 'rooms', 'halls', 'bathrooms', 'area', 'budget'];

            for (const field of requiredFields) {
                const element = document.getElementById(field);
                if (!element || !element.value.trim()) {
                    alert(`请填写${element ? element.previousElementSibling.textContent.replace(' *', '') : field}`);
                    if (element) element.focus();
                    return false;
                }
            }

            // 验证手机号
            const phone = document.getElementById('phone').value.trim();
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                document.getElementById('phone').focus();
                return false;
            }

            return true;
        }

        // 保存当前标签页数据
        function saveCurrentTabData() {
            switch (currentTab) {
                case 'tab2':
                    saveRequirementFormData();
                    break;
                case 'tab3':
                    saveSceneSelectionData();
                    break;
            }

            // 保存到localStorage
            localStorage.setItem('currentRequirement', JSON.stringify(requirementData));
        }

        // 保存场景选择数据
        function saveSceneSelectionData() {
            requirementData.selectedProducts = selectedProducts.slice(); // 复制数组
            console.log('保存场景选择数据:', requirementData.selectedProducts);
        }

        // 保存需求表单数据
        function saveRequirementFormData() {
            requirementData.customerInfo = {
                name: document.getElementById('customerName').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                address: document.getElementById('address').value.trim()
            };

            requirementData.houseInfo = {
                rooms: parseInt(document.getElementById('rooms').value),
                halls: parseInt(document.getElementById('halls').value),
                bathrooms: parseInt(document.getElementById('bathrooms').value),
                area: document.getElementById('area').value.trim(),
                budget: document.getElementById('budget').value
            };
        }

        // 加载需求列表
        function loadRequirementsList() {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const tbody = document.getElementById('requirementsTableBody');

            if (!tbody) return;

            if (requirements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">
                            <div>
                                <p style="margin-bottom: 16px;">暂无需求数据</p>
                                <button class="btn btn-primary" onclick="showTab('tab2')">创建第一个需求</button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = requirements.map((req, index) => `
                <tr>
                    <td>${req.id || 'N/A'}</td>
                    <td>${req.customer_name || req.customerInfo?.name || 'N/A'}</td>
                    <td>${req.phone || req.customerInfo?.phone || 'N/A'}</td>
                    <td>${req.house_rooms || req.houseInfo?.rooms || 0}室${req.house_halls || req.houseInfo?.halls || 0}厅${req.house_bathrooms || req.houseInfo?.bathrooms || 0}卫</td>
                    <td>${req.area || req.houseInfo?.area || 'N/A'}㎡</td>
                    <td>${req.budget || req.houseInfo?.budget || 'N/A'}</td>
                    <td>
                        <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; background: #fef3c7; color: #92400e;">
                            ${req.status || '待处理'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary" onclick="viewRequirement(${index})" style="padding: 4px 8px; margin-right: 4px;">查看</button>
                        <button class="btn btn-secondary" onclick="editRequirement(${index})" style="padding: 4px 8px;">编辑</button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新汇总信息
        function updateSummary() {
            if (requirementData.customerInfo.name) {
                document.getElementById('summaryName').textContent = requirementData.customerInfo.name;
                document.getElementById('summaryPhone').textContent = requirementData.customerInfo.phone;
                document.getElementById('summaryHouseType').textContent =
                    `${requirementData.houseInfo.rooms}室${requirementData.houseInfo.halls}厅${requirementData.houseInfo.bathrooms}卫`;
                document.getElementById('summaryArea').textContent = requirementData.houseInfo.area + '㎡';
            }
        }

        // 提交需求
        function submitRequirement() {
            let requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            
            // 检查是否为编辑模式
            const isEditMode = requirementData.editIndex !== undefined;
            
            if (isEditMode) {
                // 编辑模式：更新现有需求
                const editIndex = requirementData.editIndex;
                const existingReq = requirements[editIndex];
                
                if (!existingReq) {
                    showToast('要编辑的需求不存在', 'error');
                    return;
                }
                
                // 更新需求数据
                const updatedData = {
                    ...existingReq, // 保留原有数据
                    customer_name: requirementData.customerInfo.name,
                    phone: requirementData.customerInfo.phone,
                    address: requirementData.customerInfo.address,
                    house_rooms: requirementData.houseInfo.rooms,
                    house_halls: requirementData.houseInfo.halls,
                    house_bathrooms: requirementData.houseInfo.bathrooms,
                    area: requirementData.houseInfo.area,
                    budget: requirementData.houseInfo.budget,
                    selectedProducts: requirementData.selectedProducts || [],
                    updateTime: new Date().toISOString()
                };
                
                requirements[editIndex] = updatedData;
                localStorage.setItem('smart_home_requirements', JSON.stringify(requirements));
                
                showToast(`需求更新成功！需求编号：${existingReq.id}`, 'success');
            } else {
                // 新建模式：创建新需求
                const requirementId = 'REQ_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                
                const finalData = {
                    id: requirementId,
                    customer_name: requirementData.customerInfo.name,
                    phone: requirementData.customerInfo.phone,
                    address: requirementData.customerInfo.address,
                    house_rooms: requirementData.houseInfo.rooms,
                    house_halls: requirementData.houseInfo.halls,
                    house_bathrooms: requirementData.houseInfo.bathrooms,
                    area: requirementData.houseInfo.area,
                    budget: requirementData.houseInfo.budget,
                    selectedProducts: requirementData.selectedProducts || [],
                    status: '待处理',
                    createTime: new Date().toISOString(),
                    source: 'pc_admin'
                };
                
                requirements.unshift(finalData);
                localStorage.setItem('smart_home_requirements', JSON.stringify(requirements));
                
                showToast(`需求提交成功！需求编号：${requirementId}`, 'success');
            }

            // 清除当前需求数据
            localStorage.removeItem('currentRequirement');
            
            // 重置数据
            requirementData = {
                customerInfo: {
                    name: '',
                    phone: '',
                    address: ''
                },
                houseInfo: {
                    rooms: 1,
                    halls: 1,
                    bathrooms: 1,
                    area: '',
                    budget: ''
                },
                selectedProducts: []
            };

            // 返回需求列表
            showTab('tab1');
        }

        // 查看需求详情
        function viewRequirement(index) {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const req = requirements[index];

            if (!req) {
                alert('需求数据不存在');
                return;
            }

            const details = `
需求详情：

📋 基本信息：
• 需求编号：${req.id}
• 客户姓名：${req.customer_name}
• 联系电话：${req.phone}
• 详细地址：${req.address}

🏠 房屋信息：
• 房屋户型：${req.house_rooms}室${req.house_halls}厅${req.house_bathrooms}卫
• 房屋面积：${req.area}㎡
• 预算范围：${req.budget}

📊 状态信息：
• 处理状态：${req.status}
• 创建时间：${new Date(req.createTime).toLocaleString()}
            `;

            alert(details);
        }

        // 编辑需求
        function editRequirement(index) {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const req = requirements[index];

            if (!req) {
                showToast('需求数据不存在', 'error');
                return;
            }

            // 确认编辑
            if (!confirm('确定要编辑这个需求吗？')) {
                return;
            }

            // 将需求数据加载到表单中
            requirementData = {
                customerInfo: {
                    name: req.customer_name || '',
                    phone: req.phone || '',
                    address: req.address || ''
                },
                houseInfo: {
                    rooms: req.house_rooms || 1,
                    halls: req.house_halls || 1,
                    bathrooms: req.house_bathrooms || 1,
                    area: req.area || '',
                    budget: req.budget || ''
                },
                selectedProducts: req.selectedProducts || [],
                editIndex: index // 记录编辑的索引
            };

            // 保存到localStorage供其他函数使用
            localStorage.setItem('currentRequirement', JSON.stringify(requirementData));

            // 切换到需求表单页面
            showTab('tab2');

            // 填充表单数据
            setTimeout(() => {
                document.getElementById('customerName').value = requirementData.customerInfo.name;
                document.getElementById('phone').value = requirementData.customerInfo.phone;
                document.getElementById('address').value = requirementData.customerInfo.address;
                document.getElementById('rooms').value = requirementData.houseInfo.rooms;
                document.getElementById('halls').value = requirementData.houseInfo.halls;
                document.getElementById('bathrooms').value = requirementData.houseInfo.bathrooms;
                document.getElementById('area').value = requirementData.houseInfo.area;
                document.getElementById('budget').value = requirementData.houseInfo.budget;
                
                showToast('需求数据已加载到表单，可以开始编辑', 'success');
            }, 100);
        }

        // ==================== 图片管理功能 ====================

        // 清空所有图片
        function clearAllImages() {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreviewList = document.getElementById('imagePreviewList');
            const imageCount = document.getElementById('imageCount');

            if (imagePreviewList) {
                imagePreviewList.innerHTML = '';
            }

            if (imageCount) {
                imageCount.textContent = '0';
            }

            if (imagePreviewContainer) {
                imagePreviewContainer.style.display = 'none';
            }

            // 清空文件输入
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.value = '';
            }

            // 显示成功提示
            showToast('已清空所有图片', 'success');
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            if (!files || files.length === 0) return;

            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreviewList = document.getElementById('imagePreviewList');
            const imageCount = document.getElementById('imageCount');

            // 显示预览容器
            if (imagePreviewContainer) {
                imagePreviewContainer.style.display = 'block';
            }

            let currentCount = imagePreviewList ? imagePreviewList.children.length : 0;
            let addedCount = 0;

            Array.from(files).forEach((file, index) => {
                if (currentCount + addedCount >= 6) {
                    showToast('最多只能上传6张图片', 'error');
                    return;
                }

                // 验证文件类型
                if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
                    showToast(`文件 ${file.name} 格式不支持`, 'error');
                    return;
                }

                // 验证文件大小 (10MB)
                if (file.size > 10 * 1024 * 1024) {
                    showToast(`文件 ${file.name} 大小超过10MB`, 'error');
                    return;
                }

                // 创建预览元素
                const previewItem = document.createElement('div');
                previewItem.className = 'image-preview-item';
                previewItem.innerHTML = `
                    <div class="preview-image">
                        ${file.type.startsWith('image/') ?
                            `<img src="${URL.createObjectURL(file)}" alt="${file.name}">` :
                            `<div class="pdf-icon">📄</div>`
                        }
                    </div>
                    <div class="preview-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${(file.size / 1024).toFixed(1)} KB</div>
                    </div>
                    <button type="button" class="remove-image" onclick="removeImage(this)">×</button>
                `;

                if (imagePreviewList) {
                    imagePreviewList.appendChild(previewItem);
                }

                addedCount++;
            });

            // 更新计数
            if (imageCount) {
                imageCount.textContent = (currentCount + addedCount).toString();
            }

            if (addedCount > 0) {
                showToast(`成功添加 ${addedCount} 个文件`, 'success');
            }
        }

        // 移除单个图片
        function removeImage(button) {
            const previewItem = button.closest('.image-preview-item');
            const imagePreviewList = document.getElementById('imagePreviewList');
            const imageCount = document.getElementById('imageCount');
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');

            if (previewItem) {
                previewItem.remove();

                // 更新计数
                const currentCount = imagePreviewList ? imagePreviewList.children.length : 0;
                if (imageCount) {
                    imageCount.textContent = currentCount.toString();
                }

                // 如果没有图片了，隐藏预览容器
                if (currentCount === 0 && imagePreviewContainer) {
                    imagePreviewContainer.style.display = 'none';
                }

                showToast('图片已移除', 'success');
            }
        }

        // Toast 提示功能
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // ==================== 需求列表功能 ====================

        // 导出需求数据
        function exportRequirements() {
            // 获取需求数据
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            
            if (requirements.length === 0) {
                showToast('暂无数据可导出', 'warning');
                return;
            }

            // 准备导出数据
            const csvData = [];
            csvData.push(['需求编号', '客户姓名', '联系电话', '房屋户型', '面积(㎡)', '预算范围', '状态', '创建时间']);

            requirements.forEach(req => {
                csvData.push([
                    req.id || 'N/A',
                    req.customer_name || 'N/A',
                    req.phone || 'N/A',
                    `${req.house_rooms || 0}室${req.house_halls || 0}厅${req.house_bathrooms || 0}卫`,
                    req.area || 'N/A',
                    req.budget || 'N/A',
                    req.status || '待处理',
                    req.createTime ? new Date(req.createTime).toLocaleString() : 'N/A'
                ]);
            });

            // 生成CSV内容
            const csvContent = csvData.map(row => 
                row.map(field => `"${field}"`).join(',')
            ).join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            link.href = url;
            link.download = `智能家居需求列表_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
            
            URL.revokeObjectURL(url);
            showToast('需求数据导出成功', 'success');
        }

        // 搜索需求
        function searchRequirements() {
            const searchTerm = document.getElementById('requirementSearch')?.value?.trim();
            
            if (!searchTerm) {
                loadRequirementsList(); // 重新加载全部数据
                return;
            }

            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const filteredRequirements = requirements.filter(req => {
                return (
                    (req.id && req.id.toLowerCase().includes(searchTerm.toLowerCase())) ||
                    (req.customer_name && req.customer_name.includes(searchTerm)) ||
                    (req.phone && req.phone.includes(searchTerm)) ||
                    (req.address && req.address.includes(searchTerm))
                );
            });

            updateRequirementsTable(filteredRequirements);
            showToast(`找到 ${filteredRequirements.length} 条相关需求`, 'info');
        }

        // 筛选需求
        function filterRequirements() {
            const filterStatus = document.getElementById('statusFilter')?.value;
            
            if (!filterStatus) {
                loadRequirementsList(); // 重新加载全部数据
                return;
            }

            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const filteredRequirements = requirements.filter(req => 
                req.status === filterStatus || (!req.status && filterStatus === '待处理')
            );

            updateRequirementsTable(filteredRequirements);
            showToast(`找到 ${filteredRequirements.length} 条${filterStatus}需求`, 'info');
        }

        // 更新需求表格
        function updateRequirementsTable(requirements) {
            const tbody = document.getElementById('requirementsTableBody');
            if (!tbody) return;

            if (requirements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">
                            <div>
                                <p style="margin-bottom: 16px;">暂无符合条件的需求数据</p>
                                <button class="btn btn-secondary" onclick="loadRequirementsList()">重置筛选</button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            // 重新计算原始索引
            const allRequirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            
            tbody.innerHTML = requirements.map((req) => {
                const originalIndex = allRequirements.findIndex(r => r.id === req.id);
                return `
                    <tr>
                        <td>${req.id || 'N/A'}</td>
                        <td>${req.customer_name || req.customerInfo?.name || 'N/A'}</td>
                        <td>${req.phone || req.customerInfo?.phone || 'N/A'}</td>
                        <td>${req.house_rooms || req.houseInfo?.rooms || 0}室${req.house_halls || req.houseInfo?.halls || 0}厅${req.house_bathrooms || req.houseInfo?.bathrooms || 0}卫</td>
                        <td>${req.area || req.houseInfo?.area || 'N/A'}㎡</td>
                        <td>${req.budget || req.houseInfo?.budget || 'N/A'}</td>
                        <td>
                            <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; background: #fef3c7; color: #92400e;">
                                ${req.status || '待处理'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-primary" onclick="viewRequirement(${originalIndex})" style="padding: 4px 8px; margin-right: 4px;">查看</button>
                            <button class="btn btn-secondary" onclick="editRequirement(${originalIndex})" style="padding: 4px 8px;">编辑</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // ==================== 数据分析功能 ====================

        // 更新分析数据
        function updateAnalysis() {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            
            // 计算各种统计数据
            const stats = {
                total: requirements.length,
                pending: requirements.filter(r => !r.status || r.status === '待处理').length,
                processing: requirements.filter(r => r.status === '处理中').length,
                completed: requirements.filter(r => r.status === '已完成').length
            };

            // 更新DOM
            if (document.getElementById('totalRequests')) {
                document.getElementById('totalRequests').textContent = stats.total;
            }
            if (document.getElementById('pendingRequests')) {
                document.getElementById('pendingRequests').textContent = stats.pending;
            }
            if (document.getElementById('processingRequests')) {
                document.getElementById('processingRequests').textContent = stats.processing;
            }
            if (document.getElementById('completedRequests')) {
                document.getElementById('completedRequests').textContent = stats.completed;
            }

            showToast('分析数据已更新', 'success');
        }

        // 刷新分析
        function refreshAnalysis() {
            updateAnalysis();
            showToast('数据已刷新', 'info');
        }

        // 导出分析报表
        function exportAnalysisReport() {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            
            if (requirements.length === 0) {
                showToast('暂无数据可导出', 'warning');
                return;
            }

            // 生成分析报表数据
            const reportData = [];
            reportData.push(['智能家居需求分析报表']);
            reportData.push(['生成时间', new Date().toLocaleString()]);
            reportData.push(['']);
            reportData.push(['基础统计']);
            reportData.push(['总需求数', requirements.length]);
            reportData.push(['待处理', requirements.filter(r => !r.status || r.status === '待处理').length]);
            reportData.push(['处理中', requirements.filter(r => r.status === '处理中').length]);
            reportData.push(['已完成', requirements.filter(r => r.status === '已完成').length]);
            reportData.push(['']);

            // 预算分布
            const budgetStats = {};
            requirements.forEach(req => {
                const budget = req.budget || '未设置';
                budgetStats[budget] = (budgetStats[budget] || 0) + 1;
            });

            reportData.push(['预算分布']);
            Object.entries(budgetStats).forEach(([budget, count]) => {
                reportData.push([budget, count]);
            });

            // 生成CSV
            const csvContent = reportData.map(row => 
                row.map(field => `"${field}"`).join(',')
            ).join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            link.href = url;
            link.download = `需求分析报表_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
            
            URL.revokeObjectURL(url);
            showToast('分析报表导出成功', 'success');
        }

        // ==================== 系统设置功能 ====================

        // 保存设置
        function saveSettings() {
            const settings = {
                defaultBudget: document.getElementById('defaultBudget')?.value || '',
                autoAssign: document.getElementById('autoAssign')?.checked || false,
                notification: document.getElementById('notification')?.checked || true,
                retention: document.getElementById('retention')?.value || '365',
                updateTime: new Date().toISOString()
            };

            localStorage.setItem('requirementSettings', JSON.stringify(settings));
            showToast('设置保存成功', 'success');
        }

        // 重置设置
        function resetSettings() {
            if (!confirm('确定要重置所有设置为默认值吗？')) {
                return;
            }

            localStorage.removeItem('requirementSettings');
            
            // 重置表单
            if (document.getElementById('defaultBudget')) document.getElementById('defaultBudget').value = '';
            if (document.getElementById('autoAssign')) document.getElementById('autoAssign').checked = false;
            if (document.getElementById('notification')) document.getElementById('notification').checked = true;
            if (document.getElementById('retention')) document.getElementById('retention').value = '365';

            showToast('设置已重置为默认值', 'success');
        }

        // 备份数据
        function backupData() {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const settings = JSON.parse(localStorage.getItem('requirementSettings') || '{}');
            
            const backupData = {
                requirements,
                settings,
                backupTime: new Date().toISOString(),
                version: '1.0'
            };

            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            link.href = url;
            link.download = `需求数据备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            showToast('数据备份完成', 'success');
        }

        // 清理数据
        function cleanupData() {
            if (!confirm('确定要清理所有历史数据吗？此操作不可撤销！')) {
                return;
            }

            if (!confirm('再次确认：这将删除所有需求数据和设置，确定继续吗？')) {
                return;
            }

            localStorage.removeItem('smart_home_requirements');
            localStorage.removeItem('currentRequirement');
            localStorage.removeItem('requirementSettings');
            
            // 刷新当前页面数据
            loadRequirementsList();
            
            showToast('数据清理完成', 'success');
        }

        // 加载设置
        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('requirementSettings') || '{}');
            
            if (document.getElementById('defaultBudget')) {
                document.getElementById('defaultBudget').value = settings.defaultBudget || '';
            }
            if (document.getElementById('autoAssign')) {
                document.getElementById('autoAssign').checked = settings.autoAssign || false;
            }
            if (document.getElementById('notification')) {
                document.getElementById('notification').checked = settings.notification !== false;
            }
            if (document.getElementById('retention')) {
                document.getElementById('retention').value = settings.retention || '365';
            }
        }
    </script>
<!-- Code injected by live-server -->
<script>
	// <![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// ]]>
</script>
</body>
</html>

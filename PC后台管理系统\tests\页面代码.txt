
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能家居需求管理 - Table版本</title>
    <!-- 使用本地图标，不依赖外部资源 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .logo-text {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 20px 8px;
        }

        .nav-item {
            display: block;
            padding: 8px 20px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
        }

        .nav-item:hover {
            background: #f3f4f6;
            color: #1f2937;
        }

        .nav-item.active {
            background: #000;
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
        }

        .breadcrumb-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .breadcrumb-description {
            color: #6b7280;
            font-size: 14px;
        }

        /* 流程步骤导航 */
        .process-nav {
            background: white;
            border-bottom: 2px solid #e5e7eb;
            padding: 0 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .process-steps {
            display: flex;
            align-items: center;
            gap: 0;
            min-height: 80px;
        }

        .process-step {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            border-bottom: 4px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            background: #f8fafc;
            border-right: 1px solid #e5e7eb;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .process-step:last-child {
            border-right: none;
        }

        .process-step.active {
            background: #3b82f6;
            color: white;
            border-bottom-color: #1e40af;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .process-step:hover:not(.active) {
            background: #e5e7eb;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .process-step.active:hover {
            background: #2563eb;
        }

        .step-number {
            display: inline-block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            line-height: 32px;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .process-step.active .step-number {
            background: rgba(255, 255, 255, 0.9);
            color: #3b82f6;
            border-color: rgba(255, 255, 255, 0.9);
        }

        .step-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
        }

        .page-content {
            flex: 1;
            padding: 24px;
        }

        /* 标签页内容 */
        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-content h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .tab-content p {
            color: #6b7280;
            margin-bottom: 20px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        /* 场景选择样式 */
        .scene-selection-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            min-height: 500px;
        }

        .scene-selection-left {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .scene-selection-right {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .scene-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .scene-card {
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .scene-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .scene-card.selected {
            border-color: #3b82f6;
            background: #eff6ff;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .scene-card h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .selected-scenes-table {
            max-height: 300px;
            overflow-y: auto;
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .scene-selection-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .scene-selection-right {
                order: -1;
            }

            .scene-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .scene-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 文件上传样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        /* 功能模块卡片 */
        .function-modules {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            margin-bottom: 24px;
        }

        .module-row {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            border-bottom: 1px solid #e5e7eb;
        }

        .module-card {
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            border-right: 1px solid #e5e7eb;
            background: white;
        }

        .module-card:last-child {
            border-right: none;
        }

        .module-card:hover {
            background: #f8fafc;
        }

        .module-card.active {
            background: #000;
            color: white;
        }

        .module-icon {
            width: 48px;
            height: 48px;
            background: #f3f4f6;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            transition: all 0.2s;
        }

        .module-card.active .module-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .module-content h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .module-content p {
            font-size: 12px;
            color: #6b7280;
        }

        .module-card.active .module-content p {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 内容区域 */
        .content-area {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .content-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            background: #fafafa;
        }

        .content-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .content-header p {
            color: #6b7280;
            font-size: 14px;
        }

        /* 产品选择区域 */
        .product-selection-area {
            padding: 24px;
        }

        /* Tab导航 */
        .product-tab-navigation {
            margin-bottom: 24px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .product-tab-navigation::-webkit-scrollbar {
            display: none;
        }

        .product-tab-container {
            display: flex;
            gap: 4px;
            min-width: max-content;
        }

        .product-tab-btn {
            padding: 12px 20px;
            border: 1px solid #e5e7eb;
            background: white;
            color: #6b7280;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .product-tab-btn:hover {
            border-color: #d1d5db;
            background: #f9fafb;
        }

        .product-tab-btn.active {
            background: #000;
            color: white;
            border-color: #000;
        }

        /* 产品列表 */
        .product-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .product-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 20px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .product-item:hover {
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .product-item.selected {
            border-color: #000;
            background: #f8fafc;
        }

        .product-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .product-content {
            flex: 1;
        }

        .product-title {
            font-size: 16px;
            font-weight: 600;
            color: #0f172a;
            margin-bottom: 4px;
        }

        .product-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }

        .product-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #000;
            flex-shrink: 0;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .module-row {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .module-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .module-row {
                grid-template-columns: 1fr;
            }
            
            .product-item {
                flex-direction: column;
                text-align: center;
            }
            
            .product-image {
                width: 100%;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
                <aside class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="logo-text">智能家居管理</div>
                        <div style="font-size: 10px; color: #6b7280;">管理系统</div>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
                        <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">个人中心</div>
                    <a href="../01-personal/my-todos.html" class="nav-item">我的代办</a>
                    <a href="../01-personal/my-orders.html" class="nav-item">我的订单</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <a href="../02-business/design-products.html" class="nav-item">设计商品</a>
                    <a href="../02-business/requirements-management.html" class="nav-item">需求管理</a>
                    <a href="../02-business/design-center.html" class="nav-item">设计中心</a>
                    <a href="../02-business/project-center.html" class="nav-item">项目中心</a>
                    <a href="../02-business/construction-management.html" class="nav-item">施工管理</a>
                    <a href="../02-business/construction-guide.html" class="nav-item">施工指导</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">商务中心</div>
                    <a href="../03-commerce/products.html" class="nav-item">商品管理</a>
                    <a href="../03-commerce/orders.html" class="nav-item">订单管理</a>
                    <a href="../03-commerce/customer-management.html" class="nav-item">客户管理</a>
                    <a href="../03-commerce/marketing-management.html" class="nav-item">营销管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">知识库</div>
                    <a href="../04-knowledge/design-knowledge.html" class="nav-item">设计知识库</a>
                    <a href="../04-knowledge/delivery-knowledge.html" class="nav-item">知识库管理</a>
                    <a href="../04-knowledge/wiring-knowledge.html" class="nav-item">布线知识库</a>
                    <a href="../04-knowledge/installation-knowledge.html" class="nav-item">安装知识库</a>
                    <a href="../04-knowledge/debugging-knowledge.html" class="nav-item">调试知识库</a>
                    <a href="../04-knowledge/product-knowledge.html" class="nav-item">产品知识库</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统工具</div>
                    <a href="../05-tools/api-tools.html" class="nav-item">API 工具</a>
                    <a href="../05-tools/erp-documentation.html" class="nav-item">ERP文档</a>
                    <a href="../05-tools/system-settings.html" class="nav-item">系统配置</a>
                    <a href="../05-tools/user-management.html" class="nav-item">用户管理</a>
                    <a href="../05-tools/internal-permissions.html" class="nav-item">内部权限</a>
                    <a href="../05-tools/customer-permissions.html" class="nav-item">客户权限</a>
                    <a href="../05-tools/data-management.html" class="nav-item">数据管理</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <a href="../06-analytics/requirements-analytics.html" class="nav-item">需求分析</a>
                    <a href="../06-analytics/project-analytics.html" class="nav-item">项目分析</a>
                    <a href="../06-analytics/order-analytics.html" class="nav-item">订单分析</a>
                    <a href="../06-analytics/customer-analytics.html" class="nav-item">客户分析</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">个人资料</div>
                    <a href="../07-profile/demo.html" class="nav-item">演示展示</a>
                    <a href="../07-profile/user-profile.html" class="nav-item">个人资料</a>
                    <a href="../07-profile/logout.html" class="nav-item">退出登录</a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-nav">
                <div class="breadcrumb-content">
                    <h1 class="breadcrumb-title">需求管理</h1>
                    <p class="breadcrumb-description">管理客户需求，从需求收集到完成交付的全流程管理</p>
                    <div style="margin-top: 12px; padding: 12px; background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; font-size: 14px; color: #1e40af;">
                        💡 <strong>使用提示：</strong>点击下方的流程步骤标签页可以切换到不同的功能模块。您可以直接点击任意步骤进行跳转。
                    </div>
                </div>
            </header>

            <!-- 流程步骤导航 -->
            <div class="process-nav">
                <div class="process-steps">
                    <div class="process-step active" onclick="showTab('tab1')">
                        <div class="step-number">1</div>
                        <div class="step-title">需求列表</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab2')">
                        <div class="step-number">2</div>
                        <div class="step-title">新建需求</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab3')">
                        <div class="step-number">3</div>
                        <div class="step-title">场景选择</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab4')">
                        <div class="step-number">4</div>
                        <div class="step-title">图纸上传</div>
                    </div>
                    <div class="process-step" onclick="showTab('tab5')">
                        <div class="step-number">5</div>
                        <div class="step-title">备注说明</div>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 标签页内容 -->
                <div id="tab1" class="tab-content active">
                    <h2>需求列表</h2>
                    <p>查看和管理所有客户需求</p>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                        <thead>
                            <tr style="background: #f9fafb;">
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">需求ID</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">客户姓名</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">联系电话</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">房屋户型</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">面积</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">预算范围</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">状态</th>
                                <th style="padding: 12px; border: 1px solid #e5e7eb;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="requirementsTableBody">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                    <div style="text-align: right; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="showTab('tab2')">➕ 新建需求</button>
                    </div>
                </div>

                <div id="tab2" class="tab-content">
                    <h2>新建需求</h2>
                    <p>填写客户基本信息和房屋信息</p>

                    <div style="margin-top: 30px;">
                        <h3 style="margin-bottom: 20px;">客户基本信息</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">客户姓名 *</label>
                                <input type="text" class="form-input" id="customerName" placeholder="请输入客户姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话 *</label>
                                <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="form-label">详细地址 *</label>
                                <input type="text" class="form-input" id="address" placeholder="请输入详细地址">
                            </div>
                        </div>

                        <h3 style="margin: 30px 0 20px;">房屋信息</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">房间数 *</label>
                                <input type="number" class="form-input" id="rooms" value="3" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">客厅数 *</label>
                                <input type="number" class="form-input" id="halls" value="2" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">卫生间数 *</label>
                                <input type="number" class="form-input" id="bathrooms" value="2" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">房屋面积 *</label>
                                <input type="number" class="form-input" id="area" placeholder="平方米" min="20" max="1000">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="form-label">预算范围 *</label>
                                <select class="form-input" id="budget">
                                    <option value="">请选择预算范围</option>
                                    <option value="20000-50000">2-5万元</option>
                                    <option value="50000-100000">5-10万元</option>
                                    <option value="100000-200000">10-20万元</option>
                                    <option value="200000-500000">20-50万元</option>
                                    <option value="500000+">50万元以上</option>
                                </select>
                            </div>
                        </div>

                        <div style="text-align: right; margin-top: 30px;">
                            <button class="btn btn-secondary" onclick="showTab('tab1')" style="margin-right: 10px;">返回列表</button>
                            <button class="btn btn-primary" onclick="saveAndNext('tab3')">保存并下一步</button>
                        </div>
                    </div>
                </div>

                <div id="tab3" class="tab-content">
                    <h2>场景选择</h2>

                    <div class="scene-selection-container" style="margin-top: 30px;">
                        <!-- 左侧：场景选择区域 -->
                        <div class="scene-selection-left">
                            <div class="scene-grid">
                                <div class="scene-card" onclick="toggleScene(this, 'lighting')" data-scene="lighting">
                                    <div style="font-size: 48px; margin-bottom: 15px;">💡</div>
                                    <h4>智能照明</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">全屋智能照明控制，支持场景模式、定时开关、亮度调节</p>
                                </div>
                                <div class="scene-card" onclick="toggleScene(this, 'security')" data-scene="security">
                                    <div style="font-size: 48px; margin-bottom: 15px;">🔒</div>
                                    <h4>安防监控</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">智能门锁、摄像头、传感器，全方位保护家庭安全</p>
                                </div>
                                <div class="scene-card" onclick="toggleScene(this, 'climate')" data-scene="climate">
                                    <div style="font-size: 48px; margin-bottom: 15px;">🌡️</div>
                                    <h4>环境控制</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">智能空调、新风系统、温湿度控制，舒适的居住环境</p>
                                </div>
                                <div class="scene-card" onclick="toggleScene(this, 'entertainment')" data-scene="entertainment">
                                    <div style="font-size: 48px; margin-bottom: 15px;">🎵</div>
                                    <h4>影音娱乐</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">智能音响、投影仪、背景音乐，打造家庭娱乐中心</p>
                                </div>
                                <div class="scene-card" onclick="toggleScene(this, 'curtains')" data-scene="curtains">
                                    <div style="font-size: 48px; margin-bottom: 15px;">🪟</div>
                                    <h4>窗帘控制</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">电动窗帘、百叶窗控制，自动调节室内光线</p>
                                </div>
                                <div class="scene-card" onclick="toggleScene(this, 'appliances')" data-scene="appliances">
                                    <div style="font-size: 48px; margin-bottom: 15px;">🏠</div>
                                    <h4>智能家电</h4>
                                    <p style="color: #6b7280; margin-top: 10px; font-size: 12px;">智能冰箱、洗衣机、扫地机器人等家电控制</p>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：已选择场景表格 -->
                        <div class="scene-selection-right">
                            <div class="selected-scenes-table">
                                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                    <thead>
                                        <tr style="background: #f9fafb;">
                                            <th style="padding: 12px; border: 1px solid #e5e7eb; text-align: left;">场景</th>
                                            <th style="padding: 12px; border: 1px solid #e5e7eb; text-align: left;">描述</th>
                                            <th style="padding: 12px; border: 1px solid #e5e7eb; text-align: center; width: 80px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="selectedScenesTableBody">
                                        <tr>
                                            <td colspan="3" style="padding: 40px; text-align: center; color: #6b7280;">
                                                请从左侧选择智能家居场景
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 操作按钮移到右侧表格下方 -->
                            <div style="margin-top: 20px; text-align: right;">
                                <button class="btn btn-secondary" onclick="showTab('tab2')" style="margin-right: 10px;">上一步</button>
                                <button class="btn btn-primary" onclick="saveAndNext('tab4')">保存并下一步</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tab4" class="tab-content">
                    <h2>图纸上传</h2>
                    <p>上传房屋平面图、设计图纸或参考图片</p>

                    <div class="upload-area" style="margin-top: 30px;" onclick="document.getElementById('fileInput').click()">
                        <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                        <div style="font-size: 16px; margin-bottom: 10px;">点击或拖拽上传文件</div>
                        <div style="font-size: 14px; color: #6b7280;">支持 JPG、PNG、PDF 格式，单个文件最大 10MB</div>
                        <input type="file" id="fileInput" accept="image/*,.pdf" multiple style="display: none;">
                    </div>

                    <div style="text-align: right; margin-top: 30px;">
                        <button class="btn btn-secondary" onclick="showTab('tab3')" style="margin-right: 10px;">上一步</button>
                        <button class="btn btn-primary" onclick="saveAndNext('tab5')">保存并下一步</button>
                    </div>
                </div>

                <div id="tab5" class="tab-content">
                    <h2>备注说明</h2>
                    <p>填写特殊需求和备注，确认需求信息</p>

                    <div style="margin-top: 30px;">
                        <div class="form-group">
                            <label class="form-label">详细需求描述</label>
                            <textarea class="form-input" rows="6" placeholder="请详细描述特殊需求、偏好设置、注意事项等..."></textarea>
                        </div>

                        <h3 style="margin: 30px 0 20px;">需求汇总</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">客户姓名</div>
                                <div style="font-weight: 500;" id="summaryName">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">联系电话</div>
                                <div style="font-weight: 500;" id="summaryPhone">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">房屋户型</div>
                                <div style="font-weight: 500;" id="summaryHouseType">未填写</div>
                            </div>
                            <div style="padding: 15px; background: #f9fafb; border-radius: 6px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">房屋面积</div>
                                <div style="font-weight: 500;" id="summaryArea">未填写</div>
                            </div>
                        </div>

                        <div style="text-align: right; margin-top: 30px;">
                            <button class="btn btn-secondary" onclick="showTab('tab4')" style="margin-right: 10px;">上一步</button>
                            <button class="btn btn-success" onclick="submitRequirement()" style="background: #10b981;">提交完整需求</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 产品选择模块 -->
                    <div id="product-selection" class="module-content active">
                        <div class="content-header">
                            <h2>智能家居场景选择</h2>
                            <p>请根据客户需求选择智能场景，支持多选</p>
                        </div>
                        
                        <div class="product-selection-area">
                            <!-- 产品分类Tab -->
                            <div class="product-tab-navigation">
                                <div class="product-tab-container">
                                    <button class="product-tab-btn active" data-category="lighting" onclick="switchProductCategory('lighting')">
                                        💡 智能照明
                                    </button>
                                    <button class="product-tab-btn" data-category="security" onclick="switchProductCategory('security')">
                                        📷 监控类
                                    </button>
                                    <button class="product-tab-btn" data-category="custom" onclick="switchProductCategory('custom')">
                                        🎨 定制场景
                                    </button>
                                    <button class="product-tab-btn" data-category="auto" onclick="switchProductCategory('auto')">
                                        🤖 自动场景
                                    </button>
                                    <button class="product-tab-btn" data-category="safety" onclick="switchProductCategory('safety')">
                                        🛡️ 安防类
                                    </button>
                                    <button class="product-tab-btn" data-category="audio" onclick="switchProductCategory('audio')">
                                        🎵 智能影音
                                    </button>
                                    <button class="product-tab-btn" data-category="ai" onclick="switchProductCategory('ai')">
                                        🗣️ AI语音
                                    </button>
                                    <button class="product-tab-btn" data-category="hotel" onclick="switchProductCategory('hotel')">
                                        🏨 酒店民宿
                                    </button>
                                    <button class="product-tab-btn" data-category="business" onclick="switchProductCategory('business')">
                                        🏢 商业场景
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 产品列表 -->
                            <div class="product-list" id="productList">
                                <!-- 产品将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 需求表单模块 -->
                    <div id="requirement-form" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>需求表单</h2>
                            <p>填写客户需求信息，与H5端功能保持一致</p>
                        </div>

                        <div class="form-container" style="padding: 24px;">
                            <form id="requirementForm">
                                <!-- 基本信息 -->
                                <div class="form-section">
                                    <h3 class="section-title">基本信息</h3>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label" for="customerName">客户姓名 <span class="required">*</span></label>
                                            <input type="text" id="customerName" class="form-input" placeholder="请输入客户姓名" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label" for="phone">联系电话 <span class="required">*</span></label>
                                            <input type="tel" id="phone" class="form-input" placeholder="请输入联系电话" required>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="address">详细地址 <span class="required">*</span></label>
                                        <input type="text" id="address" class="form-input" placeholder="请输入详细地址" required>
                                    </div>
                                </div>

                                <!-- 房屋信息 -->
                                <div class="form-section">
                                    <h3 class="section-title">房屋信息</h3>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">房屋户型 <span class="required">*</span></label>
                                            <div class="house-type-selector">
                                                <div class="house-type-item">
                                                    <label for="rooms">室</label>
                                                    <input type="number" id="rooms" min="1" max="10" value="3" required>
                                                </div>
                                                <div class="house-type-item">
                                                    <label for="halls">厅</label>
                                                    <input type="number" id="halls" min="1" max="10" value="2" required>
                                                </div>
                                                <div class="house-type-item">
                                                    <label for="bathrooms">卫</label>
                                                    <input type="number" id="bathrooms" min="1" max="10" value="2" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label" for="area">房屋面积 <span class="required">*</span></label>
                                            <input type="number" id="area" class="form-input" placeholder="平方米" min="20" max="1000" required>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="budget">预算范围 <span class="required">*</span></label>
                                        <select id="budget" class="form-input" required>
                                            <option value="">请选择预算范围</option>
                                            <option value="20000-50000">2-5万元</option>
                                            <option value="50000-100000">5-10万元</option>
                                            <option value="100000-200000">10-20万元</option>
                                            <option value="200000-500000">20-50万元</option>
                                            <option value="500000+">50万元以上</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 产品选择提示 -->
                                <div class="form-section">
                                    <h3 class="section-title">产品选择</h3>
                                    <div class="selected-products-display">
                                        <p class="form-hint">请先在"产品选择"模块中选择所需产品，已选择的产品将自动关联到此需求。</p>
                                        <div id="formSelectedProducts" class="selected-products-list">
                                            <span class="no-selection">暂未选择任何产品</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 需求描述 -->
                                <div class="form-section">
                                    <h3 class="section-title">需求描述</h3>
                                    <div class="form-group">
                                        <label class="form-label" for="requirements">详细需求 <span class="optional">(可选)</span></label>
                                        <textarea id="requirements" class="form-input form-textarea" placeholder="请详细描述您的智能家居需求，如：希望实现哪些功能、有什么特殊要求等..." rows="4"></textarea>
                                    </div>
                                </div>

                                <!-- 参考图片 -->
                                <div class="form-section">
                                    <h3 class="section-title">参考图片</h3>
                                    <div class="form-group">
                                        <label class="form-label">上传图片 <span class="optional">(可选)</span></label>
                                        <div class="image-upload-container">
                                            <!-- 上传区域 -->
                                            <div class="upload-area" id="uploadArea">
                                                <div class="upload-content">
                                                    <div class="upload-icon">📷</div>
                                                    <div class="upload-text">点击或拖拽上传图片</div>
                                                    <div class="upload-hint">支持 JPG、PNG、WEBP 格式，单张最大 5MB，最多 6 张</div>
                                                </div>
                                                <input type="file" id="imageInput" accept="image/jpeg,image/png,image/webp" multiple style="display: none;">
                                            </div>

                                            <!-- 图片预览区域 -->
                                            <div class="image-preview-container" id="imagePreviewContainer" style="display: none;">
                                                <div class="preview-header">
                                                    <span class="preview-title">已选择图片 (<span id="imageCount">0</span>/6)</span>
                                                    <button type="button" class="clear-all-btn" onclick="clearAllImages()">清空所有</button>
                                                </div>
                                                <div class="image-preview-grid" id="imagePreviewList"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="submit-section">
                                    <button type="submit" class="submit-btn" id="submitBtn">
                                        <span class="btn-text">提交需求</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div id="requirements-list" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>需求列表</h2>
                            <p>查看和管理所有需求</p>
                        </div>
                        <div style="padding: 24px;">
                            <p>需求列表功能开发中...</p>
                        </div>
                    </div>

                    <div id="data-analysis" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>数据分析</h2>
                            <p>需求统计和分析报告</p>
                        </div>
                        <div style="padding: 24px;">
                            <p>数据分析功能开发中...</p>
                        </div>
                    </div>

                    <div id="system-settings" class="module-content" style="display: none;">
                        <div class="content-header">
                            <h2>系统设置</h2>
                            <p>配置系统参数</p>
                        </div>
                        <div style="padding: 24px;">
                            <p>系统设置功能开发中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 产品数据
        const productData = {
            lighting: [
                {
                    id: 'lighting_1',
                    title: '客餐厅无主灯',
                    desc: '无主灯设计的筒灯、射灯、灯带、轨道灯组合，营造层次丰富的照明效果',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif'
                },
                {
                    id: 'lighting_2',
                    title: '客厅无主灯场景',
                    desc: '通过情景面板或语音控制场景切换，实现不同时段的照明需求',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif'
                },
                {
                    id: 'lighting_3',
                    title: '卧室场景',
                    desc: '温馨、助眠、起夜、阅读等不同场景变化，满足卧室多样化需求',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222987728772.gif'
                },
                {
                    id: 'lighting_4',
                    title: '夏冬场景照明',
                    desc: '四季如春的智能调光定制场景，根据季节自动调整色温和亮度',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207113660186018.gif'
                }
            ],
            security: [
                {
                    id: 'security_1',
                    title: '空气质量智能显示',
                    desc: '实时显示CO2、tVoc、PM2.5、温湿度等参数，守护家庭健康',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326103963896389.jpg'
                },
                {
                    id: 'security_2',
                    title: '中控屏监控视窗',
                    desc: '中控屏直接显示监控实时画面，随时掌握家中动态',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326110565366536.jpg'
                },
                {
                    id: 'security_3',
                    title: '监控双向对讲',
                    desc: '实时观看屋外画面并与来访者对讲，安全便捷',
                    image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926112142764276.jpg'
                }
            ],
            custom: [
                {
                    id: 'custom_1',
                    title: '智能调光',
                    desc: '色温亮度随意掌控，灯光缓开缓灭，营造舒适的光环境',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif'
                },
                {
                    id: 'custom_2',
                    title: '面板个性化雕刻',
                    desc: '开关面板雕刻图案和文字提示，个性化定制专属标识',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif'
                },
                {
                    id: 'custom_3',
                    title: '预回家场景',
                    desc: '烈日炎炎，未到家，空调已经凉风习习；寒风凛冽，未到家，房间已经温暖如春',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190516162537523752.jpg'
                }
            ],
            auto: [
                {
                    id: 'auto_1',
                    title: '回家场景',
                    desc: '使用指定指纹解锁可启动常用的回家场景，灯光逐个打开，窗帘打开，电视打开',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200207113660186018.gif'
                },
                {
                    id: 'auto_2',
                    title: '离家场景',
                    desc: '离家时只需一个动作，关闭家中所有灯光、窗帘、空调、景观台及其他电器；扫地机器人开始清扫',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326110565366536.jpg'
                },
                {
                    id: 'auto_3',
                    title: '睡眠场景',
                    desc: '安防系统会开启来保证您的睡眠安全；空调和空气净化器也被设置成不打扰您休息的睡眠模式',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222987728772.gif'
                },
                {
                    id: 'auto_4',
                    title: '起床场景',
                    desc: '空调提前调到适合温度，灯光柔和舒适，窗帘缓缓开启，布防模式自动关闭',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222535483548.gif'
                }
            ],
            safety: [
                {
                    id: 'safety_1',
                    title: '预防天然气泄漏',
                    desc: '厨房天然气泄漏预警守卫厨房安全，天然气浓度达到爆炸阈值的4%就可以触发高分贝报警声',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200326103963896389.jpg'
                },
                {
                    id: 'safety_2',
                    title: '火灾预警',
                    desc: '家庭里一旦烟雾浓度过高，烟雾报警器立即发出刺耳的报警声，及时通知到您',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2020032610390637637.jpg'
                },
                {
                    id: 'safety_3',
                    title: '漏水自动断水',
                    desc: '浸水感应器检测到漏水网关发出警报，并向手机推送警报通知，对应水路的进水阀门中断水路',
                    image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926112142764276.jpg'
                },
                {
                    id: 'safety_4',
                    title: '智能猫眼',
                    desc: '智能猫眼由门外广角摄像头主门内高清显示平板组成，当门外有人，门内的人经过屏幕则自动亮起',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816155029322932.jpg'
                }
            ],
            audio: [
                {
                    id: 'audio_1',
                    title: '一键KTV',
                    desc: '通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现KTV画面和声音一键切换到KTV模式',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20200308131356585658.gif'
                },
                {
                    id: 'audio_2',
                    title: '一键观影',
                    desc: '通过小爱语音、无线开关、智能家居控制中心等设备一键操作。实现电视画面和声音一键切换到观影模式',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220816164220722072.gif'
                },
                {
                    id: 'audio_3',
                    title: '小爱背景音乐',
                    desc: '小爱音箱语音点歌用无线开关远程切歌、开始播放、停止播放联动回家或离家场景',
                    image: 'https://www.cx-smarthome.com:443/sysmain/home/<USER>/20210926094113671367.jpg'
                },
                {
                    id: 'audio_4',
                    title: '观影模式',
                    desc: '电视和影音的结合，足不出户享受电影院的视听震撼新体验。一键切换观景模式',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/2022081616430477477.gif'
                }
            ],
            ai: [
                {
                    id: 'ai_1',
                    title: '智能手表控制',
                    desc: '通过小米智能手表控制全屋灯光、米家全系列智能家电、智能场景',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20191207222668326832.gif'
                },
                {
                    id: 'ai_2',
                    title: '语音控制',
                    desc: '劳累了一天的你只需要躺在床上，一切的控制只需一句话。寂寞的夜，一切动作都显多余',
                    image: 'https://cloud.cx-smarthome.com:443/vip95cx111158888/sysmain/home/<USER>/20210926095518211821.jpg'
                },
                {
                    id: 'ai_3',
                    title: '米家平板中控',
                    desc: '平板磁吸上墙，开启米家中控模式秒变家庭中控屏，可以通过卡片直观地控制智能家居设备',
                    image: 'https://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20240326154048154815.jpg'
                }
            ],
            hotel: [
                {
                    id: 'hotel_1',
                    title: '酒店AI语音控制',
                    desc: '房间内可实现语音控制窗帘开关、电视开关、电视节目内容搜索、灯光开关',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162498089808.jpg'
                },
                {
                    id: 'hotel_2',
                    title: '回房场景',
                    desc: '住客通过门卡开门以后，门厅灯自动亮起，同时小爱同学提示请将房卡插入卡槽',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162316821682.jpg'
                },
                {
                    id: 'hotel_3',
                    title: '离房场景',
                    desc: '当住客离开房间的时候，门厅的灯会为方便他换鞋而暂时的亮起，其它的灯会自动的熄灭',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190317162329462946.jpg'
                }
            ],
            business: [
                {
                    id: 'business_1',
                    title: '人体运动监测',
                    desc: '公司重要位置及每个办公室通过人体运动传感器，让您掌握公司内下班无人后的安全动态',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323223031353135.jpg'
                },
                {
                    id: 'business_2',
                    title: '一键上下班场景',
                    desc: '一键上下班可以节约最后离开的员工整理时间，可以有效保障公司的用电安全',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/20190323220525172517.jpg'
                },
                {
                    id: 'business_3',
                    title: '会议投影场景',
                    desc: '一键开会，自动完成以下动作：关闭会议室主灯、将投影幕布展开到适当位置、关闭窗帘',
                    image: 'http://cloud.cx-smarthome.com/vip95cx111158888/sysmain/home/<USER>/2019032321560442442.jpg'
                },
                {
                    id: 'business_4',
                    title: '商业照明场景',
                    desc: '智能场景随心切换，每个区域设定不同场景，让照明随场景不同而随心切换',
                    image: 'https://www.cx-smarthome.com/sysmain/home/<USER>/20220414141238523852.gif'
                }
            ]
        };

        // 当前选中的产品
        let selectedProducts = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认加载智能照明产品
            switchProductCategory('lighting');
        });

        // 切换主模块
        function showModule(moduleId) {
            // 更新模块卡片状态
            document.querySelectorAll('.module-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.module-card').classList.add('active');

            // 显示对应的内容
            document.querySelectorAll('.module-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(moduleId).style.display = 'block';
        }

        // 切换产品分类
        function switchProductCategory(category) {
            console.log('切换到分类:', category);

            // 更新Tab按钮状态
            document.querySelectorAll('.product-tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // 加载对应分类的产品
            loadProducts(category);
        }

        // 加载产品列表
        function loadProducts(category) {
            console.log('加载产品分类:', category);
            const productList = document.getElementById('productList');
            if (!productList) {
                console.error('产品列表容器未找到');
                return;
            }

            const products = productData[category] || [];
            console.log('找到产品数量:', products.length);

            if (products.length === 0) {
                productList.innerHTML = '<div style="text-align: center; padding: 40px; color: #64748b;">暂无产品数据</div>';
                return;
            }

            productList.innerHTML = products.map(product => `
                <div class="product-item" onclick="toggleProduct('${product.id}')">
                    <img src="${product.image}" alt="${product.title}" class="product-image" onerror="this.style.display='none'">
                    <div class="product-content">
                        <div class="product-title">${product.title}</div>
                        <div class="product-desc">${product.desc}</div>
                    </div>
                    <input type="checkbox" class="product-checkbox" id="${product.id}" name="selectedProducts" value="${product.id}">
                </div>
            `).join('');

            console.log('产品列表已更新');
        }

        // 切换产品选择状态
        function toggleProduct(productId) {
            const checkbox = document.getElementById(productId);
            const productItem = checkbox.closest('.product-item');

            if (checkbox.checked) {
                checkbox.checked = false;
                productItem.classList.remove('selected');
                selectedProducts = selectedProducts.filter(id => id !== productId);
            } else {
                checkbox.checked = true;
                productItem.classList.add('selected');
                selectedProducts.push(productId);
            }

            console.log('已选择的产品:', selectedProducts);
        }

        // 清空所有选择
        function clearAllSelections() {
            selectedProducts = [];
            document.querySelectorAll('.product-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.querySelectorAll('.product-item').forEach(item => {
                item.classList.remove('selected');
            });
            console.log('已清空所有选择');
        }

        // 保存选择
        function saveSelections() {
            if (selectedProducts.length === 0) {
                alert('请至少选择一个产品');
                return;
            }

            console.log('保存选择的产品:', selectedProducts);
            alert(`已保存 ${selectedProducts.length} 个产品选择`);
        }

        // ===== 新增：标签页功能 =====

        // 场景数据定义
        const sceneData = {
            lighting: { name: '智能照明', description: '全屋智能照明控制，支持场景模式、定时开关、亮度调节', icon: '💡' },
            security: { name: '安防监控', description: '智能门锁、摄像头、传感器，全方位保护家庭安全', icon: '🔒' },
            climate: { name: '环境控制', description: '智能空调、新风系统、温湿度控制，舒适的居住环境', icon: '🌡️' },
            entertainment: { name: '影音娱乐', description: '智能音响、投影仪、背景音乐，打造家庭娱乐中心', icon: '🎵' },
            curtains: { name: '窗帘控制', description: '电动窗帘、百叶窗控制，自动调节室内光线', icon: '🪟' },
            appliances: { name: '智能家电', description: '智能冰箱、洗衣机、扫地机器人等家电控制', icon: '🏠' }
        };

        let selectedScenes = [];
        let currentTab = 'tab1';
        let requirementData = {
            customerInfo: {},
            houseInfo: {},
            selectedScenes: [],
            uploadedFiles: [],
            notes: ''
        };

        // 标签页切换
        function showTab(tabId) {
            console.log('切换到标签页:', tabId);

            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航标签的激活状态
            document.querySelectorAll('.process-step').forEach(step => {
                step.classList.remove('active');
            });

            // 显示目标标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('显示标签页:', tabId);
            } else {
                console.error('找不到标签页:', tabId);
            }

            // 激活对应的导航标签
            const tabIndex = parseInt(tabId.replace('tab', ''));
            const processSteps = document.querySelectorAll('.process-step');
            if (processSteps[tabIndex - 1]) {
                processSteps[tabIndex - 1].classList.add('active');
            }

            currentTab = tabId;

            // 根据标签页执行特定操作
            switch(tabId) {
                case 'tab1':
                    loadRequirementsList();
                    break;
                case 'tab5':
                    updateSummary();
                    break;
            }
        }

        // 切换场景选择
        function toggleScene(element, sceneId) {
            const scene = sceneData[sceneId];
            if (!scene) return;

            element.classList.toggle('selected');

            const index = selectedScenes.findIndex(s => s.id === sceneId);
            if (index > -1) {
                // 移除选择
                selectedScenes.splice(index, 1);
            } else {
                // 添加选择
                selectedScenes.push({
                    id: sceneId,
                    name: scene.name,
                    description: scene.description,
                    icon: scene.icon
                });
            }

            updateSelectedScenesTable();
            console.log('当前选择的场景:', selectedScenes);
        }

        // 更新已选择场景表格
        function updateSelectedScenesTable() {
            const tbody = document.getElementById('selectedScenesTableBody');
            if (!tbody) return;

            if (selectedScenes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" style="padding: 40px; text-align: center; color: #6b7280;">
                            请从左侧选择智能家居场景
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = selectedScenes.map((scene, index) => `
                <tr>
                    <td style="padding: 12px; border: 1px solid #e5e7eb;">
                        <div style="display: flex; align-items: center;">
                            <span style="font-size: 20px; margin-right: 8px;">${scene.icon}</span>
                            <span style="font-weight: 500;">${scene.name}</span>
                        </div>
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb; font-size: 13px; color: #6b7280;">
                        ${scene.description}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb; text-align: center;">
                        <button onclick="removeScene('${scene.id}')" style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; cursor: pointer;">
                            移除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 移除场景
        function removeScene(sceneId) {
            // 从选择列表中移除
            selectedScenes = selectedScenes.filter(s => s.id !== sceneId);

            // 更新卡片状态
            const sceneCard = document.querySelector(`[data-scene="${sceneId}"]`);
            if (sceneCard) {
                sceneCard.classList.remove('selected');
            }

            // 更新表格
            updateSelectedScenesTable();
            console.log('移除场景:', sceneId);
        }

        // 保存并下一步
        function saveAndNext(nextTab) {
            if (validateCurrentTab()) {
                saveCurrentTabData();
                showTab(nextTab);
            }
        }

        // 验证当前标签页
        function validateCurrentTab() {
            switch (currentTab) {
                case 'tab2':
                    return validateRequirementForm();
                default:
                    return true;
            }
        }

        // 验证需求表单
        function validateRequirementForm() {
            const requiredFields = ['customerName', 'phone', 'address', 'rooms', 'halls', 'bathrooms', 'area', 'budget'];

            for (const field of requiredFields) {
                const element = document.getElementById(field);
                if (!element || !element.value.trim()) {
                    alert(`请填写${element ? element.previousElementSibling.textContent.replace(' *', '') : field}`);
                    if (element) element.focus();
                    return false;
                }
            }

            // 验证手机号
            const phone = document.getElementById('phone').value.trim();
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                document.getElementById('phone').focus();
                return false;
            }

            return true;
        }

        // 保存当前标签页数据
        function saveCurrentTabData() {
            switch (currentTab) {
                case 'tab2':
                    saveRequirementFormData();
                    break;
            }

            // 保存到localStorage
            localStorage.setItem('currentRequirement', JSON.stringify(requirementData));
        }

        // 保存需求表单数据
        function saveRequirementFormData() {
            requirementData.customerInfo = {
                name: document.getElementById('customerName').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                address: document.getElementById('address').value.trim()
            };

            requirementData.houseInfo = {
                rooms: parseInt(document.getElementById('rooms').value),
                halls: parseInt(document.getElementById('halls').value),
                bathrooms: parseInt(document.getElementById('bathrooms').value),
                area: document.getElementById('area').value.trim(),
                budget: document.getElementById('budget').value
            };
        }

        // 加载需求列表
        function loadRequirementsList() {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const tbody = document.getElementById('requirementsTableBody');

            if (!tbody) return;

            if (requirements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">
                            <div>
                                <p style="margin-bottom: 16px;">暂无需求数据</p>
                                <button class="btn btn-primary" onclick="showTab('tab2')">创建第一个需求</button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = requirements.map((req, index) => `
                <tr>
                    <td>${req.id || 'N/A'}</td>
                    <td>${req.customer_name || req.customerInfo?.name || 'N/A'}</td>
                    <td>${req.phone || req.customerInfo?.phone || 'N/A'}</td>
                    <td>${req.house_rooms || req.houseInfo?.rooms || 0}室${req.house_halls || req.houseInfo?.halls || 0}厅${req.house_bathrooms || req.houseInfo?.bathrooms || 0}卫</td>
                    <td>${req.area || req.houseInfo?.area || 'N/A'}㎡</td>
                    <td>${req.budget || req.houseInfo?.budget || 'N/A'}</td>
                    <td>
                        <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; background: #fef3c7; color: #92400e;">
                            ${req.status || '待处理'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary" onclick="viewRequirement(${index})" style="padding: 4px 8px; margin-right: 4px;">查看</button>
                        <button class="btn btn-secondary" onclick="editRequirement(${index})" style="padding: 4px 8px;">编辑</button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新汇总信息
        function updateSummary() {
            if (requirementData.customerInfo.name) {
                document.getElementById('summaryName').textContent = requirementData.customerInfo.name;
                document.getElementById('summaryPhone').textContent = requirementData.customerInfo.phone;
                document.getElementById('summaryHouseType').textContent =
                    `${requirementData.houseInfo.rooms}室${requirementData.houseInfo.halls}厅${requirementData.houseInfo.bathrooms}卫`;
                document.getElementById('summaryArea').textContent = requirementData.houseInfo.area + '㎡';
            }
        }

        // 提交需求
        function submitRequirement() {
            // 生成需求ID
            const requirementId = 'REQ_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 组织最终数据
            const finalData = {
                id: requirementId,
                customer_name: requirementData.customerInfo.name,
                phone: requirementData.customerInfo.phone,
                address: requirementData.customerInfo.address,
                house_rooms: requirementData.houseInfo.rooms,
                house_halls: requirementData.houseInfo.halls,
                house_bathrooms: requirementData.houseInfo.bathrooms,
                area: requirementData.houseInfo.area,
                budget: requirementData.houseInfo.budget,
                status: '待处理',
                createTime: new Date().toISOString(),
                source: 'pc_admin'
            };

            // 保存到localStorage
            let requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            requirements.unshift(finalData);
            localStorage.setItem('smart_home_requirements', JSON.stringify(requirements));

            // 清除当前需求数据
            localStorage.removeItem('currentRequirement');

            alert(`需求提交成功！\n需求编号：${requirementId}`);

            // 返回需求列表
            showTab('tab1');
        }

        // 查看需求详情
        function viewRequirement(index) {
            const requirements = JSON.parse(localStorage.getItem('smart_home_requirements') || '[]');
            const req = requirements[index];

            if (!req) {
                alert('需求数据不存在');
                return;
            }

            const details = `
需求详情：

📋 基本信息：
• 需求编号：${req.id}
• 客户姓名：${req.customer_name}
• 联系电话：${req.phone}
• 详细地址：${req.address}

🏠 房屋信息：
• 房屋户型：${req.house_rooms}室${req.house_halls}厅${req.house_bathrooms}卫
• 房屋面积：${req.area}㎡
• 预算范围：${req.budget}

📊 状态信息：
• 处理状态：${req.status}
• 创建时间：${new Date(req.createTime).toLocaleString()}
            `;

            alert(details);
        }

        // 编辑需求
        function editRequirement(index) {
            alert('编辑功能开发中...');
        }
    </script>
<!-- Code injected by live-server -->
<script>
	// <![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// ]]>
</script>
</body>
</html>

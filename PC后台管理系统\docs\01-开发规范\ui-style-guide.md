# 后台系统UI样式与结构指南

**版本**: v1.0
**制定日期**: 2024年7月26日

## 1. 目的

本指南旨在为PC后台管理系统建立一套统一的UI样式和页面结构规范。所有新页面的开发都应遵循此指南，以确保系统整体的视觉一致性和开发效率。

---

## 2. 整体布局风格

我们采用 **"黑白风格"** 的主题，其核心是：
- **侧边栏**：深色背景 (`bg-gray-800`)，白色文字。
- **内容区**：浅灰色背景 (`bg-gray-100`)，卡片/页面主体为白色背景 (`bg-white`)。
- **激活/高亮色**：蓝色 (`#2563eb`)，用于标识当前选中的菜单项、按钮和重要链接。

---

## 3. 字体规范 (Typography)

为了保持界面的清晰和层级感，我们定义以下字体标准 (基于Tailwind CSS)。

| 用途 | 描述 | Tailwind CSS 类 | 示例 |
| --- | --- | --- | --- |
| **页面主标题 (H1)** | 每个页面的最顶层标题 | `text-2xl font-semibold text-gray-900` | **用户管理** |
| **内容区大标题 (H2)** | 卡片或主要内容区域的标题 | `text-xl font-semibold text-gray-800` | **用户列表** |
| **卡片/分组标题 (H3)** | 卡片内或表单分组的标题 | `text-lg font-medium text-gray-700` | 用户基本信息 |
| **正文/描述文字** | 普通的段落或描述信息 | `text-sm text-gray-600` | 在这里管理所有用户的数据。 |
| **表格头部文字** | 表格列的标题 | `text-xs font-medium text-gray-500 uppercase tracking-wider` | 用户ID |
| **表格单元格文字** | 表格中的数据 | `text-sm text-gray-800` | 1001 |
| **输入框标签** | 表单项的标签 | `text-sm font-medium text-gray-700` | 登录账号 |
| **链接** | 普通的文字链接 | `text-sm text-blue-600 hover:text-blue-700` | 查看详情 |

---

## 4. 页面结构模板

### 4.1 列表页 (List Page)

用于展示数据列表，如用户列表、订单列表等。

**参考实现**: `users.html`

**标准HTML结构**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- ... head ... -->
</head>
<body class="bg-gray-100 p-6">
    <div class="bg-white p-6 rounded-lg shadow-md">
        <!-- 1. 页面主标题 -->
        <h1 class="text-2xl font-semibold text-gray-900 mb-4">用户管理</h1>

        <!-- 2. 过滤与操作区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center space-x-2">
                <!-- 搜索框和筛选器 -->
            </div>
            <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                添加新用户
            </button>
        </div>

        <!-- 3. 数据表格 -->
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">...</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据行 -->
                </tbody>
            </table>
        </div>

        <!-- 4. 分页 -->
        <div class="mt-4 flex justify-end">
            <!-- 分页组件 -->
        </div>
    </div>
</body>
</html>
```

### 4.2 表单页 / 详情页 (Form/Detail Page)

用于创建或编辑一条具体的数据。

**标准HTML结构**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- ... head ... -->
</head>
<body class="bg-gray-100 p-6">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto">
        <!-- 1. 页面主标题和描述 -->
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">创建新用户</h1>
            <p class="mt-1 text-sm text-gray-600">请填写以下信息以创建一个新用户账号。</p>
        </div>

        <div class="mt-6 border-t border-gray-200"></div>

        <form class="mt-6 space-y-6">
            <!-- 2. 表单分组 -->
            <div>
                <h3 class="text-lg font-medium text-gray-700">账号信息</h3>
                <div class="mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
                    <!-- 表单项 -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
                        <div class="mt-1">
                            <input type="text" name="username" id="username" class="block w-full shadow-sm sm:text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <!-- ... 更多表单项 ... -->
                </div>
            </div>

            <div class="mt-6 border-t border-gray-200"></div>

            <!-- 3. 表单提交按钮 -->
            <div class="pt-5">
                <div class="flex justify-end">
                    <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </div>
        </form>
    </div>
</body>
</html>
```
---

此文档应作为所有前端页面开发的**首要参考**。如有任何关于UI/UE的疑问，应首先参照本指南。 